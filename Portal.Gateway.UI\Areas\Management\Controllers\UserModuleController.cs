﻿using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.Management.User;
using Portal.Gateway.ApiModel.Requests.Management.UserModule;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Management.User;
using Portal.Gateway.ApiModel.Responses.Management.UserModule;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.User;
using Portal.Gateway.UI.Areas.Management.ViewModels.UserModule;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class UserModuleController : BaseController<UserModuleController>
    {
        public UserModuleController(
                IOptions<AppSettings> appSettings,
                ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        #region Add

        public IActionResult PartialAddUserModule(string encryptedUserId)
        {
            var viewModel = new AddUserModuleViewModel
            {
                EncryptedUsedId = encryptedUserId
            };

            return PartialView("_AddUserModule", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddUserModule(AddUserModuleViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddUserModuleApiRequest
            {
                CompanyId = UserSession.CompanyId,
                UserId = viewModel.EncryptedUsedId.ToDecryptInt(),
                ModuleId = viewModel.ModuleId
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Management.AddUserModule, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteUserModule(string encryptedUserModuleId)
        {
            int id = encryptedUserModuleId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Management.DeleteUserModule + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> List(string encryptedUserId)
        {
            if (string.IsNullOrEmpty(encryptedUserId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "User", new { Area = "Management" });
            }

            int userId = encryptedUserId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<UserApiResponse>>
                (ApiMethodName.Management.GetUser + userId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", result);
                return RedirectToAction("List", "User", new { Area = "Management" });
            }

            var userViewModel = new UserViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Name = apiResponse.Data.Name,
                Surname = apiResponse.Data.Surname,
                Email = apiResponse.Data.Email,
                PhoneNumber = apiResponse.Data.PhoneNumber,
                IsActive = apiResponse.Data.IsActive
            };

            ViewData["UserModule_List_User"] = userViewModel;
            return View();
        }

        public async Task<IActionResult> GetUserModules([DataSourceRequest] DataSourceRequest request, string encryptedUserId)
        {
            var apiRequest = new UserModulesApiRequest
            {
                CompanyId = UserSession.CompanyId,
                UserId = encryptedUserId.ToDecryptInt()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UserModulesApiResponse>>
                (apiRequest, ApiMethodName.Management.GetUserModules, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var data = new List<UserModuleViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.UserModules.Any())
            {
                data = apiResponse.Data.UserModules.Select(p => new UserModuleViewModel
                {
                    EncryptedId = p.Id.ToEncrypt(),
                    UserId = p.UserId,
                    ModuleId = p.ModuleId,
                    User = new UserViewModel
                    {
                        EncryptedId = p.User.Id.ToEncrypt(),
                        Name = p.User.Name,
                        Surname = p.User.Surname,
                        Email = p.User.Email,
                        PhoneNumber = p.User.PhoneNumber,
                        IsActive = p.User.IsActive
                    },
                    IsActive = p.IsActive
                }).ToList();
            }

            return Json(data.ToDataSourceResult(request));
        }

        #endregion
    }
}
