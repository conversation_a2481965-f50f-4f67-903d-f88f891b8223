﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Org.BouncyCastle.Crypto;
using Portal.Gateway.Contracts.Entities.Dto.PrinterAgent.Response;
using Portal.Gateway.Contracts.Services;
using Portal.Gateway.Contracts.Settings.AppSettings;
using Portal.Gateway.Contracts.UnitOfWork;
using Portal.Gateway.Entity.Context;
using Portal.Gateway.Entity.Entities.Portal;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Portal.Gateway.Services
{
    public class PrinterInfoService : BaseService, IPrinterInfoService
    {
        private readonly AppSettings _appSettings;
        private readonly IUnitOfWork<PortalDbContext> _unitOfWorkPortalDb;
        private readonly IMapper _mapper;
        public PrinterInfoService(IOptions<AppSettings> appSettings, IUnitOfWork<PortalDbContext> unitOfWorkPortalDb, IMapper mapper) : base(appSettings)
        {
            _appSettings = appSettings.Value;
            _unitOfWorkPortalDb = unitOfWorkPortalDb;
            _mapper = mapper;
        }

        public async Task<List<PrinterInfoDto>> GetPrinterInfos(string hostName)
        {
            var repostory = _unitOfWorkPortalDb.GetRepository<PrinterInfo>();
            var data = await repostory.Entities.Include(i => i.PrinterAgent).Include(k => k.PrinterType).Where(k => k.PrinterAgent.HostName == hostName).ToListAsync();
            var mapped = _mapper.Map<List<PrinterInfoDto>>(data);

            return mapped;
        }

        public async Task<List<PrinterInfoDto>> GetPrinterInfos(int agentId)
        {
            var repostory = _unitOfWorkPortalDb.GetRepository<PrinterInfo>();
            var result = await (repostory.Entities.Include(i => i.PrinterAgent).Where(k => k.PrinterAgentId == agentId)).ToListAsync();
            var mapped = _mapper.Map<List<PrinterInfoDto>>(result);

            return mapped;
        }
        public async Task<PrinterInfoDto> AddPrinterInfo(PrinterInfoDto printerInfo)
        {
            var repostory = _unitOfWorkPortalDb.GetRepository<PrinterInfo>();
            var mapped = _mapper.Map<PrinterInfo>(printerInfo);
            mapped.UpdatedAt = DateTime.Now;
            mapped.UpdatedBy = printerInfo.UserId;
            mapped.IsActive = true;
            mapped.IsDeleted = false;
            await repostory.AddAsync(mapped);

            repostory.SaveChanges();

            printerInfo.Id = mapped.Id;

            return printerInfo;
        }



        public async Task<PrinterInfoDto> UpdatePrinterInfo(PrinterInfoDto printerInfo)
        {
            var repostory = _unitOfWorkPortalDb.GetRepository<PrinterInfo>();
            var mapped = _mapper.Map<PrinterInfo>(printerInfo);
            mapped.UpdatedAt = DateTime.Now;
            mapped.UpdatedBy = printerInfo.UserId;
            mapped.IsActive = true;
            mapped.IsDeleted = false;
            repostory.Update(mapped);
           
            repostory.SaveChanges();

            var info = repostory.Entities.Include(i => i.PrinterAgent).Where(k => k.PrinterAgentId == printerInfo.PrinterAgentId).ToList();

            return printerInfo;
        }

        public async Task<PrinterInfoDto> GetPrinterInfo(int id)
        {
            var repostory = _unitOfWorkPortalDb.GetRepository<PrinterInfo>();
            var result = await (repostory.Entities.Where(k => k.Id == id))

                .FirstOrDefaultAsync();
            var mapped = _mapper.Map<PrinterInfoDto>(result);
            return mapped;
        }

        public async Task<bool> DeletePrinterInfo(int id)
        {
            var repostory = _unitOfWorkPortalDb.GetRepository<PrinterInfo>();
            var result = await repostory.Entities.Include(i => i.PrinterAgent).FirstOrDefaultAsync(k => k.Id == id);
            repostory.Delete(result);
            repostory.SaveChanges();
            var info = repostory.Entities.Where(k => k.PrinterAgentId == result.PrinterAgentId).ToList();

            return true;
        }

        public async Task<string> GetHostNameByAgentId(int agentId)
        {
            var repostory = _unitOfWorkPortalDb.GetRepository<PrinterAgent>();
            var agent = await repostory.Entities.FirstOrDefaultAsync(k => k.Id == agentId);
            return agent.HostName;
        }

        public async Task<string> GetHostNameByAgentInfo(int infoId)
        {
            var repostory = _unitOfWorkPortalDb.GetRepository<PrinterInfo>();
            var printerInfo = await repostory.Entities.Include(i => i.PrinterAgent).FirstOrDefaultAsync(k => k.Id == infoId);
            return printerInfo.PrinterAgent.HostName;

        }
    }
}
