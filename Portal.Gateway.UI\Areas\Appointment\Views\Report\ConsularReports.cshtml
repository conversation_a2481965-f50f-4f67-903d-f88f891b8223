﻿@{
	ViewData["Title"] = @SiteResources.ConsularReporting.ToTitleCase();
}

@model FilterReportViewModel
@using Microsoft.AspNetCore.Http
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using Microsoft.Extensions.Caching.Memory
@using Portal.Gateway.ApiModel.Responses.Management.RoleAction
@using SessionExtensions = Portal.Gateway.UI.Extensions.SessionExtensions
@using Portal.Gateway.UI.Constants
@using Portal.Gateway.UI.Models
@inject IHttpContextAccessor HttpContextAccessor
@inject ICacheHelper CacheHelper
@{
	var currentUser = SessionExtensions.Get<UserModel>(HttpContextAccessor.HttpContext?.Session, SessionKeys.UserSession);
	var isAuthorizedForTurkmenistanStatisticReport = false;
	var roleActions = await CacheHelper.GetRoleActionsAsync();
	isAuthorizedForTurkmenistanStatisticReport = roleActions.RoleActionSites.Where(r => currentUser.RoleIds.Contains(r.Role.Id))
		.Any(p => p.RoleActions.Any(q => q.Action.ActionTranslations.Any(r => r.Name == SiteResources.IsAuthorizedForTurkmenistanStatisticReport) && q.Action.IsActive));
}

<style>
	[aria-selected='true'] span {
		padding-right: 10px;
	}

	[aria-hidden='true'] span {
		top: -2px;
	}
</style>

<div class="card card-custom card-stretch">
	<div class="card-header">
		<div class="card-title">
			<h3 class="card-label">
				@SiteResources.ConsularReporting.ToTitleCase()
			</h3>
		</div>
		<div class="card-toolbar">
			<div class="btn-group">
			</div>
		</div>
	</div>
	<div class="card-body">
		@if (ViewData.ModelState.Any(x => x.Value.Errors.Any()))
		{
			@foreach (var modelError in Html.ViewData.ModelState.SelectMany(keyValuePair => keyValuePair.Value.Errors))
			{
				<div class="alert alert-custom alert-light-danger fade show my-5" role="alert">
					<div class="alert-icon"><i class="flaticon-danger"></i></div>
					<div class="alert-text">
						<span>@modelError.ErrorMessage</span>
					</div>
					<div class="alert-close">
						<button type="button" class="close" data-dismiss="alert" aria-label="Close">
							<span aria-hidden="true"><i class="ki ki-close"></i></span>
						</button>
					</div>
				</div>
			}
		}
		<form id="formReport" method="post" action="@Url.Action("CreateReport", "Report", new { Area = "Appointment" })">
			<div class="form-group row">
				<div class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.ReportType.ToTitleCase()</label>
					@(Html.Kendo().DropDownListFor(m => m.FilterReportTypeId)
												.HtmlAttributes(new { @class = "form-control" })
												.Filter(FilterType.Contains)
												.OptionLabel(SiteResources.Select)
												.DataTextField("Text")
												.DataValueField("Value")
												.DataSource(source =>
												{
													source.Read(read =>
													{
														read.Action("GetConsularReportTypeSelectList", "Parameter", new { Area = "", isAuthorizedForTmStatisticReport = isAuthorizedForTurkmenistanStatisticReport });
													});
												}))
				</div>
			</div>
			<div class="form-group row">
				<div id="divUser" class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.UserNameSurname.ToTitleCase()</label>
					@(Html.Kendo().DropDownListFor(m => m.FilterUserId)
												.HtmlAttributes(new { @class = "form-control" })
												.Filter(FilterType.Contains)
												.OptionLabel(SiteResources.Select)
												.DataTextField("Text")
												.DataValueField("Value")
												.DataSource(source =>
												{
													source.Read(read =>
													{
														read.Action("GetReportUserSelectList", "Parameter", new { Area = "" });
													});
												}))
				</div>
				<div id="divBranch" class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.BranchName.ToTitleCase()</label>
					@(Html.Kendo().DropDownListFor(m => m.FilterBranchId)
												.HtmlAttributes(new { @class = "form-control" })
												.Filter(FilterType.Contains)
												.OptionLabel(SiteResources.Select)
												.DataTextField("Text")
												.DataValueField("Value")
												.DataSource(source =>
												{
													source.Read(read =>
													{
														read.Action("GetReportBranchWithCountrySelectList", "Parameter", new { Area = "" });
													});
												}))
				</div>
				<div id="divBranches" class="col-lg-6 col-md-6">
					<label class="font-weight-bold">@SiteResources.BranchName</label>
					@(Html.Kendo().MultiSelectFor(m => m.FilterBranchIds)
												.HtmlAttributes(new { @class = "form-control" })
												.Events(events => events.Change("MultiStatusSelectByBranches"))
												.Filter(FilterType.Contains)
												.Placeholder(SiteResources.SelectBranches)
												.AutoClose(false)
												.DataTextField("Text")
												.DataValueField("Value")
												.DataSource(source =>
												{
													source.Read(read =>
													{
														read.Action("GetReportBranchWithCountrySelectList", "Parameter", new { Area = "" }).Data("filterBranches");
													});
												}))
				</div>
				<div id="divApplicationStatus" class="col-lg-6 col-md-6">
					<label class="font-weight-bold">@SiteResources.Status</label>
					@(Html.Kendo().MultiSelectFor(m => m.FilterApplicationStatusIds)
												.HtmlAttributes(new { @class = "form-control" })
												.Filter(FilterType.Contains)
												.Placeholder(SiteResources.SelectStatuses)
												.AutoClose(false)
												.DataTextField("Text")
												.DataValueField("Value")
												.DataSource(source =>
												{
													source.Read(read =>
													{
														read.Action("GetCachedApplicationStatusList", "Parameter", new { Area = "", EncryptedBranchId = "", FilterBranchIds = Model?.FilterBranchIds });
													});
												}))
				</div>
				<div id="divBranchApplicationCountry" class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.BranchApplicationCountry.ToTitleCase()</label>
					@(Html.Kendo().DropDownListFor(m => m.FilterBranchApplicationCountryId)
												.HtmlAttributes(new { @class = "form-control" })
												.Filter(FilterType.Contains)
												.OptionLabel(SiteResources.Select)
												.DataTextField("Text")
												.DataValueField("Value")
												.DataSource(source =>
												{
													source.Read(read =>
													{
														read.Action("GetReportBranchApplicationCountrySelectList", "Parameter", new { Area = "" });
													});
												}))
				</div>
				<div id="divStartDate" class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.StartDate.ToTitleCase()</label>
					@(Html.Kendo().DatePickerFor(m => m.FilterStartDate).Format(SiteResources.DatePickerFormatView).Value(DateTime.Today))
				</div>
				<div id="divEndDate" class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.EndDate.ToTitleCase()</label>
					@(Html.Kendo().DatePickerFor(m => m.FilterEndDate).Format(SiteResources.DatePickerFormatView).Value(DateTime.Today))
				</div>
				<div id="divReportDate" class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.ReportDate.ToTitleCase()</label>
					@(Html.Kendo().DatePickerFor(m => m.FilterReportDate).Format(SiteResources.DatePickerFormatView).Value(DateTime.Today))
				</div>
			</div>
			<div class="form-group row">
				<div class="col-lg-3 col-md-6">
					<button type="submit" class="btn btn-primary font-weight-bold">@SiteResources.Create</button>
				</div>
			</div>
		</form>
	</div>
</div>

@section Scripts {
	<partial name="Scripts/_ValidationScripts" />

	<script src="~/js/site.js"></script>
	<script src="~/js/Appointment/Report/report.js"></script>
	<script src="~/js/Appointment/Report/MultiSelectreport.js"></script>

	<script>

		$(function () {
			checkReportFilters();

			$('#FilterReportTypeId').change(function () {
				checkReportFilters();
			});
		});

		function checkReportFilters() {

			$("#divUser").hide();
			$("#divBranch").hide();
			$("#divBranchApplicationCountry").hide();
			$("#divStartDate").hide();
			$("#divEndDate").hide();
			$("#divApplicationStatus").hide();
			$("#divBranches").hide();
			$("#divReportDate").hide();

			$("#FilterUserId").data("kendoDropDownList").select(null);
			$("#FilterBranchId").data("kendoDropDownList").select(null);
			$("#FilterBranchApplicationCountryId").data("kendoDropDownList").select(null);
			$("#FilterStartDate").val('');
			$("#FilterEndDate").val('');

			$('#FilterBranchIds').data('kendoMultiSelect').dataSource.read();
			$('#FilterBranchIds').data('kendoMultiSelect').refresh();

			// Report 69
			if ($("#FilterReportTypeId").val() === '@ConsularReportType.MediationReport.ToInt()') {
				$("#divBranches").show();
				$("#divStartDate").show();
				$("#divEndDate").show();
			}
			// Report 72
			if ($("#FilterReportTypeId").val() === '@ConsularReportType.MailReportBefore.ToInt()') {
				$("#divBranches").show();
				$("#divStartDate").show();
				$("#divEndDate").show();
			}

			// Report 73
			if ($("#FilterReportTypeId").val() === '@ConsularReportType.MailReportAfter.ToInt()') {
				$("#divBranches").show();
				$("#divStartDate").show();
				$("#divEndDate").show();
			}
			// Report 74
			if ($("#FilterReportTypeId").val() === '@ConsularReportType.ReceivedAtEmbassyReport.ToInt()') {
				$("#divBranches").show();
				$("#divStartDate").show();
				$("#divEndDate").show();
			}

			if ($("#FilterReportTypeId").val() === '@ConsularReportType.TurkmenistanStatisticReport.ToInt()') {
				$("#divStartDate").show();
				$("#divEndDate").show();
			}
			// Report 76
			if ($("#FilterReportTypeId").val() === '@ConsularReportType.RejectedReport.ToInt()') {
				$("#divBranches").show();
				$("#divStartDate").show();
				$("#divEndDate").show();
			}
		}

		function filterBranches() {
			return {
				reportType: $("#FilterReportTypeId").val()
			};
		}

	</script>
}