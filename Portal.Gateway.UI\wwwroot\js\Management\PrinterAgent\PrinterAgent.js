﻿$(function () {
});

function refreshGridgridPrinterAgent() {
    $('#gridPrinterAgent').data('kendoGrid').dataSource.read();
    $('#gridPrinterAgent').data('kendoGrid').refresh();
}

function refreshGridgridPrinterAgentDetail() {
    $('#gridPrinterAgentDetail').data('kendoGrid').dataSource.read();
    $('#gridPrinterAgentDetail').data('kendoGrid').refresh();
}

function partialAddUpdatePrinterAgentBranch(HostName, Id, BranchId) {
    $.post('/Management/PrinterAgent/PartialAddPrinterAgentBranch', { HostName: HostName, Id: Id, BranchId: BranchId },
        function (data) {
            $('#modalPrinterAgentBranch').modalShow({ body: data });
        }, 'html');
}

$("#formAddUpdatePrinterAgentBranch").submit(function (e) {
    e.preventDefault();
    let form = $(this);
    let validator = form.kendoValidator().data("kendoValidator");

    if (!validator.validate()) return;

    if ($("#Id").val() == '0') {
        $.ajax({
            type: "POST",
            url: "/Management/PrinterAgent/AddPrinterAgent",
            data: form.serialize(),
            success: function (data) {
                if (data.Type === "success") {
                    showNotification(data.Type, data.Message);
                    window.location.reload();
                }
                else {
                    bootbox.alert({
                        title: jsResources.Warning,
                        message: data.Message,
                        callback: function (data) { }
                    });
                }
            },
            error: function (data) {
                showNotification('danger', jsResources.ErrorOccurred);
            }
        });
    } else {
        $.ajax({
            type: "PUT",
            url: "/Management/PrinterAgent/UpdatePrinterAgent",
            data: form.serialize(),
            success: function (data) {
                if (data.Type === "success") {
                    showNotification(data.Type, data.Message);
                    window.location.reload();
                }
                else {
                    showNotification('danger', jsResources.ErrorOccurred);
                }
            },
            error: function (data) {
                showNotification('danger', jsResources.ErrorOccurred);
            }
        });
    }
});

function partialAddUpdatePrinter(PrinterAgentId, PrinterAgentName, Id, Name, PrinterTypeId, CopyCount) {
    $.post('/Management/PrinterAgent/PartialAddUpdatePrinter', { PrinterAgentId: PrinterAgentId, PrinterAgentName: PrinterAgentName, Id: Id, Name: Name, PrinterTypeId: PrinterTypeId, CopyCount: CopyCount },
        function (data) {
            $('#modalPartialAddUpdatePrinter').modalShow({ body: data });
        }, 'html');
}

$("#formAddUpdatePrinter").submit(function (e) {
    e.preventDefault();
    let form = $(this);
    let validator = form.kendoValidator().data("kendoValidator");

    if (!validator.validate()) return;

    if ($("#PrinterId").val() == '0') {
        $.ajax({
            type: "POST",
            url: "/Management/PrinterAgent/CreatePrinterInfo",
            data: form.serialize(),
            success: function (data) {
                if (data.Type === "success") {
                    showNotification(data.Type, data.Message);
                    window.location.reload();
                }
                else {
                    showNotification('danger', jsResources.ErrorOccurred);
                }
            },
            error: function (data) {
                showNotification('danger', jsResources.ErrorOccurred);
            }
        });
    } else {
        $.ajax({
            type: "PUT",
            url: "/Management/PrinterAgent/UpdatePrinterInfo",
            data: form.serialize(),
            success: function (data) {
                if (data.Type === "success") {
                    showNotification(data.Type, data.Message);
                    window.location.reload();
                }
                else {
                    showNotification('danger', jsResources.ErrorOccurred);
                }
            },
            error: function (data) {
                showNotification('danger', jsResources.ErrorOccurred);
            }
        });
    }
});

function deletePrinterAgent(Id) {
    bootbox.confirm(jsResources.AreYouSureToDeleteThisRecord, function (result) {
        if (result) {
            $.ajax({
                type: "DELETE",
                url: "/Management/PrinterAgent/DeletePrinterAgent",
                data: { Id: Id },
                success: function (data) {
                    showNotification(data.Type, data.Message);
                    if (data.Type === "success") {
                        refreshGridgridPrinterAgent();
                    }
                },
                error: function (data) {
                    showNotification('danger', jsResources.ErrorOccurred);
                }
            });
        }
    });
}

function deletePrinter(Id) {
    bootbox.confirm(jsResources.AreYouSureToDeleteThisRecord, function (result) {
        if (result) {
            $.ajax({
                type: "DELETE",
                url: "/Management/PrinterAgent/DeletePrinterInfo",
                data: { Id: Id },
                success: function (data) {
                    showNotification(data.Type, data.Message);
                    if (data.Type === "success") {
                        refreshGridgridPrinterAgentDetail();
                    }
                },
                error: function (data) {
                    showNotification('danger', jsResources.ErrorOccurred);
                }
            });
        }
    });
}

function onChangePrinterType() {
    if ($("#PrinterTypeId").val() == 1) {
        $("#icrLanguage").show();

    }
    else {
        $("#icrLanguage").hide();
    }
}
