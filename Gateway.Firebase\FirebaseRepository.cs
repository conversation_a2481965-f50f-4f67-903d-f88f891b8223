﻿using FirebaseAdmin.Auth;
using FirebaseAdmin.Messaging;
using Gateway.Firebase.Dto;

namespace Gateway.Firebase
{
    public class FirebaseRepository : IFirebaseRepository
    {
        public async Task<SendMessageResult> SendMessage(SendMessageRequest request)
        {
            try
            {
                var message = new Message()
                {
                    Token = request.RegistryToken,
                    Notification = new FirebaseAdmin.Messaging.Notification()
                    {
                        Title = request.Title,
                        Body = request.Message
                    }
                };

                var response = await FirebaseMessaging.DefaultInstance.SendAsync(message);

                return new SendMessageResult()
                {
                    Result = response != null,
                    Message = response
                };
            }
            catch (Exception ex)
            {
                return new SendMessageResult()
                {
                    Result = false,
                    Message = ex.Message
                };
            }
        }

        public async Task<SubscribeToTopicResult> SubscribeToTopic(SubscribeToTopicRequest request)
        {
            try
            {
                var response = await FirebaseMessaging.DefaultInstance.SubscribeToTopicAsync(new List<string>() { request.RegistryToken }, request.Topic);

                return new SubscribeToTopicResult()
                {
                    Result = response.SuccessCount > 0,
                    Message = response.SuccessCount > 0 ? "SUCCESS" : "FAILED"
                };
            }
            catch (Exception ex)
            {
                return new SubscribeToTopicResult()
                {
                    Result = false,
                    Message = ex.Message
                };
            }
        }

        public async Task<SubscribeToTopicResult> UnSubscribeToTopic(UnSubscribeToTopicRequest request)
        {
            try
            {
                var response = await FirebaseMessaging.DefaultInstance.UnsubscribeFromTopicAsync(new List<string>() { request.RegistryToken }, request.Topic);

                return new SubscribeToTopicResult()
                {
                    Result = response.SuccessCount > 0,
                    Message = response.SuccessCount > 0 ? "SUCCESS" : "FAILED"
                };
            }
            catch (Exception ex)
            {
                return new SubscribeToTopicResult()
                {
                    Result = false,
                    Message = ex.Message
                };
            }
        }

        public async Task<SendMessageResult> SendMessageToTopic(SendMessageToTopicRequest request)
        {
            try
            {
                var message = new Message()
                {
                    Topic = request.Topic,
                    Notification = new FirebaseAdmin.Messaging.Notification()
                    {
                        Title = request.Title ?? string.Empty,
                        Body = request.Message
                    }
                };

                var response = await FirebaseMessaging.DefaultInstance.SendAsync(message);

                return new SendMessageResult()
                {
                    Result = response != null,
                    Message = response
                };
            }
            catch (Exception ex)
            {
                return new SendMessageResult()
                {
                    Result = false,
                    Message = ex.Message
                };
            }
        }

        public async Task<UnRegisterResult> UnRegister(UnRegisterRequest request)
        {
            try
            {
               await FirebaseAuth.DefaultInstance.DeleteUserAsync(request.UId);

               return new UnRegisterResult()
               {
                   Result = true,
                   Message = "SUCCESS"
               };
            }
            catch (FirebaseAuthException ex)
            {
                return new UnRegisterResult()
                {
                    Result = false,
                    Message = ex.Message
                };
            }
        }
    }
}
