﻿using Gateway.Core.Pagination;

namespace Gateway.External.Api.Models.User
{
    public class UserRegisterRequestModel
    {
        public string Email { get; set; }
        public string Password { get; set; }
        public string ConfirmPassword { get; set; }
        public string? Surname { get; set; }
        public string? Name { get; set; }
        public int NationalityId { get; set; }
    }

    public class UpdateUserProfileRequestModel
    {
        public string? Surname { get; set; }
        public string? Name { get; set; }
        public int NationalityId { get; set; }
    }

    public class AddUpdateUserDeviceEntryRequestModel
    {
        public string Location { get; set; }
        public string RegistryToken { get; set; }
        public string DeviceLanguage { get; set; }
        public string DeviceId { get; set; }
    }

    public class GetUserPushNotificationsRequestModel 
    {
        public PaginationRequest Pagination { get; set; }
    }

    public class UpdateUserSettingsRequestModel
    {
        public bool IsAllowedSendNotification { get; set; }
        public bool IsAllowedSendSms { get; set; }
        public bool IsAllowedSendEmail { get; set; }
    }
}
