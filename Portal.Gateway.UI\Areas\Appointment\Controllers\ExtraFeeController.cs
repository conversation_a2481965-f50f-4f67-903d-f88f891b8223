﻿using System;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Appointment.ExtraFee;
using Portal.Gateway.ApiModel.Requests.Appointment.MainExtraFeeCategory;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Appointment.ExtraFee;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.ExtraFee;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.MainExtraFeeCategory;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Portal.Gateway.ApiModel.Responses.Appointment.MainExtraFeeCategory;

namespace Portal.Gateway.UI.Areas.Appointment.Controllers
{
    [Area("Appointment")]
    public class ExtraFeeController : BaseController<ExtraFeeController>
    {
        public ExtraFeeController(
                IOptions<AppSettings> appSettings,
                ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        #region ExtraFee

        #region Add

        public async Task<IActionResult> PartialAddExtraFee()
        {
            var viewModel = new AddExtraFeeViewModel
            {
                TypeList = EnumHelper.GetEnumAsDictionary(typeof(ExtraFeeType)).Select(p => new SelectListItem
                {
                    Text = p.Value,
                    Value = p.Key.ToString()
                }).ToList(),
                CategoryList = EnumHelper.GetEnumAsDictionary(typeof(ExtraFeeCategoryType)).Select(p => new SelectListItem
                {
                    Text = p.Value,
                    Value = p.Key.ToString()
                }).ToList(),
                AgeRangeList = EnumHelper.GetEnumAsDictionary(typeof(YSSCategoryAgeRange)).Select(p => new SelectListItem
                {
                    Text = p.Value,
                    Value = p.Key.ToString()
                }).ToList(),
                MainExtraFeeCategoryList = await GetMainExtraFeeCategoriesSelectList()
                
            };

            viewModel.TypeList.Insert(0, new SelectListItem { Text = SiteResources.Select, Value = "" });
            viewModel.CategoryList.Insert(0, new SelectListItem { Text = SiteResources.Select, Value = "" });
            viewModel.AgeRangeList.Insert(0, new SelectListItem { Text = SiteResources.Select, Value = "" });
            viewModel.MainExtraFeeCategoryList.Insert(0, new SelectListItem { Text = SiteResources.Select, Value = "" });

            foreach (var item in EnumHelper.GetEnumAsDictionary(typeof(ICRLanguage)))
            {
                viewModel.ExtraFeeTranslations.Add(new AddExtraFeeTranslationViewModel { LanguageId = item.Key });
            }

            return PartialView("_AddExtraFee", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddExtraFee(AddExtraFeeViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddExtraFeeApiRequest
            {
                Type = viewModel.Type,
                Category = viewModel.Category,
                MinimumItem = viewModel.MinimumItem,
                PolicyPeriod = viewModel.PolicyPeriod,
                AgeRange = viewModel.AgeRange,
                IsMainExtraFeeCategory = viewModel.IsMainExtraFeeCategory,
                IsSubExtraFeeCategory = viewModel.IsSubExtraFeeCategory,
                MainExtraFeeCategoryId = viewModel.MainExtraFeeCategoryId,
                RelatedMainExtraFeeCategoryId = viewModel.RelatedMainExtraFeeCategoryId,
                ExtraFeeTranslations = viewModel.ExtraFeeTranslations.Select(p => new AddExtraFeeTranslationApiRequest
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Appointment.AddExtraFee, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateExtraFee(string encryptedExtraFeeId)
        {
            int id = encryptedExtraFeeId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ExtraFeeApiResponse>>
                (ApiMethodName.Appointment.GetExtraFee + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdateExtraFeeViewModel
            {
                Id = apiResponse.Data.Id,
                Type = apiResponse.Data.Type,
                Category = apiResponse.Data.Category,
                MinimumItem = apiResponse.Data.MinimumItem,
                PolicyPeriod = apiResponse.Data.PolicyPeriod,
                IsMainExtraFeeCategory = apiResponse.Data.IsMainExtraFeeCategory,
                IsSubExtraFeeCategory = apiResponse.Data.IsSubExtraFeeCategory,
                MainExtraFeeCategoryId = apiResponse.Data.MainExtraFeeCategoryId,
                RelatedMainExtraFeeCategoryId = apiResponse.Data.RelatedMainExtraFeeCategoryId,
                IsActive = apiResponse.Data.IsActive,
                TypeList = EnumHelper.GetEnumAsDictionary(typeof(ExtraFeeType)).Select(p => new SelectListItem()
                {
                    Text = p.Value,
                    Value = p.Key.ToString()
                }).ToList(),
                CategoryList = EnumHelper.GetEnumAsDictionary(typeof(ExtraFeeCategoryType)).Select(p => new SelectListItem()
                {
                    Text = p.Value,
                    Value = p.Key.ToString()
                }).ToList(),
                AgeRangeList = EnumHelper.GetEnumAsDictionary(typeof(YSSCategoryAgeRange)).Select(p => new SelectListItem
                {
                    Text = p.Value,
                    Value = p.Key.ToString()
                }).ToList(),
                MainExtraFeeCategoryList = await GetMainExtraFeeCategoriesSelectList(),
                ExtraFeeTranslations = apiResponse.Data.ExtraFeeTranslations.Select(p => new UpdateExtraFeeTranslationViewModel
                {
                    Id = p.Id,
                    LanguageId = p.LanguageId,
                    Name = p.Name
                }).ToList()
            };

            viewModel.MainExtraFeeCategoryList.Insert(0, new SelectListItem { Text = SiteResources.Select, Value = "" });

            return PartialView("_UpdateExtraFee", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateExtraFee(UpdateExtraFeeViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            viewModel.RelatedMainExtraFeeCategoryId = viewModel.IsMainExtraFeeCategory ? null : viewModel.RelatedMainExtraFeeCategoryId;

            viewModel.MainExtraFeeCategoryId = viewModel.IsSubExtraFeeCategory ? null : viewModel.MainExtraFeeCategoryId;

            var apiRequest = new UpdateExtraFeeApiRequest
            {
                Id = viewModel.Id,
                Type = viewModel.Type,
                Category = viewModel.Category,
                MinimumItem = viewModel.MinimumItem,
                PolicyPeriod = viewModel.PolicyPeriod,
                IsMainExtraFeeCategory = viewModel.IsMainExtraFeeCategory,
                IsSubExtraFeeCategory = viewModel.IsSubExtraFeeCategory,
                MainExtraFeeCategoryId = viewModel.MainExtraFeeCategoryId,
                RelatedMainExtraFeeCategoryId = viewModel.RelatedMainExtraFeeCategoryId,
                IsActive = viewModel.IsActive,
                ExtraFeeTranslations = viewModel.ExtraFeeTranslations.Select(p => new UpdateExtraFeeTranslationApiRequest
                {
                    Id = p.Id,
                    LanguageId = p.LanguageId,
                    Name = p.Name
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<EmptyApiResponse>>
                (apiRequest, ApiMethodName.Appointment.UpdateExtraFee, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteExtraFee(string encryptedExtraFeeId)
        {
            int id = encryptedExtraFeeId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Appointment.DeleteExtraFee + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> PartialExtraFee(string encryptedExtraFeeId)
        {
            int id = encryptedExtraFeeId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ExtraFeeApiResponse>>
                (ApiMethodName.Appointment.GetExtraFee + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var mainExtraFeeCategoryTypes = await GetMainExtraFeeCategoriesSelectList();

            var viewModel = new ExtraFeeViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Type = EnumHelper.GetEnumDescription(typeof(ExtraFeeType), apiResponse.Data.Type.ToString()),
                Category = EnumHelper.GetEnumDescription(typeof(ExtraFeeCategoryType), apiResponse.Data.Category.ToString()),
                MinimumItem = apiResponse.Data.MinimumItem,
                PolicyPeriod = apiResponse.Data.PolicyPeriod,
                IsMainExtraFeeCategory = apiResponse.Data.IsMainExtraFeeCategory,
                IsSubExtraFeeCategory = apiResponse.Data.IsSubExtraFeeCategory,
                MainExtraFeeCategoryName = mainExtraFeeCategoryTypes.FirstOrDefault(w => Convert.ToInt32(w.Value) == apiResponse.Data.MainExtraFeeCategoryId)?.Text,
                SubExtraFeeCategoryName = mainExtraFeeCategoryTypes.FirstOrDefault(w => Convert.ToInt32(w.Value) == apiResponse.Data.RelatedMainExtraFeeCategoryId)?.Text,
                IsActive = apiResponse.Data.IsActive,
                ExtraFeeTranslations = apiResponse.Data.ExtraFeeTranslations.Select(p => new AddExtraFeeTranslationViewModel()
                {
                    Name = p.Name,
                    LanguageId = p.LanguageId
                }).ToList()
            };

            return PartialView("_ExtraFee", viewModel);
        }

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedExtraFees([DataSourceRequest] DataSourceRequest request, FilterExtraFeeViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedExtraFeesApiRequest
            {
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<ExtraFeesApiResponse>>>
                (apiRequest, ApiMethodName.Appointment.GetPaginatedExtraFees, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<ExtraFeeItemViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().ExtraFees
                    .Select(p => new ExtraFeeItemViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        Name = p.Name,
                        IsActive = p.IsActive,
                        Category = EnumHelper.GetEnumDescription(typeof(ExtraFeeCategoryType), p.Category.ToString()),
                        MinimumItem = p.MinimumItem
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #endregion
        #endregion


        #region MainExtraFeeCategory


        #region Add

        public IActionResult PartialAddMainExtraFeeCategory()
        {
            var viewModel = new AddMainExtraFeeCategoryViewModel()
            {
                Name = ""
            };

            foreach (var item in EnumHelper.GetEnumAsDictionary(typeof(Language)))
            {
                viewModel.MainExtraFeeCategoryTranslations.Add(new AddMainExtraFeeCategoryTranslationViewModel() { LanguageId = item.Key });
            }

            return PartialView("_AddMainExtraFeeCategory", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddMainExtraFeeCategory(AddMainExtraFeeCategoryViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var name = viewModel.MainExtraFeeCategoryTranslations
                .FirstOrDefault(w => w.LanguageId == (int)Language.English)?.Name;

            var apiRequest = new AddMainExtraFeeCategoryApiRequest
            {

                Name = name,
                MainExtraFeeCategoryTranslations = viewModel.MainExtraFeeCategoryTranslations.Select(p => new AddMainExtraFeeCategoryTranslationApiRequest
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Appointment.AddMainExtraFeeCategory, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateMainExtraFeeCategory(string encryptedMainExtraFeeCategoryId)
        {
            int id = encryptedMainExtraFeeCategoryId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<MainExtraFeeCategoryApiResponse>>
                (ApiMethodName.Appointment.GetMainExtraFeeCategory + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdateMainExtraFeeCategoryViewModel
            {
                Id = apiResponse.Data.Id,
                MainExtraFeeCategoryName = apiResponse.Data.Name,
                IsActive = apiResponse.Data.IsActive,
                MainExtraFeeCategoryTranslations = apiResponse.Data.MainExtraFeeCategoryTranslations.Select(p => new UpdateMainExtraFeeCategoryTranslationViewModel
                {
                    Id = p.Id,
                    LanguageId = p.LanguageId,
                    Name = p.Name
                }).ToList()
            };

            return PartialView("_UpdateMainExtraFeeCategory", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateMainExtraFeeCategory(UpdateMainExtraFeeCategoryViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var name = viewModel.MainExtraFeeCategoryTranslations
                .FirstOrDefault(w => w.LanguageId == (int)Language.English)?.Name;

            var apiRequest = new UpdateMainExtraFeeCategoryApiRequest
            {
                Id = viewModel.Id,
                Name = name,
                IsActive = viewModel.IsActive,
                MainExtraFeeCategoryTranslations = viewModel.MainExtraFeeCategoryTranslations.Select(p => new UpdateMainExtraFeeCategoryTranslationApiRequest
                {
                    Id = p.Id,
                    LanguageId = p.LanguageId,
                    Name = p.Name
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<EmptyApiResponse>>
                (apiRequest, ApiMethodName.Appointment.UpdateMainExtraFeeCategory, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteMainExtraFeeCategory(string encryptedMainExtraFeeCategoryId)
        {
            int id = encryptedMainExtraFeeCategoryId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Appointment.DeleteMainExtraFeeCategory + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> GetMainExtraFeeCategories()
        {

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<MainExtraFeeCategoriesApiResponse>>
                (ApiMethodName.Appointment.GetMainExtraFeeCategories, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var viewModel = new List<MainExtraFeeCategoryItemViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.MainExtraFeeCategories.Any())
            {
                viewModel = apiResponse.Data.MainExtraFeeCategories
                    .Select(p => new MainExtraFeeCategoryItemViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        MaineExtraFeeCategoryName = p.Name,
                        IsActive = p.IsActive
                    }).ToList();
            }

            return PartialView("_ListMainExtraFeeCategory", viewModel);
        }

        #endregion

        #region PrivateMethods

        private async Task<List<SelectListItem>> GetMainExtraFeeCategoriesSelectList()
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<MainExtraFeeCategoriesApiResponse>>
                    (ApiMethodName.Appointment.GetMainExtraFeeCategories, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return new List<SelectListItem>();

            var selectListItems = new List<SelectListItem>();

            if (apiResponse.Data != null && apiResponse.Data.MainExtraFeeCategories.Any())
            {
                selectListItems = apiResponse.Data.MainExtraFeeCategories
                    .Select(p => new SelectListItem
                    {
                        Text = p.Name,
                        Value = p.Id.ToString(),
                    }).ToList();
            }

            return selectListItems;
        }


        #endregion
        #endregion
    }
}
