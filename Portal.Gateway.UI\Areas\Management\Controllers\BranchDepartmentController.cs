﻿using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.Management.BranchDepartment;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.ApiModel.Responses.Management.BranchDepartment;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.BranchDepartment;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class BranchDepartmentController : BaseController<BranchDepartmentController>
    {
        public BranchDepartmentController(
            IOptions<AppSettings> appSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        #region Add

        public async Task<IActionResult> PartialAddBranchDepartment(string encryptedBranchId)
        {
            var departmentList = await CacheHelper.GetDepartmentsAsync();

            var viewModel = new AddUpdateBranchDepartmentViewModel
            {
                EncryptedBranchId = encryptedBranchId,
                EncryptedBranchDepartmentId = string.Empty,
                DepartmentList = departmentList.Departments.Select(q => new AddUpdateBranchDepartmentViewModel.DepartmentViewModel
                {
                    Name = q.NameTranslations.Any(p => p.LanguageId == LanguageId) ?
                                q.NameTranslations.First(p => p.LanguageId == LanguageId).Name :
                                q.NameTranslations.FirstOrDefault().Name,
                    Id = q.Id,
                    Order = 0,
                    IsSelected = false
                }).ToList()
            };

            return PartialView("_AddBranchDepartment", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> AddBranchDepartment(AddUpdateBranchDepartmentViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddBranchDepartmentApiRequest
            {
                BranchId = viewModel.EncryptedBranchId.ToDecryptInt(),
                Name = viewModel.Name,
                Departments = viewModel.DepartmentList.OrderBy(q => q.Order).Select((q, index) => new AddBranchDepartmentApiRequest.OrderApiRequest
                {
                    DepartmentId = q.Id,
                    Order = index + 1,
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Management.AddBranchDepartment, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateBranchDepartment(string encryptedBranchDepartmentId)
        {
            var branchDepartmentId = encryptedBranchDepartmentId.ToDecryptInt();

            var departmentList = await CacheHelper.GetDepartmentsAsync();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BranchDepartmentApiResponse>>
                (ApiMethodName.Management.GetBranchDepartment + branchDepartmentId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new AddUpdateBranchDepartmentViewModel
            {
                EncryptedBranchId = string.Empty,
                EncryptedBranchDepartmentId = encryptedBranchDepartmentId,
                Name = apiResponse.Data.Name,
                IsActive = apiResponse.Data.IsActive,
                DepartmentList = departmentList.Departments.Select(q => new AddUpdateBranchDepartmentViewModel.DepartmentViewModel
                {
                    Name = q.NameTranslations.Any(p => p.LanguageId == LanguageId) ?
                                q.NameTranslations.First(p => p.LanguageId == LanguageId).Name :
                                q.NameTranslations.FirstOrDefault().Name,
                    Id = q.Id,
                    Order = apiResponse.Data.Departments.FirstOrDefault(p => p.DepartmentId == q.Id)?.Order ?? 0,
                    IsSelected = apiResponse.Data.Departments.Any(p => p.DepartmentId == q.Id)
                }).ToList()
            };

            return PartialView("_UpdateBranchDepartment", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateBranchDepartment(AddUpdateBranchDepartmentViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateBranchDepartmentApiRequest
            {
                BranchDepartmentId = viewModel.EncryptedBranchDepartmentId.ToDecryptInt(),
                BranchId = viewModel.EncryptedBranchId.ToDecryptInt(),
                Name = viewModel.Name,
                IsActive = viewModel.IsActive,
                Departments = viewModel.DepartmentList.OrderBy(q => q.Order).Select((q, index) => new UpdateBranchDepartmentApiRequest.OrderApiRequest()
                {
                    DepartmentId = q.Id,
                    Order = index + 1
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<EmptyApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateBranchDepartment, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteBranchDepartment(string encryptedBranchDepartmentId)
        {
            int id = encryptedBranchDepartmentId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Management.DeleteBranchDepartment + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> List(string encryptedBranchId)
        {
            if (string.IsNullOrEmpty(encryptedBranchId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Branch", new { Area = "Management" });
            }

            int branchId = encryptedBranchId.ToDecryptInt();

            var branchList = await CacheHelper.GetBranchesAsync();

            var branchDepartmentViewModel = new BranchDepartmentViewModel
            {
                EncryptedBranchId = encryptedBranchId,
                BranchName = branchList.Branches?.FirstOrDefault(q => q.Id == branchId)?.BranchTranslations.FirstOrDefault(q => q.LanguageId == LanguageId)?.Name,
            };

            ViewData["BranchDepartment_List"] = branchDepartmentViewModel;

            return View();
        }

        public async Task<IActionResult> GetBranchDepartments([DataSourceRequest] DataSourceRequest request, FilterBranchDepartmentViewModel filterViewModel)
        {
            var branchId = filterViewModel.EncryptedBranchId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BranchDepartmentsApiResponse>>
                (ApiMethodName.Management.GetBranchDepartments + branchId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var data = new List<BranchDepartmentItemViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.BranchDepartments.Any())
            {
                data = apiResponse.Data.BranchDepartments
                    .Select(p => new BranchDepartmentItemViewModel
                    {
                        EncryptedBranchDepartmentId = p.BranchDepartmentId.ToEncrypt(),
                        Name = p.Name,
                        IsActive = p.IsActive,
                        ProcessOrder = string.Join(" -> ", p.Departments.OrderBy(q => q.Order).Select(q => q.DepartmentName))
                    }).ToList();
            }

            return Json(data.ToDataSourceResult(request));
        }

        public async Task<IActionResult> PartialBranchDepartment(string encryptedBranchDepartmentId)
        {
            var branchDepartmentId = encryptedBranchDepartmentId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BranchDepartmentApiResponse>>
                (ApiMethodName.Management.GetBranchDepartment + branchDepartmentId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new BranchDepartmentOrderViewModel
            {
                IsActive = apiResponse.Data.IsActive,
                Name = apiResponse.Data.Name,
                DepartmentList = apiResponse.Data.Departments.Select(q => new BranchDepartmentOrderViewModel.DepartmentViewModel
                {
                    Name = q.DepartmentName,
                    Order = q.Order
                }).OrderBy(q => q.Order).ToList()
            };

            int branchId = apiResponse.Data.BranchId;

            var branchList = await CacheHelper.GetBranchesAsync();

            viewModel.BranchName = branchList.Branches?.FirstOrDefault(q => q.Id == branchId)?.BranchTranslations.FirstOrDefault(q => q.LanguageId == LanguageId)?.Name;

            return PartialView("_BranchDepartment", viewModel);
        }

        #endregion
    }
}
