﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Portal.Gateway.Entity.EFMigrationsHistory.PortalDb
{
    /// <inheritdoc />
    public partial class AddIsActivePolicyNumberForGetVisaRejectedListFunction : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            var seedName = "20250723165144_Up";
            migrationBuilder.Sql(Portal.Gateway.Entity.Context.PortalDbContext.SeedCustomDbScripts(seedName));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            var seedName = "20250723165144_Down";
            migrationBuilder.Sql(Portal.Gateway.Entity.Context.PortalDbContext.SeedCustomDbScripts(seedName));
        }
    }
}
