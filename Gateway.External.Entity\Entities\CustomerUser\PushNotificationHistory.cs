﻿using System;

namespace Gateway.External.Entity.Entities.CustomerUser
{
    public class PushNotificationHistory 
    {
        public int Id { get; set; }
        public int CustomerUserId { get; set; }
        public int PushNotificationId { get; set; }
        public int LocationId { get; set; }
        public int NationalityId { get; set; }
        public string Text { get; set; }
        public string Subject { get; set; }
        public DateTime? CreatedAt { get; set; }
        public PushNotification PushNotification { get; set; }
    }
}
