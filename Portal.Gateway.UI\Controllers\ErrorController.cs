﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Helpers;

namespace Portal.Gateway.UI.Controllers
{
    [AllowAnonymous]
    public class ErrorController : BaseController<ErrorController>
    {
        public ErrorController(
                IOptions<AppSettings> appSettings,
                ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        [Route("Error/400")]
        public IActionResult BadRequest()
        {
            return View();
        }

        [Route("Error/401")]
        public IActionResult PageUnauthorized()
        {
            return View();
        }

        [Route("Error/404")]
        public IActionResult PageNotFound()
        {
            return View();
        }

        [Route("Error/500")]
        public IActionResult ServerError()
        {
            return View();
        }
    }
}