﻿using Gateway.Extensions;
using Gateway.Http;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.Management.Screen;
using Portal.Gateway.ApiModel.Requests.QmsFolder;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.Screen;
using Portal.Gateway.UI.Areas.Management.ViewModels.Screen.RequestModels;
using Portal.Gateway.UI.Areas.Management.ViewModels.Screen.Results;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class ScreenController : BaseController<ScreenController>
    {
        public ScreenController(IOptions<AppSettings> appSettings, ICacheHelper cacheHelper) : base(appSettings, cacheHelper) { }

        #region Get

        [Action(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedScreens([DataSourceRequest] DataSourceRequest request, FilterScreenViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedScreenApiRequest
            {
                FilterBranchId = filterViewModel.FilterBranchId,
                FilterLineId = filterViewModel.FilterLineId,
                FilterDepartmentId = filterViewModel.FilterDepartmentId,
                FilterName = filterViewModel.FilterName,
                FilterBlockNumber = filterViewModel.FilterBlockNumber,
                FilterFloorNumber = filterViewModel.FilterFloorNumber,
                FilterContentType = filterViewModel.FilterContentType,
                FilterLastHeartbeatTime = filterViewModel.FilterLastHeartbeatTime,
                FilterScreenType = filterViewModel.FilterScreenType,
                FilterHostName = filterViewModel.FilterHostName,
                FilterVoiceAnnouncement = filterViewModel.FilterVoiceAnnouncement,
                Pagination = new QmsPaginationApiRequest
                {
                    PageNumber = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = "Name",
                    Ascending = true
                }
            };

            var apiResponse = await RestHttpClient.Create().Post<GetPaginatedScreensResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.Screens, QMSApiDefaultRequestHeaders, apiRequest);

            if (apiResponse.Data is null)
                return Content(apiResponse.Message);

            var paginatedData = apiResponse.Data.Select(p => new ScreenViewModel
            {
                EncryptedId = p.Id.ToEncrypt(),
                Branch = new BranchDto
                {
                    Id = p.Branch.Id,
                    Name = p.Branch.Name
                },
                Line = new LineDto
                {
                    Id = p.Line.Id,
                    Name = p.Line.Name
                },
                Department = new DepartmentDto
                {
                    Id = p.Department.Id,
                    Name = p.Department.Name
                },
                Name = p.Name,
                Version = p.Version,
                BlockNumber = p.BlockNumber,
                FloorNumber = p.FloorNumber,
                ContentType = p.ContentType,
                ContentTypeName = EnumHelper.GetEnumDescription(typeof(QMSContentType), p.ContentType.ToString()),
                LastHeartbeatTime = DateTime.Now.Date == p.LastHeartbeatTime.GetValueOrDefault().Date && DateTime.Now.TimeOfDay.Add(-p.LastHeartbeatTime.GetValueOrDefault().TimeOfDay) < new TimeSpan(00, 10, 00) ? "Online" : "Offline",
                VoiceAnnouncement = p.VoiceAnnouncement,
                ScreenType = p.ScreenType,
                ScreenTypeName = EnumHelper.GetEnumDescription(typeof(QMSScreenType), p.ScreenType.ToString()),
                ScreenCounterType = new CounterDto
                {
                    Id = p.ScreenCounterType.Id,
                    Name = p.ScreenCounterType.Name
                },
                HostName = p.HostName
            }).ToList();

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.TotalNumberOfRecords });
        }

        #endregion

        #region Detail
        public async Task<IActionResult> GetScreen(string encryptedId)
        {
            var resourceId = encryptedId.ToDecryptInt();

            var apiResponse = await RestHttpClient.Create().Get<GetScreenResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.Screen + resourceId, QMSApiDefaultRequestHeaders);

            if (apiResponse.Data is null)
                return Content(apiResponse.Message);

            var viewModel = new ScreenViewModel
            {
                Name = apiResponse.Data.Name,
                BlockNumber = apiResponse.Data.BlockNumber,
                FloorNumber = apiResponse.Data.FloorNumber,
                ContentTypeName = EnumHelper.GetEnumDescription(typeof(QMSContentType), apiResponse.Data.ContentType.ToString()),
                IsProcessSameCounter= apiResponse.Data.IsProcessSameCounter,
                ScreenTypeName = EnumHelper.GetEnumDescription(typeof(QMSScreenType), apiResponse.Data.ScreenType.ToString()),
                HostName = apiResponse.Data.HostName,
                VoiceAnnouncement = apiResponse.Data.VoiceAnnouncement,
                Version = apiResponse.Data.Version
            };

            if(apiResponse.Data.Branch != null)
            {
                viewModel.Branch = new BranchDto
                {
                    Id = apiResponse.Data.Branch.Id,
                    Name = apiResponse.Data.Branch.Name
                };
            }
            if (apiResponse.Data.Line != null)
            {
                viewModel.Line = new LineDto
                {
                    Id = apiResponse.Data.Line.Id,
                    Name = apiResponse.Data.Line.Name
                };
            }
            if (apiResponse.Data.Department != null)
            {
                viewModel.Department = new DepartmentDto
                {
                    Id = apiResponse.Data.Department.Id,
                    Name = apiResponse.Data.Department.Name
                };
            }
            if (apiResponse.Data.ScreenCounterType != null)
            {
                viewModel.ScreenCounterType = new CounterDto
                {
                    Id = apiResponse.Data.ScreenCounterType.Id,
                    Name = apiResponse.Data.ScreenCounterType.Name
                };
            }

            return PartialView("_Screen", viewModel);
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateScreen(string encryptedId)
        {
            var resourceId = encryptedId.ToDecryptInt();

            var apiResponse = await RestHttpClient.Create().Get<GetScreenResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.Screen + resourceId, QMSApiDefaultRequestHeaders);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var viewModel = new UpdateScreenViewModel { EncryptedId = resourceId.ToEncrypt() };

            if (apiResponse.Data.Branch != null)
                viewModel.BranchId = apiResponse.Data.Branch.Id;
            if (apiResponse.Data.Line != null)
                viewModel.LineId = apiResponse.Data.Line.Id;
            if (apiResponse.Data.Department != null) {
                viewModel.DepartmentId = apiResponse.Data.Department.Id;
                viewModel.IsProcessSameCounter = apiResponse.Data.IsProcessSameCounter;
            }
            if (apiResponse.Data.Name != null)
                viewModel.Name = apiResponse.Data.Name;
            if (apiResponse.Data.BlockNumber != null)
                viewModel.BlockNumber = apiResponse.Data.BlockNumber;
            if (apiResponse.Data.FloorNumber != null)
                viewModel.FloorNumber = apiResponse.Data.FloorNumber;
            if (apiResponse.Data.ContentType != null)
                viewModel.ContentType = apiResponse.Data.ContentType;
            if (apiResponse.Data.ScreenType != null)
                viewModel.ScreenType = apiResponse.Data.ScreenType;
            if (apiResponse.Data.ScreenCounterType != null)
                viewModel.ScreenCounterType = apiResponse.Data.ScreenCounterType.Id;
            if (apiResponse.Data.VoiceAnnouncement)
                viewModel.VoiceAnnouncement = apiResponse.Data.VoiceAnnouncement;

            return PartialView("_UpdateScreen", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateScreen(UpdateScreenViewModel viewModel)
        {
            if (viewModel is null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var resourceId = viewModel.EncryptedId.ToDecryptInt();

            var apiRequest = new UpdateScreenRequestModel
            {
                Id = viewModel.EncryptedId.ToDecryptInt(),
                BranchId = viewModel.BranchId,
                LineId = viewModel.LineId,
                DepartmentId = viewModel.DepartmentId,
                Name = viewModel.Name,
                BlockNumber = viewModel.BlockNumber,
                FloorNumber = viewModel.FloorNumber,
                ContentType = viewModel.ContentType,
                ScreenType = viewModel.ScreenType,
                VoiceAnnouncement = viewModel.VoiceAnnouncement,
                ScreenCounterType = viewModel.ScreenCounterType,
            };

            var apiResponse = await RestHttpClient.Create().Put<UpdateScreenResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.Screen + resourceId, QMSApiDefaultRequestHeaders, apiRequest);

            return Json(apiResponse.Data is null ? 
                new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Danger } : 
                new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteScreen(string encryptedId)
        {
            if (encryptedId.IsNullOrWhitespace())
            {
                return Content("Missing WhiteList id");
            }

            var resourceId = encryptedId.ToDecryptInt();

            var apiResponse = await RestHttpClient.Create().Delete<DeleteScreenResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.Screen + resourceId, QMSApiDefaultRequestHeaders);

            return apiResponse.Data is null ? 
                Json(apiResponse.Message) : 
                Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Screen Tokens

        public async Task<IActionResult> GetScreenTokens(string encryptedId)
        {
            var resourceId = encryptedId.ToDecryptInt();

            var apiResponse = await RestHttpClient.Create().Get<GetScreenResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.ScreenTokens + resourceId, QMSApiDefaultRequestHeaders);

            if (apiResponse?.Data is null)
                return Json(new { Message = apiResponse?.Message ?? SiteResources.NotFound });
                
            var viewModel = new ScreenViewModel
            {
                Tokens = apiResponse.Data.Tokens
            };

            return PartialView("_ScreenTokens", viewModel);
        }

        #endregion

        public async Task<IActionResult> ScreenIsProcessSameCounter(int departmentId, int lineId)
        {
            if (departmentId == 0) 
                return Json(new { Result = true });

            var apiResponse = await RestHttpClient.Create().Get<IsProcessSameCounterResult>($"{AppSettings.Qms.BaseApiUrl}/api/screens/departments/{(departmentId > 0 ? departmentId : 0)}/lines/{(lineId > 0 ? lineId : 0)}", QMSApiDefaultRequestHeaders);

            return apiResponse.Data is null ?
                Json(new 
                {
                    Result = true,
                    apiResponse.Message
                }) :
                Json(new { Result = apiResponse.Data.IsProcessSameCounter });
        }
    }
}