﻿using Gateway.IO;
using Gateway.ObjectStoring;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Management.Vms;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Management.Vms;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.Vms;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using static Portal.Gateway.UI.Areas.Management.ViewModels.Vms.AddVmsAnnouncementViewModel;
using AppSettings = Portal.Gateway.UI.Config.AppSettings;


namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class VmsController : BaseController<VmsController>
    {
        private readonly IFileStorage _fileStorage;

        public VmsController(ICacheHelper cacheHelper, IFileStorage fileStorage, IConfiguration configuration, IOptions<AppSettings> appSettings) : base(appSettings, cacheHelper)
        {
            _fileStorage = fileStorage;
        }

        [Action(IsMenuItem = true)]
        public IActionResult List()
        {
            var viewModel = new FilterVmsViewModel
            {
                VideoStartDateTime = DateTime.Now,
                VideoEndDateTime = DateTime.Now,
                AnnouncementStartDateTime = DateTime.Now,
                AnnouncementEndDateTime = DateTime.Now,
            };
            return View(viewModel);
        }

        #region Get

        public async Task<IActionResult> GetPaginatedVmsVideos([DataSourceRequest] DataSourceRequest request, FilterVmsViewModel filterViewModel)
        {
            var paginatedData = new List<VmsViewModel.VmsVideoViewModel>();

            if (filterViewModel.BranchId == default)
                return Json(new DataSourceResult { Data = paginatedData, Total = 0 });

            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new GetPaginatedVmsVideoByBranchApiRequest
            {
                BranchId = filterViewModel.BranchId,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<GetPaginatedVmsVideoByBranchApiResponse>>>
                    (apiRequest, ApiMethodName.Management.GetPaginatedVmsVideoByBranch, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().Videos
                    .Select(p => new VmsViewModel.VmsVideoViewModel
                    {
                        Id = p.Id,
                        Name = p.Name,
                        Order = p.Order,
                        StartDate = p.StartDate,
                        EndDate = p.EndDate
                    }).ToList();
            }

            var startDate = paginatedData.FirstOrDefault()?.StartDate ?? DateTime.Now;
            filterViewModel.VideoStartDateTime = startDate;

            var endDate = paginatedData.FirstOrDefault()?.EndDate ?? DateTime.Now;
            filterViewModel.VideoEndDateTime = endDate;

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        public async Task<IActionResult> GetPaginatedVmsAnnouncements([DataSourceRequest] DataSourceRequest request, FilterVmsViewModel filterViewModel)
        {
            var paginatedData = new List<VmsViewModel.VmsAnnouncementViewModel>();

            if (filterViewModel.BranchId == default)
                return Json(new DataSourceResult { Data = paginatedData, Total = 0 });

            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new GetPaginatedVmsAnnouncementByBranchApiRequest
            {
                BranchId = filterViewModel.BranchId,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<GetPaginatedVmsAnnouncementByBranchApiResponse>>>
                    (apiRequest, ApiMethodName.Management.GetPaginatedVmsAnnouncementByBranch, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);


            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().Announcements
                    .Select((p, index) => new VmsViewModel.VmsAnnouncementViewModel
                    {
                        Id = p.Id,
                        Title = p.Title,
                        Order = index + 1,
                        Languages = string.Join(", ", p.Translation.Where(i => i.IsActive).Select(s => s.Language)),
                        IsActive = p.IsActive
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        [HttpGet]
        public async Task<IActionResult> GetVideoDateTimes(int branchId)
        {
            if (branchId == 0)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<VmsTimeLineApiResponse>>
                    (ApiMethodName.Management.GetVmsTimeLines + branchId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            var filterViewModel = new FilterVmsViewModel
            {
                VideoStartDateTime = apiResponse.Data.VideoStartDateTime,
                VideoEndDateTime = apiResponse.Data.VideoEndDateTime,
                AnnouncementStartDateTime = apiResponse.Data.AnnouncementStartDateTime,
                AnnouncementEndDateTime = apiResponse.Data.AnnouncementEndDateTime
            };

            return Json(filterViewModel);
        }

        #endregion

        [HttpPost]
        public async Task<IActionResult> SessionUploadVideoFile([FromForm] IFormFile file, string fileSessionId)
        {
            if (file != null && !string.IsNullOrEmpty(fileSessionId))
            {
                using (var ms = new MemoryStream())
                {
                    file.CopyTo(ms);
                    await _fileStorage.SaveFileAsync(FileStorage.FileStorageBucketName, $"{FileStorage.SessionUploadFileFolderName}/{fileSessionId}", ms, "");
                }

                var fileModel = FileHelper.GetFileInfo(file);
                Extensions.SessionExtensions.Set(HttpContext.Session, $"SessionUploadFile{fileSessionId}", fileModel);
            }

            return Content("");
        }

        public IActionResult SessionRemoveFile([FromForm] IFormFile file, string fileSessionId)
        {
            if (file != null && !string.IsNullOrEmpty(fileSessionId))
            {
                var fileModel = Extensions.SessionExtensions.Get<FileModel>(HttpContext.Session, $"SessionUploadFile{fileSessionId}");
                Extensions.SessionExtensions.Set(HttpContext.Session, $"SessionUploadFile{fileSessionId}", fileModel);
            }

            return Content("");
        }

        #region Add
        public IActionResult PartialAddVmsVideo(int branchId, DateTime videoStartDate, DateTime videoEndDate)
        {
            var viewModel = new AddVmsVideoViewModel
            {
                BranchId = branchId,
                FileSessionId = Guid.NewGuid().ToString(),
                StarDateTime = videoStartDate,
                EndDateTime = videoEndDate
            };

            return PartialView("_AddVmsVideo", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddVmsVideoFile(AddVmsVideoViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var fileModel = Extensions.SessionExtensions.Get<FileModel>(HttpContext.Session, $"SessionUploadFile{viewModel.FileSessionId}");

            if (fileModel == null)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var fileName = string.Concat(fileModel.OriginalFileName.Split(Path.GetInvalidFileNameChars()));

            var apiRequest = new AddVmsVideoApiRequest
            {
                BranchId = viewModel.BranchId,
                Name = viewModel.Name,
                FileName = fileName,
                FileExtension = fileModel.FileExtension,
                UploadPath = $"{viewModel.BranchId}/{fileModel.OriginalFileName}",
            };

            if ((await _fileStorage.ExistsAsync("qms-video", $"{apiRequest.UploadPath}{apiRequest.FileExtension}")))
            {
                return Json(new ResultModel { Message = SiteResources.RESOURCE_ALREADY_REGISTERED, ResultType = ResultType.Danger });
            }

            var stream = await _fileStorage.GetFileStreamAsync(FileStorage.FileStorageBucketName, $"{FileStorage.SessionUploadFileFolderName}/{viewModel.FileSessionId}");

            await _fileStorage.SaveFileAsync("qms-video", $"{apiRequest.UploadPath}{apiRequest.FileExtension}", stream, MimeType.GetMimeType(apiRequest.FileExtension));

            if (!(await _fileStorage.ExistsAsync("qms-video", $"{apiRequest.UploadPath}{apiRequest.FileExtension}")))
            {
                return Json(new ResultModel { Message = SiteResources.Exception_NoRecordsFound, ResultType = ResultType.Danger });
            }

            var addVmsVideoApiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                    (apiRequest, ApiMethodName.Management.AddVmsVideo, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

            if (!await addVmsVideoApiResponse.Validate(out ResultModel addDocumentsResult).ConfigureAwait(false))
                return Json(addDocumentsResult);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        public IActionResult PartialAddVmsAnnouncement(int branchId, DateTime announcementStartDate, DateTime announcementEndDate)
        {
            var viewModel = new AddVmsAnnouncementViewModel
            {
                BranchId = branchId,
                Title = "",
                StartTime = announcementStartDate,
                EndTime = announcementEndDate
            };

            var languages = EnumHelper.GetEnumAsDictionary(typeof(VmsAnnouncementLanguage)).OrderBy(o => o.Value);

            foreach (var item in languages)
            {
                viewModel.Translations.Add(new AddVmsAnnouncementViewModel.AddVmsAnnouncementTranslationViewModel { LanguageId = item.Key });
            }

            return PartialView("_AddVmsAnnouncement", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddVmsAnnouncement(AddVmsAnnouncementViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddVmsAnnouncementApiRequest
            {
                BranchId = viewModel.BranchId,
                Title = viewModel.Title,
                Translation = viewModel.Translations.Select(p => new VmsAnnouncementTranslationDto
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                    IsActive = p.IsActive
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                    (apiRequest, ApiMethodName.Management.AddVmsAnnouncement, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }
        #endregion

        #region Update
        public async Task<IActionResult> PartialUpdateVmsAnnouncement(int id)
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<GetPaginatedVmsAnnouncementByBranchApiResponse.VmsAnnouncementDto>>
                (ApiMethodName.Management.GetVmsAnnouncement + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new AddVmsAnnouncementViewModel
            {
                Id = apiResponse.Data.Id,
                Title = apiResponse.Data.Title,
                IsActive = apiResponse.Data.IsActive,
                IsUpdate = true,
                Translations = apiResponse.Data.Translation.Select(p => new AddVmsAnnouncementTranslationViewModel
                {
                    Id = p.Id,
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                    IsActive = p.IsActive
                }).ToList()
            };


            return PartialView("_AddVmsAnnouncement", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateVmsAnnouncement(AddVmsAnnouncementViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateVmsAnnouncementApiRequest
            {
                Id = viewModel.Id.Value,
                BranchId = viewModel.BranchId,
                Title = viewModel.Title,
                IsActive = viewModel.IsActive,
                Translation = viewModel.Translations.Select(p => new VmsAnnouncementTranslationDto
                {
                    Id = p.Id,
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                    IsActive = p.IsActive
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<EmptyApiResponse>>
                    (apiRequest, ApiMethodName.Management.UpdateVmsAnnouncement + viewModel.Id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpPut]
        public async Task<IActionResult> UpdateVmsVideoOrder(UpdateVmsVideoOrderViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateVmsVideoOrderApiRequest
            {
                Videos = viewModel.VideoOrders.Select(s => new UpdateVmsVideoOrderApiRequest.VmsVideoOrderDto
                {
                    Id = s.Id,
                    Order = s.NewOrder
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                    (apiRequest, ApiMethodName.Management.UpdateVmsVideoOrder, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpPut]
        public async Task<IActionResult> UpdateVmsAnnouncementTimeline(VmsTimeLineViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateVmsAnnouncementTimelineApiRequest
            {
                BranchId = viewModel.BranchId,
                StartDate = viewModel.StartDate,
                EndDate = viewModel.EndDate
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                    (apiRequest, ApiMethodName.Management.UpdateVmsAnnouncementTimeline, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpPut]
        public async Task<IActionResult> UpdateVmsVideoTimeline(VmsTimeLineViewModel viewModel)
        {

            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateVmsVideoTimelineApiRequest
            {
                BranchId = viewModel.BranchId,
                StartDate = viewModel.StartDate,
                EndDate = viewModel.EndDate
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                    (apiRequest, ApiMethodName.Management.UpdateVmsVideoTimeline, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }
        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteVmsVideo(int id)
        {
            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                    (ApiMethodName.Management.DeleteVmsVideo + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteVmsAnnouncement(int id)
        {
            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                    (ApiMethodName.Management.DeleteVmsAnnouncement + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion
    }
}