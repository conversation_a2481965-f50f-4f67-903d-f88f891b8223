﻿using ExcelDataReader;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using JsonSerializer = System.Text.Json.JsonSerializer;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gateway.Extensions;
using Gateway.Http;
using Portal.Gateway.ApiModel.Requests.QmsFolder;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.ApiModel.Responses.Management.RoleAction;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.PreApplication;
using Portal.Gateway.UI.Areas.QMS.Helpers.Scanners.Desko;
using Portal.Gateway.UI.Areas.QMS.Models;
using Portal.Gateway.UI.Areas.QMS.Models.QMS.Requests;
using Portal.Gateway.UI.Areas.QMS.Models.QMS.Requests.Actions.RequestModels;
using Portal.Gateway.UI.Areas.QMS.Models.QMS.Responses;
using Portal.Gateway.UI.Areas.QMS.Models.QMS.Responses.Actions;
using Portal.Gateway.UI.Areas.QMS.ViewModels;
using Portal.Gateway.UI.Areas.QMS.ViewModels.Action;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Models;
using GeneratedTokenNote = Portal.Gateway.UI.Areas.QMS.ViewModels.GeneratedTokenNote;
using SessionExtensions = Portal.Gateway.UI.Extensions.SessionExtensions;
using Gateway.ObjectStoring;
using Portal.Gateway.UI.Helpers;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Mvc.Rendering;
using Portal.Gateway.ApiModel.Requests.Agency.Agency;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Responses.Agency.Agency;
using System.ComponentModel;
using Portal.Gateway.ApiModel.Requests.Public.SlotType;

namespace Portal.Gateway.UI.Areas.QMS.Controllers
{
    [Area("QMS")]
    public class QmsController : BaseController<QmsController>
    {
        private const string QMS_API_RESPONSE_SUCCESS = "SUCCESS";
        private readonly IFileStorage _fileStorage;

        public QmsController(
            IFileStorage fileStorage,
            IConfiguration configuration,
            IOptions<AppSettings> appSettings,
            ICacheHelper cacheHelper) : base(appSettings, cacheHelper) {
            _fileStorage = fileStorage;
        }

        [ActionAttribute(IsMenuItem = true)]
        public async Task<IActionResult> SelectQmsFlowAsync()
        {
            if (UserSession?.BranchId is null)
                return RedirectToAction("SelectModuleBranch", "User", new { Area = string.Empty });

            var apiResponse = await RestHttpClient.Create().Get<GetLinesByBranchResult>(
                $"{AppSettings.Qms.BaseApiUrl}/api/branches/{UserSession.BranchId}/lines", QMSApiDefaultRequestHeaders);

            ViewData["allLines"] = apiResponse?.Data?.Select(line => new
            {
                Text = $"{line.Name}",
                Value = line.Id.ToString()
            }).AsEnumerable();

            ViewData["appointmentLines"] = apiResponse?.Data?.Where(r => !r.SearchByPassportNumber).Select(line => new
            {
                Text = $"{line.Name}",
                Value = line.Id.ToString()
            }).AsEnumerable();

            ViewData["passportDeliveryLines"] = apiResponse?.Data?.Where(r => r.SearchByPassportNumber).Select(line => new
            {
                Text = $"{line.Name}",
                Value = line.Id.ToString()
            }).AsEnumerable();

            ViewData["LastAppointmentId"] = UserSession.LastTakenAppointmentId;

            return View();
        }

        [HttpGet]
        public Task<IActionResult> GetQmsDropdowns()
        {
            var resultViewModel = new QmsMainQueueFlowViewModel
            {
                CounterId = UserSession.QmsDropdownsModel.CounterId ?? 0,
                MainLineId = UserSession.QmsDropdownsModel.LineId ?? 0
            };

            return Task.FromResult<IActionResult>(PartialView("_QmsDropdowns", resultViewModel));
        }

        #region SearchAppointment

        public async Task<IActionResult> PassportNumberSearch(string encryptedPassportNumber, int lineId)
        {
            if (lineId == 0)
                return Json(new ResultModel { Message = SiteResources.LineTypeShouldBeSelected, ResultType = ResultType.Warning });

            var passportNumber = encryptedPassportNumber?.ToDecrypt();
            if (string.IsNullOrEmpty(passportNumber))
                return Json(new ResultModel { Message = SiteResources.PassportNumber, ResultType = ResultType.Warning });

            var request = new CreateTicketRequestModel
            {
                LineId = lineId,
                PassportNumber = passportNumber
            };

            var apiResponse = await RestHttpClient.Create().Post<GetAppointmentInformationResult>(
            AppSettings.Qms.BaseApiUrl + QmsEndPoint.GetAppointmentInformationWithPassportNumber, QMSApiDefaultRequestHeaders, request);

            if (apiResponse?.Data is null)
                return Json(new ResultModel { Message = apiResponse?.Message ?? SiteResources.AppointmentNotFound, ResultType = ResultType.Warning });

            if (DateTime.Parse(apiResponse.Data.AppointmentDate).Date > DateTime.UtcNow.Date)
            {
                var branches = await CacheHelper.GetBranchesAsync();
                var branchName = branches?.Branches?.FirstOrDefault(p => p.Id == apiResponse.Data.BranchId)?.BranchTranslations?.FirstOrDefault(q => q.LanguageId == LanguageId)?.Name;

                return Json(new ResultModel
                {
                    Message = $"{SiteResources.EarlyAppointmentProcess} {apiResponse.Data.AppointmentDate}" + Environment.NewLine + $"{SiteResources.AppointmentBranch}: {branchName}",
                    ResultType = ResultType.Warning
                });
            }

            if (DateTime.Parse(apiResponse.Data.AppointmentDate).Date < DateTime.UtcNow.Date)
                return Json(new ResultModel { Message = $"{SiteResources.AppointmentOutOfDate} {apiResponse.Data.AppointmentDate}", ResultType = ResultType.Warning });

            if (apiResponse.Data.BranchId != UserSession.BranchId)
                return Json(new ResultModel { Message = $"{SiteResources.WrongBranchNotification}", ResultType = ResultType.Warning });

            var userSession = UserSession;
            if (userSession == null)
                return Json(new ResultModel { Message = $"{SiteResources.AppointmentNotFound}", ResultType = ResultType.Warning });

            var offsetTime = await GetTimeZoneOffset();

            var branchTime = DateTime.Now.AddHours(offsetTime);
            var appointmentHour = ConvertHourStringToDateTime(apiResponse.Data.AppointmentHour);

            var viewModel = new CreateTicketViewModel
            {
                LineId = lineId,
                AppointmentNumber = apiResponse.Data.AppointmentNumber,
                AppointmentDate = apiResponse.Data.AppointmentDate,
                AppointmentHour = apiResponse.Data.AppointmentHour,
                BranchName = apiResponse.Data.BranchName,
                Agency = apiResponse.Data.Agency,
                Note = apiResponse.Data.Note,
                SearchType = (byte)SearchType.PassportNumber,
                IsPassTime = !apiResponse.Data.IsWalkin && apiResponse.Data.VasType?.Id.ToInt() != (int)VasType.Vip && branchTime >= appointmentHour,
                Applicants = apiResponse.Data.Applicants.Select(p => new CreateTicketViewModel.Applicant
                {
                    Id = p.Id,
                    Name = p.Name,
                    Surname = p.Surname,
                    PassportNumber = p.PassportNumber,
                    BirthDate = p.BirthDate,
                    PassportExpireDate = p.PassportExpireDate,
                    IsCreatedTicket = p.IsCreatedTicket,
                    GenderId = p.GenderId,
                    NationalityId = p.NationalityId,
                    Email = p.Email,
                    PhoneNumber = p.PhoneNumber,
                    InsuranceTypeIds = p.InsuranceTypeIds,
                    OldWhitelistApplicant = p.OldWhitelistApplicant,
                    HasBlackListApplicant = p.HasBlackListApplicant,
                    HasWhiteListApplicant = p.HasWhiteListApplicant,
                    BlackListModel = p.BlackListModel != null ? new BlackListModel
                    {
                        BlackListNote = p.BlackListModel.BlackListNote,
                        BlackListNoteCreatedBy = p.BlackListModel.BlackListNoteCreatedBy,
                        BlackListNoteUpdatedBy = p.BlackListModel.BlackListNoteUpdatedBy,
                    } : null
                }).ToList(),
                ApplicantType = new CreateTicketViewModel.LookupValue
                {
                    Id = apiResponse.Data.ApplicantType.Id,
                    DisplayValue = apiResponse.Data.ApplicantType.DisplayValue
                }
            };

            var cachedAppointmentNumber = Convert.ToInt32(userSession.CreateTicketViewModel?.AppointmentNumber?.TrimStart('0'));

            var isNewAppointment = userSession.CreateTicketViewModel?.Applicants?.Count == 0 ||
                                   cachedAppointmentNumber != Convert.ToInt32(apiResponse.Data.AppointmentNumber) ||
                                   userSession.CreateTicketViewModel?.LineId != lineId;

            if (isNewAppointment)
                userSession.CreateTicketViewModel = viewModel;

            if (userSession.CreateTicketViewModel?.Applicants?.Count > 0)
            {
                var createdTicketApplicantsExistingAppointment = viewModel.Applicants.Where(r => r.IsCreatedTicket).ToList();

                userSession.CreateTicketViewModel.Applicants.RemoveAll(r => createdTicketApplicantsExistingAppointment.Select(p => p.Id).ToList().Contains(r.Id));
                userSession.CreateTicketViewModel.Applicants.RemoveAll(r => createdTicketApplicantsExistingAppointment.Select(p => p.PassportNumber).ToList().Contains(r.PassportNumber));

                userSession.CreateTicketViewModel.IsPassTime = viewModel.IsPassTime;
            }

            if (userSession.CreateTicketViewModel?.Applicants?.Count == 0)
                return Json(new ResultModel { Message = $"{SiteResources.ATokenHasBeenCreatedForAllPeopleofThisAppointmentNumber}", ResultType = ResultType.Warning });

            SessionExtensions.Set(HttpContext.Session, SessionKeys.UserSession, userSession);

            return PartialView("_CreateTicketPassportNumberSearch", userSession.CreateTicketViewModel);
        }

        public async Task<IActionResult> AppointmentNumberSearch(string encryptedPreApplicationId, int lineId)
        {
            if (lineId == 0)
                return Json(new { Message = SiteResources.LineTypeShouldBeSelected });

            if (encryptedPreApplicationId is null)
                return Json(new { Message = SiteResources.AppointmentNumberIsRequired });

            var preApplication = Convert.ToInt32(encryptedPreApplicationId.ToDecrypt());

            var request = new CreateTicketRequestModel
            {
                LineId = lineId,
                AppointmentId = preApplication
            };

            var apiResponse = await RestHttpClient.Create().Post<GetAppointmentInformationResult>(
            AppSettings.Qms.BaseApiUrl + QmsEndPoint.GetAppointmentInformationWithAppointmentNumber, QMSApiDefaultRequestHeaders, request);

            if (apiResponse?.Data is null)
                return Json(new { Message = apiResponse?.Message ?? SiteResources.AppointmentNotFound });

            if (DateTime.Parse(apiResponse.Data.AppointmentDate).Date > DateTime.UtcNow.Date)
            {
                var branches = await CacheHelper.GetBranchesAsync();
                var branchName = branches?.Branches?.FirstOrDefault(p => p.Id == apiResponse.Data.BranchId)?.BranchTranslations?.FirstOrDefault(q => q.LanguageId == LanguageId)?.Name;

                return Json(new ResultModel
                {
                    Message = $"{SiteResources.EarlyAppointmentProcess} {apiResponse.Data.AppointmentDate}" + Environment.NewLine +
                              $"{SiteResources.AppointmentBranch}: {branchName}",
                    ResultType = ResultType.Warning
                });
            }

            if (DateTime.Parse(apiResponse.Data.AppointmentDate).Date < DateTime.UtcNow.Date)
                return Json(new ResultModel { Message = $"{SiteResources.AppointmentOutOfDate} {apiResponse.Data.AppointmentDate}" });

            if (apiResponse.Data.BranchId != UserSession.BranchId)
                return Json(new { Message = SiteResources.WrongBranchNotification });

            var userSession = UserSession;
            if (userSession == null)
                return Json(new { Message = SiteResources.AppointmentNotFound });

            var offsetTime = await GetTimeZoneOffset();

            var branchTime = DateTime.Now.AddHours(offsetTime);
            var appointmentHour = ConvertHourStringToDateTime(apiResponse.Data.AppointmentHour);

            var viewModel = new CreateTicketViewModel
            {
                LineId = lineId,
                AppointmentNumber = apiResponse.Data.AppointmentNumber,
                AppointmentDate = apiResponse.Data.AppointmentDate,
                AppointmentHour = apiResponse.Data.AppointmentHour,
                BranchName = apiResponse.Data.BranchName,
                Agency = apiResponse.Data.Agency,
                Note = apiResponse.Data.Note,
                SearchType = (byte)SearchType.AppointmentNumber,
                IsPassTime = !apiResponse.Data.IsWalkin && apiResponse.Data.VasType?.Id.ToInt() != (int)VasType.Vip && branchTime >= appointmentHour,
                Applicants = apiResponse.Data.Applicants.Select(p => new CreateTicketViewModel.Applicant
                {
                    Id = p.Id,
                    Name = p.Name,
                    Surname = p.Surname,
                    PassportNumber = p.PassportNumber,
                    BirthDate = p.BirthDate,
                    PassportExpireDate = p.PassportExpireDate,
                    IsCreatedTicket = p.IsCreatedTicket,
                    GenderId = p.GenderId,
                    NationalityId = p.NationalityId,
                    Email = p.Email,
                    PhoneNumber = p.PhoneNumber,
                    InsuranceTypeIds = p.InsuranceTypeIds,
                    OldWhitelistApplicant = p.OldWhitelistApplicant,
                    HasBlackListApplicant = p.HasBlackListApplicant,
                    HasWhiteListApplicant = p.HasWhiteListApplicant,
                    BlackListModel = p.BlackListModel != null ? new BlackListModel
                    {
                        BlackListNote = p.BlackListModel.BlackListNote,
                        BlackListNoteCreatedBy = p.BlackListModel.BlackListNoteCreatedBy,
                        BlackListNoteUpdatedBy = p.BlackListModel.BlackListNoteUpdatedBy,
                    } : null
                }).ToList(),
                ApplicantType = new CreateTicketViewModel.LookupValue
                {
                    Id = apiResponse.Data.ApplicantType.Id,
                    DisplayValue = apiResponse.Data.ApplicantType.DisplayValue
                }
            };

            var cachedAppointmentNumber = Convert.ToInt32(userSession.CreateTicketViewModel?.AppointmentNumber?.TrimStart('0'));

            var isNewAppointment = userSession.CreateTicketViewModel?.Applicants?.Count == 0 ||
                                   cachedAppointmentNumber != preApplication ||
                                   userSession.CreateTicketViewModel?.LineId != lineId;

            if (isNewAppointment)
                userSession.CreateTicketViewModel = viewModel;

            if (userSession.CreateTicketViewModel?.Applicants?.Count > 0)
            {
                var createdTicketApplicantsExistingAppointment = viewModel.Applicants.Where(r => r.IsCreatedTicket).ToList();

                userSession.CreateTicketViewModel.Applicants.RemoveAll(r => createdTicketApplicantsExistingAppointment.Select(p => p.Id).ToList().Contains(r.Id));
                userSession.CreateTicketViewModel.Applicants.RemoveAll(r => createdTicketApplicantsExistingAppointment.Select(p => p.PassportNumber).ToList().Contains(r.PassportNumber));

                var blacklistAppointments = viewModel.Applicants.Where(r => r.HasBlackListApplicant).ToList();
                userSession.CreateTicketViewModel.Applicants.Where(r => blacklistAppointments.Exists(p => p.Id == r.Id && r.PassportNumber != p.PassportNumber)).ToList().ForEach(r => r.HasBlackListApplicant = false);

                var whitelistAppointments = viewModel.Applicants.Where(r => r.HasWhiteListApplicant)/*.Select(r => r.Id)*/.ToList();
                userSession.CreateTicketViewModel.Applicants.Where(r => whitelistAppointments.Exists(p => p.Id == r.Id && r.PassportNumber != p.PassportNumber)).ToList().ForEach(r => r.HasWhiteListApplicant = false);

                userSession.CreateTicketViewModel.IsPassTime = viewModel.IsPassTime;
            }

            if (userSession.CreateTicketViewModel?.Applicants?.Count == 0)
                return Json(new { Message = SiteResources.ATokenHasBeenCreatedForAllPeopleofThisAppointmentNumber });

            SessionExtensions.Set(HttpContext.Session, SessionKeys.UserSession, userSession);

            return PartialView("_CreateTicketAppointmentNumberSearch", userSession.CreateTicketViewModel);
        }

        #endregion

        #region SearchApplication

        public async Task<IActionResult> PassportDeliverySearch(string encryptedPassportNumber, int lineId)
        {
            var passportNumber = encryptedPassportNumber?.ToDecrypt();
            if (string.IsNullOrEmpty(passportNumber))
                return Json(new { Message = SiteResources.PassportNumber });

            if (lineId == 0)
                return Json(new { Message = SiteResources.LineTypeShouldBeSelected });

            var request = new CreateTicketRequestModel
            {
                LineId = lineId,
                PassportNumber = passportNumber
            };

            var apiResponse = await RestHttpClient.Create().Post<GetApplicationInformationResult>(
                    AppSettings.Qms.BaseApiUrl + QmsEndPoint.GetApplicationInformation, QMSApiDefaultRequestHeaders, request);

            if (apiResponse?.Data is null)
                return Json(new { Message = apiResponse?.Message ?? SiteResources.ApplicationNotFound });

            if (apiResponse.Data.BranchId != UserSession.BranchId)
                return Json(new { Message = SiteResources.WrongBranchNotification });

            if (!apiResponse.Data.Applicants.Exists(p => !p.IsCreatedTicket))
                return Json(new { Message = SiteResources.ATokenHasBeenCreatedForAllPeopleofThisAppointmentNumber });

            var userSession = UserSession;
            if (userSession == null)
                return Json(new { Message = SiteResources.AppointmentNotFound });

            var viewModel = new CreateTicketViewModel
            {
                LineId = lineId,
                AppointmentNumber = apiResponse.Data.AppointmentNumber,
                BranchName = apiResponse.Data.BranchName,
                Note = apiResponse.Data.Note,
                SearchType = (byte)SearchType.Application,
                Applicants = apiResponse.Data.Applicants.Select(p => new CreateTicketViewModel.Applicant
                {
                    Id = p.Id,
                    Name = p.Name,
                    Surname = p.Surname,
                    PassportNumber = p.PassportNumber,
                    IsCreatedTicket = p.IsCreatedTicket,
                    HasBlackListApplicant = p.HasBlackListApplicant,
                    HasWhiteListApplicant = p.HasWhiteListApplicant,
                    BlackListModel = p.BlackListModel != null ? new BlackListModel
                    {
                        BlackListNote = p.BlackListModel.BlackListNote,
                        BlackListNoteCreatedBy = p.BlackListModel.BlackListNoteCreatedBy,
                        BlackListNoteUpdatedBy = p.BlackListModel.BlackListNoteUpdatedBy,
                    } : null
                }).ToList()
            };

            var cachedPassportNumber = userSession.CreateTicketViewModel?.PassportNumber;

            var isNewApplication = userSession.CreateTicketViewModel?.Applicants?.Count == 0 ||
                                   cachedPassportNumber != passportNumber ||
                                   userSession.CreateTicketViewModel?.LineId != lineId;

            if (isNewApplication)
            {
                userSession.CreateTicketViewModel = viewModel;
                userSession.CreateTicketViewModel.PassportNumber = passportNumber;
            }

            if (userSession.CreateTicketViewModel?.Applicants?.Count > 0)
            {
                var createdTicketApplicantsExistingAppointment = viewModel.Applicants.Where(r => r.IsCreatedTicket).ToList();

                userSession.CreateTicketViewModel.Applicants.RemoveAll(r => createdTicketApplicantsExistingAppointment.Select(p => p.Id).ToList().Contains(r.Id));
                userSession.CreateTicketViewModel.Applicants.RemoveAll(r => createdTicketApplicantsExistingAppointment.Select(p => p.PassportNumber).ToList().Contains(r.PassportNumber));
            }

            if (userSession.CreateTicketViewModel?.Applicants?.Count == 0)
                return Json(new { Message = SiteResources.ATokenHasBeenCreatedForAllPeopleofThisAppointmentNumber });

            SessionExtensions.Set(HttpContext.Session, SessionKeys.UserSession, userSession);

            return PartialView("_CreateTicketPassportDelivery", userSession.CreateTicketViewModel);
        }

        #endregion

        #region Ticket

        [HttpPost]
        public async Task<IActionResult> CreateAndPrintTicket(int appointmentId, int lineId, int[] applicantIds, int relationalType, bool priority, bool passportDelivery)
        {
            var applicants = applicantIds.ToList();

            if (lineId == 0 || appointmentId == 0)
                return Json(new ResultModel { Message = SiteResources.LineTypeShouldBeSelected, ResultType = ResultType.Warning });

            if ((relationalType == (int)ApplicantType.Family || relationalType == (int)ApplicantType.Group) && applicants.Count == 1)
                return Json(new ResultModel { Message = SiteResources.MoreThanOnePersonShouldBeSelectedForFamilyOrGroupAppointments, ResultType = ResultType.Warning });

            var userSession = UserSession;
            var branchScreenTitleName = string.Empty;
            var branchCityName = string.Empty;
            var applicantsInfo = userSession.CreateTicketViewModel.Applicants.Where(r => applicantIds.Contains(r.Id)).ToList();

            var cacheItemBranch = await CacheHelper.GetBranchesAsync();
            branchScreenTitleName = cacheItemBranch?.Branches?.FirstOrDefault(p => p.Id == userSession.BranchId)?.QmsScreenTitleName;
            branchCityName = cacheItemBranch?.Branches?.FirstOrDefault(p => p.Id == userSession.BranchId)?.CityName;

            if (userSession.IsPassportScanRequired && !passportDelivery)
            {
                if (applicantsInfo.Exists(r => !r.IsScan))
                    return Json(new ResultModel { Message = SiteResources.UnscannedPassport, ResultType = ResultType.Warning });
            }

            var selectedPassports = applicantsInfo.Select(r => r.PassportNumber).ToList();

            foreach (var passport in selectedPassports)
            {
                if (selectedPassports.Where(r => r == passport).Select(r => r).ToList().Count > 1)
                    return Json(new ResultModel { Message = SiteResources.RepetitiveRecording, ResultType = ResultType.Warning });
            }

            var request = new CreateTicketRequestModel
            {
                LineId = lineId,
                RelationalType = (byte)relationalType,
                AppointmentId = appointmentId,
                ApplicantIds = applicants,
                Applicants = applicantsInfo,
                Priority = priority,
                PassportDeliverySearch = passportDelivery
            };

            var apiResponseGetDepartmentsByLine = await RestHttpClient.Create().Get<GetDepartmentsByLineResult>(
                AppSettings.Qms.BaseApiUrl + $"/api/lines/{lineId}/departments", QMSApiDefaultRequestHeaders);

            if (apiResponseGetDepartmentsByLine?.Data is null)
                return Json(ResultModel.Error(apiResponseGetDepartmentsByLine?.Message ?? $"{ResultMessage.RecordNotFound.ToDescription()}"));

            var apiResponseCreateTicket = await RestHttpClient.Create().Post<CreateTicketResult>(
                AppSettings.Qms.BaseApiUrl + QmsEndPoint.CreateTicket, QMSApiDefaultRequestHeaders, request);

            switch (apiResponseCreateTicket?.Data)
            {
                case null when apiResponseCreateTicket?.Message == SiteResources.RESOURCE_ALREADY_REGISTERED:
                    return Json(new ResultModel { Message = SiteResources.TicketHaBeenTakenForSelectedPeople, ResultType = ResultType.Warning });
                case null:
                    return Json(new ResultModel { Message = SiteResources.TokenNotFound, ResultType = ResultType.Warning });
            }

            UpdateSessionLastPreApplicationId(appointmentId, false);

            var timeZoneOffset = await GetTimeZoneOffset();

            var viewModel = new PrintTicketViewModel
            {
                Token = apiResponseCreateTicket.Data.Token,
                AppointmentId = appointmentId,
                TimeOffset = timeZoneOffset,
                ApplicantCount = applicants.Count,
                ChildTokens = apiResponseCreateTicket.Data.ChildTokens.Select(p => new PrintTicketViewModel.ChildToken
                {
                    Department = p.Department,
                    Token = p.Token
                }).ToList(),
                Departments = apiResponseGetDepartmentsByLine.Data.Select(p => new PrintTicketViewModel.Department
                {
                    IsProcessSameCounter = p.IsProcessSameCounter,
                    Order = p.Order,
                    DepartmentName = p.DepartmentName
                }).ToList(),
                LineName = apiResponseCreateTicket.Data.Line,
                WaitingCount = apiResponseCreateTicket.Data.WaitingCount,
                QmsCompanyId = userSession.QmsCompanyId,
                BranchScreenTitleName = branchScreenTitleName,
                BranchCityName = branchCityName,
            };

            return Json(viewModel);
        }

        public ActionResult PrintTicketView()
        {
            var viewModel = JsonSerializer.Deserialize<PrintTicketViewModel>(Request.Form["model"].ToString());

            return View("_PrintTicketView", viewModel);
        }

        #endregion

        #region RePrint

        [HttpPost]
        public async Task<IActionResult> RePrintTicket(int lineId, string encryptedPassportNumber)
        {
            if (lineId == 0)
                return Json(new { Message = SiteResources.LineTypeShouldBeSelected });

            var passportNumber = encryptedPassportNumber?.ToDecrypt();
            if (string.IsNullOrEmpty(passportNumber))
                return Json(new ResultModel { Message = SiteResources.PassportNumber, ResultType = ResultType.Warning });

            var branchScreenTitleName = string.Empty;
            var branchCityName = string.Empty;

            var cacheItemBranch = await CacheHelper.GetBranchesAsync();
            branchScreenTitleName = cacheItemBranch?.Branches?.FirstOrDefault(p => p.Id == UserSession.BranchId)?.QmsScreenTitleName;
            branchCityName = cacheItemBranch?.Branches?.FirstOrDefault(p => p.Id == UserSession.BranchId)?.CityName;

            var request = new CreateTicketRequestModel
            {
                LineId = lineId,
                PassportNumber = passportNumber
            };

            var apiResponseGetDepartmentsByLine = await RestHttpClient.Create().Get<GetDepartmentsByLineResult>(
                AppSettings.Qms.BaseApiUrl + $"/api/lines/{lineId}/departments", QMSApiDefaultRequestHeaders);

            if (apiResponseGetDepartmentsByLine?.Data is null)
                return Json(ResultModel.Error(apiResponseGetDepartmentsByLine?.Message ?? $"{ResultMessage.RecordNotFound.ToDescription()}"));

            var apiResponseCreateTicket = await RestHttpClient.Create().Post<CreateTicketResult>(
                AppSettings.Qms.BaseApiUrl + QmsEndPoint.RePrintTicket, QMSApiDefaultRequestHeaders, request);

            if (apiResponseCreateTicket?.Data is null)
                return Json(new { Message = apiResponseCreateTicket?.Message ?? @SiteResources.TokenNotFound });

            var timeZoneOffset = await GetTimeZoneOffset();

            var viewModel = new PrintTicketViewModel
            {
                Token = apiResponseCreateTicket.Data.Token,
                TimeOffset = timeZoneOffset,
                AppointmentId = apiResponseCreateTicket.Data.AppointmentId,
                ApplicantCount = apiResponseCreateTicket.Data.Count,
                Departments = apiResponseGetDepartmentsByLine.Data.Select(p => new PrintTicketViewModel.Department
                {
                    IsProcessSameCounter = p.IsProcessSameCounter,
                    Order = p.Order,
                    DepartmentName = p.DepartmentName
                }).ToList(),
                LineName = apiResponseCreateTicket.Data.Line,
                WaitingCount = apiResponseCreateTicket.Data.WaitingCount,
                QmsCompanyId = UserSession.QmsCompanyId,
                BranchScreenTitleName = branchScreenTitleName,
                BranchCityName = branchCityName,
            };

            return Json(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> MissedTokenNumberPrint(int lineId, string encryptedPassportNumber)
        {
            if (lineId == 0)
                return Json(new { Message = SiteResources.LineTypeShouldBeSelected });

            var passportNumber = encryptedPassportNumber?.ToDecrypt();
            if (string.IsNullOrEmpty(passportNumber))
                return Json(new ResultModel { Message = SiteResources.PassportNumber, ResultType = ResultType.Warning });

            var branchScreenTitleName = string.Empty;
            var branchCityName = string.Empty;

            var cacheItemBranch = await CacheHelper.GetBranchesAsync();
            branchScreenTitleName = cacheItemBranch?.Branches?.FirstOrDefault(p => p.Id == UserSession.BranchId)?.QmsScreenTitleName;
            branchCityName = cacheItemBranch?.Branches?.FirstOrDefault(p => p.Id == UserSession.BranchId)?.CityName;

            var request = new CreateTicketRequestModel
            {
                LineId = lineId,
                PassportNumber = passportNumber
            };

            var apiResponseGetDepartmentsByLine = await RestHttpClient.Create().Get<GetDepartmentsByLineResult>(
                AppSettings.Qms.BaseApiUrl + $"/api/lines/{lineId}/departments", QMSApiDefaultRequestHeaders);

            if (apiResponseGetDepartmentsByLine?.Data is null)
                return Json(ResultModel.Error(apiResponseGetDepartmentsByLine?.Message ?? $"{ResultMessage.RecordNotFound.ToDescription()}"));

            var apiResponseCreateTicket = await RestHttpClient.Create().Post<CreateTicketResult>(
                AppSettings.Qms.BaseApiUrl + QmsEndPoint.MissedTokenNumberPrint, QMSApiDefaultRequestHeaders, request);

            if (apiResponseCreateTicket?.Data is null)
                return Json(new { Message = apiResponseCreateTicket?.Message ?? @SiteResources.TokenNotFound });

            var timeZoneOffset = await GetTimeZoneOffset();

            var viewModel = new PrintTicketViewModel
            {
                Token = apiResponseCreateTicket.Data.Token,
                AppointmentId = apiResponseCreateTicket.Data.AppointmentId,
                TimeOffset = timeZoneOffset,
                ApplicantCount = apiResponseCreateTicket.Data.ChildTokens.Count,
                ChildTokens = apiResponseCreateTicket.Data.ChildTokens.Select(p => new PrintTicketViewModel.ChildToken
                {
                    Department = p.Department,
                    Token = p.Token
                }).ToList(),
                Departments = apiResponseGetDepartmentsByLine.Data.Where(r => apiResponseCreateTicket.Data.LineDepartmentIds.Contains(r.Id)).Select(p => new PrintTicketViewModel.Department
                {
                    IsProcessSameCounter = p.IsProcessSameCounter,
                    Order = p.Order,
                    DepartmentName = p.DepartmentName
                }).ToList(),
                LineName = apiResponseCreateTicket.Data.Line,
                WaitingCount = apiResponseCreateTicket.Data.WaitingCount,
                QmsCompanyId = UserSession.QmsCompanyId,
                BranchScreenTitleName = branchScreenTitleName,
                BranchCityName = branchCityName,
            };

            return Json(viewModel);
        }

        #endregion

        #region Scan

        [HttpPut]
        public async Task<IActionResult> ScanPassportAndUpdateInformation(string encryptedApplicantId, string passportData)
        {
            try
            {
                var mrzEntries = JsonSerializer.Deserialize<Dictionary<string, string>>(passportData);
                if (mrzEntries is null || mrzEntries.Count == 0)
                    return Json(new ResultModel
                    {
                        Message = EnumResources.PassportCouldNotBeRead,
                        ResultType = ResultType.Danger
                    });

                var countries = await CacheHelper.GetCountriesAsync();
                MrzHelper.ParseMrzEntries(mrzEntries, countries, out var passportModel);
                if (passportModel is null)
                    return Json(new ResultModel
                    {
                        Message = EnumResources.PassportCouldNotBeRead,
                        ResultType = ResultType.Danger
                    });

                var userSession = UserSession;
                var createTicketViewModel = userSession.CreateTicketViewModel;
                var applicant = createTicketViewModel.Applicants.Find(p => p.Id == encryptedApplicantId.ToDecryptInt());
                if (applicant is null)
                    return Json(new ResultModel
                    {
                        Message = ResultMessage.RecordNotFound.ToDescription(),
                        ResultType = ResultType.Danger
                    });

                var scannedCount = createTicketViewModel.Applicants.Count(p => p.PassportNumber == passportModel.PassportNumber && p.IsScan);

                if (scannedCount > 1)
                    return Json(new ResultModel
                    {
                        Message = ResultMessage.ExistingData.ToDescription(),
                        ResultType = ResultType.Warning
                    });

                var request = new ScanPassportAndAddApplicantRequestModel
                {
                    PassportNumber = passportModel.PassportNumber,
                    NationalityId = passportModel.NationalityId
                };

                var apiResponse = await RestHttpClient.Create()
                    .Post<GetWhitelistAndBlacklistInformation>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.GetWhitelistAndBlacklistInformation,
                        QMSApiDefaultRequestHeaders, request);

                if (apiResponse?.Data is null)
                    return Json(new ResultModel
                    {
                        Message = apiResponse?.Message ?? ResultMessage.ErrorOccurred.ToDescription(),
                        ResultType = ResultType.Danger
                    });

                applicant.PassportNumber = passportModel.PassportNumber;
                applicant.PassportExpireDate = passportModel.PassportExpireDate;
                applicant.Name = passportModel.Name;
                applicant.Surname = passportModel.Surname;
                applicant.BirthDate = passportModel.BirthDate;
                applicant.GenderId = passportModel.GenderId;
                applicant.NationalityId = passportModel.NationalityId;
                applicant.HasWhiteListApplicant = apiResponse.Data.HasWhiteList;
                applicant.HasBlackListApplicant = apiResponse.Data.HasBlackList;
                applicant.IsScan = true;

                SessionExtensions.Set(HttpContext.Session, SessionKeys.UserSession, userSession);

                return Json(new ResultModel
                {
                    Message = ResultMessage.OperationIsSuccessful.ToDescription(),
                    ResultType = ResultType.Success
                });
            }
            catch (Exception ex)
            {
                return Json(new ResultModel
                {
                    Message = ex.Message,
                    ResultType = ResultType.Danger
                });
            }
        }

        [HttpPost]
        public async Task<IActionResult> ScanPassportAndAddApplicant(string encryptedAppointmentNumber,
            string passportData)
        {
            try
            {
                var appointmentNumber = Convert.ToInt32(encryptedAppointmentNumber.ToDecrypt());
                if (appointmentNumber == 0)
                    return Json(new ResultModel
                    {
                        Message = EnumResources.MissingOrInvalidData,
                        ResultType = ResultType.Danger
                    });

                var mrzEntries = JsonSerializer.Deserialize<Dictionary<string, string>>(passportData);
                if (mrzEntries is null || mrzEntries.Count == 0)
                    return Json(new ResultModel
                    {
                        Message = EnumResources.PassportCouldNotBeRead,
                        ResultType = ResultType.Danger
                    });
                
                var countries = await CacheHelper.GetCountriesAsync();
                MrzHelper.ParseMrzEntries(mrzEntries, countries, out PassportModel passportModel);
                if (passportModel is null)
                    return Json(new ResultModel
                    {
                        Message = EnumResources.PassportCouldNotBeRead,
                        ResultType = ResultType.Danger
                    });

                var userSession = UserSession;
                var createTicketViewModel = userSession.CreateTicketViewModel;

                var isAddedBefore = createTicketViewModel.Applicants.Exists(applicant => applicant.PassportNumber == passportModel.PassportNumber);

                if (isAddedBefore)
                    return Json(new ResultModel
                    {
                        Message = EnumResources.ExistingData,
                        ResultType = ResultType.Warning
                    });

                var request = new ScanPassportAndAddApplicantRequestModel
                {
                    GenderId = passportModel.GenderId,
                    Name = passportModel.Name,
                    Surname = passportModel.Surname,
                    BirthDate = passportModel.BirthDate,
                    NationalityId = passportModel.NationalityId,
                    PassportNumber = passportModel.PassportNumber,
                    PassportExpireDate = passportModel.PassportExpireDate
                };

                var apiResponse = await RestHttpClient.Create()
                    .Post<ScanPassportAndAddApplicantResult>(AppSettings.Qms.BaseApiUrl + $"/api/appointments/{appointmentNumber}/applicant",
                        QMSApiDefaultRequestHeaders, request);

                if (apiResponse?.Data is null)
                    return Json(new ResultModel
                    {
                        Message = apiResponse?.Message,
                        ResultType = ResultType.Danger
                    });

                createTicketViewModel.Applicants.Add(new CreateTicketViewModel.Applicant
                {
                    Id = apiResponse.Data.Id,
                    PassportNumber = apiResponse.Data.PassportNumber,
                    PassportExpireDate = apiResponse.Data.PassportExpireDate,
                    Name = apiResponse.Data.Name,
                    Surname = apiResponse.Data.Surname,
                    BirthDate = apiResponse.Data.BirthDate,
                    GenderId = apiResponse.Data.GenderId,
                    NationalityId = apiResponse.Data.NationalityId,
                    HasBlackListApplicant = apiResponse.Data.HasBlackListApplicant,
                    HasWhiteListApplicant = apiResponse.Data.HasWhiteListApplicant,
                    IsScan = true
                });

                SessionExtensions.Set(HttpContext.Session, SessionKeys.UserSession, userSession);

                return Json(new ResultModel
                {
                    Message = ResultMessage.OperationIsSuccessful.ToDescription(),
                    ResultType = ResultType.Success
                });
            }
            catch
            {
                return Json(new ResultModel
                {
                    Message = ResultMessage.ErrorOccurred.ToDescription(),
                    ResultType = ResultType.Danger
                });
            }
        }

        #endregion

        #region Actions

        [HttpPost]
        public async Task<IActionResult> CallNext(int lineId, int counterId)
        {
            if (!lineId.IsNumericAndGreaterThenZero() || !counterId.IsNumericAndGreaterThenZero())
                return Content(EnumResources.MissingOrInvalidData);

            var whitelistProcessAuthorization = false;

            var roleActions = await CacheHelper.GetRoleActionsAsync();

            whitelistProcessAuthorization = roleActions.RoleActionSites.Where(r => UserSession.RoleIds.Contains(r.Role.Id))
                .Any(p => p.RoleActions.Any(q => q.Action.ActionTranslations.Any(r => r.Name == SiteResources.WhitelistProcessAuthorization) && q.Action.IsActive));

            var request = new CallNextRequestModel
            {
                LineId = lineId,
                BranchId = UserSession.BranchId.GetValueOrDefault(),
                CounterId = counterId,
                IsAuthoritativeForWhiteList = whitelistProcessAuthorization,
                UserId = UserSession.UserId
            };

            var apiResponse = await RestHttpClient.Create().Post<CallNextResult>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.CallNext,
                QMSApiDefaultRequestHeaders, request);

            if (apiResponse?.Data is null)
                return Json(new { Message = apiResponse?.Message ?? $"{ResultMessage.RecordNotFound.ToDescription()}", ResultType = ResultType.Danger });

            var timeZoneOffset = await GetTimeZoneOffset();

            var viewModel = new GetCallNextViewModel
            {
                AppointmentId = apiResponse.Data.AppointmentId,
                QmsFilterAppointmentNumber = string.Empty,
                QmsBranchApplicationCountryId = apiResponse.Data.QmsBranchApplicationCountryId,
                ActiveTokenId = apiResponse.Data.ActiveTokenId,
                Screen = apiResponse.Data.TokenNumber,
                OwnerToken = apiResponse.Data.OwnerToken,
                Priority = apiResponse.Data.Priority,
                LineId = lineId,
                IsLineDepartmentCreateApplication = apiResponse.Data.IsLineDepartmentCreateApplication,
                LineDepartmentId = apiResponse.Data.LineDepartmentId,
                IsSameProcessCounter = apiResponse.Data.IsSameProcessCounter,
                SearchByPassportNumber = apiResponse.Data.SearchByPassportNumber,
                Interval = apiResponse.Data.Interval.GetValueOrDefault(),
                Applicants = apiResponse.Data.Applicants.Select(p => new GetCallNextViewModel.Applicant
                {
                    Id = p.Id,
                    GeneratedTokenId = p.GeneratedTokenId,
                    PassportNumber = p.PassportNumber,
                    ApplicationTime = p.ApplicationTime,
                    NameSurname = p.NameSurname,
                    IsIndividual = p.IsIndividual,
                    IsFamily = p.IsFamily,
                    IsGroup = p.IsGroup,
                    ApplicantState = (byte)TokenState.InProgress,
                    IsAddedToAnotherToken = p.IsAddedToAnotherToken,
                    ApplicationCompletedCounterId = p.ApplicationCompletedCounterId,
                    ApplicationCompletedLineDepartmentId = p.ApplicationCompletedLineDepartmentId,
                    HealthInsurance = !string.IsNullOrEmpty(p.InsuranceTypeIds) && p.InsuranceTypeIds.Split(',').Select(int.Parse).Any(i => i == (int)PreApplicationInsuranceType.HealthInsurance),
                    TrafficInsurance = !string.IsNullOrEmpty(p.InsuranceTypeIds) && p.InsuranceTypeIds.Split(',').Select(int.Parse).Any(i => i == (int)PreApplicationInsuranceType.TrafficInsurance),
                    InsuranceTypeIds = string.IsNullOrEmpty(p.InsuranceTypeIds) ? string.Empty : string.Join(",", from i in p.InsuranceTypeIds.Split(',').Select(int.Parse).ToList() select EnumHelper.GetEnumDescription(typeof(PreApplicationInsuranceType), i.ToString())),
                    IsBlackListApplicant = p.IsBlackListApplicant,
                    IsWhiteListApplicant = p.IsWhiteListApplicant,
                    BlackListInformationViewModel = p.IsBlackListApplicant && p.BlackListModel != null ? new BlackListInformationViewModel
                    {
                        BlackListNote = p.BlackListModel.BlackListNote,
                        BlackListNoteCreatedBy = p.BlackListModel.BlackListNoteCreatedBy,
                        BlackListNoteUpdatedBy = p.BlackListModel?.BlackListNoteUpdatedBy,
                    } : null,
                    WhiteListInformationViewModel = p.IsWhiteListApplicant && p.WhiteListInformation != null ? new RedisWhiteListInformationViewModel
                    {
                        Id = p.WhiteListInformation.Id,
                        DocumentExemption = p.WhiteListInformation.DocumentExemption == null ? string.Empty : EnumHelper.GetEnumDescription(typeof(QMSWhiteListDocumentExemption), p.WhiteListInformation.DocumentExemption.ToString()),
                        BiometricData = p.WhiteListInformation.BiometricData == null ? string.Empty : EnumHelper.GetEnumDescription(typeof(QMSWhiteListBiometricData), p.WhiteListInformation.BiometricData.ToString()),
                        MissionNotes = p.WhiteListInformation.MissionNotes,
                        RelevantInstitutionPerson = p.WhiteListInformation.RelevantInstitutionPerson
                    } : null,
                    HasNotCompletedReason = p.HasNotCompletedReason,
                    NotCompletedReasonViewModel = p.HasNotCompletedReason && p.NotCompletedReasons is not null ? p.NotCompletedReasons.Select(s => new RedisNotCompletedReasonViewModel
                    {
                        ActionBy = s.ActionBy,
                        ReasonId = s.ReasonId,
                        ActionAt = s.ActionAt.AddHours(3 + timeZoneOffset).ToString("dd/MM/yyyy HH:mm"),
                        Reason = s.OtherReason ? $"{SiteResources.Other}: {s.Reason}" : EnumHelper.GetEnumDescription(typeof(NotCompletedReason), s.ReasonId.ToString())
                    }).ToList() : null
                }).ToArray(),
                Notes = apiResponse.Data?.Notes != null
                    ? apiResponse.Data?.Notes.Select(p => new GeneratedTokenNote
                    {
                        Note = p.Note,
                        CreatedAt = p.CreatedAt.AddHours(3 + timeZoneOffset),
                        CreatedBy = p.CreatedBy
                    }).ToList()
                    : new List<GeneratedTokenNote>()
            };

            return PartialView("_CallNextPage", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> ActionCallNextFromHoldOnPartial(int lineId, int counterId)
        {
            if (!lineId.IsNumericAndGreaterThenZero() || !counterId.IsNumericAndGreaterThenZero())
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData });

            var request = new CallNextRequestModel
            {
                LineId = lineId,
                BranchId = UserSession.BranchId.GetValueOrDefault(),
                CounterId = counterId,
                UserId = UserSession.UserId
            };

            var apiResponse = await RestHttpClient.Create()
                .Post<CallNextResult>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.GetUserWaitingList,
                    QMSApiDefaultRequestHeaders, request);

            if (apiResponse?.Data is null)
                return Json(new ResultModel { Message = apiResponse?.Message ?? $"{ResultMessage.RecordNotFound.ToDescription()}", ResultType = ResultType.Danger });

            if (apiResponse.Data.UserWaitingTokens.Count == 0)
                return Json(new ResultModel { Message = $"{ResultMessage.RecordNotFound.ToDescription()}", ResultType = ResultType.Info });

            var viewModel = new GetCallNextViewModel { UserWaitingTokens = apiResponse.Data.UserWaitingTokens };

            return PartialView("_CallNextFromHoldOnPartial", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> CallNextFromHoldOn(string encryptedTokenId, int lineId, int counterId, string token)
        {
            if (!lineId.IsNumericAndGreaterThenZero() || !counterId.IsNumericAndGreaterThenZero() || encryptedTokenId == null || token == null)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData });

            var request = new CallNextRequestModel
            {
                LineId = lineId,
                CounterId = counterId,
                TokenId = encryptedTokenId.ToDecryptGuid(),
                Token = token,
                BranchId = UserSession.BranchId.GetValueOrDefault(),
                UserId = UserSession.UserId
            };

            var apiResponse = await RestHttpClient.Create()
                .Post<CallNextResult>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.CallNextFromHoldOn,
                    QMSApiDefaultRequestHeaders, request);

            if (apiResponse?.Data is null)
                return Json(new ResultModel { Message = apiResponse?.Message ?? $"{ResultMessage.RecordNotFound.ToDescription()}", ResultType = ResultType.Info });

            var timeZoneOffset = await GetTimeZoneOffset();

            var viewModel = new GetCallNextViewModel
            {
                AppointmentId = Convert.ToInt32(apiResponse.Data.AppointmentId),
                ActiveTokenId = apiResponse.Data.ActiveTokenId,
                QmsFilterAppointmentNumber = string.Empty,
                QmsBranchApplicationCountryId = apiResponse.Data.QmsBranchApplicationCountryId,
                Screen = apiResponse.Data.TokenNumber,
                OwnerToken = apiResponse.Data.OwnerToken,
                IsLineDepartmentCreateApplication = apiResponse.Data.IsLineDepartmentCreateApplication,
                LineDepartmentId = apiResponse.Data.LineDepartmentId,
                Interval = apiResponse.Data.Interval.GetValueOrDefault(),
                LineId = lineId,
                IsSameProcessCounter = apiResponse.Data.IsSameProcessCounter,
                SearchByPassportNumber = apiResponse.Data.SearchByPassportNumber,
                Applicants = apiResponse.Data.Applicants.Select(p => new GetCallNextViewModel.Applicant
                {
                    Id = p.Id,
                    GeneratedTokenId = p.GeneratedTokenId,
                    PassportNumber = p.PassportNumber,
                    ApplicationTime = p.ApplicationTime,
                    NameSurname = p.NameSurname,
                    IsIndividual = p.IsIndividual,
                    IsFamily = p.IsFamily,
                    IsGroup = p.IsGroup,
                    ApplicantState = (byte)TokenState.InProgress,
                    IsAddedToAnotherToken = p.IsAddedToAnotherToken,
                    IsApplicationCompleted = p.IsApplicationCompleted,
                    ApplicationCompletedCounterId = p.ApplicationCompletedCounterId,
                    ApplicationCompletedLineDepartmentId = p.ApplicationCompletedLineDepartmentId,
                    HealthInsurance = !string.IsNullOrEmpty(p.InsuranceTypeIds) && p.InsuranceTypeIds.Split(',').Select(int.Parse).Any(i => i == (int)PreApplicationInsuranceType.HealthInsurance),
                    TrafficInsurance = !string.IsNullOrEmpty(p.InsuranceTypeIds) && p.InsuranceTypeIds.Split(',').Select(int.Parse).Any(i => i == (int)PreApplicationInsuranceType.TrafficInsurance),
                    InsuranceTypeIds = string.IsNullOrEmpty(p.InsuranceTypeIds) ? string.Empty : string.Join(",", from i in p.InsuranceTypeIds.Split(',').Select(int.Parse).ToList() select EnumHelper.GetEnumDescription(typeof(PreApplicationInsuranceType), i.ToString())),
                    IsBlackListApplicant = p.IsBlackListApplicant,
                    IsWhiteListApplicant = p.IsWhiteListApplicant,
                    BlackListInformationViewModel = p.IsBlackListApplicant && p.BlackListModel != null ? new BlackListInformationViewModel
                    {
                        BlackListNote = p.BlackListModel.BlackListNote,
                        BlackListNoteCreatedBy = p.BlackListModel.BlackListNoteCreatedBy,
                        BlackListNoteUpdatedBy = p.BlackListModel?.BlackListNoteUpdatedBy,
                    } : null,
                    WhiteListInformationViewModel = p.IsWhiteListApplicant && p.WhiteListInformation != null ? new RedisWhiteListInformationViewModel()
                    {
                        Id = p.WhiteListInformation.Id,
                        DocumentExemption = p.WhiteListInformation.DocumentExemption == null ? "" : EnumHelper.GetEnumDescription(typeof(QMSWhiteListDocumentExemption), p.WhiteListInformation.DocumentExemption.ToString()),
                        BiometricData = p.WhiteListInformation.BiometricData == null ? "" : EnumHelper.GetEnumDescription(typeof(QMSWhiteListBiometricData), p.WhiteListInformation.BiometricData.ToString()),
                        MissionNotes = p.WhiteListInformation.MissionNotes,
                        RelevantInstitutionPerson = p.WhiteListInformation.RelevantInstitutionPerson
                    } : null,
                    HasNotCompletedReason = p.HasNotCompletedReason,
                    NotCompletedReasonViewModel = p.HasNotCompletedReason && p.NotCompletedReasons is not null ? p.NotCompletedReasons.Select(s => new RedisNotCompletedReasonViewModel
                    {
                        ActionBy = s.ActionBy,
                        ReasonId = s.ReasonId,
                        ActionAt = s.ActionAt.AddHours(3 + timeZoneOffset).ToString("dd/MM/yyyy HH:mm"),
                        Reason = s.OtherReason ? $"{SiteResources.Other}: {s.Reason}" : EnumHelper.GetEnumDescription(typeof(NotCompletedReason), s.ReasonId.ToString())
                    }).ToList() : null
                }).ToArray(),
                Notes = apiResponse.Data?.Notes != null
                    ? apiResponse.Data?.Notes.Select(p => new GeneratedTokenNote
                    {
                        Note = p.Note,
                        CreatedAt = p.CreatedAt.AddHours(3 + timeZoneOffset),
                        CreatedBy = p.CreatedBy
                    }).ToList()
                    : new List<GeneratedTokenNote>()
            };

            return PartialView("_CallNextPage", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddGeneratedTokenNote(string qmsNote, string generatedTokenNumber, int appointmentId, int lineId)
        {
            if (qmsNote.IsNullOrWhitespace())
                return Json(new { Message = SiteResources.EnterValidQmsNote });

            var request = new AddGeneratedTokenNoteRequestModel
            {
                QmsNote = qmsNote,
                AppointmentId = appointmentId,
                LineId = lineId,
            };

            var apiResponse = await RestHttpClient.Create().Post<AddGeneratedTokenNoteResult>(
                AppSettings.Qms.BaseApiUrl + $"/api/tickets/{generatedTokenNumber}/notes", QMSApiDefaultRequestHeaders,
                request);

            if (apiResponse.Status != QMS_API_RESPONSE_SUCCESS)
                return Json(new { Message = apiResponse.Message });

            return Json(new ResultModel
            {
                Data = apiResponse.Data,
                ResultType = ResultType.Success
            });
        }

        [HttpPost]
        public async Task<IActionResult> GetGeneratedTokenNotes(GetGeneratedTokenNotesViewModel viewModel)
        {
            if (viewModel is null) return Content(EnumResources.MissingOrInvalidData);

            var request = new GetGeneratedTokenNotesRequestModel
            {
                LineId = viewModel.LineId,
                OwnerToken = viewModel.OwnerToken
            };

            var apiResponse = await RestHttpClient.Create()
                .Post<GeneratedTokenNotesResult>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.GetGeneratedTokenNotes,
                    QMSApiDefaultRequestHeaders, request);

            if (apiResponse?.Data is null)
                return PartialView("_QmsMainMessage", new GetCallNextViewModel
                {
                    Notes = new List<GeneratedTokenNote>(),
                    QmsFormNote = string.Empty
                });

            var timeZoneOffset = await GetTimeZoneOffset();

            var tokenNotes = apiResponse.Data.Select(p => new GeneratedTokenNote
            {
                Note = p.Note,
                CreatedAt = p.CreatedAt.AddHours(3 + timeZoneOffset),
                CreatedBy = p.CreatedBy
            }).ToList();

            ViewData["lineId"] = viewModel.LineId;

            return PartialView("_QmsMainMessage", new GetCallNextViewModel
            {
                Notes = tokenNotes
            });
        }


        [HttpPost]
        public Task<IActionResult> ActionNotFoundPartial(bool isIndividualAction, int applicantId, List<int?> applicantIds, bool isSameProcessCounter)
        {
            var resultViewModel = new ActionNotFoundPartialViewModel
            {
                ApplicantId = applicantId,
                IsIndividualAction = isIndividualAction,
                ApplicantIds = applicantIds,
                IsSameProcessCounter = isSameProcessCounter
            };

            return Task.FromResult<IActionResult>(PartialView("_QmsActionNotFound", resultViewModel));
        }

        [HttpPost]
        public async Task<IActionResult> ActionNotFound(ActionNotFoundViewModel viewModel)
        {
            if (viewModel == null) return Json(new { ErrorMessage = EnumResources.MissingOrInvalidData });

            if (!viewModel.NotCompletedReasonId.IsNumericAndGreaterThenZero()) 
                return Json(new { ErrorMessage = EnumResources.ActionCantTakenWithoutChoosingReason });

            if (viewModel.NotCompletedReasonId == (byte)NotCompletedReason.Other && viewModel.ReasonDescription == null)
                return Json(new { ErrorMessage = EnumResources.EnterNotCompletedReason });

            if (viewModel.IsIndividualAction)
            {
                var requestIndividual = new ActionSelectedTokenRequestModel
                {
                    LineId = viewModel.LineId,
                    BranchId = UserSession.BranchId.GetValueOrDefault(),
                    AppointmentId = viewModel.AppointmentId,
                    CounterId = viewModel.CounterId,
                    TokenNumber = viewModel.TokenNumber,
                    State = (byte)TokenState.NotCompleted,
                    UserId = UserSession.UserId,
                    ActiveTokenId = viewModel.ActiveTokenId,
                    ApplicantId = viewModel.ApplicantId,
                    NotCompletedReasonId = viewModel.NotCompletedReasonId,
                    Reason = viewModel.ReasonDescription
                };

                var apiResponseIndividual = await RestHttpClient.Create()
                    .Post<ActionSelectedTokenResult>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.ActionSelectedToken, QMSApiDefaultRequestHeaders, requestIndividual);

                return apiResponseIndividual.Status != QMS_API_RESPONSE_SUCCESS
                    ? Json(new { ErrorMessage = apiResponseIndividual.Message })
                    : Json(new
                    {
                        Data = apiResponseIndividual.Data,
                        Message = $"{ResultMessage.OperationIsSuccessful.ToDescription()}",
                        ResultType = ResultType.Success,
                        IsIndividualAction = true
                    });
            }
            else
            {
                var request = new ActionNotFoundRequestModel
                {
                    LineId = viewModel.LineId,
                    BranchId = UserSession.BranchId.GetValueOrDefault(),
                    CounterId = viewModel.CounterId,
                    TokenNumber = viewModel.TokenNumber,
                    AppointmentId = viewModel.AppointmentId,
                    UserId = UserSession.UserId,
                    ActiveTokenId = viewModel.ActiveTokenId,
                    ApplicantIds = viewModel.ApplicantIds,
                    IsSameProcessCounter = viewModel.IsSameProcessCounter,
                    NotCompletedReasonId = viewModel.NotCompletedReasonId,
                    Reason = viewModel.ReasonDescription
                };

                var apiResponse = await RestHttpClient.Create()
                    .Post<ActionNotFoundResult>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.ActionNotFound, QMSApiDefaultRequestHeaders, request);

                return apiResponse.Status != QMS_API_RESPONSE_SUCCESS
                    ? Json(new { ErrorMessage = apiResponse.Message })
                    : Json(new
                    {
                        Data = apiResponse.Data,
                        Message = $"{ResultMessage.OperationIsSuccessful.ToDescription()}",
                        ResultType = ResultType.Success,
                        IsIndividualAction = false
                    });
            }
        }

        [HttpPost]
        public Task<IActionResult> ActionAssignPartial(bool isIndividualAction, int applicantId, List<int?> applicantIds, bool isSameProcessCounter)
        {
            var resultViewModel = new ActionAssignPartialViewModel()
            {
                ApplicantId = applicantId,
                IsIndividualAction = isIndividualAction,
                ApplicantIds = applicantIds,
                IsSameProcessCounter = isSameProcessCounter
            };

            return Task.FromResult<IActionResult>(PartialView("_QmsActionAssign", resultViewModel));
        }

        [HttpPost]
        public async Task<IActionResult> ActionAssign(ActionAssignViewModel viewModel)
        {
            if (viewModel is null) return Content(EnumResources.MissingOrInvalidData);

            if (viewModel.IsIndividualAction)
            {
                var requestIndividual = new ActionSelectedTokenRequestModel
                {
                    LineId = viewModel.LineId,
                    BranchId = UserSession.BranchId.GetValueOrDefault(),
                    AppointmentId = viewModel.AppointmentId,
                    CounterId = viewModel.CounterId,
                    TokenNumber = viewModel.TokenNumber,
                    Assigner = viewModel.Assigner,
                    Assignee = viewModel.Assignee,
                    State = (byte)TokenState.Assign,
                    UserId = UserSession.UserId,
                    ActiveTokenId = viewModel.ActiveTokenId,
                    ApplicantId = viewModel.ApplicantId
                };

                var apiResponseIndividual = await RestHttpClient.Create()
                    .Post<ActionSelectedTokenResult>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.ActionSelectedToken, QMSApiDefaultRequestHeaders, requestIndividual);

                return apiResponseIndividual.Status != QMS_API_RESPONSE_SUCCESS
                    ? Json(new { ErrorMessage = apiResponseIndividual.Message })
                    : Json(new
                    {
                        Data = apiResponseIndividual.Data,
                        Message = $"{ResultMessage.OperationIsSuccessful.ToDescription()}",
                        ResultType = ResultType.Success,
                        IsIndividualAction = true
                    });
            }
            else
            {
                var request = new ActionAssignRequestModel
                {
                    LineId = viewModel.LineId,
                    BranchId = UserSession.BranchId.GetValueOrDefault(),
                    CounterId = viewModel.CounterId,
                    TokenNumber = viewModel.TokenNumber,
                    AppointmentId = viewModel.AppointmentId,
                    UserId = UserSession.UserId,
                    Assignee = viewModel.Assignee,
                    Assigner = viewModel.Assigner,
                    ActiveTokenId = viewModel.ActiveTokenId,
                    ApplicantIds = viewModel.ApplicantIds,
                    IsSameProcessCounter = viewModel.IsSameProcessCounter
                };

                var apiResponse = await RestHttpClient.Create()
                    .Post<ActionAssignResult>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.ActionAssign, QMSApiDefaultRequestHeaders, request);

                return apiResponse.Status != QMS_API_RESPONSE_SUCCESS
                    ? Json(new { ErrorMessage = apiResponse.Message })
                    : Json(new
                    {
                        Data = apiResponse.Data,
                        Message = $"{ResultMessage.OperationIsSuccessful.ToDescription()}",
                        ResultType = ResultType.Success,
                        IsIndividualAction = false
                    });
            }
        }

        [HttpPost]
        public async Task<IActionResult> ActionRecall(ActionRecallViewModel viewModel)
        {
            if (viewModel == null) return Content(EnumResources.MissingOrInvalidData);

            var request = new ActionRecallRequestModel
            {
                LineId = viewModel.LineId,
                BranchId = UserSession.BranchId.GetValueOrDefault(),
                AppointmentId = viewModel.AppointmentId,
                CounterId = viewModel.CounterId,
                TokenNumber = viewModel.TokenNumber,
                UserId = UserSession.UserId,
                ActiveTokenId = viewModel.ActiveTokenId,
                ApplicantIds = viewModel.ApplicantIds
            };

            var apiResponse = await RestHttpClient.Create()
                .Post<ActionRecallResult>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.ActionRecall, QMSApiDefaultRequestHeaders, request);

            return apiResponse.Status != QMS_API_RESPONSE_SUCCESS
                ? Json(new { ErrorMessage = apiResponse.Message })
                : Json(new ResultModel
                {
                    Data = apiResponse.Data,
                    Message = $"{ResultMessage.OperationIsSuccessful.ToDescription()}",
                    ResultType = ResultType.Success
                });
        }

        [HttpPost]
        public async Task<IActionResult> ActionCancelledByApplicant(ActionCancelledByApplicantViewModel viewModel)
        {
            if (viewModel == null) return Content(EnumResources.MissingOrInvalidData);

            var request = new ActionCancelledByApplicantRequestModel
            {
                LineId = viewModel.LineId,
                BranchId = UserSession.BranchId.GetValueOrDefault(),
                CounterId = viewModel.CounterId,
                AppointmentId = viewModel.AppointmentId,
                TokenNumber = viewModel.TokenNumber,
                UserId = UserSession.UserId,
                ActiveTokenId = viewModel.ActiveTokenId,
                ApplicantIds = viewModel.ApplicantIds,
                IsSameProcessCounter = viewModel.IsSameProcessCounter
            };

            var apiResponse = await RestHttpClient.Create()
                .Post<ActionCancelledByApplicantResult>(
                    AppSettings.Qms.BaseApiUrl + QmsEndPoint.ActionCancelledByApplicant, QMSApiDefaultRequestHeaders, request);

            return apiResponse.Status != QMS_API_RESPONSE_SUCCESS
                ? Json(new { ErrorMessage = apiResponse.Message })
                : Json(new ResultModel
                {
                    Data = apiResponse.Data,
                    Message = $"{ResultMessage.OperationIsSuccessful.ToDescription()}",
                    ResultType = ResultType.Success
                });
        }

        [HttpPost]
        public async Task<IActionResult> ActionPostponePartial(int appointmentId, int qmsBranchApplicationCountryId,
            int applicantCount, bool isIndividualAction, int? applicantId, List<int?> applicantIds)
        {
            if (appointmentId == 0 || !qmsBranchApplicationCountryId.IsNumericAndGreaterThenZero())
            {
                return Content(EnumResources.MissingOrInvalidData);
            }

            var apiResponseAgency = await PortalHttpClientHelper
               .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedAgencyApiResponse>>>
               (
               new PaginatedAgencyApiRequest
               {
                   Pagination = new PaginationApiRequest
                   {
                       Page = 1,
                       PageSize = 100,
                       OrderBy = string.Empty,
                       SortDirection = ListSortDirection.Ascending
                   }

               }, ApiMethodName.Agency.GetPaginatedAgencies, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
               .ConfigureAwait(false);

            var apiResponseSlotType = await PortalHttpClientHelper
               .GetAsync<ApiResponse<GetSlotTypeByBranchApplicationCountryApiResponse>>
               (ApiMethodName.Parameter.GetSlotTypeByBranchApplicationCountry + qmsBranchApplicationCountryId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
               .ConfigureAwait(false);

            var viewModel = new ActionPostponePartialViewModel
            {
                EncryptedPreApplicationId = appointmentId.ToEncrypt(),
                PostponeAppointmentId = appointmentId,
                QmsBranchApplicationCountryId = qmsBranchApplicationCountryId,
                ApplicantCount = applicantCount,
                PostponeDate = DateTime.Today.AddDays(1),
                ApplicantId = applicantId,
                IsIndividualAction = isIndividualAction,
                ApplicantIds = applicantIds,
                SlotTypeSelectList = apiResponseSlotType.Data.SlotTypes.Select(p => new SelectListItem()
                {
                    Text = p.Name.ToTitleCase(),
                    Value = p.Value.ToString()
                }).ToList(),
                AgencySelectList = apiResponseAgency.Data.Items?.SelectMany(p => p.Agencies).Where(q => q.ActivationStatusId == ActivationStatusType.Active.ToInt()).Select(p => new SelectListItem()
                {
                    Text = p.Name,
                    Value = p.Id.ToString()
                }).ToList()
            };

            return PartialView("_QmsActionPostpone", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> ActionPostpone(ActionPostponeViewModel viewModel)
        {
            if (viewModel == null) return Content(EnumResources.MissingOrInvalidData);

            if (viewModel.IsIndividualAction)
            {
                var requestIndividual = new ActionSelectedTokenRequestModel()
                {
                    LineId = viewModel.LineId,
                    BranchId = UserSession.BranchId.GetValueOrDefault(),
                    AppointmentId = viewModel.AppointmentId,
                    CounterId = viewModel.CounterId,
                    TokenNumber = viewModel.TokenNumber,
                    PostponeAppointmentId = viewModel.PostponeAppointmentId,
                    PostponeSlotId = viewModel.PostponeSlotId,
                    State = (byte)TokenState.Postpone,
                    UserId = UserSession.UserId,
                    ApplicantId = viewModel.ApplicantId,
                    ActiveTokenId = viewModel.ActiveTokenId
                };

                var apiResponseIndividual = await RestHttpClient.Create()
                    .Post<ActionSelectedTokenResult>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.ActionSelectedToken, QMSApiDefaultRequestHeaders, requestIndividual);

                if (apiResponseIndividual.Status != QMS_API_RESPONSE_SUCCESS)
                {
                    return Json(new { ErrorMessage = apiResponseIndividual.Message });
                }
                else
                {
                    var notificationRequest = new SendIndividualPostponeNotificationRequest
                    {
                        BranchId = UserSession.BranchId.GetValueOrDefault(),
                        LanguageId = LanguageId,
                        AppointmentDate = apiResponseIndividual.Data.NotificationInformation.AppointmentDate,
                        Applicants = apiResponseIndividual.Data.NotificationInformation.Applicants,
                        BranchName = apiResponseIndividual.Data.NotificationInformation.BranchName,
                        BranchCountryId = apiResponseIndividual.Data.NotificationInformation.BranchCountryId,
                        AppointmentNumber = apiResponseIndividual.Data.NotificationInformation.AppointmentNumber
                    };

                    var apiResponseNotification = await PortalHttpClientHelper
                        .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                            (notificationRequest, ApiMethodName.QueueMatic.SendIndividualPostponeNotification, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                        .ConfigureAwait(false);

                    return Json(new
                    {
                        Data = apiResponseIndividual.Data,
                        Message = $"{ResultMessage.OperationIsSuccessful.ToDescription()}",
                        ResultType = ResultType.Success,
                        IsIndividualAction = true
                    });
                }
            }
            else
            {
                var request = new ActionPostponeRequestModel
                {
                    LineId = viewModel.LineId,
                    BranchId = UserSession.BranchId.GetValueOrDefault(),
                    AppointmentId = viewModel.AppointmentId,
                    CounterId = viewModel.CounterId,
                    TokenNumber = viewModel.TokenNumber,
                    PostponeAppointmentId = viewModel.PostponeAppointmentId,
                    PostponeSlotId = viewModel.PostponeSlotId,
                    UserId = UserSession.UserId,
                    ActiveTokenId = viewModel.ActiveTokenId,
                    ApplicantIds = viewModel.ApplicantIds
                };

                var apiResponse = await RestHttpClient.Create()
                    .Post<ActionPostponeResult>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.ActionPostpone, QMSApiDefaultRequestHeaders,
                        request);

                if (apiResponse.Status != QMS_API_RESPONSE_SUCCESS)
                {
                    return Json(new { ErrorMessage = apiResponse.Message });
                }
                else
                {
                    var notificationRequest = new SendBulkPostponeNotificationRequest
                    {
                        BranchId = UserSession.BranchId.GetValueOrDefault(),
                        LanguageId = LanguageId,
                        AppointmentDate = apiResponse.Data.NotificationInformation.AppointmentDate,
                        Applicants = apiResponse.Data.NotificationInformation.Applicants,
                        BranchName = apiResponse.Data.NotificationInformation.BranchName,
                        BranchCountryId = apiResponse.Data.NotificationInformation.BranchCountryId,
                        AppointmentNumber = apiResponse.Data.NotificationInformation.AppointmentNumber
                    };

                    var apiResponseNotification = await PortalHttpClientHelper
                        .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                            (notificationRequest, ApiMethodName.QueueMatic.SendBulkPostponeNotification, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                        .ConfigureAwait(false);

                    return Json(new
                    {
                        Data = apiResponse.Data,
                        Message = $"{ResultMessage.OperationIsSuccessful.ToDescription()}",
                        ResultType = ResultType.Success,
                        IsIndividualAction = false
                    });
                }
            }
        }

        [HttpPost]
        public async Task<IActionResult> ActionHoldOn(ActionHoldOnViewModel viewModel)
        {
            if (viewModel == null) return Content(EnumResources.MissingOrInvalidData);

            var request = new ActionHoldOnRequestModel
            {
                LineId = viewModel.LineId,
                BranchId = UserSession.BranchId.GetValueOrDefault(),
                AppointmentId = viewModel.AppointmentId,
                CounterId = viewModel.CounterId,
                TokenNumber = viewModel.TokenNumber,
                UserId = UserSession.UserId,
                ActiveTokenId = viewModel.ActiveTokenId,
                ApplicantIds = viewModel.ApplicantIds,
                IsSameProcessCounter = viewModel.IsSameProcessCounter
            };

            var apiResponse = await RestHttpClient.Create()
                .Post<ActionHoldOnResult>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.ActionHoldOn, QMSApiDefaultRequestHeaders, request);

            return apiResponse.Status != QMS_API_RESPONSE_SUCCESS
                ? Json(new { ErrorMessage = apiResponse.Message })
                : Json(new ResultModel
                {
                    Data = apiResponse.Data,
                    Message = $"{ResultMessage.OperationIsSuccessful.ToDescription()}",
                    ResultType = ResultType.Success
                });
        }

        [HttpPost]
        public async Task<IActionResult> ActionCompleted(ActionCompletedViewModel viewModel)
        {
            if (viewModel == null) return Content(EnumResources.MissingOrInvalidData);

            var request = new ActionCompletedRequestModel
            {
                LineId = viewModel.LineId,
                BranchId = UserSession.BranchId.GetValueOrDefault(),
                AppointmentId = viewModel.AppointmentId,
                CounterId = viewModel.CounterId,
                TokenNumber = viewModel.TokenNumber,
                UserId = UserSession.UserId,
                ActiveTokenId = viewModel.ActiveTokenId,
                ApplicantIds = viewModel.ApplicantIds
            };

            var apiResponse = await RestHttpClient.Create()
                .Post<ActionCompletedResult>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.ActionCompleted, QMSApiDefaultRequestHeaders,
                    request);

            return apiResponse.Status != QMS_API_RESPONSE_SUCCESS
                ? Json(new { ErrorMessage = apiResponse.Message })
                : Json(new ResultModel
                {
                    Data = apiResponse.Data,
                    Message = $"{ResultMessage.OperationIsSuccessful.ToDescription()}",
                    ResultType = ResultType.Success
                });
        }

        #endregion

        #region IndividualActions

        [HttpPost]
        public async Task<IActionResult> GetPreApplicationApplicantsByAppointmentNumber(string appointmentNumber, string ownerToken, int lineId, int counterId)
        {
            if (appointmentNumber.IsNullOrWhitespace())
                return Json(new { Message = SiteResources.WhiteSpaceAppointmentNumber });

            if (!int.TryParse(appointmentNumber, out var value))
                return Json(new { Message = SiteResources.EnterNumericAppointmentNumber });

            var request = new GetPreApplicationApplicantsByAppointmentNumberRequest()
            {
                AppointmentNumber = value,
                OwnerToken = ownerToken,
                LineId = lineId,
                BranchId = (int)UserSession.BranchId,
                CounterId = counterId
            };

            var apiResponse = await RestHttpClient.Create()
                .Post<GetAppointmentApplicantsByAppointmentNumberResult>(
                    AppSettings.Qms.BaseApiUrl + QmsEndPoint.GetAppointmentApplicantsByAppointmentNumber, QMSApiDefaultRequestHeaders, request);

            if (apiResponse.Data == null)
                return Json(new { Message = apiResponse.Message });

            var applicants = apiResponse.Data.Select(p =>
                new GetPreApplicationApplicantsByAppointmentNumberViewModel.AppointmentApplicant
                {
                    Name = p.Name,
                    Surname = p.Surname,
                    PhoneNumber = p.PhoneNumber,
                    BranchId = p.BranchId,
                    ApplicantNumber = p.ApplicantNumber,
                    Email = p.Email,
                    AppointmentTime = p.AppointmentTime ?? SiteResources.WalkIn,
                    BranchName = p.BranchName,
                    ApplicantType =
                        new GetPreApplicationApplicantsByAppointmentNumberViewModel.AppointmentApplicant.LookupValue
                        {
                            Id = p.ApplicantType.Id,
                            DisplayValue = p.ApplicantType.DisplayValue
                        }
                }).ToList();

            var viewModel = new GetPreApplicationApplicantsByAppointmentNumberViewModel()
            {
                AppointmentApplicants = applicants
            };

            return PartialView("_QmsSearchApplicant", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddApplicantsFromAnotherPreApplication(string appointmentIds, int encryptedAppointmentNumber, string ownerToken, int lineId, int counterId, Guid activeTokenId)
        {
            if (appointmentIds == null || appointmentIds.Length == 0 || !encryptedAppointmentNumber.IsNumericAndGreaterThenZero())
                return Content(EnumResources.MissingOrInvalidData);

            var idCollection = appointmentIds.Split(",");

            var request = new AddApplicantsFromAnotherPreApplicationRequestModel
            {
                NewApplicantIds = idCollection.Where(r => r != "").ToList(),
                UserId = UserSession.UserId,
                OwnerToken = ownerToken,
                BranchId = (int)UserSession.BranchId,
                LineId = lineId,
                CounterId = counterId,
                ActiveTokenId = activeTokenId
            };

            var apiResponse = await RestHttpClient.Create()
                .Post<AddApplicantsFromAnotherPreApplicationResult>(
                    AppSettings.Qms.BaseApiUrl + $"/api/appointments/{encryptedAppointmentNumber}/applicants", QMSApiDefaultRequestHeaders,
                    request);

            if (apiResponse.Data == null)
                return Json(new ResultModel
                {
                    Message = apiResponse.ValidationMessages.Any() ? apiResponse.ValidationMessages.First() : apiResponse.Message,
                    ResultType = ResultType.Warning
                });

            return Json(new ResultModel
            {
                Data = apiResponse.Data,
                Message = ResultMessage.OperationIsSuccessful.ToDescription(),
                ResultType = ResultType.Success
            });
        }

        [HttpPost]
        public async Task<IActionResult> RemoveAddedApplicant(int applicantId, int appointmentId, string ownerToken, int lineId, Guid activeTokenId)
        {
            if (!applicantId.IsNumericAndGreaterThenZero() || !appointmentId.IsNumericAndGreaterThenZero())
                return Content(EnumResources.MissingOrInvalidData);

            var request = new RemoveAddedApplicantRequestModel
            {
                ApplicantId = applicantId,
                AppointmentId = appointmentId,
                UserId = UserSession.UserId,
                OwnerToken = ownerToken,
                LineId = lineId,
                BranchId = (int)UserSession.BranchId,
                ActiveTokenId = activeTokenId
            };

            var apiResponse = await RestHttpClient.Create()
                .Post<RemoveAddedApplicantResult>(
                    AppSettings.Qms.BaseApiUrl + $"/api/applicants/remove", QMSApiDefaultRequestHeaders,
                    request);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            return Json(new ResultModel
            {
                Data = apiResponse.Data,
                Message = ResultMessage.OperationIsSuccessful.ToDescription(),
                ResultType = ResultType.Success
            });
        }

        [HttpPost]
        public async Task<IActionResult> ActionSelectedToken(ActionSelectedTokenViewModel viewModel)
        {
            if (viewModel == null) return Content(EnumResources.MissingOrInvalidData);

            var request = new ActionSelectedTokenRequestModel
            {
                LineId = viewModel.LineId,
                BranchId = UserSession.BranchId.GetValueOrDefault(),
                AppointmentId = viewModel.AppointmentId,
                CounterId = viewModel.CounterId,
                TokenNumber = viewModel.TokenNumber,
                UserId = UserSession.UserId,
                State = viewModel.State,
                ApplicantId = viewModel.ApplicantId,
                Assigner = viewModel.Assigner,
                Assignee = viewModel.Assigner,
                PostponeAppointmentId = viewModel.PostponeAppointmentId,
                PostponeSlotId = viewModel.PostponeSlotId,
                ActiveTokenId = viewModel.ActiveTokenId
            };

            var apiResponse = await RestHttpClient.Create()
                .Post<ActionSelectedTokenResult>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.ActionSelectedToken, QMSApiDefaultRequestHeaders, request);

            return apiResponse.Status != QMS_API_RESPONSE_SUCCESS
                ? Json(new { ErrorMessage = apiResponse.Message })
                : Json(new ResultModel
                {
                    Data = apiResponse.Data,
                    Message = $"{ResultMessage.OperationIsSuccessful.ToDescription()}",
                    ResultType = ResultType.Success
                });
        }

        #endregion

        #region Application

        [HttpPost]
        public async Task<IActionResult> CreateApplication(CreateApplicationViewModel viewModel)
        {
            if (viewModel.EncryptedApplicantId.IsNullOrWhitespace() || viewModel.EncryptedApplicantTypeId.IsNullOrWhitespace())
                return Json(new { Message = SiteResources.RequiredField });

            try
            {
                var apiResponse = await RestHttpClient.Create()
                    .Get<CreateApplicationResult>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.CreateApplication,
                        QMSApiDefaultRequestHeaders);

                if (apiResponse?.Data is null)
                    return Json(new { Message = SiteResources.RecordNotFound });

                var relationalApplications = apiResponse.Data.Applicants?
                        .Where(p => p.RelationalApplicationId.HasValue && p.RelationalApplicationId == p.ApplicationId &&
                            p.ApplicantTypeId.ToEncrypt() == viewModel.EncryptedApplicantTypeId)
                    .ToList();
                if (relationalApplications?.Count <= 0)
                    return Json(new { Message = SiteResources.RecordNotFound });

                var relationalApplicationsListInfo = new FamilySelectionViewModel
                {
                    EncryptedApplicantId = viewModel.EncryptedApplicantId,
                    EncryptedApplicantTypeId = viewModel.EncryptedApplicantTypeId,
                    EncryptedTokenId = viewModel.EncryptedTokenId,
                    EncryptedWhiteListId = viewModel.EncryptedWhiteListId,
                    Families = relationalApplications?.Select(p => new FamilySelectionViewModel.Family
                    {
                        Applicant = p.NameSurname,
                        EncryptedRelationalApplicationId = p.RelationalApplicationId.ToEncrypt(),
                        EncryptedApplicantTypeId = p.ApplicantTypeId.ToEncrypt(),
                        EncryptedBranchApplicationCountryId = p.BranchApplicationCountryId.ToEncrypt()
                    }).ToList()
                };

                return PartialView("_FamilyOrGroupSelection", relationalApplicationsListInfo);
            }
            catch (Exception)
            {
                return Json(new { Message = SiteResources.RecordNotFound });
            }
        }

        #endregion

        #region InfoDesk

        [HttpPost]
        public async Task<IActionResult> AddNewApplicantsFromAnotherPreApplication(string encryptedAppointmentId, int lineId)
        {
            try
            {
                var appointmentId = Convert.ToInt32(encryptedAppointmentId.ToDecrypt());

                if (encryptedAppointmentId is null || appointmentId == 0 || lineId == 0)
                    return Content(EnumResources.MissingOrInvalidData);

                var request = new CreateTicketRequestModel
                {
                    LineId = lineId,
                    AppointmentId = appointmentId
                };

                var apiResponse = await RestHttpClient.Create().Post<GetAppointmentInformationResult>(
                    AppSettings.Qms.BaseApiUrl + QmsEndPoint.GetAppointmentInformationWithAppointmentNumber, QMSApiDefaultRequestHeaders, request);

                if (apiResponse?.Data is null)
                    return Json(new { Message = apiResponse is { Message: { } } ? apiResponse.Message : SiteResources.AppointmentNotFound });

                if(DateTime.Parse(apiResponse.Data.AppointmentDate).Date > DateTime.UtcNow.Date)
                    return Json(new ResultModel 
                    { Message = $"{SiteResources.EarlyAppointmentProcess} {apiResponse.Data.AppointmentDate}" });

                if (DateTime.Parse(apiResponse.Data.AppointmentDate).Date < DateTime.UtcNow.Date)
                    return Json(new ResultModel { Message = $"{SiteResources.AppointmentOutOfDate} {apiResponse.Data.AppointmentDate}" });

                var userSession = UserSession;

                if (apiResponse.Data.BranchId != userSession.BranchId)
                    return Json(new { Message = $"{SiteResources.SelectedAppointmentInAnotherBranch} / {apiResponse.Data.BranchName}" });

                var createTicketViewModel = userSession.CreateTicketViewModel;
                var passportNumbers = createTicketViewModel.Applicants.Select(r => r.PassportNumber).ToList();
                var availableApplicants = apiResponse.Data.Applicants.Where(r => !r.IsCreatedTicket && !passportNumbers.Contains(r.PassportNumber)).ToList();

                if (availableApplicants.Count == 0)
                    return Json(new ResultModel
                    {
                        Message = ResultMessage.ExistingData.ToDescription(),
                        ResultType = ResultType.Warning
                    });

                foreach (var applicant in availableApplicants)
                {
                    var newApplicant = new CreateTicketViewModel.Applicant
                    {
                        Id = applicant.Id,
                        Name = applicant.Name,
                        Surname = applicant.Surname,
                        BirthDate = applicant.BirthDate,
                        GenderId = applicant.GenderId,
                        NationalityId = applicant.NationalityId,
                        PassportNumber = applicant.PassportNumber,
                        PassportExpireDate = applicant.PassportExpireDate,
                        Email = applicant.Email,
                        PhoneNumber = applicant.PhoneNumber,
                        InsuranceTypeIds = applicant.InsuranceTypeIds,
                        BlackListModel = applicant.BlackListModel != null ? new BlackListModel
                        {
                            BlackListNote = applicant.BlackListModel.BlackListNote,
                            BlackListNoteCreatedBy = applicant.BlackListModel.BlackListNoteCreatedBy,
                            BlackListNoteUpdatedBy = applicant.BlackListModel.BlackListNoteUpdatedBy
                        } : null,
                        WhiteListModel = new CreateTicketViewModel.WhiteListModel
                        {
                            MissionNotes = applicant.WhiteListModel?.MissionNotes,
                            RelevantInstitutionPerson = applicant.WhiteListModel?.RelevantInstitutionPerson,
                            BiometricData = applicant.WhiteListModel?.BiometricData,
                            DocumentExemption = applicant.WhiteListModel?.BiometricData
                        },
                        OldWhitelistApplicant = applicant.OldWhitelistApplicant,
                        HasBlackListApplicant = applicant.HasBlackListApplicant,
                        HasWhiteListApplicant = applicant.HasWhiteListApplicant
                    };
                    createTicketViewModel.Applicants.Add(newApplicant);
                }
                SessionExtensions.Set(HttpContext.Session, SessionKeys.UserSession, userSession);

                return Json(new ResultModel
                {
                    Message = ResultMessage.OperationIsSuccessful.ToDescription(),
                    ResultType = ResultType.Success
                });
            }
            catch (Exception ex)
            {
                return Json(new ResultModel
                {
                    Message = ResultMessage.ErrorOccurred.ToDescription() + "\r\n" + ex.Message,
                    ResultType = ResultType.Danger
                });
            }
        }

        #endregion

        [HttpPost]
        public async Task<IActionResult> LoadState()
        {
            if (UserSession.BranchId is null) return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });

            var apiResponse = await RestHttpClient.Create().Get<LoadStateResult>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.LoadState, QMSApiDefaultRequestHeaders);

            if (apiResponse?.Data is null)
                return Json(SiteResources.RecordNotFound);

            var timeZoneOffset = await GetTimeZoneOffset();

            var resultViewModel = new GetCallNextViewModel
            {
                AppointmentId = apiResponse.Data.CacheModel.AppointmentId.ToInt(),
                QmsFilterAppointmentNumber = string.Empty,
                QmsBranchApplicationCountryId = apiResponse.Data.CacheModel.QmsBranchApplicationCountryId,
                ActiveTokenId = apiResponse.Data.ActiveToken!.Id,
                Screen = apiResponse.Data.IsProcessSameCounter
                    ? apiResponse.Data.CacheModel.OwnerToken
                    : apiResponse.Data.TokenNumber,
                OwnerToken = apiResponse.Data.CacheModel.OwnerToken,
                IsLineDepartmentCreateApplication = apiResponse.Data.IsLineDepartmentCreateApplication,
                LineDepartmentId = apiResponse.Data.LineDepartmentId,
                IsSameProcessCounter = apiResponse.Data.IsProcessSameCounter,
                SearchByPassportNumber = apiResponse.Data.SearchByPassportNumber,
                Interval = apiResponse.Data.CacheModel.ActiveDepartment.Interval.GetValueOrDefault(),
                LineId = apiResponse.Data.CacheModel.LineId,
                CounterId = apiResponse.Data.CounterId,
                TotalSeconds = (int)(DateTime.UtcNow - apiResponse.Data.CacheModel.ActiveDepartment.CalledAt).TotalSeconds,
                Applicants = apiResponse.Data.CacheModel.Applicants?.Select(p => new GetCallNextViewModel.Applicant
                {
                    Id = p.Id,
                    GeneratedTokenId = p.GeneratedTokenId,
                    NameSurname = p.NameSurname,
                    PassportNumber = p.PassportNumber,
                    ApplicationTime = p.ApplicationTime,
                    IsIndividual = p.IsIndividual,
                    IsFamily = p.IsFamily,
                    IsGroup = p.IsGroup,
                    IsApplicationCompleted = p.IsApplicationCompleted,
                    ApplicationCompletedCounterId = p.ApplicationCompletedCounterId,
                    ApplicationCompletedLineDepartmentId = p.ApplicationCompletedLineDepartmentId,
                    ApplicantState = (byte)TokenState.InProgress,
                    IsAddedToAnotherToken = p.IsAddedToAnotherToken,
                    HealthInsurance = !string.IsNullOrEmpty(p.InsuranceTypeIds) && p.InsuranceTypeIds.Split(',').Select(int.Parse).Any(i => i == (int)PreApplicationInsuranceType.HealthInsurance),
                    TrafficInsurance = !string.IsNullOrEmpty(p.InsuranceTypeIds) && p.InsuranceTypeIds.Split(',').Select(int.Parse).Any(i => i == (int)PreApplicationInsuranceType.TrafficInsurance),
                    InsuranceTypeIds = string.IsNullOrEmpty(p.InsuranceTypeIds) ? string.Empty : string.Join(",", from i in p.InsuranceTypeIds.Split(',').Select(int.Parse).ToList() select EnumHelper.GetEnumDescription(typeof(PreApplicationInsuranceType), i.ToString())),
                    IsBlackListApplicant = p.IsBlackListApplicant,
                    IsWhiteListApplicant = p.IsWhiteListApplicant,
                    BlackListInformationViewModel = p.IsBlackListApplicant && p.BlackListModel is not null ? new BlackListInformationViewModel
                    {
                        BlackListNote = p.BlackListModel.BlackListNote,
                        BlackListNoteCreatedBy = p.BlackListModel.BlackListNoteCreatedBy,
                        BlackListNoteUpdatedBy = p.BlackListModel.BlackListNoteUpdatedBy,
                    } : null,
                    WhiteListInformationViewModel = p.IsWhiteListApplicant && p.WhiteListModel is not null ? new RedisWhiteListInformationViewModel
                    {
                        Id = p.WhiteListModel.Id,
                        BiometricData = p.WhiteListModel.BiometricData != null
                            ? EnumHelper.GetEnumDescription(typeof(QMSWhiteListBiometricData), p.WhiteListModel.BiometricData.ToString())
                            : string.Empty,
                        DocumentExemption = p.WhiteListModel.DocumentExemption != null
                            ? EnumHelper.GetEnumDescription(typeof(QMSWhiteListDocumentExemption), p.WhiteListModel.DocumentExemption.ToString())
                            : string.Empty,
                        MissionNotes = p.WhiteListModel.MissionNotes,
                        RelevantInstitutionPerson = p.WhiteListModel.RelevantInstitutionPerson,
                    } : null,
                    HasNotCompletedReason = p.HasNotCompletedReason,
                    NotCompletedReasonViewModel = p.HasNotCompletedReason && p.NotCompletedReasons is not null ? p.NotCompletedReasons.Select(s => new RedisNotCompletedReasonViewModel
                    {
                        ActionBy = s.ActionBy,
                        ReasonId = s.ReasonId,
                        ActionAt = s.ActionAt.AddHours(3 + timeZoneOffset).ToString("dd/MM/yyyy HH:mm"),
                        Reason = s.OtherReason ? $"{SiteResources.Other}: {s.Reason}" : EnumHelper.GetEnumDescription(typeof(NotCompletedReason), s.ReasonId.ToString())
                    }).ToList() : null
                }).ToArray(),
                Notes = apiResponse.Data.CacheModel.Notes != null ? apiResponse.Data.CacheModel.Notes.Select(r => new GeneratedTokenNote
                {
                    Note = r.Note,
                    CreatedAt = r.CreatedAt.AddHours(3 + timeZoneOffset),
                    CreatedBy = r.CreatedBy
                }).ToList()
                : new List<GeneratedTokenNote>()
            };

            var userModel = UserSession;

            if ((userModel?.QmsDropdownsModel?.LineId == null || userModel.QmsDropdownsModel?.LineId == 0) && userModel?.BranchId == apiResponse.Data.CacheModel.BranchId)
            {
                userModel.QmsDropdownsModel.LineId = apiResponse.Data.CacheModel.LineId;
                userModel.QmsDropdownsModel.CounterId = apiResponse.Data.CounterId;

                SessionExtensions.Set(HttpContext?.Session, SessionKeys.UserSession, userModel);
            }

            return PartialView("_CallNextPage", resultViewModel);
        }

        public async Task<IActionResult> SetQmsDropdownsToSession(int lineId, int counterId)
        {
            var userModel = UserSession;
            userModel.QmsDropdownsModel.LineId = lineId;
            userModel.QmsDropdownsModel.CounterId = counterId;

            try
            {
                SessionExtensions.Set(HttpContext.Session, SessionKeys.UserSession, userModel);

                if (counterId.IsNumericAndGreaterThenZero() && lineId.IsNumericAndGreaterThenZero())
                {
                    var apiResponse = await RestHttpClient.Create().Get<GetConnectedLineDepartmentsResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.GetLineDepartmentConnectionsForWaitingCount
                        .Replace("{lineId}", lineId.ToString())
                        .Replace("{counterId}", counterId.ToString()), QMSApiDefaultRequestHeaders);

                    if (apiResponse?.Data is null)
                        return await Task.FromResult<IActionResult>(Json(new ResultModel
                        {
                            Message = EnumResources.InvalidOperation,
                            ResultType = ResultType.Danger
                        }));

                    if (!apiResponse.Data.Exists(r => r.ShowWaitingButton))
                        return await Task.FromResult<IActionResult>(Json(new ResultModel
                        {
                            Message = EnumResources.OperationIsSuccessful,
                            ResultType = ResultType.Success
                        }));

                    return await Task.FromResult<IActionResult>(Json(new ResultModel
                    {
                        Data = apiResponse.Data.Where(s => s.ShowWaitingButton).ToList(),
                        Message = EnumResources.OperationIsSuccessful,
                        ResultType = ResultType.Success
                    }));
                }

                return await Task.FromResult<IActionResult>(Json(new ResultModel
                {
                    Message = EnumResources.OperationIsSuccessful,
                    ResultType = ResultType.Success
                }));
            }
            catch (Exception)
            {
                return await Task.FromResult<IActionResult>(Json(new ResultModel
                {
                    Message = EnumResources.InvalidOperation,
                    ResultType = ResultType.Danger
                }));
            }
        }

        [HttpPost]
        public async Task<IActionResult> SessionUploadFile([FromForm] IFormFile file, string fileSessionId)
        {
            if (file != null && !string.IsNullOrEmpty(fileSessionId))
            {
                using (var ms = new MemoryStream())
                {
                    file.CopyTo(ms);
                    await _fileStorage.SaveFileAsync(FileStorage.FileStorageBucketName, $"{FileStorage.SessionUploadFileFolderName}/{fileSessionId}", ms, "");
                }

                var fileModel = FileHelper.GetFileInfo(file);
                SessionExtensions.Set(HttpContext.Session, $"SessionUploadFile{fileSessionId}", fileModel);
            }

            return Content("");
        }

        public IActionResult SessionRemoveFile([FromForm] IFormFile file, string fileSessionId)
        {
            if (file != null && !string.IsNullOrEmpty(fileSessionId))
            {
                var fileModel = Extensions.SessionExtensions.Get<FileModel>(HttpContext.Session, $"SessionUploadFile{fileSessionId}");
                SessionExtensions.Set(HttpContext.Session, $"SessionUploadFile{fileSessionId}", fileModel);
            }

            return Content("");
        }

        [Action(IsMenuItem = true)]
        public IActionResult GetAppointmentsFromExcel()
        {
            var viewModel = new PreApplicationDataFromExcelViewModel()
            {
                FileSessionId = Guid.NewGuid().ToString(),
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> GetAppointmentDataFromExcel([FromForm] IFormFile uploadFile, [FromServices] IWebHostEnvironment environment)
        {
            if (uploadFile == null || uploadFile.FileName.IsNullOrWhitespace())
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Warning });

            var dirName = $"{environment.WebRootPath}\\vfs\\appointment\\";

            if (!Directory.Exists(dirName))
                Directory.CreateDirectory(dirName);

            var fileName = $"{environment.WebRootPath}\\vfs\\appointment\\{uploadFile.FileName}";

            try
            {
                await using (var fileStream = System.IO.File.Create(fileName))
                {
                    await uploadFile.CopyToAsync(fileStream);
                    fileStream.Flush();
                }

                var appointments = await GetAppointmentsFromFile(fileName);

                return appointments.Item2 != EnumResources.OperationIsSuccessful ?
                    Json(new ResultModel { Message = appointments.Item2, ResultType = ResultType.Warning }) :
                    Json(new ResultModel { Data = appointments.Item1 });
            }
            catch (Exception)
            {
                return Json(new ResultModel { Message = EnumResources.ErrorOccurred, ResultType = ResultType.Warning });
            }
            finally
            {
                if (System.IO.File.Exists(fileName))
                    System.IO.File.Delete(fileName);
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddBulkPreApplications(string rows)
        {
            if (rows.IsNullOrWhitespace())
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Warning });

            try
            {
                var viewModel = JsonSerializer.Deserialize<List<PreApplicationDataFromExcelViewModel>>(rows);

                if (viewModel == null || !viewModel.Any() || viewModel.Find(r => r.IsValid is true) == null)
                    return Json(new ResultModel { Message = EnumResources.AppointmentNotFound, ResultType = ResultType.Warning });

                var request = new AddBulkAppointmentsFromExcelRequest
                {
                    Appointments = viewModel.Where(r => r.IsValid is true).ToList()
                };

                var apiResponse = await RestHttpClient.Create().Post<AddBulkAppointmentsFromExcelResult>(
                    AppSettings.Qms.BaseApiUrl + QmsEndPoint.AddBulkAppointmentsFromExcel, QMSApiDefaultRequestHeaders, request);

                return apiResponse.Data == null ?
                    Json(new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Warning }) :
                    Json(new ResultModel { Data = apiResponse.Data.DataCount });
            }
            catch (Exception)
            {
                return Json(new ResultModel { Message = EnumResources.ErrorOccurred, ResultType = ResultType.Warning });
            }
        }

        private async Task<Tuple<IEnumerable<PreApplicationDataFromExcelViewModel>, string>> GetAppointmentsFromFile(string fName)
        {
            var appointments = new List<PreApplicationDataFromExcelViewModel>();

            try
            {
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

                await using var stream = System.IO.File.Open(fName, FileMode.Open, FileAccess.Read);

                using var reader = ExcelReaderFactory.CreateReader(stream);

                do
                {
                    while (reader.Read())
                    { }
                }
                while (reader.NextResult());

                var result = reader.AsDataSet();
                var dataTable = result.Tables[0];

                if (dataTable.Rows[0][0].ToString()?.Trim().ToLower() != "sr. no" || dataTable.Rows[0][1].ToString()?.Trim().ToLower() != "group reference no"
                                                                        || dataTable.Rows[0][2].ToString()?.Trim().ToLower() != "individual reference no" || dataTable.Rows[0][3].ToString()?.Trim().ToLower() != "first name"
                                                                        || dataTable.Rows[0][4].ToString()?.Trim().ToLower() != "last name" || dataTable.Rows[0][13].ToString()?.Trim().ToLower() != "appointment date time"
                                                                        || (dataTable.Rows[0][14].ToString() != null && !dataTable.Rows[0][14].ToString()!.Trim().ToLower().Contains("branch")))
                    return new Tuple<IEnumerable<PreApplicationDataFromExcelViewModel>, string>(appointments, EnumResources.InvalidExcelFormat);

                for (var i = 1; i < dataTable.Rows.Count; i++)
                {
                    if (dataTable.Rows[i][0].ToString().Trim().ToLower().Contains("customer")) continue;
                    var appointment = new PreApplicationDataFromExcelViewModel
                    {

                        GroupReferanceNo = dataTable.Rows[i][1].ToString()?.Trim(),
                        IndividualReferanceNo = dataTable.Rows[i][2].ToString()?.Trim(),
                        FirstName = dataTable.Rows[i][3].ToString()?.Trim(),
                        LastName = dataTable.Rows[i][4].ToString()?.Trim(),
                        BirthDate = DateTime.TryParse(dataTable.Rows[i][5].ToString().Trim(), out var birthdate) ? birthdate.Date : null,
                        PassportNumber = dataTable.Rows[i][6].ToString()?.Trim(),
                        PassportExpiryDate = DateTime.TryParse(dataTable.Rows[i][7].ToString().Trim(), out var passportExpireDate) ? passportExpireDate.Date : null,
                        Category = dataTable.Rows[i][8].ToString()?.Trim(),
                        Gender = dataTable.Rows[i][9].ToString()?.Trim(),
                        Nationality = dataTable.Rows[i][10].ToString()?.Trim(),
                        Email = dataTable.Rows[i][11].ToString()?.Trim(),
                        PhoneNumber = dataTable.Rows[i][12].ToString()?.Trim(),
                        AppointmentDate = dataTable.Rows[i][13].ToString()?.Trim(),
                        BranchName = dataTable.Rows[i][14].ToString()?.Trim(),
                        //TransactionId = dataTable.Rows[i][15].ToString()?.Trim(),
                        VasType = string.Empty,
                        ApplicantType = string.Empty,
                    };

                    appointments.Add(appointment);
                }

                var validateApiRequest = new ValidateBulkAppointmentsFromExcelRequest
                {
                    Appointments = appointments
                };

                var apiResponse = await RestHttpClient.Create().Post<ValidateBulkAppointmentsFromExcelResult>(
                    AppSettings.Qms.BaseApiUrl + QmsEndPoint.ValidateBulkAppointmentsFromExcel, QMSApiDefaultRequestHeaders, validateApiRequest);

                if (apiResponse.Data == null)
                    return new Tuple<IEnumerable<PreApplicationDataFromExcelViewModel>, string>(appointments, EnumResources.ErrorOccuredOnValidation);

                foreach (var appointment in appointments)
                {
                    if (appointments.Count(r => r.PassportNumber == appointment.PassportNumber) > 1)
                    {
                        appointment.IsValid = false;
                        appointment.InvalidReason = SiteResources.HasSamePassportNumberInExcel;
                        appointment.ApplicantType = apiResponse.Data.First(d => d.ValidatedData.PassportNumber == appointment.PassportNumber).ValidatedData.ApplicantType;
                        appointment.VasType = apiResponse.Data.First(d => d.ValidatedData.PassportNumber == appointment.PassportNumber).ValidatedData.VasType;
                        appointment.AppointmentDate = apiResponse.Data.First(d => d.ValidatedData.PassportNumber == appointment.PassportNumber).ValidatedData.AppointmentDate;
                    }
                    else
                    {
                        appointment.IsValid = apiResponse.Data.First(d => d.ValidatedData.PassportNumber == appointment.PassportNumber).IsValid;
                        appointment.InvalidReason = apiResponse.Data.First(d => d.ValidatedData.PassportNumber == appointment.PassportNumber).InvalidReason;
                        appointment.ApplicantType = apiResponse.Data.First(d => d.ValidatedData.PassportNumber == appointment.PassportNumber).ValidatedData.ApplicantType;
                        appointment.VasType = apiResponse.Data.First(d => d.ValidatedData.PassportNumber == appointment.PassportNumber).ValidatedData.VasType;
                        appointment.AppointmentDate = apiResponse.Data.First(d => d.ValidatedData.PassportNumber == appointment.PassportNumber).ValidatedData.AppointmentDate;
                    }
                }

                return new Tuple<IEnumerable<PreApplicationDataFromExcelViewModel>, string>(appointments, EnumResources.OperationIsSuccessful);
            }
            catch (Exception)
            {
                return new Tuple<IEnumerable<PreApplicationDataFromExcelViewModel>, string>(appointments, EnumResources.MissingOrInvalidData);
            }
        }

        public IActionResult GetWhiteListWindowContent(string data)
        {
            var viewModel = JsonSerializer.Deserialize<RedisWhiteListInformationViewModel>(data);

            if (viewModel != null)
                return Json(new
                {
                    success = true,
                    Data = $" {SiteResources.BiometricData} : {viewModel.BiometricData} </br>" +
                           $" {SiteResources.DocumentExemption} : {viewModel.DocumentExemption} </br>" +
                           $" {SiteResources.RelevantInstitutionPerson} : {viewModel.RelevantInstitutionPerson} </br>" +
                           $" {SiteResources.MissionNotes}: {viewModel.MissionNotes}"
                });
            else
                return Json(new
                {
                    success = false,
                    Data = ""
                });
        }

        public async Task<IActionResult> GetNotCompletedReasonContent([DataSourceRequest] DataSourceRequest request, string data)
        {
            var viewModel = JsonSerializer.Deserialize<List<RedisNotCompletedReasonViewModel>>(data);

            var dataSource = await viewModel.ToDataSourceResultAsync(request);

            return Json(dataSource);
        }

        #region Private Methods

        private async Task<int> GetTimeZoneOffset()
        {
            var timeZoneOffset = 0;

            var cacheItem = await CacheHelper.GetBranchesAsync();

            var zoneOffset = cacheItem?.Branches?
                .FirstOrDefault(q => q.Id == UserSession.BranchId.GetValueOrDefault())?.TimeZoneOffset;

            if (zoneOffset != null) timeZoneOffset = (int)zoneOffset;

            return timeZoneOffset;
        }

        public static DateTime ConvertHourStringToDateTime(string hourString)
        {
            DateTime currentDate = DateTime.Today;
            string[] parts = hourString.Split(':');
            int hour = int.Parse(parts[0]);
            int minute = int.Parse(parts[1]);

            DateTime result = new DateTime(currentDate.Year, currentDate.Month, currentDate.Day, hour, minute, 0);
            return result;
        }

        #endregion

    }
}