﻿using DocumentFormat.OpenXml.Spreadsheet;
using FirebaseAdmin.Messaging;
using Gateway.Core.Pagination;
using Gateway.Extensions;
using Gateway.External.Application.Customer;
using Gateway.External.Application.Enums;
using Gateway.External.Application.IdentityServer;
using Gateway.External.Application.IdentityServer.Dto;
using Gateway.External.Application.Lookup;
using Gateway.External.Application.User.Dto;
using Gateway.External.Application.User.Dto.Request;
using Gateway.External.Application.User.Dto.Response;
using Gateway.External.Application.User.Validator;
using Gateway.External.Core.Context;
using Gateway.External.Entity.Entities.CustomerUser;
using Gateway.External.Persistence;
using Gateway.External.Resources;
using Gateway.Firebase;
using Gateway.Firebase.Dto;
using Gateway.Http;
using Gateway.Redis;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace Gateway.External.Application.User
{
    public class UserService : IUserService
    {
        private static readonly ILogger Logger = Log.ForContext<UserService>();
        private readonly IContext _context;
        private readonly IRedisClient _redisClient;
        private readonly IValidationService _validationService;
        private readonly IConfiguration _configuration;
        private readonly IFirebaseRepository _firebaseRepository;
        private readonly string environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
        private readonly ApiDbContext _dbContext;
        public IdentityServerEndpointRouting IsRouting;
        public WebHeaderCollection Header;

        public UserService(IContext context, IRedisClient redisClient, IValidationService validationService, IConfiguration configuration, ApiDbContext dbContext, IFirebaseRepository firebaseRepository)
        {
            _context = context;
            _redisClient = redisClient;
            _validationService = validationService;
            _configuration = configuration;
            _dbContext = dbContext;
            _firebaseRepository = firebaseRepository;

            Header = new WebHeaderCollection();
            IsRouting = new IdentityServerEndpointRouting();
        }

        public async Task<UserLogoutResult> UserLogout(UserLogoutRequest request)
        {
            try
            {
                var key = await _redisClient.GetAsync<List<TokenBlacklistDto>>($"token-blacklist:{request.Context.ChannelTypeId}");

                if (key == null || !key.Any())
                {
                    await _redisClient.AddAsync($"token-blacklist:{request.Context.ChannelTypeId}", new List<TokenBlacklistDto> { new()
                    {
                        Token = _context.Identity.Token,
                        CreatedAt = DateTime.Now
                    }});
                }
                else
                {
                    key.Add(new TokenBlacklistDto
                    {
                        Token = _context.Identity.Token,
                        CreatedAt = DateTime.Now
                    });

                    await _redisClient.AddAsync($"token-blacklist:{request.Context.ChannelTypeId}", key);
                }

                if (request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.Mobile)
                {
                    var existingUser = await _dbContext.CustomerUser
                        .Include(i => i.CustomerUserDevices)
                        .Where(s => s.Email.Equals(request.Context.Identity.Email))
                        .FirstOrDefaultAsync();

                    var existingDevice = existingUser?.CustomerUserDevices
                        .FirstOrDefault(s => s.DeviceId == request.DeviceId);

                    if (existingDevice != null)
                    {
                        existingDevice.IsActive = false;
                        existingDevice.UpdatedAt = DateTime.Now;
                        _dbContext.CustomerUserDevice.Update(existingDevice);
                        await _dbContext.SaveChangesAsync();
                    }
                }

                return new UserLogoutResult
                {
                    Status = UserStatus.Successful,
                    Message = ServiceResources.SUCCESS,
                };
            }
            catch (Exception exc)
            {
                Logger.Error(exc, $@"Exception:{exc.Message}:{DateTime.Now}");

                return new UserLogoutResult
                {
                    Status = UserStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<UserRegisterResult> UserRegister(UserRegisterRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UserRegisterValidator), request);

            if (!validationResult.IsValid)
                return new UserRegisterResult
                {
                    Status = UserStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            await using var transaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                var isExistingUser = await _dbContext.CustomerUser
                    .Where(r => r.Email.Equals(request.Email)).AsNoTracking()
                    .AnyAsync();

                if (isExistingUser)
                    return new UserRegisterResult
                    {
                        Status = UserStatus.AlreadyExist,
                        Message = ServiceResources.USER_ALREADY_REGISTERED,
                    };

                var customerUser = new Entity.Entities.CustomerUser.CustomerUser()
                {
                    Email = request.Email,
                    Name = request.Name,
                    Surname = request.Surname,
                    IsActive = true,
                    IsDeleted = false,
                    NationalityId = request.NationalityId,
                    CreatedBy = request.Email,
                    CreatedAt = DateTime.Now
                };

                await _dbContext.CustomerUser.AddAsync(customerUser);

                await _dbContext.SaveChangesAsync();

                #region IS

                var iSResponse = await IdentityServerPublicRegister(request.Email, request.Password, null);

                if (iSResponse.Status != "SUCCESS")
                {
                    await transaction.RollbackAsync();

                    return new UserRegisterResult
                    {
                        Status = UserStatus.ExternalServiceError,
                        Message = iSResponse.Message,
                        ValidationMessages = iSResponse.ValidationMessages
                    };
                }

                #endregion

                await transaction.CommitAsync();

                return new UserRegisterResult
                {
                    Status = UserStatus.Created,
                    Message = ServiceResources.RESOURCE_CREATED,
                    Id = customerUser.Id
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");
                await transaction.RollbackAsync();

                return new UserRegisterResult
                {
                    Status = UserStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetUserProfileResult> GetUserProfile(GetUserProfileRequest request)
        {
            try
            {
                var existingUser = await (
                        from cu in _dbContext.CustomerUser
                        where cu.Email.Equals(request.Context.Identity.Email)
                        join co in _dbContext.Country on cu.NationalityId equals co.Id into countryGroup
                        from c in countryGroup.DefaultIfEmpty()
                        select new
                        {
                            CustomerUser = cu,
                            Country = c
                        })
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                if (existingUser == null)
                    return new GetUserProfileResult
                    {
                        Status = UserStatus.NotFound,
                        Message = ServiceResources.USER_NOT_FOUND,
                    };

                var userDto = new CustomerUserDto()
                {
                    Name = existingUser.CustomerUser.Name,
                    Surname = existingUser.CustomerUser.Surname,
                    Email = existingUser.CustomerUser.Email,
                    Nationality = existingUser.Country != null ? new LookupValue()
                    {
                        Id = existingUser.Country.Id.ToString(),
                        DisplayValue = GetCountryName(existingUser.Country, _context.LanguageId)
                    } : null,
                    Settings = new CustomerUserSettingsDto()
                    {
                        IsAllowedForEmail = existingUser.CustomerUser.IsAllowedForEmail,
                        IsAllowedForNotification = existingUser.CustomerUser.IsAllowedForNotification,
                        IsAllowedForSms = existingUser.CustomerUser.IsAllowedForSms,
                    }
                };

                return new GetUserProfileResult()
                {
                    Status = UserStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    Data = userDto
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetUserProfileResult
                {
                    Status = UserStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<UpdateUserProfileResult> UpdateUserProfile(UpdateUserProfileRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UpdateUserProfileValidator), request);

            if (!validationResult.IsValid)
                return new UpdateUserProfileResult
                {
                    Status = UserStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var existingUser = await _dbContext.CustomerUser
                    .Where(r => r.Email.Equals(request.Context.Identity.Email)).AsNoTracking()
                    .FirstOrDefaultAsync();

                if (existingUser == null)
                    return new UpdateUserProfileResult
                    {
                        Status = UserStatus.NotFound,
                        Message = ServiceResources.USER_NOT_FOUND,
                    };

                var customerDevice = await _dbContext.CustomerUserDevice
                       .Where(r => r.CustomerUserId == existingUser.Id && r.IsActive && !r.IsDeleted).OrderByDescending(s => s.Id)
                       .FirstOrDefaultAsync();

                if (customerDevice == null)
                    return new UpdateUserProfileResult
                    {
                        Status = UserStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var locationCountry = await _dbContext.Country
                    .Where(s => s.Id == customerDevice.LocationId)
                    .FirstOrDefaultAsync();

                var languageCountry = await _dbContext.Country
                    .Where(s => s.Id == customerDevice.LanguageId)
                    .FirstOrDefaultAsync();

                var isNationalityUpdated = existingUser.NationalityId != request.NationalityId;

                if (isNationalityUpdated)
                {
                    await UnsubscribeFromTopics(locationCountry, languageCountry, existingUser, customerDevice.RegistryToken);
                }

                existingUser.Name = request.Name;
                existingUser.Surname = request.Surname;
                existingUser.NationalityId = request.NationalityId;
                existingUser.UpdatedAt = DateTime.Now;
                existingUser.UpdatedBy = request.Context.Identity.Email;

                if (isNationalityUpdated)
                {
                    await SubscribeToTopics(locationCountry, languageCountry, existingUser, customerDevice.RegistryToken, customerDevice.CustomerUserId);
                }

                _dbContext.CustomerUser.Update(existingUser);

                await _dbContext.SaveChangesAsync();

                return new UpdateUserProfileResult
                {
                    Status = UserStatus.Successful,
                    Message = ServiceResources.RESOURCE_UPDATED,
                    Id = existingUser.Id
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new UpdateUserProfileResult
                {
                    Status = UserStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<AddUpdateUserDeviceEntryResult> AddUpdateUserDeviceEntry(AddUpdateUserDeviceEntryRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UpdateUserDeviceEntryValidator), request);

            if (!validationResult.IsValid)
                return new AddUpdateUserDeviceEntryResult
                {
                    Status = UserStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var existingUser = await _dbContext.CustomerUser
                    .Where(r => r.Email.Equals(request.Context.Identity.Email)).AsNoTracking()
                    .FirstOrDefaultAsync();

                if (existingUser == null)
                    return new AddUpdateUserDeviceEntryResult
                    {
                        Status = UserStatus.NotFound,
                        Message = ServiceResources.USER_NOT_FOUND,
                    };

                var locationCountry = await _dbContext.Country.Where(s => s.ISO2 == request.Location.ToUpper()).FirstOrDefaultAsync();

                if (locationCountry == null)
                    return new AddUpdateUserDeviceEntryResult
                    {
                        Status = UserStatus.NotFound,
                        Message = ServiceResources.COUNTRY_NOT_FOUND,
                    };

                var languageCountry = await _dbContext.Country.Where(s => s.ISO2 == request.DeviceLanguage.ToUpper()).FirstOrDefaultAsync();

                var customerDevice = await _dbContext.CustomerUserDevice
                    .Where(r => r.CustomerUserId == existingUser.Id && r.DeviceId == request.DeviceId)
                    .FirstOrDefaultAsync();

                if (customerDevice == null)
                {
                    await CreateNewDeviceAsync(existingUser, request, locationCountry, languageCountry);
                }
                else
                {
                    await UpdateExistingDeviceAsync(customerDevice, existingUser, request, locationCountry, languageCountry);
                }

                return new AddUpdateUserDeviceEntryResult
                {
                    Status = UserStatus.Successful,
                    Message = ServiceResources.SUCCESS,
                    Id = existingUser.Id
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new AddUpdateUserDeviceEntryResult
                {
                    Status = UserStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetUserPushNotificationsResult> GetUserPushNotifications(GetUserPushNotificationsRequest request)
        {
            try
            {
                var existingUser = await _dbContext.CustomerUser
                    .Where(r => r.Email.Equals(request.Context.Identity.Email)).AsNoTracking()
                    .FirstOrDefaultAsync();

                if (existingUser == null)
                    return new GetUserPushNotificationsResult
                    {
                        Status = UserStatus.NotFound,
                        Message = ServiceResources.USER_NOT_FOUND,
                    };

                var userHistory = await _dbContext.PushNotification
                    .Include(i => i.PushNotificationHistories)
                    .Where(s => s.PushNotificationHistories.Any(r => r.CustomerUserId == existingUser.Id) && s.IsActive && !s.IsDeleted && s.StatusId == (int)Enums.Enums.NotificationStatusType.Sent).ToListAsync();

                var paginationResult = PagedResultsFactory.CreatePagedResult(
                    userHistory.AsQueryable(), request.Pagination.PageNumber, request.Pagination.PageSize,
                    request.Pagination.OrderBy, request.Pagination.Ascending);

                return new GetUserPushNotificationsResult
                {
                    Status = UserStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    PushNotifications = paginationResult.Results.Select(s => new PushNotificationDto
                    {
                        Id = s.Id,
                        Title = s.Title,
                        Status = GetLookupValue((int)LookupType.PushNotificationStatus,s.StatusId),
                        SendAt = s.SendAt?.ToString("dd/MM/yyyy HH:mm"),
                        CreatedAt = s.CreatedAt?.ToString("dd/MM/yyyy HH:mm"),
                        Subject = s.PushNotificationHistories.Where(s => s.CustomerUserId == existingUser.Id).OrderByDescending(s => s.Id).FirstOrDefault()?.Subject,
                        Text = s.PushNotificationHistories.Where(s => s.CustomerUserId == existingUser.Id).OrderByDescending(s => s.Id).FirstOrDefault()?.Text,
                        LocationId = s.LastSentLocationId,
                        NationalityId = s.LastSentNationalityId,
                        Histories = s.PushNotificationHistories.Where(s => s.CustomerUserId == existingUser.Id).OrderBy(s => s.Id).Select(h => new PushNotificationHistoryDto()
                        {
                            Id = h.Id,
                            LocationId = h.LocationId,
                            NationalityId = h.NationalityId,
                            Text = h.Text,
                            CustomerUserId = h.CustomerUserId,
                            SendAt = h.CreatedAt?.ToString("dd/MM/yyyy HH:mm")
                        }).ToList()
                    }).ToList(),
                    TotalNumberOfPages = paginationResult.TotalNumberOfPages,
                    TotalNumberOfRecords = paginationResult.TotalNumberOfRecords
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetUserPushNotificationsResult
                {
                    Status = UserStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<UpdateUserSettingsResult> UpdateUserSettings(UpdateUserSettingsRequest request)
        {
            try
            {
                var existingUser = await _dbContext.CustomerUser
                    .Where(r => r.Email.Equals(request.Context.Identity.Email)).AsNoTracking()
                    .FirstOrDefaultAsync();

                if (existingUser == null)
                    return new UpdateUserSettingsResult
                    {
                        Status = UserStatus.NotFound,
                        Message = ServiceResources.USER_NOT_FOUND,
                    };

                var customerDevice = await _dbContext.CustomerUserDevice
                     .Where(r => r.CustomerUserId == existingUser.Id && r.IsActive && !r.IsDeleted).OrderByDescending(s => s.Id)
                     .FirstOrDefaultAsync();

                if (customerDevice == null)
                    return new UpdateUserSettingsResult
                    {
                        Status = UserStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var locationCountry = await _dbContext.Country
                    .Where(s => s.Id == customerDevice.LocationId)
                    .FirstOrDefaultAsync();

                var languageCountry = await _dbContext.Country
                    .Where(s => s.Id == customerDevice.LanguageId)
                    .FirstOrDefaultAsync();

                var customerTopics = await _dbContext.CustomerUserTopic
                        .Where(s => s.IsActive && !s.IsDeleted && s.CustomerUserId == existingUser.Id)
                        .ToListAsync();

                if (!request.IsAllowedSendNotification)
                {
                    await UnsubscribeFromTopics(locationCountry, languageCountry, existingUser, customerDevice.RegistryToken);

                    foreach (var topic in customerTopics)
                    {
                        topic.IsDeleted = true;
                        topic.IsActive = false;
                        topic.DeletedAt = DateTime.Now;
                        _dbContext.CustomerUserTopic.Update(topic);
                    }

                    await _dbContext.SaveChangesAsync();
                }
                else
                {
                    if(customerTopics.Count == 0) 
                    {
                        await SubscribeToTopics(locationCountry, languageCountry, existingUser, customerDevice.RegistryToken, customerDevice.CustomerUserId);
                    }
                }

                existingUser.IsAllowedForEmail = request.IsAllowedSendEmail;
                existingUser.IsAllowedForNotification = request.IsAllowedSendNotification;
                existingUser.IsAllowedForSms = request.IsAllowedSendSms;
                existingUser.UpdatedAt = DateTime.Now;
                existingUser.UpdatedBy = request.Context.Identity.Email;

                _dbContext.CustomerUser.Update(existingUser);

                await _dbContext.SaveChangesAsync();

                return new UpdateUserSettingsResult
                {
                    Status = UserStatus.Successful,
                    Message = ServiceResources.RESOURCE_UPDATED,
                    Id = existingUser.Id
                };

            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new UpdateUserSettingsResult
                {
                    Status = UserStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        #region Private Methods

        private static LookupValue GetLookupValue(int enumType, int? value)
        {
            return EnumExtensions.GetEnumAsDictionary(LookupTypeFactory.GetInstance(enumType))
                .Select(x => new LookupValue { Id = x.Key.ToString(), DisplayValue = x.Value })
                .FirstOrDefault(p => p.Id == value.ToString());
        }

        private async Task CreateNewDeviceAsync(CustomerUser existingUser, AddUpdateUserDeviceEntryRequest request, Entity.Entities.Country.Country locationCountry, Entity.Entities.Country.Country languageCountry)
        {
            var newDevice = new CustomerUserDevice()
            {
                CustomerUserId = existingUser.Id,
                DeviceId = request.DeviceId,
                LocationId = locationCountry.Id,
                LanguageId = languageCountry?.Id ?? 0,
                RegistryToken = request.RegistryToken,
                IsActive = true,
                IsDeleted = false,
                CreatedBy = request.Context.Identity.Email,
                CreatedAt = DateTime.Now
            };

            await _dbContext.CustomerUserDevice.AddAsync(newDevice);
            await _dbContext.SaveChangesAsync();

            await SubscribeToTopics(locationCountry, languageCountry, existingUser, request.RegistryToken,existingUser.Id);
        }

        private async Task UpdateExistingDeviceAsync(CustomerUserDevice customerDevice, CustomerUser existingUser, AddUpdateUserDeviceEntryRequest request, Entity.Entities.Country.Country locationCountry, Entity.Entities.Country.Country languageCountry)
        {
            if (customerDevice.LocationId != locationCountry.Id)
            {
                var existingLocation = await _dbContext.Country
                    .Where(s => s.Id == customerDevice.LocationId)
                    .FirstOrDefaultAsync();

                await UnsubscribeFromTopics(existingLocation, languageCountry, existingUser, request.RegistryToken);
            }

            if (customerDevice.LanguageId != languageCountry.Id)
            {
                var existingLanguage= await _dbContext.Country
                    .Where(s => s.Id == customerDevice.LanguageId)
                    .FirstOrDefaultAsync();

                await UnsubscribeFromTopics(locationCountry, existingLanguage, existingUser, request.RegistryToken);
            }

            customerDevice.LocationId = locationCountry.Id;
            customerDevice.RegistryToken = request.RegistryToken;
            customerDevice.LanguageId = languageCountry?.Id ?? 0;

            await SubscribeToTopics(locationCountry,languageCountry, existingUser, request.RegistryToken, customerDevice.CustomerUserId);

            _dbContext.CustomerUserDevice.Update(customerDevice);
            await _dbContext.SaveChangesAsync();
        }

        private async Task SubscribeToTopics(Entity.Entities.Country.Country locationCountry, Entity.Entities.Country.Country languageCountry, CustomerUser user, string registryToken, int userId)
        {
            var topics = GetTopics(languageCountry?.ISO2, locationCountry.ISO2,user.NationalityId);

            foreach (var topic in topics)
            {
                await _firebaseRepository.SubscribeToTopic(new SubscribeToTopicRequest()
                {
                    RegistryToken = registryToken,
                    Topic = topic
                });
            }

            var customerTopics = await _dbContext.CustomerUserTopic
                .Where(s => s.IsActive && !s.IsDeleted && s.CustomerUserId == userId)
                .ToListAsync();

            foreach (var topic in topics.Where(topic => customerTopics.All(s => s.Topic != topic)))
            {
                await _dbContext.CustomerUserTopic.AddAsync(new CustomerUserTopic()
                {
                    IsActive = true,
                    IsDeleted = false,
                    CustomerUserId = userId,
                    Topic = topic,
                    CreatedAt = DateTime.Now,
                });
            }

            var topicsToRemove = customerTopics.Where(ct => !topics.Contains(ct.Topic)).ToList();
            foreach (var item in topicsToRemove)
            {
                item.IsDeleted = true;
                item.IsActive = false;
                item.DeletedAt = DateTime.Now;
                _dbContext.CustomerUserTopic.Update(item);
            }

            await _dbContext.SaveChangesAsync();
        }

        private async Task UnsubscribeFromTopics(Entity.Entities.Country.Country locationCountry, Entity.Entities.Country.Country languageCountry, CustomerUser user, string registryToken)
        {
            var topics = GetTopics(languageCountry?.ISO2, locationCountry.ISO2, user.NationalityId);

            foreach (var topic in topics)
            {
                await _firebaseRepository.UnSubscribeToTopic(new UnSubscribeToTopicRequest()
                {
                    RegistryToken = registryToken,
                    Topic = topic
                });
            }
        }

        private List<string> GetTopics(string languageCode, string locationCode, int nationalityId)
        {
            var environment = environmentName.ToLower();

            var topics = new List<string>();

            if (!string.IsNullOrEmpty(languageCode))
            {
                topics.AddRange(new List<string>()
                {
                    $"{environment}_{locationCode}_{nationalityId}_{languageCode}",
                    $"{environment}_{locationCode}_all_{languageCode}",
                    $"{environment}_all_{nationalityId}_{languageCode}",
                    $"{environment}_{locationCode}_{languageCode}",
                    $"{environment}_{nationalityId}_{languageCode}",
                    $"{environment}_all_{languageCode}",
                });
            }
            else
            {
                // --- Language-Agnostic Topics (Old Format, Backward Compatibility) ---
                topics.AddRange(new List<string>()
                {
                    // Original topics without language
                    $"{environment}_{locationCode}_{nationalityId}",
                    $"{environment}_{locationCode}_all",
                    $"{environment}_all_{nationalityId}",
                    $"{environment}_all",
                });
            }

            return topics;
        }

        private async Task<IdentityServerPublicRegisterResult> IdentityServerPublicRegister(string email, string password, List<RoleAssignDto> roles)
        {
            var baseAddress = _configuration.GetValue<string>("GatewayMobileIdentityServer:BaseAddress");
            var applicationResourceId = _configuration.GetValue<string>("GatewayMobileIdentityServer:ApplicationResourceId");

            Header.Add("Accept-Language", _context.LanguageId == (int)Enums.Enums.Language.Turkish ? "tr-TR" : "en-US");

            var registerRequest = new IdentityServerExternalRegisterRequest()
            {
                Password = password,
                Email = email,
                ApplicationResourceId = applicationResourceId,
            };

            return await RestHttpClient.Create().Post<IdentityServerPublicRegisterResult>
                (baseAddress + IsRouting.PublicRegister, Header, registerRequest).ConfigureAwait(false);
        }

        private static string GetCountryName(Entity.Entities.Country.Country country, int languageId)
        {
            return languageId switch
            {
                1 => country.NameTr,
                3 => country.NameAr,
                _ => country.Name
            };
        }

        #endregion


    }
}
