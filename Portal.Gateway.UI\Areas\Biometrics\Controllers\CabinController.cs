﻿using DocumentFormat.OpenXml.Wordprocessing;
using Gateway.Extensions;
using Gateway.Http;
using Gateway.Redis;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.QmsFolder;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.UI.Areas.Biometrics.Models.Cabin.RequestModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.Cabin.Results;
using Portal.Gateway.UI.Areas.Biometrics.Models.Cabin.ViewModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.Cabin;
using Portal.Gateway.UI.Models;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using System;
using System.Linq;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.UI.Areas.Biometrics.Models.ClientConfigurationInventory.Results;
using Portal.Gateway.UI.Constants;

namespace Portal.Gateway.UI.Areas.Biometrics.Controllers
{
    
    [Area("Biometrics")]
    public class CabinController : BaseController<CabinController>
    {
        public CabinController(
            IOptions<AppSettings> appSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper) { }

        #region Add

        public async Task<IActionResult> PartialAddCabinAsync()
        {
            var viewModel = new AddCabinViewModel();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<CabinInventoriesUnAssignedResponse>>(null, BiometricsEndPoint.ParameterGetCabinInventoriesUnAssigned, AppSettings.Biometrics.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (apiResponse.Data != null)
            {
                viewModel.UnAssignedInventories = apiResponse.Data.CabinInventories;
            }

            return PartialView("_AddCabin", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddCabin(AddCabinViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var request = new AddCabinRequestModel
            {
                Name = viewModel.Name,
                OfficeId = viewModel.OfficeId,
                IsBiometricCabin = viewModel.IsBiometricCabin,
                Status = 0,

            };

            var apiResponse = await RestHttpClient.Create().Post<AddCabinResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.AddCabin, headers, request);

            if (apiResponse.Data == null)
                return Json(new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateCabin(string encryptedCabinId)
        {
            if (encryptedCabinId.IsNullOrWhitespace())
            {
                return Content("Missing Cabin id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedCabinId.ToDecrypt());

            var apiResponse = await RestHttpClient.Create().Get<GetCabinResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetCabin + resourceId, headers);

            var viewModel = new UpdateCabinViewModel
            {
                Id = apiResponse.Data.Id,
                Name = apiResponse.Data.Name,
                OfficeId = apiResponse.Data.OfficeId,
                IsBiometricCabin = apiResponse.Data.IsBiometricCabin,
                Inventories = apiResponse.Data.Inventories,
                UnAssignedInventories = apiResponse.Data.UnAssignedInventories,
                Status = 0,
            };

            return PartialView("_UpdateCabin", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateCabin(UpdateCabinViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid) //buraya fluent val yazılıcak viewmodellere
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var apiRequest = new UpdateCabinRequestModel
            {
                Name = viewModel.Name,
                OfficeId = viewModel.OfficeId,
                IsBiometricCabin = viewModel.IsBiometricCabin,
                Status = viewModel.Status,
                Inventories = viewModel.Inventories.Select(n => new Models.Inventory.InventoryDto() { Id = n.Id}).ToList(),
            };

            var apiResponse = await RestHttpClient.Create().Put<UpdateCabinResult>(
                AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.UpdateCabin + viewModel.Id, headers,
                apiRequest);


            if (apiResponse.Data == null)
                return Json(new ResultModel { Message = apiResponse.ValidationMessages.ToPlainText(), ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion
        

        #region Get

        public async Task<IActionResult> PartialDetailCabin(string encryptedCabinId)
        {
            if (encryptedCabinId.IsNullOrWhitespace())
            {
                return Content("Missing Cabin id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedCabinId.ToDecrypt());

            var apiResponse = await RestHttpClient.Create().Get<GetCabinResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetCabin + resourceId, headers);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var viewModel = new CabinViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Name = apiResponse.Data.Name,
                OfficeId = apiResponse.Data.OfficeId,
                Office = apiResponse.Data.Office,
                IsBiometricCabin = apiResponse.Data.IsBiometricCabin,
                Status = apiResponse.Data.Status,
            };

            return PartialView("_DetailCabin", viewModel);
        }


        [Action(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedCabins([DataSourceRequest] DataSourceRequest request, FilterCabinViewModel filterViewModel)
        {
            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedCabinApiRequest
            {
                FilterCountryId = filterViewModel.FilterCountryId,
                Pagination = new QmsPaginationApiRequest
                {
                    PageNumber = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = "Name",
                    Ascending = true
                }
            };

            var apiResponse = await RestHttpClient.Create().Post<GetPaginatedCabinsResult>(
                AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetPaginatedCabins, headers, apiRequest);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var paginatedData = apiResponse.Data.Select(p =>
                new CabinViewModel
                {
                    EncryptedId = p.Id.ToEncrypt(),
                    Name = p.Name,
                    OfficeId = p.OfficeId,
                    IsBiometricCabin = p.IsBiometricCabin,
                    Status = p.Status,
                    Office = p.Office
                   
                }).ToList();

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.TotalNumberOfRecords });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteCabin(string encryptedCabinId)
        {
            if (encryptedCabinId.IsNullOrWhitespace())
            {
                return Content("Missing Cabin id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedCabinId.ToDecrypt());

            var apiResponse = await RestHttpClient.Create().Delete<DeleteCabinResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.DeleteCabin + resourceId, headers);

            if (apiResponse.Data == null)
                return Json(apiResponse.Message);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

    }

}
