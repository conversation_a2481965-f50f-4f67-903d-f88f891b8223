﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.Appointment.BranchStatusSms;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Appointment.BranchStatusSms;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.BranchStatusSms;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Appointment.Controllers
{
    [Area("Appointment")]
    public class BranchStatusSmsController : BaseController<BranchStatusSmsController>
    {
        public BranchStatusSmsController(
            IOptions<AppSettings> appSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        [HttpGet]
        public async Task<IActionResult> List(string encryptedBranchId)
        {
            if (string.IsNullOrEmpty(encryptedBranchId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Branch", new { Area = "Management" });
            }

            int branchId = encryptedBranchId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BranchStatusSmsApiResponse>>
                (ApiMethodName.Appointment.GetBranchStatusSms + branchId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", result);
                return RedirectToAction("List", "Branch", new { Area = "Management" });
            }

            var viewModel = new BranchStatusSmsViewModel()
            {
                EncryptedProviderId = apiResponse.Data.ProviderId.ToEncrypt(),
                EncryptedBranchId = apiResponse.Data.BranchId.ToEncrypt(),
                BranchName = apiResponse.Data.BranchName,
                ExtraFeeCheck = apiResponse.Data.ExtraFeeCheck,
                CargoFeeCheck = apiResponse.Data.CargoFeeCheck,
                BranchStatusSms = apiResponse.Data.BranchStatusSms.Select(p => new BranchStatusSmsItemViewModel()
                {
                    EncryptedBranchApplicationStatusId = p.BranchApplicationStatusId.ToEncrypt(),
                    ApplicationStatusName = p.ApplicationStatusName,
                    Text = p.Text,
                    IsActive = p.IsActive
                }).ToList()
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateBranchStatusSms(BranchStatusSmsViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateBranchStatusSmsApiRequest
            {
                BranchId = viewModel.EncryptedBranchId.ToDecryptInt(),
                ProviderId = viewModel.EncryptedProviderId.ToDecryptInt(),
                ExtraFeeCheck = viewModel.ExtraFeeCheck,
                CargoFeeCheck = viewModel.CargoFeeCheck,
                BranchStatusSms = viewModel.BranchStatusSms.Select(p => new UpdateBranchStatusSmsItemApiRequest()
                {
                    BranchApplicationStatusId = p.EncryptedBranchApplicationStatusId.ToDecryptInt(),
                    Text = p.Text,
                    IsActive = p.IsActive
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Appointment.UpdateBranchStatusSms, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }
    }
}
