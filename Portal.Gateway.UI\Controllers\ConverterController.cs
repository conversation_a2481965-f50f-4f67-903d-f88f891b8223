﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Helpers;
using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Controllers
{
    [AllowAnonymous]
    public class ConverterController : BaseController<ConverterController>
    {
        public ConverterController(
                IOptions<AppSettings> appSettings,
                ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {
        }

        public ContentResult GetYesNo(string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(key))
            {
                if (key == "false")
                    result = SiteResources.No;
                else if (key == "true")
                    result = SiteResources.Yes;
            }
            return Content(result);
        }

        public ContentResult ToEncrypt(string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(key))
            {
                result = key.ToEncrypt();
            }
            return Content(result);
        }

        public ContentResult ToDecryptInt(string key)
        {
            int result = 0;
            if (!string.IsNullOrEmpty(key))
            {
                result = key.ToDecryptInt();
            }
            return Content(result.ToString());
        }

        #region Enum

        public ContentResult GetLanguage(string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(key))
            {
                result = EnumHelper.GetEnumDescription(typeof(Language), key);
            }
            return Content(result);
        }

        public ContentResult GetApplicantType(string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(key))
            {
                result = EnumHelper.GetEnumDescription(typeof(ApplicantType), key);
            }
            return Content(result);
        }

        public ContentResult GetApplicationType(string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(key))
            {
                result = EnumHelper.GetEnumDescription(typeof(ApplicationType), key);
            }
            return Content(result);
        }

        public ContentResult GetVasType(string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(key))
            {
                result = EnumHelper.GetEnumDescription(typeof(VasType), key);
            }
            return Content(result);
        }

        public ContentResult GetApplicationPassportStatus(string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(key))
            {
                result = EnumHelper.GetEnumDescription(typeof(ApplicationPassportStatus), key);
            }
            return Content(result);
        }

        public ContentResult GetTitle(string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(key))
            {
                result = EnumHelper.GetEnumDescription(typeof(Title), key);
            }
            return Content(result);
        }

        public ContentResult GetGender(string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(key))
            {
                result = EnumHelper.GetEnumDescription(typeof(Gender), key);
            }
            return Content(result);
        }

        public ContentResult GetRelationShip(string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(key))
            {
                result = EnumHelper.GetEnumDescription(typeof(ApplicantFamilyRelationship), key);
            }
            return Content(result);
        }

        public ContentResult GetMaritalStatus(string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(key))
            {
                result = EnumHelper.GetEnumDescription(typeof(MaritalStatus), key);
            }
            return Content(result);
        }

        public async Task<ContentResult> GetNationality(string key)
        {
            var countries = await CacheHelper.GetCountriesAsync();
            var result = countries.Countries.FirstOrDefault(p => p.Id == key.ToInt())?.Name;
            return Content(result);
        }

        public ContentResult GetReimbursementType(string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(key))
            {
                result = EnumHelper.GetEnumDescription(typeof(ReimbursementType), key);
            }
            return Content(result);
        }

        public async Task<ContentResult> GetVisaCategoryType(string key)
        {
            var visaCategories = await GetCachedVisaCategories();
            string result = string.Empty;
            if (!string.IsNullOrEmpty(key))
            {
                result = GetVisaCategoryNameFromId(key.ToInt(), visaCategories);
            }
            return Content(result);
        }

        public ContentResult GetNumberOfEntryType(string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(key))
            {
                result = EnumHelper.GetEnumDescription(typeof(NumberOfEntryType), key);
            }
            return Content(result);
        }

        public ContentResult GetCurrencyType(string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(key))
            {
                result = EnumHelper.GetEnumDescription(typeof(CurrencyType), key);
            }
            return Content(result);
        }

        public ContentResult GetDataOccupationType(string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(key))
            {
                result = EnumHelper.GetEnumDescription(typeof(DataOccupationType), key);
            }
            return Content(result);
        }

		public async Task<ContentResult> GetForeignCity(string key)
		{
			var result = new StringBuilder();
			if (!string.IsNullOrEmpty(key))
			{
                var foreignCities = await CacheHelper.GetForeignCitiesAsync();

                if (foreignCities != null && foreignCities.Cities != null && foreignCities.Cities.Count > 0)
				{
					result.Append(foreignCities.Cities.FirstOrDefault(p => p.Id == Convert.ToInt32(key)).Name);
				}
			}
			return Content(result.ToString());
		}

        public async Task<ContentResult> GetForeignCityPostalCode(string key)
        {
            var result = new StringBuilder();
            if (!string.IsNullOrEmpty(key))
            {
                var foreignCities = await CacheHelper.GetForeignCitiesAsync();
                
                if (foreignCities != null && foreignCities.Cities != null && foreignCities.Cities.Count > 0)
                {
                    result.Append(foreignCities.Cities.FirstOrDefault(p => p.Id == Convert.ToInt32(key)).PostalCode);
                }
            }
            return Content(result.ToString());
        }

        #endregion

        #region PassportData

        public ContentResult GetPassportDataGender(string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(key))
            {
                if (key == "M")
                    result = ((int)Gender.Male).ToString();
                if (key == "F")
                    result = ((int)Gender.Female).ToString();
            }
            return Content(result);
        }

        public async Task<ContentResult> GetPassportDataNationality(string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(key))
            {
                var countries = await CacheHelper.GetCountriesAsync();
                if (countries != null && countries.Countries != null && countries.Countries.Count > 0)
                {
                    var nationality = countries.Countries.FirstOrDefault(p => p.ISO3.ToUpper() == key.ToUpper());

                    if (nationality != null)
                        result = nationality.Id.ToString();
                }
            }
            return Content(result);
        }

        #endregion
    }
}