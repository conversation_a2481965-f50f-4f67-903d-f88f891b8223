﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace Portal.Gateway.UI.Extensions
{
    public static class TranslationOrderingExtensions
    {
        public static List<T> OrderTranslationsByLanguagePriority<T>(
            this IEnumerable<T> translations,
            Func<T, int> languageIdSelector,
            params int[] priorityOrder) where T : class
        {
            // Create a dictionary for quick priority lookup
            var priorityDict = new Dictionary<int, int>();
            for (int i = 0; i < priorityOrder.Length; i++)
            {
                priorityDict[priorityOrder[i]] = i;
            }

            return translations
                .OrderBy(t => priorityDict.TryGetValue(languageIdSelector(t), out var priority)
                    ? priority
                    : int.MaxValue)
                .ToList();
        }
    }
}
