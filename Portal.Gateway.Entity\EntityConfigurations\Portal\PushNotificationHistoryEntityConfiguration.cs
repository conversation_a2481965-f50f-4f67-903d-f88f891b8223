﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Portal.Gateway.Entity.Entities.Portal;

namespace Portal.Gateway.Entity.EntityConfigurations.Portal
{
    public class PushNotificationHistoryEntityConfiguration : IEntityTypeConfiguration<PushNotificationHistory>
    {
        public void Configure(EntityTypeBuilder<PushNotificationHistory> builder)
        {
            builder.ToTable("PushNotificationHistory");

            #region StaticAuditableEntityConfig

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).IsRequired().ValueGeneratedOnAdd();
            builder.Property(e => e.IsActive).IsRequired();
            builder.Property(e => e.IsDeleted).IsRequired();
            builder.Property(e => e.CreatedBy);
            builder.Property(e => e.CreatedAt).HasColumnType("timestamp with time zone");
            builder.Property(e => e.UpdatedBy);
            builder.Property(e => e.UpdatedAt).HasColumnType("timestamp with time zone");
            builder.Property(e => e.DeletedBy);
            builder.Property(e => e.DeletedAt).HasColumnType("timestamp with time zone");

            #endregion

            builder.Property(e => e.LocationId)
                .IsRequired();

            builder.Property(e => e.NationalityId)
                .IsRequired();

            builder.Property(e => e.Subject).HasColumnType("citext").HasDefaultValueSql("''::citext");

            builder.Property(e => e.LanguageId).HasDefaultValueSql("0");

            builder.HasOne(d => d.PushNotification)
                .WithMany(p => p.PushNotificationHistories)
                .HasForeignKey(d => d.PushNotificationId)
                .OnDelete(DeleteBehavior.Restrict);
        }
    }
}
