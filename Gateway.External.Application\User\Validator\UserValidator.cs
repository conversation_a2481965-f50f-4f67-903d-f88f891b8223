﻿using FluentValidation;
using Gateway.Extensions;
using Gateway.External.Application.User.Dto.Request;
using Gateway.External.Resources;

namespace Gateway.External.Application.User.Validator
{
    internal class UserRegisterValidator : AbstractValidator<UserRegisterRequest>
    {
        public UserRegisterValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.Email.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Email)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Email.IsValidEmail())
                    context.AddFailure(string.Format(ServiceResources.INVALID_EMAIL_ADDRESS, nameof(item.Email)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.Password.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Password)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.NationalityId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.NationalityId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.Password != item.ConfirmPassword)
                    context.AddFailure(string.Format(ServiceResources.PASSWORD_MISMATCH, nameof(item.Password) + "-" + nameof(item.ConfirmPassword)));
            });
        }
    }

    internal class UpdateUserProfileValidator : AbstractValidator<UpdateUserProfileRequest>
    {
        public UpdateUserProfileValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.NationalityId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.NationalityId)));
            });
        }
    }

    internal class UpdateUserDeviceEntryValidator : AbstractValidator<AddUpdateUserDeviceEntryRequest>
    {
        public UpdateUserDeviceEntryValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.RegistryToken.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.RegistryToken)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.DeviceId.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.DeviceId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.Location.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Location)));

                if (item.Location.Length != 2)
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_MAX_LENGTH_ERROR, nameof(item.Location),2));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.DeviceLanguage.IsNullOrWhitespace() && item.DeviceLanguage.Length != 2)
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_MAX_LENGTH_ERROR, nameof(item.DeviceLanguage), 2));
            });
        }
    }
}
