﻿using ClosedXML.Excel;
using Gateway.Extensions;
using Gateway.Http;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.Management.WhiteListResen;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.ApiModel.Responses.Management.WhiteListResen;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.WhiteListResen;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class WhiteListResenController : BaseController<WhiteListResenController>
    {
        public WhiteListResenController(
               IOptions<AppSettings> appSettings,
               ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        #region Get

        [Action(IsMenuItem = true)]
        public IActionResult List()
        {
            if (UserSession.BranchId == null)
            {
                return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
            }

            var filterViewModel = new FilterWhiteListResenViewModel
            {
                InternalBranchId = (int)UserSession.BranchId
            };

            return View(filterViewModel);
        }

        public async Task<IActionResult> GetPaginatedWhiteListResenByBranch([DataSourceRequest] DataSourceRequest request, FilterWhiteListResenViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedWhiteListResenApiRequest
            {
                Pagination = new PaginatedWhiteListResenApiRequest.PaginationRequest
                {
                    PageNumber = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = "Id",
                    Ascending = false
                },
                FilterApplicantTypeId = filterViewModel.FilterApplicantTypeId,
                InternalBranchId = UserSession.BranchId.GetValueOrDefault(),
                FilterEndDate = filterViewModel.FilterEndDate,
                FilterName = filterViewModel.FilterName,
                FilterNationalityId = filterViewModel.FilterNationalityId,
                FilterPassportNumber = filterViewModel.FilterPassportNumber,
                FilterReferenceNumber = filterViewModel.FilterReferenceNumber,
                FilterStartDate = filterViewModel.FilterStartDate,
                FilterSurname = filterViewModel.FilterSurname,
                FilterVisaCategoryId = filterViewModel.FilterVisaCategoryId
            };

            var apiResponse = await RestHttpClient.Create().Post<GetPaginatedWhiteListResenByBranchResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.GetPaginatedWhiteListResenByBranch, QMSApiDefaultRequestHeaders, apiRequest);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var visaCategories = await GetCachedVisaCategories();

            var paginatedData = apiResponse.Data.Select(p =>
                new WhiteListResenViewModel
                {
                    EncryptedId = p.Id.ToEncrypt(),
                    ReferenceNumber = p.Id.ToString(),
                    ApplicationNote = p.ApplicationNote,
                    BirthDate = p.BirthDate.ToString("dd/MM/yyyy"),
                    GenderId = p.GenderId,
                    Name = p.Name,
                    NationalityId = p.NationalityId,
                    PassportNumber = p.PassportNumber,
                    Surname = p.Surname,
                    VisaCategoryId = p.VisaCategoryId,
                    ApplicantTypeId = p.ApplicantTypeId,
                    BranchId = p.BranchId,
                    CreatedAt = p.CreatedAt.ToString("dd/MM/yyyy"),
                    Nationality = p.Nationality,
                    ApplicantType = EnumHelper.GetEnumDescription(typeof(ApplicantType), p.ApplicantTypeId.ToString()),
                    Gender = EnumHelper.GetEnumDescription(typeof(Gender), p.GenderId.ToString()),
                    VisaCategory = GetVisaCategoryNameFromId(p.VisaCategoryId, visaCategories),
                }).ToList();

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.TotalNumberOfRecords });
        }

        public async Task<IActionResult> GetWhiteListResen(string encryptedId)
        {
            var resourceId = encryptedId.ToDecryptInt();

            var apiResponse = await RestHttpClient.Create().Get<GetWhiteListResenResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.GetWhiteListResen + resourceId, QMSApiDefaultRequestHeaders);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var visaCategories = await GetCachedVisaCategories();

            var viewModel = new WhiteListResenViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                ReferenceNumber = apiResponse.Data.Id.ToString(),
                ApplicationNote = apiResponse.Data.ApplicationNote,
                BirthDate = apiResponse.Data.BirthDate.ToString("dd/MM/yyyy"),
                GenderId = apiResponse.Data.GenderId,
                Name = apiResponse.Data.Name,
                NationalityId = apiResponse.Data.NationalityId,
                PassportNumber = apiResponse.Data.PassportNumber,
                Surname = apiResponse.Data.Surname,
                VisaCategoryId = apiResponse.Data.VisaCategoryId,
                ApplicantTypeId = apiResponse.Data.ApplicantTypeId,
                BranchId = apiResponse.Data.BranchId,
                CreatedAt = apiResponse.Data.CreatedAt.ToString("dd/MM/yyyy"),
                Nationality = apiResponse.Data.Nationality,
                ApplicantType = EnumHelper.GetEnumDescription(typeof(ApplicantType), apiResponse.Data.ApplicantTypeId.ToString()),
                Gender = EnumHelper.GetEnumDescription(typeof(Gender), apiResponse.Data.GenderId.ToString()),
                VisaCategory = GetVisaCategoryNameFromId(apiResponse.Data.VisaCategoryId, visaCategories)
            };

            return PartialView("_WhiteListResen", viewModel);
        }

        #endregion

        #region Add

        public IActionResult PartialAddWhiteListResenCount(int branchId)
        {

            var viewModel = new AddWhiteListResenViewModel
            {
                BranchId = branchId,

                WhiteListResenCount = 1
            };

            return PartialView("_AddWhiteListResenCount", viewModel);
        }

        public IActionResult PartialAddWhiteListResen(int branchId, int whiteListResenCount, int applicantType)
        {
            var viewModel = new AddWhiteListResenViewModel
            {
                BranchId = branchId,
                ApplicantTypeId = applicantType,
                WhiteListResenCount = whiteListResenCount
            };

            return PartialView("_AddWhiteListResen", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddWhiteListResen(AddWhiteListResenViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = $"{SiteResources.FillRequiredFields} - {SiteResources.AddWhiteListResen}", ResultType = ResultType.Danger });

            var request = new AddWhiteListResenRequestModel
            {
                BranchId = viewModel.BranchId,
                ApplicantTypeId = viewModel.ApplicantTypeId,
                WhiteListResenCount = viewModel.WhiteListResens.Count(a => a.WhiteListResenEnabled),
                WhiteListResens = viewModel.WhiteListResens.Where(a => a.WhiteListResenEnabled).ToList()
            };

            var apiResponse = await RestHttpClient.Create().Post<AddWhiteListResenResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.AddWhiteListResen, QMSApiDefaultRequestHeaders, request);

            if (apiResponse.Status != "SUCCESS")
                return Json(new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateWhiteListResen(string encryptedId, int branchId)
        {
            var resourceId = encryptedId.ToDecryptInt();

            var apiResponse = await RestHttpClient.Create().Get<GetWhiteListResenResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.GetWhiteListResen + resourceId, QMSApiDefaultRequestHeaders);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var viewModel = new UpdateWhiteListResenViewModel
            {
                EncryptedId = encryptedId,
                BranchId = branchId,
                ApplicantTypeId = apiResponse.Data.ApplicantTypeId,
                Id = resourceId,
                ApplicationNote = apiResponse.Data.ApplicationNote,
                BirthDate = apiResponse.Data.BirthDate,
                GenderId = apiResponse.Data.GenderId,
                Name = apiResponse.Data.Name,
                NationalityId = apiResponse.Data.NationalityId,
                PassportNumber = apiResponse.Data.PassportNumber,
                Surname = apiResponse.Data.Surname,
                VisaCategoryId = apiResponse.Data.VisaCategoryId,
                WhiteListResenEnabled = true
            };

            return PartialView("_UpdateWhiteListResen", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateWhiteListResen(UpdateWhiteListResenViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = $"{SiteResources.FillRequiredFields} - {SiteResources.UpdateWhiteListResen}", ResultType = ResultType.Danger });

            var apiRequest = new UpdateWhiteListResenRequestModel
            {
                Id = viewModel.EncryptedId.ToDecryptInt(),
                BranchId = viewModel.BranchId,
                ApplicationNote = viewModel.ApplicationNote,
                BirthDate = viewModel.BirthDate,
                GenderId = viewModel.GenderId,
                Name = viewModel.Name,
                NationalityId = viewModel.NationalityId,
                PassportNumber = viewModel.PassportNumber,
                ApplicantTypeId = viewModel.ApplicantTypeId,
                Surname = viewModel.Surname,
                VisaCategoryId = viewModel.VisaCategoryId,
            };

            var apiResponse = await RestHttpClient.Create().Put<UpdateWhiteListResenResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.UpdateWhiteListResen, QMSApiDefaultRequestHeaders, apiRequest);

            if (apiResponse.Status != "SUCCESS")
                return Json(new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }


        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteWhiteListResen(string EncryptedId)
        {
            if (EncryptedId.IsNullOrWhitespace())
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var resourceId = EncryptedId.ToDecryptInt();

            var apiResponse = await RestHttpClient.Create().Delete<DeleteWhiteListResenResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.DeleteWhiteListResen + resourceId, QMSApiDefaultRequestHeaders);

            if (apiResponse.Status != "SUCCESS")
                return Json(apiResponse.Message);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Report

        [HttpPost]
        public async Task<FileContentResult> QmsWhiteListResenReport(FilterWhiteListResenViewModel filterViewModel)
        {
            var apiRequest = new PaginatedWhiteListResenApiRequest
            {
                FilterPassportNumber = filterViewModel.FilterPassportNumber,
                FilterName = filterViewModel.FilterName,
                FilterSurname = filterViewModel.FilterSurname,
                FilterNationalityId = filterViewModel.FilterNationalityId,
                FilterStartDate = filterViewModel.FilterStartDate,
                FilterEndDate = filterViewModel.FilterEndDate,
                FilterApplicantTypeId = filterViewModel.FilterApplicantTypeId,
                FilterReferenceNumber = filterViewModel.FilterReferenceNumber,
                FilterVisaCategoryId = filterViewModel.FilterVisaCategoryId,
                InternalBranchId = filterViewModel.InternalBranchId,
            };

            var apiResponse = await RestHttpClient.Create().Post<WhiteListResenApplicationReportResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.WhiteListResenApplicationReport, QMSApiDefaultRequestHeaders, apiRequest);

            var reportName = SiteResources.WhitelistResenApplicationReport.ToTitleCase();
            var branchName = string.Empty;
            var cacheItem = await CacheHelper.GetBranchesAsync();
            var branch = cacheItem.Branches.FirstOrDefault(p => p.Id == UserSession.BranchId.GetValueOrDefault());
            if (branch != null)
            {
                branchName = branch.BranchTranslations.Any(p => p.LanguageId == LanguageId) ?
                                        branch.BranchTranslations.First(p => p.LanguageId == LanguageId).Name :
                                        branch.BranchTranslations.First().Name;
            }

            var reportCreatedBy = UserSession.FullName;
            var reportDate = DateTime.UtcNow.Date;

            using (var workbook = new XLWorkbook())
            {
                var worksheet = workbook.Worksheets.Add(branchName);
                var currentRow = 1;
                var headerRow = -1;

                worksheet.Cell(currentRow, 1).Value = reportName;
                var rangeTitle = worksheet.Range(worksheet.Cell(currentRow, 1), worksheet.Cell(currentRow, 3));
                rangeTitle.Merge().Style.Font.SetBold().Font.FontSize = 14;
                currentRow++;
                currentRow++;

                worksheet.Cell(currentRow, 1).Value = SiteResources.BranchName.ToTitleCase();
                worksheet.Cell(currentRow, 1).Style.Font.SetBold();
                currentRow++;
                worksheet.Cell(currentRow, 1).Value = branchName;
                currentRow++;
                currentRow++;

                worksheet.Cell(currentRow, 1).Value = SiteResources.CreatedBy.ToTitleCase();
                worksheet.Cell(currentRow, 1).Style.Font.SetBold();
                currentRow++;
                worksheet.Cell(currentRow, 1).Value = reportCreatedBy;
                currentRow++;
                currentRow++;

                worksheet.Cell(currentRow, 1).Value = SiteResources.ReportDate.ToTitleCase();
                worksheet.Cell(currentRow, 1).Style.Font.SetBold();
                worksheet.Cell(currentRow, 3).Value = SiteResources.StartDate.ToTitleCase();
                worksheet.Cell(currentRow, 3).Style.Font.SetBold();
                worksheet.Cell(currentRow, 5).Value = SiteResources.EndDate.ToTitleCase();
                worksheet.Cell(currentRow, 5).Style.Font.SetBold();
                currentRow++;

                worksheet.Cell(currentRow, 1).Value = reportDate;
                worksheet.Cell(currentRow, 1).Style.DateFormat.Format = SiteResources.DatePickerFormatView;
                worksheet.Cell(currentRow, 3).Value = apiRequest.FilterStartDate.GetValueOrDefault();
                worksheet.Cell(currentRow, 3).Style.DateFormat.Format = SiteResources.DatePickerFormatView;
                worksheet.Cell(currentRow, 5).Value = apiRequest.FilterEndDate.GetValueOrDefault();
                worksheet.Cell(currentRow, 5).Style.DateFormat.Format = SiteResources.DatePickerFormatView;

                currentRow++;
                currentRow++;

                headerRow = currentRow;
                worksheet.Cell(currentRow, 1).Value = SiteResources.OrderNumber.ToTitleCase();
                worksheet.Cell(currentRow, 2).Value = SiteResources.ReferenceNumber.ToTitleCase();
                worksheet.Cell(currentRow, 3).Value = SiteResources.PassportNumber.ToTitleCase();
                worksheet.Cell(currentRow, 4).Value = SiteResources.NameSurname.ToTitleCase();
                worksheet.Cell(currentRow, 5).Value = SiteResources.Gender.ToTitleCase();
                worksheet.Cell(currentRow, 6).Value = SiteResources.Nationality.ToTitleCase();
                worksheet.Cell(currentRow, 7).Value = SiteResources.BirthDate.ToTitleCase();
                worksheet.Cell(currentRow, 8).Value = SiteResources.VisaCategory.ToTitleCase();
                worksheet.Cell(currentRow, 9).Value = SiteResources.ApplicantType.ToTitleCase();
                worksheet.Cell(currentRow, 10).Value = SiteResources.ApplicationType.ToTitleCase();
                worksheet.Cell(currentRow, 11).Value = SiteResources.ApplicationNote.ToTitleCase();
                worksheet.Cell(currentRow, 12).Value = SiteResources.ProcessedBy.ToTitleCase();
                worksheet.Cell(currentRow, 13).Value = SiteResources.ApplicationDate.ToTitleCase();
                currentRow++;

                var visaCategories = await GetCachedVisaCategories();

                for (int i = 0; i < apiResponse.Data.Data.Count(); i++)
                {
                    var data = apiResponse.Data.Data.ElementAt(i);
                    worksheet.Cell(currentRow, 1).Value = i + 1;
                    worksheet.Cell(currentRow, 2).Value = data.ReferenceNumber;
                    worksheet.Cell(currentRow, 2).DataType = XLDataType.Text;
                    worksheet.Cell(currentRow, 3).Value = data.PassportNumber;
                    worksheet.Cell(currentRow, 3).DataType = XLDataType.Text;
                    worksheet.Cell(currentRow, 4).Value = data.NameSurname;
                    worksheet.Cell(currentRow, 5).Value = EnumHelper.GetEnumDescription(typeof(Gender), data.GenderId.ToString());
                    worksheet.Cell(currentRow, 6).Value = data.Nationality;
                    worksheet.Cell(currentRow, 7).Value = data.BirthDate; 
                    worksheet.Cell(currentRow, 8).Value = GetVisaCategoryNameFromId(data.VisaCategoryId, visaCategories);
                    worksheet.Cell(currentRow, 9).Value = EnumHelper.GetEnumDescription(typeof(ApplicantType), data.ApplicantTypeId.ToString());
                    worksheet.Cell(currentRow, 10).Value = data.ApplicationType;
                    worksheet.Cell(currentRow, 11).Value = data.ApplicationNote;
                    worksheet.Cell(currentRow, 12).Value = data.ProcessBy;
                    worksheet.Cell(currentRow, 13).Value = data.ProcessAt;

                    currentRow++;
                }

                var rangeTable = worksheet.Range(worksheet.Cell(headerRow, 1), worksheet.Cell(currentRow - 1, 13));
                rangeTable.Style.Border.SetTopBorder(XLBorderStyleValues.Thin);
                rangeTable.Style.Border.SetBottomBorder(XLBorderStyleValues.Thin);
                rangeTable.Style.Border.SetLeftBorder(XLBorderStyleValues.Thin);
                rangeTable.Style.Border.SetRightBorder(XLBorderStyleValues.Thin);
                rangeTable.Style.Border.SetInsideBorder(XLBorderStyleValues.Thin);
                rangeTable.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                var rangeAlignment = worksheet.Range(worksheet.Cell(1, 1), worksheet.Cell(currentRow - 1, 13));
                rangeAlignment.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);

                for (int i = 1; i <= 13; i++)
                {
                    worksheet.Column(i).AdjustToContents();
                }

                currentRow++;
                currentRow++;

                #region Totals

                for (int i = 0; i < apiResponse.Data.Groups.Count; i++)
                {
                    var initialTotalRow = currentRow;

                    var group = apiResponse.Data.Groups[i];
                    worksheet.Cell(currentRow, 1).Value = group.MainTitle.ToTitleCase();
                    var rangeItem = worksheet.Range(worksheet.Cell(currentRow, 1), worksheet.Cell(currentRow, 2));
                    rangeItem.Merge().Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center).Alignment.WrapText = true; ;
                    currentRow++;

                    foreach (var data in group.Data)
                    {
                        worksheet.Cell(currentRow, 1).Value = data.Title.ToTitleCase();
                        worksheet.Cell(currentRow, 2).Value = data.Value.ToTitleCase();
                        currentRow++;
                    }

                    var rangeTableGroup = worksheet.Range(worksheet.Cell(initialTotalRow, 1), worksheet.Cell(currentRow - 1, 2));
                    rangeTableGroup.Style.Border.SetTopBorder(XLBorderStyleValues.Thin);
                    rangeTableGroup.Style.Border.SetBottomBorder(XLBorderStyleValues.Thin);
                    rangeTableGroup.Style.Border.SetLeftBorder(XLBorderStyleValues.Thin);
                    rangeTableGroup.Style.Border.SetRightBorder(XLBorderStyleValues.Thin);
                    rangeTableGroup.Style.Border.SetInsideBorder(XLBorderStyleValues.Thin);
                    rangeTableGroup.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                    var rangeAlignmentGroup = worksheet.Range(worksheet.Cell(initialTotalRow, 1), worksheet.Cell(currentRow - 1,2));
                    rangeAlignmentGroup.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);

                    for (int s = 1; s <= 2; s++)
                    {
                        worksheet.Column(s).AdjustToContents();
                    }

                    currentRow++;
                }

                #endregion

                using (var stream = new MemoryStream())
                {
                    workbook.SaveAs(stream);
                    var content = stream.ToArray();
                    return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{reportName} - {reportDate.ToString("ddMMyyyy")}.xlsx");
                }
            }
        }

        #endregion
    }
}

