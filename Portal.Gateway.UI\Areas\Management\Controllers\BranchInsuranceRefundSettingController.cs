﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.UI.Areas.Management.ViewModels.Branch;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.ApiModel.Responses.Management.BranchInsuranceRefundSetting;
using System.Collections.Generic;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.ApiModel.Requests.Management.BranchInsuranceRefundSetting;
using System.Linq;
using Portal.Gateway.UI.Helpers;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class BranchInsuranceRefundSettingController : BaseController<BranchInsuranceRefundSettingController>
    {
        public BranchInsuranceRefundSettingController(IOptions<AppSettings> appSettings, ICacheHelper cacheHelper) : base(appSettings, cacheHelper){}

        #region Add/Update

        public async Task<IActionResult> PartialAddUpdateBranchInsuranceRefundSetting(string encryptedBranchId)
        {
            int id = encryptedBranchId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BranchInsuranceRefundSettingApiResponse>>
                (ApiMethodName.Management.GetBranchInsuranceRefundSetting + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var settingList = new List<BranchInsuranceRefundSettingTypeViewModel>(); 

            foreach (var item in EnumHelper.GetEnumAsDictionary(typeof(BranchInsuranceRefundSettingType)).Where(s => s.Key != (int)BranchInsuranceRefundSettingType.NoRestrictionPolicyRefund))
            {
                var existData = apiResponse.Data?.BranchInsuranceRefundSettings.Find(s => s.TypeId == item.Key);

                settingList.Add(new BranchInsuranceRefundSettingTypeViewModel
                {
                    Name = item.Value,
                    BlockDate = existData != null ? existData.BlockDate : null,
                    TypeId = item.Key,
                    IsBlocked = existData != null ? true : false,
                });
            }

            var viewModel = new AddUpdateBranchInsuranceRefundSettingViewModel
            {
                EncryptedBranchId = encryptedBranchId,
                IsRefundBlockByAllPolicies = apiResponse.Data.IsRefundBlockByAllPolicies,
                RefundBlockByAllPoliciesDate = apiResponse.Data.RefundBlockByAllPoliciesDate,
                RefundBlockByPolicyMailContent = apiResponse.Data.RefundBlockByPolicyMailContent,
                RefundBlockByPolicySmsContent = apiResponse.Data.RefundBlockByPolicySmsContent,
                BranchInsuranceRefundSettings = settingList
            };

            return PartialView("_AddUpdateBranchInsuranceRefundSetting", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddUpdateBranchInsuranceRefundSetting(AddUpdateBranchInsuranceRefundSettingViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            if(viewModel.BranchInsuranceRefundSettings.Any(s => s.IsBlocked && s.BlockDate is null))
            {
                var invalidData = viewModel.BranchInsuranceRefundSettings.Find(s => s.IsBlocked && s.BlockDate == null);
                return Json(new ResultModel { Message = $"{SiteResources.PleaseEnterDateValue} - {invalidData.Name}", ResultType = ResultType.Danger });
            }

            if (viewModel.BranchInsuranceRefundSettings.Any(s => s.IsBlocked) && viewModel.IsRefundBlockByAllPolicies)
            {
                return Json(new ResultModel { Message = EnumResources.InvalidSelectionForPolicyType, ResultType = ResultType.Danger });
            }

            int id = viewModel.EncryptedBranchId.ToDecryptInt();

            var apiRequest = new AddUpdateBranchInsuranceRefundSettingApiRequest
            {
                IsRefundBlockByAllPolicies = viewModel.IsRefundBlockByAllPolicies,
                RefundBlockByAllPoliciesDate = !viewModel.IsRefundBlockByAllPolicies ? null : viewModel.RefundBlockByAllPoliciesDate,
                MailContent = viewModel.RefundBlockByPolicyMailContent,
                SmsContent = viewModel.RefundBlockByPolicySmsContent,
                BranchId = id,
                InsuranceList = viewModel.IsRefundBlockByAllPolicies ? new List<AddUpdateBranchInsuranceRefundSettingApiRequest.RefundBlockEntryDto>() :
                viewModel.BranchInsuranceRefundSettings.Where(s => s.IsBlocked && s.BlockDate is not null).Select(s => new AddUpdateBranchInsuranceRefundSettingApiRequest.RefundBlockEntryDto
                {
                    TypeId = s.TypeId,
                    StartDate = s.BlockDate
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Management.AddUpdateBranchInsuranceRefundSetting, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion
    }
}
