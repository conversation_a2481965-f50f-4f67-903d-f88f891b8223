﻿//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Reflection;
//using System.Threading.Tasks;
//using Kendo.Mvc.Extensions;
//using Kendo.Mvc.UI;
//using Microsoft.AspNetCore.Mvc;
//using Microsoft.Extensions.Caching.Memory;
//using Microsoft.Extensions.Options;
//using Portal.Gateway.ApiModel.Requests.Management.Action;
//using Portal.Gateway.ApiModel.Responses;
//using Portal.Gateway.Common.Utility;
//using Portal.Gateway.Contracts.Attributes;
//using Portal.Gateway.Contracts.Entities.Constants;
//using Portal.Gateway.UI.Config;
//using Portal.Gateway.UI.Constants;
//using Portal.Gateway.UI.Models;

//namespace Portal.Gateway.UI.Controllers
//{
//    public class TestController : BaseController<TestController>
//    {
//        public TestController(IMemoryCache memoryCache,
//                IOptions<AppSettings> appSettings)
//            : base(appSettings, cacheHelper)
//        {

//        }

//        public IActionResult Index()
//        {
//            return View();
//        }

//        public async Task<IActionResult> RoleActions()
//        {
//            if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out object value))
//            {
//                return Json(value);
//            }
//            else
//            {
//                return Content("Error");
//            }
//        }

//        public ActionResult GetGridItems([DataSourceRequest] DataSourceRequest request)
//        {
//            var result = Enumerable.Range(0, 50).Select(i => new TestViewModel
//            {
//                Id = i,
//                Parameter1 = i * 10,
//                Parameter2 = "Parameter " + i,
//                Parameter3 = new DateTime(2016, 9, 15).AddDays(i % 7),
//            });

//            var dsResult = result.ToDataSourceResult(request);
//            return Json(dsResult);
//        }

//        [HttpGet]
//        public async Task<IActionResult> Action()
//        {
//            Assembly asm = Assembly.GetExecutingAssembly();
//            var controlleractionlist = asm.GetTypes()
//                    .Where(type => typeof(Controller).IsAssignableFrom(type))
//                    .SelectMany(type => type.GetMethods(BindingFlags.Instance | BindingFlags.DeclaredOnly | BindingFlags.Public))
//                    .Select(x => new
//                    {
//                        Controller = x.DeclaringType.Name,
//                        Action = x.Name,
//                        Area = x.DeclaringType.CustomAttributes.Where(c => c.AttributeType == typeof(AreaAttribute)),
//                        IsMenuItem = ((ActionAttribute)x.GetCustomAttributes(typeof(ActionAttribute), false).FirstOrDefault())?.IsMenuItem ?? false
//                    }).ToList();
//            var list = new List<ControllerActions>();
//            var insertList = new List<AddActionsApiRequest.ActionApiRequest>();
//            var index = 1;
//            foreach (var item in controlleractionlist)
//            {
//                if (item.Area.Count() != 0)
//                {
//                    /*
//                     * Seed file
//                     */

//                    list.Add(new ControllerActions()
//                    {
//                        Id = index++,
//                        Controller = item.Controller.Replace("Controller", ""),
//                        Method = item.Action,
//                        Area = item.Area.Select(v => v.ConstructorArguments[0].Value.ToString()).FirstOrDefault(),
//                        IsMenuItem = item.IsMenuItem,
//                        IsPublic = false,
//                        Order = null,
//                        IsActive = true,
//                        IsDeleted = false
//                    });

//                    /*
//                     * Auto insert
//                     */

//                    insertList.Add(new AddActionsApiRequest.ActionApiRequest()
//                    {
//                        Area = item.Area.Select(v => v.ConstructorArguments[0].Value.ToString()).FirstOrDefault(),
//                        Controller = item.Controller.Replace("Controller", ""),
//                        Method = item.Action
//                    });
//                }
//                else
//                {
//                    /*
//                     * Seed file
//                     */

//                    list.Add(new ControllerActions()
//                    {
//                        Id = index++,
//                        Controller = item.Controller.Replace("Controller", ""),
//                        Method = item.Action,
//                        Area = null,
//                        IsMenuItem = item.IsMenuItem,
//                        IsPublic = false,
//                        Order = null,
//                        ParentId = null,
//                        IsActive = true,
//                        IsDeleted = false
//                    });

//                    /*
//                     * Auto insert
//                     */

//                    insertList.Add(new AddActionsApiRequest.ActionApiRequest()
//                    {
//                        Area = null,
//                        Controller = item.Controller.Replace("Controller", ""),
//                        Method = item.Action
//                    });
//                }
//            }

//            var apiRequest = new AddActionsApiRequest()
//            {
//                Actions = insertList
//            };

//            /*
//             * Enable for insert actions
//             */

//            //var apiResponse = await PortalHttpClientHelper
//            //    .PostAsJsonAsync<ApiResponse<AddApiResponse>>
//            //    (apiRequest, ApiMethodName.Management.AddActions, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
//            //    .ConfigureAwait(false);

//            return Json(list);
//        }

//        [HttpGet]
//        public IActionResult ActionTranslation()
//        {
//            Assembly asm = Assembly.GetExecutingAssembly();
//            var controlleractionlist = asm.GetTypes()
//                    .Where(type => typeof(Controller).IsAssignableFrom(type))
//                    .SelectMany(type => type.GetMethods(BindingFlags.Instance | BindingFlags.DeclaredOnly | BindingFlags.Public))
//                    .Select(x => new
//                    {
//                        Controller = x.DeclaringType.Name,
//                        Action = x.Name,
//                        Area = x.DeclaringType.CustomAttributes.Where(c => c.AttributeType == typeof(AreaAttribute)),
//                        IsMenuItem = ((ActionAttribute)x.GetCustomAttributes(typeof(ActionAttribute), false).FirstOrDefault())?.IsMenuItem ?? false
//                    }).ToList();
//            var list = new List<ControllerActionTranslations>();
//            var index = 1;
//            foreach (var item in controlleractionlist)
//            {
//                if (item.Area.Count() != 0)
//                {
//                    list.Add(new ControllerActionTranslations()
//                    {
//                        Id = index,
//                        Description = $"{item.Area.Select(v => v.ConstructorArguments[0].Value.ToString()).FirstOrDefault()}/{item.Controller.Replace("Controller", "")}/{item.Action}",
//                        Name = " ",
//                        ActionId = index++,
//                        LanguageId = 1,
//                        IsActive = true,
//                        IsDeleted = false
//                    });
//                }
//                else
//                {
//                    list.Add(new ControllerActionTranslations()
//                    {
//                        Id = index,
//                        Description = $"{item.Area.Select(v => v.ConstructorArguments[0].Value.ToString()).FirstOrDefault()}/{item.Controller.Replace("Controller", "")}/{item.Action}",
//                        Name = " ",
//                        ActionId = index++,
//                        LanguageId = 1,
//                        IsActive = true,
//                        IsDeleted = false
//                    });
//                }
//            }
//            return Json(list);
//        }
//    }

//    public class ControllerActions
//    {
//        public int Id { get; set; }
//        public string Controller { get; set; }
//        public string Method { get; set; }
//        public string Area { get; set; }
//        public bool IsMenuItem { get; set; }
//        public bool IsPublic { get; set; }
//        public int? Order { get; set; }
//        public int? ParentId { get; set; }
//        public bool IsActive { get; set; }
//        public bool IsDeleted { get; set; }
//    }

//    public class ControllerActionTranslations
//    {
//        public int Id { get; set; }
//        public string Name { get; set; }
//        public string Description { get; set; }
//        public int LanguageId { get; set; }
//        public int ActionId { get; set; }
//        public bool IsActive { get; set; }
//        public bool IsDeleted { get; set; }
//    }
//}