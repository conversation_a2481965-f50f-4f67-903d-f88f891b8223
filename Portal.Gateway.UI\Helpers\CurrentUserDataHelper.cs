﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.ApiModel.Responses.Management.RoleAction;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Helpers
{
    public class CurrentUserDataHelper
    {
        private readonly HttpContext _httpContext;
        private readonly ICacheHelper _cacheHelper;

        public CurrentUserDataHelper(ICacheHelper cacheHelper)
        {
            _cacheHelper = cacheHelper;
        }

        public async Task<MenuModel> BuildMenuModel(UserModel currentUser)
        {
            var response = await _cacheHelper.GetRoleActionsAsync();

            var roleFilteredResponse = currentUser.IsSysAdmin ?
                    response.RoleActionSites.Where(i => i.Role.Id != 28) :
                    response.RoleActionSites.Where(p => currentUser.RoleIds.Any(x => x == p.Role.Id)).ToList();

            var menuModel = new MenuModel
            {
                MainMenuList = new List<MainMenu>()
            };

            foreach (var roleActionSite in roleFilteredResponse)
            {
                foreach (var roleAction in roleActionSite.RoleActions.Where(p => p.Action.ParentId == null && p.Action.IsActive && p.Action.IsMenuItem).OrderBy(o => o.Action.Order))
                {
                    if (!menuModel.MainMenuList.Any(p => p.Id == roleAction.Action.Id))
                    {
                        var newMainMenu = new MainMenu
                        {
                            Id = roleAction.Action.Id,
                            Order = roleAction.Action.Order.GetValueOrDefault(),
                            MenuTranslation = roleAction.Action.ActionTranslations.Select(p => new MenuTranslation()
                            {
                                Name = p.Name,
                                LanguageId = p.LanguageId
                            }).ToList(),
                            SubMenu = new List<SubMenu>()
                        };

                        menuModel.MainMenuList.Add(newMainMenu);
                    }
                }

                foreach (var roleAction in roleActionSite.RoleActions.Where(p => menuModel.MainMenuList.Any(p2 => p2.Id == p.Action.ParentId && p.Action.IsActive && p.Action.IsMenuItem)).OrderBy(o => o.Action.Order)) //RoleActions
                {
                    if (!menuModel.MainMenuList.First(p => p.Id == roleAction.Action.ParentId).SubMenu.Any(p => p.Id == roleAction.Action.Id))
                    {
                        var newSubMenu = new SubMenu
                        {
                            Id = roleAction.Action.Id,
                            Order = roleAction.Action.Order.GetValueOrDefault(),
                            MenuTranslation = roleAction.Action.ActionTranslations.Select(p => new MenuTranslation()
                            {
                                Name = p.Name,
                                LanguageId = p.LanguageId
                            }).ToList(),
                            Action = new ActionParam
                            {
                                Area = roleAction.Action.Area,
                                Controller = roleAction.Action.Controller,
                                Method = roleAction.Action.Method,
                            },
                            Node = new List<Node>()
                        };

                        menuModel.MainMenuList.First(p => p.Id == roleAction.Action.ParentId).SubMenu.Add(newSubMenu);
                    }

                    foreach (var node in roleActionSite.RoleActions.Where(p => p.Action.ParentId == roleAction.Action.Id && p.Action.IsActive && p.Action.IsMenuItem).OrderBy(o => o.Action.Order))
                    {
                        if (!menuModel.MainMenuList.First(p => p.Id == roleAction.Action.ParentId).SubMenu.First(p2 => p2.Id == node.Action.ParentId).Node.Any(p => p.Id == node.Action.Id))
                        {
                            var newNode = new Node
                            {
                                Id = node.Action.Id,
                                Order = node.Action.Order.GetValueOrDefault(),
                                Action = new ActionParam
                                {
                                    Id = node.Action.Id,
                                    Area = node.Action.Area,
                                    Controller = node.Action.Controller,
                                    Method = node.Action.Method,

                                },
                                MenuTranslation = node.Action.ActionTranslations.Select(p => new MenuTranslation()
                                {
                                    Name = p.Name,
                                    LanguageId = p.LanguageId
                                }).ToList()
                            };

                            menuModel.MainMenuList.First(p => p.Id == roleAction.Action.ParentId).SubMenu.First(p2 => p2.Id == node.Action.ParentId).Node.Add(newNode);
                        }
                    }
                }
            }

            return menuModel;
        }

        public async Task<List<UserBranchModel>> BuildUserBranches(UserModel currentUser)
        {
            List<UserBranchModel> result = null;

            var cacheItem = await _cacheHelper.GetBranchesAsync();

            if (currentUser.BranchIds.Any())
            {
                if (cacheItem != null && cacheItem.Branches.Any(p => currentUser.BranchIds.Contains(p.Id)))
                {
                    result = cacheItem.Branches.Where(p => currentUser.BranchIds.Contains(p.Id)).Select(p => new UserBranchModel
                    {
                        UserId = currentUser.UserId,
                        BranchId = p.Id,
                        CountryId = p.Country.Id,
                        BranchNames = p.BranchTranslations.OrderBy(o => o.Name).ToDictionary(p2 => p2.LanguageId, p2 => $"{p.Country.Name} - {p2.Name}")
                    }).ToList();
                }
            }

            return result;
        }
    }
}
