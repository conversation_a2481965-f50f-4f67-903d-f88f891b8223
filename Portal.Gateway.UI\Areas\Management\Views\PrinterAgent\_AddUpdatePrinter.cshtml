﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@using Kendo.Mvc.TagHelpers
@model Portal.Gateway.UI.Areas.Management.ViewModels.PrinterAgent.AddUpdatePrinterInfoViewModel

<form id="formAddUpdatePrinter" class="card card-custom card-stretch form">
    @Html.HiddenFor(m => Model.PrinterAgentId)
    @Html.HiddenFor(m => Model.PrinterAgentName)
    @Html.HiddenFor(m => Model.PrinterId)
    
    <div class="card-body">
        <div class="row">
            <div class="col-xl-12">
                <div class="form-group row">
                    <div class="col-lg-3">
                        <label class="font-weight-bold">@SiteResources.Name.ToTitleCase()</label>
                        @(Html.Kendo().DropDownListFor(m => m.Name)
                            .HtmlAttributes(new { @class = "form-control" })
                            .OptionLabel(SiteResources.Select)
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .DataSource(source =>
                            {
                                source.Read(read =>
                                {
                                    read.Action("GetClientPrinterList", "PrinterAgent", new { Area = "Management", PrinterAgentName = Model.PrinterAgentName });
                                });
                            }))
                        @Html.ValidationMessageFor(m => m.Name)
                    </div>
                    <div class="col-lg-3">
                        <label class="font-weight-bold">@SiteResources.PrinterType.ToTitleCase()</label>
                        @(Html.Kendo().DropDownListFor(m => m.PrinterTypeId)
                            .HtmlAttributes(new { @class = "form-control" })
                            .Events(events => events.Change("onChangePrinterType"))
                            .OptionLabel(SiteResources.Select)
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .DataSource(source =>
                            {
                                source.Read(read =>
                                {
                                    read.Action("GetPrinterTypes", "PrinterAgent", new { Area = "Management" });
                                });
                            }))
                        @Html.ValidationMessageFor(m => m.PrinterTypeId)
                    </div>
                    <div class="col-lg-3">
                        <label class="font-weight-bold">@SiteResources.CopyCount.ToTitleCase()</label>
                        @(Html.Kendo().NumericTextBoxFor(m => m.CopyCount).Min(1).Decimals(0).Format("0").HtmlAttributes(new { @style = "width: 100px;" }))
                        @Html.ValidationMessageFor(m => m.CopyCount)
                    </div>
                    <div class="col-lg-3" id="icrLanguage">
                        <label class="font-weight-bold">@SiteResources.SelectLanguage.ToTitleCase()</label>
                        @(Html.Kendo().DropDownListFor(m => m.ICRTypeId)
                            .HtmlAttributes(new { @class = "form-control" })
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .DataSource(source =>
                            {
                                source.Read(read =>
                                {
                                    read.Action("GetPrinterICRLanguages", "Parameter", new { Area = "" });
                                });
                            }))
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">@SiteResources.Close</button>
        <button type="submit" class="btn btn-primary font-weight-bold">@SiteResources.Save</button>
    </div>
</form>
<partial name="Scripts/_ValidationScripts" />

<script src="~/js/Management/PrinterAgent/PrinterAgent.js"></script>
<script src="~/js/site.js"></script>

<script>
    $(function () {
        onChangePrinterType();
    });
</script>
