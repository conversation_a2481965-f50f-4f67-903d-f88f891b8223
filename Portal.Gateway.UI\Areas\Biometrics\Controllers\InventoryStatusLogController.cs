﻿using Gateway.Extensions;
using Gateway.Http;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.QmsFolder;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.UI.Areas.Biometrics.Models.Inventory.RequestModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.Inventory.ViewModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.InventoryStatusLog;
using Portal.Gateway.UI.Areas.Biometrics.Models.InventoryStatusLog.RequestModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.InventoryStatusLog.Results;
using Portal.Gateway.UI.Areas.Biometrics.Models.InventoryStatusLog.ViewModels;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Biometrics.Controllers
{

    [Area("Biometrics")]
    public class InventoryStatusLogController : BaseController<InventoryController>
    {
        public InventoryStatusLogController(
            IOptions<AppSettings> appSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper) { }



        #region Get



        [Action(IsMenuItem = true)]
        public IActionResult Panel()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedInventoryStatusLogs([DataSourceRequest] DataSourceRequest request, FilterInventoryStatusLogViewModel filterViewModel)
        {
            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedInventoryApiRequest
            {
                FilterInventoryTypeId = filterViewModel.FilterInventoryId,
                Pagination = new QmsPaginationApiRequest
                {
                    PageNumber = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = "Id",
                    Ascending = true
                }
            };

            var apiResponse = await RestHttpClient.Create().Post<GetPaginatedInventoryStausLogsResult>(
                AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetPaginatedInventories, headers, apiRequest);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var paginatedData = apiResponse.Data.Select(p =>
                new InventoryStatusLogViewModel
                {
                    //EncryptedId = p.Id.ToEncrypt(),
                    Status = p.Status,
                    CabinId = p.CabinId,
                    CountryId = p.CountryId,
                    HostName = p.HostName,
                    InventoryId = p.InventoryId,
                    Inventory = p.Inventory

                }).ToList();

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.TotalNumberOfRecords });
        }


        public async Task<IActionResult> GetLastInventoryStatusLogsForEachInventoriesOfCabin([DataSourceRequest] DataSourceRequest request, FilterLastInventoryStatusLogsForEachInventoriesOfCabinViewModel filterViewModel)
        {
            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");


            var apiRequest = new LastInventoryStatusLogsForEachInventoriesOfCabinRequest
            {
                CabinId = filterViewModel.FilterCabinId ?? 0
            };

            var apiResponse = await RestHttpClient.Create().Get<LastInventoryStatusLogsForEachInventoriesOfCabinResult>(
                AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetLastInventoryStatusLogsForEachInventoriesOfCabin + apiRequest.CabinId, headers);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            //List<InventoryStatusLogDto> paginatedData = apiResponse.Data.ToList();

            LastInventoryStatusLogsOfCabin resultData = null;
            try
            {
                resultData = apiResponse.Data.CastSafe<LastInventoryStatusLogsOfCabin>();
            }
            catch (Exception ex)
            {

            }


            return Json(new DataSourceResult { Data = resultData.LastLogsOfEachInventories });
        }

        #endregion



    }

}
