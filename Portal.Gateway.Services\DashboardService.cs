﻿using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities.Dto.Dashboard.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Dashboard.Responses;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Contracts.Services;
using Portal.Gateway.Contracts.Settings.AppSettings;
using Portal.Gateway.Contracts.UnitOfWork;
using Portal.Gateway.Entity.Context;
using Portal.Gateway.Entity.Entities.Portal;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Data;
using Portal.Gateway.Common.Utility.Extensions;

namespace Portal.Gateway.Services
{
    public class DashboardService : BaseService, IDashboardService
    {
        private readonly AppSettings _appSettings;
        private readonly IUnitOfWork<PortalDbContext> _unitOfWorkPortalDb;

        public DashboardService(
            IOptions<AppSettings> appSettings,
            IUnitOfWork<PortalDbContext> unitOfWorkPortalDb) : base(appSettings)
        {
            _appSettings = appSettings.Value;
            _unitOfWorkPortalDb = unitOfWorkPortalDb;
        }

        #region General

        /// <summary>
        /// For all dashboards
        /// </summary>
        public async Task<ActiveCountriesResponseDto> GetActiveCountriesAsync()
        {
            var existingBranches = await _unitOfWorkPortalDb.GetRepository<Branch>().Entities
                                   .Include(p => p.Country)
                                   .Include(p => p.BranchTranslations)
                                   .AsNoTracking()
                                   .Where(p => !p.IsDeleted && p.IsActive)
                                   .Select(q => new
                                   {
                                       CountryId = q.CountryId,
                                       CountryCode = q.Country.ISO2,
                                       CountryName = q.Country.Name,
                                       BranchId = q.Id,
                                       BranchName = q.BranchTranslations.FirstOrDefault(p => p.LanguageId == Language.English.ToInt()).Name
                                   }).ToListAsync();

            return new ActiveCountriesResponseDto
            {
                CountryList = existingBranches.GroupBy(q => q.CountryId).Select(p => new ActiveCountriesResponseDto.CountryResponseDto()
                {
                    CountryId = p.Key,
                    CountryCode = p.First().CountryCode,
                    CountryName = p.First().CountryName,
                    BranchList = p.Select(t => new ActiveCountriesResponseDto.CountryResponseDto.BranchResponseDto()
                    {
                        BranchId = t.BranchId,
                        BranchName = t.BranchName
                    })
                }).ToList()
            };
        }

        /// <summary>
        /// For all dashboards, indicates daily application statistics
        /// </summary>
        public async Task<DailyApplicationStatsResponseDto> GetDailyApplicationStatsAsync(GetStatsRequestDto request)
        {
            var tomorrow = DateTime.Today.AddDays(1);
            var today = DateTime.Today;

            var queryApplications = _unitOfWorkPortalDb.GetRepository<Application>().Entities
                                        .Include(p => p.BranchApplicationCountry)
                                        .ThenInclude(p => p.Branch)
                                        .ThenInclude(p => p.BranchTranslations)
                                        .AsNoTracking()
                                        .Where(p => !p.IsDeleted && p.IsActive && p.ApplicationTime < tomorrow && p.ApplicationTime >= today &&
                                            p.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled)
                                        .Select(q => new
                                        {
                                            q.BranchApplicationCountry.BranchId,
                                            q.CreatedBy
                                        });

            if (request.BranchIds.Count() > 0)
                queryApplications = queryApplications.Where(p => request.BranchIds.Any(q => q == p.BranchId));

            if (request.UserId.HasValue)
                queryApplications = queryApplications.Where(p => p.CreatedBy == request.UserId.Value);

            var response = await queryApplications.GroupBy(p => new { p.BranchId, p.CreatedBy })
                .Select(p => new
                {
                    p.Key.BranchId,
                    p.Key.CreatedBy,
                    Count = p.Count()
                }).ToListAsync();

            return new DailyApplicationStatsResponseDto
            {
                StatsDate = DateTime.UtcNow,
                BranchList = response.GroupBy(p => p.BranchId).Select(p => new DailyApplicationStatsResponseDto.BranchStatsResponseDto()
                {
                    ApplicationCount = p.Sum(q => q.Count),
                    BranchId = p.Key,
                    AgentApplicationStats = p.GroupBy(q => q.CreatedBy).Select(q => new DailyApplicationStatsResponseDto.StatsDto()
                    {
                        Id = q.Key,
                        Value = q.Sum(w => w.Count)
                    }).ToList()
                }).ToList()
            };
        }

        #endregion

        #region Joint

        /// <summary>
        /// For agent and supervisor dashboards
        /// Chairman will query this service for display all branches' quarter stats in modal, not in dashboard
        /// </summary>
        public async Task<ApplicationStatsResponseDto> GetPeriodicApplicationStatsAsync(GetStatsRequestDto request)
        {
            var today = DateTime.Today;

            var weeklyStart = today.AddDays(-7);
            var weekReportRange = Enumerable.Range(0, 7).Select(p => weeklyStart.AddDays(p));

            var dayCount = Convert.ToInt32((today.AddDays(-1) - today.AddDays(-1).AddMonths(-3)).TotalDays);
            var quarterReportRange = Enumerable.Range(0, dayCount).Select(p => today.AddMonths(-3).AddDays(p));

            var queryApplications = _unitOfWorkPortalDb.GetRepository<Application>().Entities
                                        .Include(p => p.BranchApplicationCountry)
                                        .Include(p => p.ApplicationDocument)
                                        .OrderBy(p => p.ApplicationTime)
                                        .AsNoTracking()
                                        .Where(p => !p.IsDeleted && p.IsActive && p.ApplicationTime < today && p.ApplicationTime >= quarterReportRange.First() &&
                                            p.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled)
                                        .Select(q => new
                                        {
                                            q.BranchApplicationCountry.BranchId,
                                            q.CreatedBy,
                                            q.ApplicationTime
                                        });

            if (request.BranchIds.Count() > 0)
                queryApplications = queryApplications.Where(p => request.BranchIds.Any(q => q == p.BranchId));

            if (request.UserId.HasValue)
                queryApplications = queryApplications.Where(p => p.CreatedBy == request.UserId.Value);

            var response = await queryApplications.GroupBy(p => new { p.BranchId, p.CreatedBy, p.ApplicationTime })
                .Select(p => new
                {
                    BranchId = p.Key.BranchId,
                    CreatedBy = p.Key.CreatedBy,
                    Date = p.Key.ApplicationTime.Date,
                    Count = p.Count()
                }).ToListAsync();

            return new ApplicationStatsResponseDto()
            {
                Stats = new List<ApplicationStatsResponseDto.StatsResponseDto>()
                {
                    new ApplicationStatsResponseDto.StatsResponseDto()
                    {
                        StatsType = (int)DashboardRangeTypes.Weekly,
                        BranchList = response.GroupBy(q => q.BranchId).Select(p => new ApplicationStatsResponseDto.StatsResponseDto.BranchStatsResponseDto()
                        {
                            BranchId = p.Key,
                            ApplicationStats = weekReportRange.Select(z => new ApplicationStatsResponseDto.DailyStatsDto() {
                                Date = z,
                                Value = p.Where(k => k.Date == z.Date).Sum(q => q.Count)
                            }).ToList(),
                            AgentList = p.GroupBy(k => k.CreatedBy).Select(k => new ApplicationStatsResponseDto.StatsResponseDto.BranchStatsResponseDto.AgentStatsResponseDto()
                            {
                                AgentId = k.Key,
                                ApplicationStats = weekReportRange.Select(z => new ApplicationStatsResponseDto.DailyStatsDto() {
                                    Date = z,
                                    Value = k.Where(s => s.Date == z.Date).Sum(q => q.Count)
                                }).ToList()
                            }).ToList()
                        }).ToList()
                    },

                    new ApplicationStatsResponseDto.StatsResponseDto()
                    {
                        StatsType = (int)DashboardRangeTypes.Quarter,
                        BranchList = response.GroupBy(q => q.BranchId).Select(p => new ApplicationStatsResponseDto.StatsResponseDto.BranchStatsResponseDto()
                        {
                            BranchId = p.Key,
                            ApplicationStats = quarterReportRange.Select(z => new ApplicationStatsResponseDto.DailyStatsDto() {
                                Date = z,
                                Value = p.Where(k => k.Date == z.Date).Sum(q => q.Count)
                            }).ToList(),
                            AgentList = p.GroupBy(k => k.CreatedBy).Select(k => new ApplicationStatsResponseDto.StatsResponseDto.BranchStatsResponseDto.AgentStatsResponseDto()
                            {
                                AgentId = k.Key,
                                ApplicationStats = quarterReportRange.Select(z => new ApplicationStatsResponseDto.DailyStatsDto() {
                                    Date = z,
                                    Value = k.Where(s => s.Date == z.Date).Sum(q => q.Count)
                                }).ToList()
                            }).ToList()
                        }).ToList()
                    }
                }
            };
        }

        /// <summary>
        /// For agent and supervisor dashboards
        /// Chairman will query this service for display all branches' quarter stats in modal, not in dashboard
        /// </summary>
        public async Task<ExtraFeeStatsResponseDto> GetExtraFeeStatsAsync(GetStatsRequestDto request)
        {
            var today = DateTime.UtcNow;

            var dayCount = Convert.ToInt32((today.AddDays(-1) - today.AddDays(-1).AddMonths(-1)).TotalDays);
            var quarterReportRange = Enumerable.Range(0, dayCount).Select(p => today.AddMonths(-1).AddDays(p));

            var queryExtraFees = _unitOfWorkPortalDb.GetRepository<ExtraFee>().Entities
                                        .AsNoTracking()
                                        .Include(p => p.ExtraFeeTranslations)
                                        .Select(q => new
                                        {
                                            q.Id,
                                            q.ExtraFeeTranslations.FirstOrDefault(q => q.LanguageId == request.LanguageId).Name
                                        });

            var queryApplications = _unitOfWorkPortalDb.GetRepository<ApplicationExtraFee>().Entities
                                        .Include(p => p.ExtraFee)
                                        .Include(p => p.Application)
                                        .ThenInclude(p => p.BranchApplicationCountry)
                                        .AsNoTracking()
                                        .Where(p => !p.IsDeleted && p.IsActive && p.ExtraFee.Category != (int)ExtraFeeCategoryType.Application &&
                                            p.Application.IsActive && !p.Application.IsDeleted && p.Application.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled &&
                                            p.Application.ApplicationTime <= quarterReportRange.Last().AddDays(1) && p.Application.ApplicationTime >= quarterReportRange.First())
                                        .Select(q => new
                                        {
                                            q.Application.BranchApplicationCountry.BranchId,
                                            q.ExtraFeeId,
                                            q.Application.CreatedBy
                                        });

            if (request.BranchIds.Count() > 0)
                queryApplications = queryApplications.Where(p => request.BranchIds.Any(q => q == p.BranchId));

            if (request.UserId.HasValue)
                queryApplications = queryApplications.Where(p => p.CreatedBy == request.UserId.Value);

            var response = await queryApplications.GroupBy(p => new { p.BranchId, p.CreatedBy, p.ExtraFeeId })
                .Select(p => new
                {
                    BranchId = p.Key.BranchId,
                    CreatedBy = p.Key.CreatedBy,
                    ExtraFeeId = p.Key.ExtraFeeId,
                    Count = p.Count()
                }).ToListAsync();

            var result = new ExtraFeeStatsResponseDto()
            {
                Stats = new List<ExtraFeeStatsResponseDto.StatsResponseDto>()
                {
                    new ExtraFeeStatsResponseDto.StatsResponseDto()
                    {
                        BranchList = response.GroupBy(q => q.BranchId).Select(p => new ExtraFeeStatsResponseDto.StatsResponseDto.BranchStatsResponseDto()
                        {
                            BranchId = p.Key,
                            Stats = p.GroupBy(w => w.ExtraFeeId).Select(m => new ExtraFeeStatsResponseDto.StatsDto()
                            {
                                Name = queryExtraFees.First(z => z.Id == m.Key).Name,
                                Value = m.Sum(w => w.Count)
                            }),
                            AgentList = p.GroupBy(k => k.CreatedBy).Select(k => new ExtraFeeStatsResponseDto.StatsResponseDto.BranchStatsResponseDto.AgentStatsResponseDto()
                            {
                                AgentId = k.Key,
                                Stats = k.GroupBy(w => w.ExtraFeeId).Select(m => new ExtraFeeStatsResponseDto.StatsDto()
                                {
                                    Name = queryExtraFees.FirstOrDefault(z => z.Id == m.Key).Name,
                                    Value = m.Sum(w => w.Count)
                                })
                            }).ToList()
                        }).ToList()
                    },
                }
            };

            return result;
        }

        /// <summary>
        /// For chairman and supervisor dashboards
        /// The service will be used for branch based datail report in modal tab chairman
        /// </summary>
        public async Task<StatsFromBeginningResponseDto> GetStatsFromBeginningAsync(GetStatsRequestDto request)
        {
            var today = DateTimeOffset.Now;

            var queryApplications = _unitOfWorkPortalDb.GetRepository<Application>().Entities
                            .Include(p => p.BranchApplicationCountry)
                            .Include(p => p.ApplicationDocument)
                            .AsNoTracking()
                            .Where(p => !p.IsDeleted && p.IsActive && p.ApplicationTime < today &&
                                p.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled)
                            .Select(q => new
                            {
                                q.ApplicationStatusId,
                                q.BranchApplicationCountry.BranchId,
                                q.ApplicationDocument.VisaCategoryId
                            });

            if (request.BranchIds.Count() > 0)
                queryApplications = queryApplications.Where(p => request.BranchIds.Any(q => q == p.BranchId));

            var selectedStatusList = EnumHelper.GetEnumAsDictionary(typeof(DashboardFromBeginningStatusType)).Select(p => new SelectListItem()
            {
                Text = p.Value,
                Value = p.Key.ToString()
            }).ToList(); // TODO: AZALTILACAK

            var response = await queryApplications.GroupBy(p => new { p.BranchId, p.ApplicationStatusId, p.VisaCategoryId })
                            .Select(p => new
                            {
                                BranchId = p.Key.BranchId,
                                ApplicationStatusId = p.Key.ApplicationStatusId,
                                VisaCategoryId = p.Key.VisaCategoryId,
                                Count = p.Count()
                            }).ToListAsync();

            var result = new StatsFromBeginningResponseDto()
            {
                BranchList = response.GroupBy(p => p.BranchId).Select(p => new StatsFromBeginningResponseDto.BranchStatsResponseDto()
                {
                    BranchId = p.Key,
                    Value = p.Sum(w => w.Count),
                    EntryBannedApplications = p.Where(q => q.VisaCategoryId == (int)VisaCategoryTypeEnum.EntryBanned &&
                                                        (q.ApplicationStatusId != (int)ApplicationStatusType.DeliveredToApplicant ||
                                                        q.ApplicationStatusId != (int)ApplicationStatusType.OutscanToCourrier ||
                                                        q.ApplicationStatusId != (int)ApplicationStatusType.HandDeliveredToApplicantAtEmbassy ||
                                                        q.ApplicationStatusId != (int)ApplicationStatusType.RejectedPassportDeliveredToCourier ||
                                                        q.ApplicationStatusId != (int)ApplicationStatusType.RejectionWithCountryEntryBanned ||
                                                        q.ApplicationStatusId != (int)ApplicationStatusType.RejectionRefundDone)).Sum(w => w.Count),
                    StatusList = selectedStatusList.Select(q => new StatsFromBeginningResponseDto.BranchStatsResponseDto.StatsDto()
                    {
                        TypeId = Convert.ToInt32(q.Value),
                        Value = p.Where(w => w.ApplicationStatusId == Convert.ToInt32(q.Value)).Sum(w => w.Count)
                    }).ToList(),
                }).ToList()
            };

            return result;
        }

        #endregion

        #region Supervisor

        /// <summary>
        /// For only supervisor dashboard
        /// The service can be used for branch based datail report in modal tab chairman
        /// </summary>
        public async Task<DailyPassportDeliveryStatsResponseDto> GetDailyPassportDeliveryStatsAsync(GetStatsRequestDto request)
        {
            var tomorrow = DateTime.Today.AddDays(1);
            var today = DateTime.Today;

            var queryApplications = _unitOfWorkPortalDb.GetRepository<Application>().Entities
                            .Include(p => p.BranchApplicationCountry)
                            .AsNoTracking()
                            .Where(p => !p.IsDeleted && p.IsActive &&
                                p.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled &&
                                p.ApplicationTime < tomorrow && p.ApplicationTime >= today &&
                                (p.ApplicationStatusId == (int)ApplicationStatusType.DeliveredToApplicant ||
                                p.ApplicationStatusId == (int)ApplicationStatusType.RejectionRefundDone ||
                                p.ApplicationStatusId == (int)ApplicationStatusType.OutscanToCourrier ||
                                p.ApplicationStatusId == (int)ApplicationStatusType.HandDeliveredToApplicantAtEmbassy ||
                                p.ApplicationStatusId == (int)ApplicationStatusType.RejectionWithCountryEntryBanned ||
                                p.ApplicationStatusId == (int)ApplicationStatusType.RejectedPassportDeliveredToCourier))
                            .Select(q => new
                            {
                                q.ApplicationStatusId,
                                q.BranchApplicationCountry.BranchId,
                            });

            if (request.BranchIds.Count() > 0)
                queryApplications = queryApplications.Where(p => request.BranchIds.Any(q => q == p.BranchId));

            var response = await queryApplications.GroupBy(p => new { p.ApplicationStatusId })
                            .Select(p => new { ApplicationStatusId = p.Key.ApplicationStatusId, Count = p.Count() }).ToListAsync();

            var result = new DailyPassportDeliveryStatsResponseDto()
            {
                Total = response.Sum(q => q.Count),
                Stats = response.Select(q => new DailyPassportDeliveryStatsResponseDto.StatsDto()
                {
                    ApplicationStatusType = q.ApplicationStatusId,
                    Value = q.Count
                }).ToList()
            };

            return result;
        }

        /// <summary>
        /// For only supervisor dashboard
        /// The service can be used for branch based datail report in modal tab chairman
        /// </summary>
        public async Task<BranchInsuranceStatsResonseDto> GetBranchInsuranceStatsAsync(GetStatsRequestDto request)
        {
            var today = DateTime.Today;

            var queryApplications = _unitOfWorkPortalDb.GetRepository<ApplicationInsurance>().Entities
                            .Include(p => p.Application)
                            .ThenInclude(p => p.BranchApplicationCountry)
                            .AsNoTracking()
                            .Where(p => !p.IsDeleted && p.IsActive &&
                                p.Application.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled)
                            .Select(q => new
                            {
                                q.Application.BranchApplicationCountry.BranchId,
                                IsActive = q.EndDate >= today
                            });

            if (request.BranchIds.Count() > 0)
                queryApplications = queryApplications.Where(p => request.BranchIds.Any(q => q == p.BranchId));

            var response = await queryApplications.GroupBy(p => new { p.BranchId, p.IsActive })
                .Select(p => new
                {
                    BranchId = p.Key.BranchId,
                    IsActive = p.Key.IsActive,
                    Count = p.Count()
                }).ToListAsync();

            var result = new BranchInsuranceStatsResonseDto()
            {
                Branches = response.GroupBy(p => p.BranchId).Select(q => new BranchInsuranceStatsResonseDto.BranchDto()
                {
                    BranchId = q.Key,
                    Active = q.Where(p => p.IsActive).Sum(w => w.Count),
                    Total = q.Sum(w => w.Count)
                }).ToList()
            };

            return result;
        }

        /// <summary>
        /// For only supervisor dashboard
        /// The service can be used for branch based datail report in modal tab chairman
        /// </summary>
        public async Task<PreviousDayGeneralStatsResponseDto> GetOfficeManagerPreviousDayGeneralStatsAsync(GetStatsRequestDto request)
        {
            var yesterday = DateTime.Today.AddDays(-1);

            var queryApplications = await _unitOfWorkPortalDb.GetRepository<Application>().Entities
                            .Include(i => i.BranchApplicationCountry)
                            .AsNoTracking()
                            .Where(p => p.IsActive && !p.IsDeleted &&
                                        p.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled &&
                                        p.ApplicationTime >= yesterday && p.ApplicationTime < yesterday.AddDays(1))
                            .Select(p => new
                            {
                                BranchId = p.BranchApplicationCountry.BranchId,
                                ApplicantBirthDate = p.BirthDate,
                                ApplicantGenderId = p.GenderId,
                                ApplicationTypeId = p.ApplicationTypeId,
                            }).ToListAsync();

            var applicationList = queryApplications.Where(p => request.BranchIds.Any(q => q == p.BranchId)).ToList();

            var applicationTypeList = EnumHelper.GetEnumAsDictionary(typeof(ApplicationType)).Select(p => new
            {
                Value = p.Key
            }).ToList();

            return new PreviousDayGeneralStatsResponseDto()
            {
                StatsDate = yesterday,
                BranchList = applicationList.GroupBy(b => b.BranchId).Select(b => new PreviousDayGeneralStatsResponseDto.BranchResponse()
                {
                    BranchId = b.Key,
                    Total = b.Count(),
                    ApplicationTypes = applicationTypeList.Select(p => new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                    {
                        Type = p.Value,
                        Count = applicationList.Where(q => q.BranchId == b.Key && q.ApplicationTypeId == p.Value).Count()
                    }).ToList()
                }).ToList()
            };
        }

        #endregion

        #region Chairman

        /// <summary>
        /// For only chairman dashboards
        /// </summary>
        public async Task<OfficersResponseDto> GetOfficersAsync()
        {
            var queryUserBranches = _unitOfWorkPortalDb.GetRepository<User>().Entities
                                        .AsNoTracking()
                                        .Where(p => !p.IsDeleted && p.IsActive).Count();

            var result = new OfficersResponseDto()
            {
                TotalCount = queryUserBranches
                // No need to send branchlist for now
            };

            return result;
        }

        /// <summary>
        /// For only chairman
        /// </summary>
        public async Task<QuarterApplicationSummaryResponseDto> GetQuarterApplicationSummaryAsync(GetStatsRequestDto request)
        {
            var today = DateTime.Today;
            var quarterStarts = DateTime.Today.AddMonths(-3);

            var queryApplications = _unitOfWorkPortalDb.GetRepository<Application>().Entities
                            .Include(p => p.BranchApplicationCountry)
                            .Include(p => p.ApplicationDocument)
                            .AsNoTracking()
                            .Where(p => !p.IsDeleted && p.IsActive && p.ApplicationTime < today && p.ApplicationTime >= quarterStarts &&
                                p.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled)
                            .Select(q => new
                            {
                                q.BranchApplicationCountry.BranchId,
                                q.ApplicationDocument.VisaCategoryId
                            });

            if (request.BranchIds.Count() > 0)
                queryApplications = queryApplications.Where(p => request.BranchIds.Any(q => q == p.BranchId));


            var entityVisaType = await _unitOfWorkPortalDb.GetRepository<VisaType>().Entities
                               .Include(p => p.VisaTypeTranslations)
                               .AsNoTracking()
                               .Where(p => p.IsActive && !p.IsDeleted)
                               .ToListAsync();


            Dictionary<int, string> visaCategoryTypes = entityVisaType.ToDictionary(s => s.Id, s => s.VisaTypeTranslations.Where(q => q.LanguageId == request.LanguageId).Select(q => q.Name).FirstOrDefault());

            var response = await queryApplications.GroupBy(p => new { p.BranchId, p.VisaCategoryId })
                            .Select(p => new
                            {
                                BranchId = p.Key.BranchId,
                                VisaCategoryId = p.Key.VisaCategoryId,
                                Count = p.Count()
                            }).ToListAsync();

            var result = new QuarterApplicationSummaryResponseDto()
            {
                StartDate = today.AddDays(-1).Date,
                EndDate = quarterStarts.Date,
                BranchList = response.GroupBy(p => p.BranchId).Select(p => new QuarterApplicationSummaryResponseDto.BranchStatsResponseDto()
                {
                    BranchId = p.Key,
                    TotalApplications = p.Sum(q => q.Count),
                    VisaCategoryList = visaCategoryTypes.Select(q => new QuarterApplicationSummaryResponseDto.BranchStatsResponseDto.VisaCategoryResponseDto()
                    {
                        VişaCategoryId = Convert.ToInt32(q.Key),
                        Total = p.Where(w => w.VisaCategoryId == Convert.ToInt32(q.Key)).Sum(w => w.Count)
                    }).ToList()
                }).ToList()
            };

            return result;
        }

        /// <summary>
        /// For only chairman
        /// Quarter branch based insurance stats, insurance extre fee type stats and relational age range report (according to defined extra fee insurance types) are provided
        /// </summary>
        public async Task<InsuranceStatsResponseDto> GetInsuranceStatsAsync(GetStatsRequestDto request)
        {
            var today = DateTime.Today;
            var quarterStarts = DateTime.Today.AddMonths(-3);

            var queryExtraFees = await _unitOfWorkPortalDb.GetRepository<ExtraFee>().Entities
                                .Include(i => i.ExtraFeeTranslations)
                                .AsNoTracking()
                                .Where(i => i.Category == (int)ExtraFeeCategoryType.Insurance && i.IsActive && !i.IsDeleted)
                                .Select(q => new
                                {
                                    q.Id,
                                    q.ExtraFeeTranslations
                                }).ToListAsync();

            var queryInsurance = _unitOfWorkPortalDb.GetRepository<ApplicationInsurance>().Entities
                                .Include(i => i.Application)
                                    .ThenInclude(i => i.ApplicationExtraFees)
                                    .ThenInclude(i => i.ExtraFee)
                                .Include(i => i.Application)
                                    .ThenInclude(i => i.BranchApplicationCountry)
                                .AsNoTracking()
                                .Where(i => i.Application.IsActive && !i.Application.IsDeleted &&
                                    i.Application.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled &&
                                    i.Application.ApplicationExtraFees.Any(p => p.ExtraFee.Category == (int)ExtraFeeCategoryType.Insurance && p.IsActive) &&
                                    i.Application.ApplicationTime < today && i.Application.ApplicationTime >= quarterStarts)
                                .Select(q => new
                                {
                                    q.Application.ApplicationExtraFees.First(w => w.ExtraFee.Category == (int)ExtraFeeCategoryType.Insurance).ExtraFeeId,
                                    q.Application.BranchApplicationCountry.BranchId
                                });

            var insuranceList = await queryInsurance.GroupBy(p => new { p.BranchId, p.ExtraFeeId })
                                                    .Select(p => new { BranchId = p.Key.BranchId, ExtraFeeId = p.Key.ExtraFeeId, Count = p.Count() }).ToListAsync();

            var result = new InsuranceStatsResponseDto()
            {
                StartDate = quarterStarts.Date,
                EndDate = today.Date,
                InsuranceTypes = queryExtraFees.Select(q => new InsuranceStatsResponseDto.InsuranceTypeDto()
                {
                    InsuranceName = q.ExtraFeeTranslations.Any(w => w.LanguageId == request.LanguageId) ?
                        q.ExtraFeeTranslations.First(w => w.LanguageId == request.LanguageId).Name :
                        q.ExtraFeeTranslations.FirstOrDefault().Name,
                    InsuranceId = q.Id
                }).ToList(),
                BranchList = insuranceList.GroupBy(q => q.BranchId).Select(p => new InsuranceStatsResponseDto.BranchResponseDto()
                {
                    BranchId = p.Key,
                    TotalCount = p.Sum(q => q.Count),
                    InsuranceList = p.GroupBy(q => q.ExtraFeeId).Select(q => new InsuranceStatsResponseDto.BranchResponseDto.InsuranceResponseDto()
                    {
                        InsuranceId = q.Key,
                        Count = q.Sum(w => w.Count)
                    })
                }).ToList()
            };

            return result;
        }

        /// <summary>
        /// For only chairman
        /// Daily branch based insurance stats, insurance extre fee type stats and relational age range report (according to defined extra fee insurance types) are provided
        /// </summary>
        public async Task<InsuranceStatsResponseDto> GetDailyInsuranceStatsAsync(GetStatsRequestDto request)
        {
            var today = DateTime.Today;

            var queryExtraFees = await _unitOfWorkPortalDb.GetRepository<ExtraFee>().Entities
                                .Include(i => i.ExtraFeeTranslations)
                                .AsNoTracking()
                                .Where(i => i.Category == (int)ExtraFeeCategoryType.Insurance && i.IsActive && !i.IsDeleted)
                                .Select(q => new
                                {
                                    q.Id,
                                    q.ExtraFeeTranslations
                                }).ToListAsync();

            var queryInsurance = _unitOfWorkPortalDb.GetRepository<ApplicationInsurance>().Entities
                                .Include(i => i.Application)
                                    .ThenInclude(i => i.ApplicationExtraFees)
                                    .ThenInclude(i => i.ExtraFee)
                                .Include(i => i.Application)
                                    .ThenInclude(i => i.BranchApplicationCountry)
                                .Where(i => i.Application.IsActive && !i.Application.IsDeleted &&
                                    i.Application.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled &&
                                    i.Application.ApplicationExtraFees.Any(p => p.ExtraFee.Category == (int)ExtraFeeCategoryType.Insurance && p.IsActive) &&
                                    i.Application.ApplicationTime < today.AddDays(1) && i.Application.ApplicationTime >= today)
                                .AsNoTracking()
                                .Select(q => new
                                {
                                    q.Application.ApplicationExtraFees.First(w => w.ExtraFee.Category == (int)ExtraFeeCategoryType.Insurance).ExtraFeeId,
                                    q.Application.BranchApplicationCountry.BranchId
                                });

            var insuranceList = await queryInsurance.GroupBy(p => new { p.BranchId, p.ExtraFeeId })
                                                    .Select(p => new { BranchId = p.Key.BranchId, ExtraFeeId = p.Key.ExtraFeeId, Count = p.Count() }).ToListAsync();

            var result = new InsuranceStatsResponseDto()
            {
                InsuranceTypes = queryExtraFees.Select(q => new InsuranceStatsResponseDto.InsuranceTypeDto()
                {
                    InsuranceName = q.ExtraFeeTranslations.Any(w => w.LanguageId == request.LanguageId) ?
                            q.ExtraFeeTranslations.First(w => w.LanguageId == request.LanguageId).Name :
                            q.ExtraFeeTranslations.FirstOrDefault().Name,
                    InsuranceId = q.Id
                }).ToList(),
                BranchList = insuranceList.GroupBy(q => q.BranchId).Select(p => new InsuranceStatsResponseDto.BranchResponseDto()
                {
                    BranchId = p.Key,
                    TotalCount = p.Sum(q => q.Count),
                    InsuranceList = p.GroupBy(q => q.ExtraFeeId).Select(q => new InsuranceStatsResponseDto.BranchResponseDto.InsuranceResponseDto()
                    {
                        InsuranceId = q.Key,
                        Count = q.Sum(w => w.Count)
                    })
                }).ToList()
            };

            return result;
        }

        /// <summary>
        /// For only chairman
        /// Provides active insurance counts of all branches
        /// </summary>
        public async Task<InsuranceStatsResponseDto> GetActiveInsuranceStatsAsync(GetStatsRequestDto request)
        {
            var today = DateTime.Today;

            var queryExtraFees = await _unitOfWorkPortalDb.GetRepository<ExtraFee>().Entities
                                .Include(i => i.ExtraFeeTranslations)
                                .Where(i => i.Category == (int)ExtraFeeCategoryType.Insurance && i.IsActive && !i.IsDeleted)
                                .AsNoTracking()
                                .Select(q => new
                                {
                                    q.Id,
                                    q.ExtraFeeTranslations
                                }).ToListAsync();

            var queryInsurance = _unitOfWorkPortalDb.GetRepository<ApplicationInsurance>().Entities
                                .Include(i => i.Application)
                                    .ThenInclude(i => i.ApplicationExtraFees)
                                    .ThenInclude(i => i.ExtraFee)
                                .Include(i => i.Application)
                                    .ThenInclude(i => i.BranchApplicationCountry)
                                .AsNoTracking()
                                .Where(i => i.Application.IsActive && !i.Application.IsDeleted &&
                                    i.Application.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled &&
                                    i.Application.ApplicationExtraFees.Any(p => p.ExtraFee.Category == (int)ExtraFeeCategoryType.Insurance && p.IsActive) &&
                                    i.EndDate >= today)
                                .Select(q => new
                                {
                                    q.Application.ApplicationExtraFees.First(w => w.ExtraFee.Category == (int)ExtraFeeCategoryType.Insurance).ExtraFeeId,
                                    q.Application.BranchApplicationCountry.BranchId
                                });

            var insuranceList = await queryInsurance.GroupBy(p => new { p.BranchId, p.ExtraFeeId })
                                                    .Select(p => new { BranchId = p.Key.BranchId, ExtraFeeId = p.Key.ExtraFeeId, Count = p.Count() }).ToListAsync();

            var result = new InsuranceStatsResponseDto()
            {
                InsuranceTypes = queryExtraFees.Select(q => new InsuranceStatsResponseDto.InsuranceTypeDto()
                {
                    InsuranceName = q.ExtraFeeTranslations.Any(w => w.LanguageId == request.LanguageId) ?
                        q.ExtraFeeTranslations.First(w => w.LanguageId == request.LanguageId).Name :
                        q.ExtraFeeTranslations.FirstOrDefault().Name,
                    InsuranceId = q.Id
                }).ToList(),
                BranchList = insuranceList.GroupBy(q => q.BranchId).Select(p => new InsuranceStatsResponseDto.BranchResponseDto()
                {
                    BranchId = p.Key,
                    TotalCount = p.Sum(q => q.Count),
                    InsuranceList = p.GroupBy(q => q.ExtraFeeId).Select(q => new InsuranceStatsResponseDto.BranchResponseDto.InsuranceResponseDto()
                    {
                        InsuranceId = q.Key,
                        Count = q.Sum(w => w.Count)
                    })
                }).ToList()
            };

            return result;
        }

        /// <summary>
        /// For only chairman
        /// Compares all branhes' monthly insurance sales between current month and previous
        /// </summary>
        public async Task<MonthComparativeInsuranceResponseDto> GetInsuranceMonthComparisonStatsAsync(GetStatsRequestDto request)
        {
            var today = DateTime.Today;

            var startOfMonth = new DateTime(today.Year, today.Month, 1);

            var queryInsurance = _unitOfWorkPortalDb.GetRepository<ApplicationInsurance>().Entities
                .Include(i => i.Application)
                .ThenInclude(i => i.ApplicationExtraFees)
                .ThenInclude(i => i.ExtraFee)
                .Include(i => i.Application)
                .ThenInclude(i => i.BranchApplicationCountry)
                .AsNoTracking()
                .Where(i => i.Application.IsActive && !i.Application.IsDeleted &&
                            i.Application.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled &&
                            i.Application.ApplicationExtraFees.Any(p => p.ExtraFee.Category == (int)ExtraFeeCategoryType.Insurance && p.IsActive) &&
                            i.Application.ApplicationTime >= startOfMonth.AddMonths(-1))
                .Select(q => new
                {
                    q.Application.BranchApplicationCountry.BranchId,
                    Month = q.Application.ApplicationTime.Month,
                    Year = q.Application.ApplicationTime.Year,
                    q.Application.ApplicationTime
                });

            var insuranceList = await queryInsurance.GroupBy(p => new { p.BranchId, p.Month, p.Year,p.ApplicationTime })
                .Select(p => new {
                    BranchId = p.Key.BranchId,
                    Month = p.Key.Month,
                    Year = p.Key.Year,
                    ApplicationTime = p.Key.ApplicationTime,
                    Count = p.Count()
                }).ToListAsync();


            var result = new MonthComparativeInsuranceResponseDto()
            {
                Branches = insuranceList.GroupBy(q => q.BranchId).Select(p => new MonthComparativeInsuranceResponseDto.BranchDto()
                {
                    BranchId = p.Key,
                    Months = new List<MonthComparativeInsuranceResponseDto.BranchDto.MonthDto>()
                    {
                        new MonthComparativeInsuranceResponseDto.BranchDto.MonthDto()
                        {
                            Month = startOfMonth.AddMonths(-1).Month,
                            Total = p.Sum(q => q.Count)
                        },
                        new MonthComparativeInsuranceResponseDto.BranchDto.MonthDto()
                        {
                            Month = startOfMonth.Month,
                            Total = p.Sum(q => q.Count)
                        }
                    }
                }).ToList()
            };

            return result;
        }

        /// <summary>
        /// For only chairman
        /// Provides previous day general application stats
        /// </summary>
        public async Task<PreviousDayGeneralStatsResponseDto> GetPreviousDayGeneralStatsAsync(GetStatsRequestDto request)
        {
            var yesterday = DateTime.Today.AddDays(-1);

            var applicationList = await _unitOfWorkPortalDb.GetRepository<Application>().Entities
                            .Include(i => i.BranchApplicationCountry)
                            .Include(i => i.ApplicationExtraFees)
                                .ThenInclude(i => i.ExtraFee)
                                .ThenInclude(i => i.ExtraFeeTranslations)
                            .AsNoTracking()
                            .Where(p => p.IsActive && !p.IsDeleted &&
                                        p.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled &&
                                        p.ApplicationTime >= yesterday && p.ApplicationTime < yesterday.AddDays(1))
                            .Select(p => new
                            {
                                BranchId = p.BranchApplicationCountry.BranchId,
                                ApplicantBirthDate = p.BirthDate,
                                ApplicantGenderId = p.GenderId,
                                ApplicationTypeId = p.ApplicationTypeId,
                                ExtraFeeIds = p.ApplicationExtraFees.Where(q => p.IsActive).Select(q => new
                                {
                                    q.ExtraFeeId,
                                    q.ExtraFee.Category,
                                    q.ExtraFee.ExtraFeeTranslations.FirstOrDefault().Name,
                                    q.IsActive,
                                    q.Price
                                })
                            }).ToListAsync();

            var applicationTypeList = EnumHelper.GetEnumAsDictionary(typeof(ApplicationType)).Select(p => new
            {
                Value = p.Key
            }).ToList();

            return new PreviousDayGeneralStatsResponseDto()
            {
                StatsDate = yesterday,
                BranchList = applicationList.GroupBy(b => b.BranchId).Select(b => new PreviousDayGeneralStatsResponseDto.BranchResponse()
                {
                    BranchId = b.Key,
                    Total = b.Count(),
                    ApplicationTypes = applicationTypeList.Select(p => new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                    {
                        Type = p.Value,
                        Count = applicationList.Where(q => q.BranchId == b.Key && q.ApplicationTypeId == p.Value).Count()
                    }).ToList(),
                    ApplicantAgeRanges = new List<PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse>()
                    {
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)ReportAgeRanges.Zero_Fifteen,
                            Count = applicationList.Where(q => q.BranchId == b.Key && q.ApplicantBirthDate >= DateTime.Today.AddYears(-(int)ReportAgeRanges.Zero_Fifteen)).Count()
                        },
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)ReportAgeRanges.Fifteen_TwentyFive,
                            Count = applicationList.Where(q => q.BranchId == b.Key && q.ApplicantBirthDate < DateTime.Today.AddYears(-(int)ReportAgeRanges.Zero_Fifteen) && q.ApplicantBirthDate >= DateTime.Today.AddYears(-(int)ReportAgeRanges.Fifteen_TwentyFive)).Count()
                        },
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)ReportAgeRanges.TwentyFive_Fifty,
                            Count = applicationList.Where(q => q.BranchId == b.Key && q.ApplicantBirthDate < DateTime.Today.AddYears(-(int)ReportAgeRanges.Fifteen_TwentyFive) && q.ApplicantBirthDate >= DateTime.Today.AddYears(-(int)ReportAgeRanges.TwentyFive_Fifty)).Count()
                        },
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)ReportAgeRanges.MoreThanFifty,
                            Count = applicationList.Where(q => q.BranchId == b.Key && q.ApplicantBirthDate < DateTime.Today.AddYears(-(int)ReportAgeRanges.TwentyFive_Fifty)).Count()
                        }
                    },
                    ApplicantGenders = new List<PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse>()
                    {
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)Gender.Female,
                            Count = applicationList.Where(q => q.BranchId == b.Key && q.ApplicantGenderId == (int)Gender.Female).Count()
                        },
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)Gender.Male,
                            Count = applicationList.Where(q => q.BranchId == b.Key && q.ApplicantGenderId == (int)Gender.Male).Count()
                        }
                    },
                    ApplicationProcessTypes = new List<PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse>()
                    {
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)ReportApplicationProcessTypes.VIP,
                            Count = applicationList.Where(q => q.BranchId == b.Key && q.ExtraFeeIds.Any(p => p.Name.Trim().ToUpper().Equals("VIP"))).Count()
                        },
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)ReportApplicationProcessTypes.PrimeTime,
                            Count = applicationList.Where(q => q.BranchId == b.Key && q.ExtraFeeIds.Any(p => p.Name.ToUpper().Contains("PRIME") || p.Name.ToUpper().Contains("PRİME"))).Count()
                        },
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)ReportApplicationProcessTypes.Normal,
                            Count = applicationList.Where(q => q.BranchId == b.Key &&
                                                               !q.ExtraFeeIds.Any(p => p.Name.Trim().ToUpper().Equals("VIP")) &&
                                                               !q.ExtraFeeIds.Any(p => p.Name.ToUpper().Contains("PRIME")) &&
                                                               !q.ExtraFeeIds.Any(p => p.Name.ToUpper().Contains("PRİME"))).Count()
                        }
                    },
                    FreeApplications = new List<PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse>()
                    {
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)ReportFreeApplicationType.FreeServiceFee,
                            Count = applicationList.Where(q => q.BranchId == b.Key && (!q.ExtraFeeIds.Any(p => p.Category == (int)ExtraFeeCategoryType.ServiceFee && p.IsActive) ||
                                                     q.ExtraFeeIds.Where(p => p.Category == (int)ExtraFeeCategoryType.ServiceFee && p.IsActive)?.Sum(w => w.Price) == 0)).Count()
                        },
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)ReportFreeApplicationType.FreeVisaFee,
                            Count =  applicationList.Where(q => q.BranchId == b.Key && (!q.ExtraFeeIds.Any(p => p.Category == (int)ExtraFeeCategoryType.Application && p.IsActive)  ||
                                                     q.ExtraFeeIds.Where(p => p.Category == (int)ExtraFeeCategoryType.Application && p.IsActive)?.Sum(w => w.Price) == 0)).Count()
                        },
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)ReportFreeApplicationType.NonApplicationInsurance,
                            Count =  applicationList.Where(q => q.BranchId == b.Key && q.ApplicationTypeId == (int)ApplicationType.NonApplicationInsurance).Count()
                        },
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)ReportFreeApplicationType.NonApplicationPCR,
                            Count =  applicationList.Where(q => q.BranchId == b.Key && q.ApplicationTypeId == (int)ApplicationType.NonApplicationPcr).Count()
                        }
                    }
                }).ToList()
            };
        }

        /// <summary>
        /// For only chairman
        /// Period in request model indicates the length of data in months
        /// </summary>
        public async Task<PeriodicExtraFeeStatsResponseDto> GetPeriodicExtraFeeStatsAsync(GetPeriodicExtraFeeStatsRequestDto request)
        {
            var yesterday = DateTime.Today.AddDays(-1);

            var queryExtraFees = _unitOfWorkPortalDb.GetRepository<ExtraFee>().Entities
                                        .Include(p => p.ExtraFeeTranslations)
                                        .AsNoTracking()
                                        .Select(q => new
                                        {
                                            q.Id,
                                            q.ExtraFeeTranslations.FirstOrDefault(q => q.LanguageId == request.LanguageId).Name
                                        });

            var queryApplications = _unitOfWorkPortalDb.GetRepository<ApplicationExtraFee>().Entities
                                        .Include(p => p.ExtraFee)
                                        .Include(p => p.Application)
                                        .ThenInclude(p => p.BranchApplicationCountry)
                                        .AsNoTracking()
                                        .Where(p => !p.IsDeleted && p.IsActive && p.ExtraFee.Category != (int)ExtraFeeCategoryType.Application &&
                                            p.Application.IsActive && !p.Application.IsDeleted && p.Application.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled &&
                                            p.Application.ApplicationTime <= yesterday && p.Application.ApplicationTime >= yesterday.AddMonths(-request.Period))
                                        .Select(q => new
                                        {
                                            q.Application.BranchApplicationCountry.BranchId,
                                            q.ExtraFeeId
                                        });

            var response = await queryApplications.GroupBy(p => new { p.BranchId, p.ExtraFeeId })
                .Select(p => new
                {
                    BranchId = p.Key.BranchId,
                    ExtraFeeId = p.Key.ExtraFeeId,
                    Count = p.Count()
                }).ToListAsync();

            var result = new PeriodicExtraFeeStatsResponseDto()
            {
                StartDate = yesterday.AddMonths(-request.Period),
                EndDate = yesterday,
                ExtraFees = response.GroupBy(q => q.ExtraFeeId).Select(q => new PeriodicExtraFeeStatsResponseDto.ExtraFeeTypesDto()
                {
                    Name = queryExtraFees.FirstOrDefault(w => w.Id == q.Key).Name,
                    ExtraFeeId = q.Key
                }).ToList(),
                BranchList = response.GroupBy(q => q.BranchId).Select(q => new PeriodicExtraFeeStatsResponseDto.BranchStatsResponseDto()
                {
                    BranchId = q.Key,
                    Stats = q.GroupBy(w => w.ExtraFeeId).Select(w => new PeriodicExtraFeeStatsResponseDto.BranchStatsResponseDto.StatsDto()
                    {
                        Value = w.Sum(t => t.Count),
                        ExtraFeeId = w.Key
                    }).ToList()
                }).ToList()
            };

            return result;
        }

        /// <summary>
        /// For only chairman
        /// Service will return quarter application counts of all branches grouped by date
        /// </summary>
        public async Task<QuarterApplicationStatsResponseDto> GetPeriodicAllBranchesApplicationStatsAsync(GetStatsRequestDto request)
        {
            var today = DateTime.Today;

            var weeklyStart = today.AddDays(-7);
            var weekReportRange = Enumerable.Range(0, 7).Select(p => weeklyStart.AddDays(p));

            var dayCount = Convert.ToInt32((today.AddDays(-1) - today.AddDays(-1).AddMonths(-3)).TotalDays);
            var quarterReportRange = Enumerable.Range(0, dayCount).Select(p => today.AddMonths(-3).AddDays(p));

            var queryApplications = _unitOfWorkPortalDb.GetRepository<Application>().Entities
                                        .Include(p => p.BranchApplicationCountry)
                                        .Include(p => p.ApplicationDocument)
                                        .OrderBy(p => p.ApplicationTime)
                                        .AsNoTracking()
                                        .Where(p => !p.IsDeleted && p.IsActive && p.ApplicationTime < today && p.ApplicationTime >= quarterReportRange.First() &&
                                            p.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled)
                                        .Select(q => new
                                        {
                                            q.BranchApplicationCountry.BranchId,
                                            q.ApplicationTime
                                        });

            var response = await queryApplications.GroupBy(p => new { p.BranchId, p.ApplicationTime })
                .Select(p => new
                {
                    BranchId = p.Key.BranchId,
                    Date = p.Key.ApplicationTime.Date,
                    Count = p.Count()
                }).ToListAsync();

            return new QuarterApplicationStatsResponseDto()
            {
                StartDate = quarterReportRange.First().Date,
                EndDate = today.AddDays(-1),
                BranchList = response.GroupBy(q => q.BranchId).Select(p => new QuarterApplicationStatsResponseDto.BranchStatsResponseDto()
                {
                    BranchId = p.Key,
                    ApplicationStats = quarterReportRange.Select(z => new QuarterApplicationStatsResponseDto.BranchStatsResponseDto.DailyStatsDto()
                    {
                        Date = z,
                        Value = p.Where(k => k.Date == z.Date).Sum(q => q.Count)
                    }).ToList(),
                }).ToList()
            };
        }

        /// <summary>
        /// For only chairman
        /// Application type and status shanges of previous day
        /// </summary>
        public async Task<PreviousDayChangeStatsReponseDto> GetPreviousDayChangeStatsAsync(GetStatsRequestDto request)
        {
            var yesterday = DateTime.Today.AddDays(-1);

            var branches = await _unitOfWorkPortalDb.GetRepository<Branch>().Entities
                            .AsNoTracking()
                            .Where(p => p.IsActive && !p.IsDeleted)
                            .Select(q => new
                            {
                                q.Id
                            }).ToListAsync();

            var applicationTypeChanged = _unitOfWorkPortalDb.GetRepository<ApplicationHistory>().Entities
                            .Include(p => p.Application)
                                .ThenInclude(p => p.BranchApplicationCountry)
                            .AsNoTracking()
                            .Where(p => p.IsActive && !p.IsDeleted &&
                                        EF.Functions.ILike(p.PropertyName, $"%ApplicationTypeId%") &&
                                        p.CreatedAt >= yesterday && p.CreatedAt < yesterday.AddDays(1))
                            .Select(q => new
                            {
                                q.Application.BranchApplicationCountry.BranchId
                            });

            var applicationCancellationQuery = _unitOfWorkPortalDb.GetRepository<ApplicationCancellation>().Entities
                            .Include(p => p.Application)
                                .ThenInclude(p => p.BranchApplicationCountry)
                            .AsNoTracking()
                            .Where(p => p.IsActive && !p.IsDeleted && p.CancellationStatusId == (int)ApplicationCancellationStatus.Approved &&
                                        (p.Application.StatusId == (int)Contracts.Entities.Enums.ApplicationStatus.PartiallyRefunded ||
                                        p.Application.StatusId == (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled) &&
                                        p.CreatedAt >= yesterday && p.CreatedAt < yesterday.AddDays(1))
                            .Select(q => new
                            {
                                q.Application.StatusId,
                                q.Application.BranchApplicationCountry.BranchId
                            });

            var typeChanges = await applicationTypeChanged.GroupBy(p => new { p.BranchId })
                .Select(p => new
                {
                    BranchId = p.Key.BranchId,
                    Count = p.Count()
                }).ToListAsync();

            var statusChanges = await applicationCancellationQuery.GroupBy(p => new { p.BranchId, p.StatusId })
                .Select(p => new
                {
                    BranchId = p.Key.BranchId,
                    StatusId = p.Key.StatusId,
                    Count = p.Count()
                }).ToListAsync();

            return new PreviousDayChangeStatsReponseDto()
            {
                StatsDate = yesterday,
                BranchList = branches.Select(q => new PreviousDayChangeStatsReponseDto.BranchResponseDto()
                {
                    BranchId = q.Id,
                    ChangeTypes = new List<PreviousDayChangeStatsReponseDto.BranchResponseDto.StatsResponseDto>()
                    {
                        new PreviousDayChangeStatsReponseDto.BranchResponseDto.StatsResponseDto()
                        {
                            Type = (int)DashboardApplicationChangeTypes.TypeChanged,
                            Count = typeChanges.Where(p => p.BranchId == q.Id).Sum(p => p.Count)
                        },
                        new PreviousDayChangeStatsReponseDto.BranchResponseDto.StatsResponseDto()
                        {
                            Type = (int)DashboardApplicationChangeTypes.Cancelled,
                            Count = statusChanges.Where(p => p.BranchId == q.Id && p.StatusId == (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled).Sum(p => p.Count)
                        },
                        new PreviousDayChangeStatsReponseDto.BranchResponseDto.StatsResponseDto()
                        {
                            Type = (int)DashboardApplicationChangeTypes.Refunded,
                            Count = statusChanges.Where(p => p.BranchId == q.Id && p.StatusId == (int)Contracts.Entities.Enums.ApplicationStatus.PartiallyRefunded).Sum(p => p.Count)
                        }
                    }
                })
            };
        }
        public async Task<AverageIstizanResultTimeApiResponseDto> GetAverageIstizanResultTimeAsync(GetStatsRequestDto request)
        {
            var today = DateTime.Today;
            TimeSpan totalAverage = new TimeSpan(0, 0, 0, 0); ;
            TimeSpan totalAverageTime;
            string averageIstizanResultTime;
            var totalApplicantCount = 0;
            /*var istizanApplicationStatusesList = await _unitOfWorkPortalDb.GetRepository<ApplicationStatusHistory>().Entities
                           .Where(p => p.IsActive && !p.IsDeleted && p.CreatedAt < today && p.ApplicationStatusId == 5)
                           .Select(q => new
                           {
                               q.ApplicationId,
                               q.CreatedAt
                           }).ToListAsync();

            var vacApplicationStatusesList = await _unitOfWorkPortalDb.GetRepository<ApplicationStatusHistory>().Entities
                           .Where(p => p.IsActive && !p.IsDeleted && p.CreatedAt < today && p.ApplicationStatusId == 13)
                           .Select(q => new
                           {
                               q.ApplicationId,
                               q.CreatedAt
                           }).ToListAsync();

            for (int i = 0; i < istizanApplicationStatusesList.Count; i++)
            {
                var applicationIstizanResult = vacApplicationStatusesList.Where(p => p.ApplicationId == istizanApplicationStatusesList[i].ApplicationId).ToList();

                if (applicationIstizanResult.Any())
                {
                    totalAverage = totalAverage + (applicationIstizanResult[0].CreatedAt - istizanApplicationStatusesList[i].CreatedAt);
                    totalApplicantCount += 1;
                }

            }
            totalAverageTime = totalAverage.Divide(totalApplicantCount);

            averageIstizanResultTime = totalAverageTime.Days.ToString();*/
            return new AverageIstizanResultTimeApiResponseDto()
            {
                AverageIstizanResultTime = "N/A"
            };
        }
        public async Task<AverageNormalApplicationResultTimeApiResponseDto> GetAverageNormalApplicationResultTimeAsync(GetStatsRequestDto request)
        {
            var today = DateTime.Today;
            TimeSpan totalAverage = new TimeSpan(0, 0, 0, 0); ;
            TimeSpan totalAverageTime;
            string averageNormalApplicationResultTime;
            var totalApplicantCount = 0;
            /*var applicationTakenStatusesList = await _unitOfWorkPortalDb.GetRepository<ApplicationStatusHistory>().Entities
                           .Where(p => p.IsActive && !p.IsDeleted && p.CreatedAt < today && p.ApplicationStatusId == 1)
                           .Select(q => new
                           {
                               q.ApplicationId,
                               q.CreatedAt
                           }).ToListAsync();

            var handedOverEmbassyApplicationStatusesList = await _unitOfWorkPortalDb.GetRepository<ApplicationStatusHistory>().Entities
                           .Where(p => p.IsActive && !p.IsDeleted && p.CreatedAt < today && p.ApplicationStatusId == 25)
                           .Select(q => new
                           {
                               q.ApplicationId,
                               q.CreatedAt
                           }).ToListAsync();

            for (int i = 0; i < applicationTakenStatusesList.Count; i++)
            {
                var applicationNormalResult = handedOverEmbassyApplicationStatusesList.Where(p => p.ApplicationId == applicationTakenStatusesList[i].ApplicationId).ToList();

                if (applicationNormalResult.Any())
                    {
                        totalAverage = totalAverage + (applicationNormalResult[0].CreatedAt - applicationTakenStatusesList[i].CreatedAt);
                        totalApplicantCount += 1;
                    }
                
            }
            totalAverageTime = totalAverage.Divide(totalApplicantCount);

            averageNormalApplicationResultTime = totalAverageTime.Days.ToString();*/
            return new AverageNormalApplicationResultTimeApiResponseDto()
            {
                AverageNormalApplicationResultTime = "N/A"
            };
        }
        #endregion

        #region Consular

        /// <summary>
        /// For consular
        /// Provides previous day general application stats
        /// </summary>
        public async Task<PreviousDayGeneralStatsResponseDto> GetConsularPreviousDayGeneralStatsAsync(GetStatsRequestDto request)
        {
            var yesterday = DateTime.Today.AddDays(-1);

            var queryApplications = await _unitOfWorkPortalDb.GetRepository<Application>().Entities
                            .Include(i => i.BranchApplicationCountry)
                            .AsNoTracking()
                            .Where(p => p.IsActive && !p.IsDeleted &&
                                        p.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled &&
                                        p.ApplicationTime >= yesterday && p.ApplicationTime < yesterday.AddDays(1))
                            .Select(p => new
                            {
                                BranchId = p.BranchApplicationCountry.BranchId,
                                ApplicantBirthDate = p.BirthDate,
                                ApplicantGenderId = p.GenderId,
                                ApplicationTypeId = p.ApplicationTypeId,
                            }).ToListAsync();

            var applicationList = queryApplications.Where(p => request.BranchIds.Any(q => q == p.BranchId)).ToList();

            var applicationTypeList = EnumHelper.GetEnumAsDictionary(typeof(ApplicationType)).Select(p => new
            {
                Value = p.Key
            }).ToList();

            return new PreviousDayGeneralStatsResponseDto()
            {
                StatsDate = yesterday,
                BranchList = applicationList.GroupBy(b => b.BranchId).Select(b => new PreviousDayGeneralStatsResponseDto.BranchResponse()
                {
                    BranchId = b.Key,
                    Total = b.Count(),
                    ApplicationTypes = applicationTypeList.Select(p => new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                    {
                        Type = p.Value,
                        Count = applicationList.Where(q => q.BranchId == b.Key && q.ApplicationTypeId == p.Value && (q.ApplicationTypeId == (int)ApplicationType.Turquois || q.ApplicationTypeId == (int)ApplicationType.TurquoisPremium || q.ApplicationTypeId == (int)ApplicationType.TurquoisGratis)).Count()
                    }).ToList(),
                    ApplicantAgeRanges = new List<PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse>()
                    {
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)ReportAgeRanges.Zero_Fifteen,
                            Count = applicationList.Where(q => q.BranchId == b.Key && q.ApplicantBirthDate >= DateTime.Today.AddYears(-(int)ReportAgeRanges.Zero_Fifteen)).Count()
                        },
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)ReportAgeRanges.Fifteen_TwentyFive,
                            Count = applicationList.Where(q => q.BranchId == b.Key && q.ApplicantBirthDate < DateTime.Today.AddYears(-(int)ReportAgeRanges.Zero_Fifteen) && q.ApplicantBirthDate >= DateTime.Today.AddYears(-(int)ReportAgeRanges.Fifteen_TwentyFive)).Count()
                        },
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)ReportAgeRanges.TwentyFive_Fifty,
                            Count = applicationList.Where(q => q.BranchId == b.Key && q.ApplicantBirthDate < DateTime.Today.AddYears(-(int)ReportAgeRanges.Fifteen_TwentyFive) && q.ApplicantBirthDate >= DateTime.Today.AddYears(-(int)ReportAgeRanges.TwentyFive_Fifty)).Count()
                        },
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)ReportAgeRanges.MoreThanFifty,
                            Count = applicationList.Where(q => q.BranchId == b.Key && q.ApplicantBirthDate < DateTime.Today.AddYears(-(int)ReportAgeRanges.TwentyFive_Fifty)).Count()
                        }
                    },
                    ApplicantGenders = new List<PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse>()
                    {
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)Gender.Female,
                            Count = applicationList.Where(q => q.BranchId == b.Key && q.ApplicantGenderId == (int)Gender.Female).Count()
                        },
                        new PreviousDayGeneralStatsResponseDto.BranchResponse.StatsResponse()
                        {
                            Type = (int)Gender.Male,
                            Count = applicationList.Where(q => q.BranchId == b.Key && q.ApplicantGenderId == (int)Gender.Male).Count()
                        }
                    },
                }).ToList()
            };
        }

        #endregion
    }
}
