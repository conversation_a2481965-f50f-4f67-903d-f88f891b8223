﻿using System;
using System.Collections.Generic;

namespace Portal.Gateway.UI.Areas.NotificationManagement.ViewModels.Notification
{
    public class NotificationViewModel
    {
        public NotificationViewModel()
        {
            Translations = new List<NotificationTranslationViewModel>();
        }
        public string EncryptedId { get; set; }
        public int StatusId { get; set; }
        public string NotificationNumber { get; set; }
        public string NotificationTitle { get; set; }
        public string Status { get; set; }
        public string Location { get; set; }
        public string Nationality { get; set; }
        public string SendTime { get; set; }
        public string CreatedBy { get; set; }
        public string Languages { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? ScheduledAt { get; set; }
        public DateTime? SendAt { get; set; }
        public List<NotificationTranslationViewModel> Translations { get; set; }
    }

    public class NotificationTranslationViewModel
    {
        public bool IsActive { get; set; }
        public int Id { get; set; }
        public string Language { get; set; }
        public int LanguageId { get; set; }
        public string Subject { get; set; }
        public string Name { get; set; }
    }
}
