﻿using BarcodeStandard;
using DocumentFormat.OpenXml.Presentation;
using ExcelDataReader;
using Gateway.Barcode;
using Gateway.Extensions;
using Gateway.Http;
using Gateway.ObjectStoring;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.Appointment.Application;
using Portal.Gateway.ApiModel.Requests.Appointment.PhotoBooth;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Appointment.Application;
using Portal.Gateway.ApiModel.Responses.Appointment.Cargo;
using Portal.Gateway.ApiModel.Responses.Appointment.PhotoBooth;
using Portal.Gateway.ApiModel.Responses.Management.BranchIcrNote;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.Application;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.PDF;
using Portal.Gateway.UI.Areas.Cargo.Models.Cargo.Requests;
using Portal.Gateway.UI.Areas.Cargo.Models.Cargo.Responses;
using Portal.Gateway.UI.Areas.QMS.Models.QMS.Responses;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using SkiaSharp;
using SkiaSharp.QrCode;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using static Portal.Gateway.ApiModel.Responses.Appointment.Application.ApplicationPrintAllApiResponse;
using Application = Portal.Gateway.UI.Areas.Cargo.Models.Cargo.Requests.Application;
using ImageFormat = System.Drawing.Imaging.ImageFormat;

namespace Portal.Gateway.UI.Areas.Appointment.Controllers
{
    [Area("Appointment")]
    public class PDFController : BaseController<PDFController>
    {
        private readonly IWebHostEnvironment _environment;
        private readonly IFileStorage _fileStorage;

        public PDFController(
            ICacheHelper cacheHelper,
            IWebHostEnvironment environment,
            IOptions<AppSettings> appSettings,
            IFileStorage fileStorage,
            IConfiguration configuration)
            : base(appSettings, cacheHelper)
        {
            _environment = environment;
            _fileStorage = fileStorage;
        }

        public async Task<IActionResult> GetBarcode(string encryptedId, string direction)
        {
            //test commit
            if (string.IsNullOrEmpty(encryptedId) && string.IsNullOrEmpty(direction))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var view = await GetBarcode_(encryptedId, direction);
            return base.Content(view.ToString(), "text/html", Encoding.UTF8);
        }

        public async Task<string> GetBarcode_(string encryptedId, string direction)
        {
            var applicationId = encryptedId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BarcodeApiResponse>>
                ($"{ApiMethodName.Appointment.GetBarcode + applicationId}/{LanguageId}", AppSettings.PrintData.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            var visaCategories = await GetCachedVisaCategories();

            var viewModel = new BarcodeViewModel
            {
                MainApplicationId = applicationId,
                BranchCountryId = apiResponse.Data.BranchCountryId,
                BranchName = apiResponse.Data.BranchName,
                Country = apiResponse.Data.Country,
                AgencyName = apiResponse.Data.AgencyName,
                ApplicantCount = apiResponse.Data.Applicants.Count.ToString(),
                Applicants = apiResponse.Data.Applicants.OrderBy(q => q.Id).Select(q => new BarcodeViewModel.Applicant()
                {
                    ApplicationId = q.Id,
                    EncryptedId = q.Id.ToEncrypt(),
                    PassportNumber = q.PassportNumber,
                    Name = q.Name,
                    Surname = q.Surname,
                    Address = q.Address,
                    PhoneNumber1 = q.PhoneNumber,
                    PhoneNumber2 = q.PhoneNumber2,
                    ApplicationDate = q.ApplicationTime.DateTime,
                    Title = EnumHelper.GetEnumDescription(typeof(Title), q.TitleId.ToString()),
                    Application = q.ExtraFees.Any(p => p.Category == (int)ExtraFeeCategoryType.Application) ? q.ExtraFees.FirstOrDefault(p => p.Category == (int)ExtraFeeCategoryType.Application)?.ExtraFeeName : string.Empty,
                    VisaCategory = GetVisaCategoryNameFromId(q.VisaCategoryId, visaCategories),
                    IsVIP = q.ExtraFees.Any(p => p.Category == (int)ExtraFeeCategoryType.Other && p.ExtraFeeName.Trim().ToUpper().Equals("VIP")),
                    ApplicantTypeId = q.ApplicantTypeId,
                    CreateBarcode = CreateBarcode_(q.Id.ToEncrypt()),
                    IsCargoDelivery = apiResponse.Data.BranchCountryId == 80 ?
                        (q.ExtraFees.Any(p => p.Category == (int)ExtraFeeCategoryType.Cargo && p.Quantity > 0) || (apiResponse.Data.Applicants.Any(a => a.ExtraFees.Any(e => e.Category == (int)ExtraFeeCategoryType.Cargo && e.Quantity > 0 && (e.ExtraFeeName.Contains("+3") || e.ExtraFeeName.Contains("+ 3")))) && apiResponse.Data.Applicants.Count > 2) ? true
                        : q.ExtraFees.Any(p => p.Category == (int)ExtraFeeCategoryType.Cargo && p.Quantity > 0))
                        : q.ExtraFees.Any(p => p.Category == (int)ExtraFeeCategoryType.Cargo && p.Quantity > 0),
                    InsuranceEndDate = q.ExtraFees.Any(p => p.Category == (int)ExtraFeeCategoryType.Insurance)
                        ? q.ExtraFees.First(p => p.Category == (int)ExtraFeeCategoryType.Insurance).PolicyPeriod == null ? PrinterInsurance(q.ExtraFees.First(p => p.Category == (int)ExtraFeeCategoryType.Insurance).ExtraFeeName, q.ApplicationTime.DateTime) : q.ApplicationTime.DateTime.AddDays(q.ExtraFees.First(p => p.Category == (int)ExtraFeeCategoryType.Insurance).PolicyPeriod.Value).ToShortDateString()
                        : string.Empty,
                    ExtraFeeInsuranceInfo = q.ExtraFees.Any(p => p.Category == (int)ExtraFeeCategoryType.Insurance)
                        ? ((LanguageId == 2 ? (q.ExtraFees.First(k => k.Category == (int)ExtraFeeCategoryType.Insurance).ExtraFeeName.Contains("Insurance") ? q.ExtraFees.First(k => k.Category == (int)ExtraFeeCategoryType.Insurance).ExtraFeeName.Replace("Insurance", "").Replace("Free", "") : (q.ExtraFees.First(k => k.Category == (int)ExtraFeeCategoryType.Insurance).ExtraFeeName.Contains("Extra Fee") ? q.ExtraFees.First(k => k.Category == (int)ExtraFeeCategoryType.Insurance).ExtraFeeName.Replace("Extra Fee", "").Replace("Free", "") : q.ExtraFees.First(k => k.Category == (int)ExtraFeeCategoryType.Insurance).ExtraFeeName))
                        : (q.ExtraFees.First(k => k.Category == (int)ExtraFeeCategoryType.Insurance).ExtraFeeName.Contains("Sigorta") ? q.ExtraFees.First(k => k.Category == (int)ExtraFeeCategoryType.Insurance).ExtraFeeName.Replace("Sigorta", "").Replace("Ücretsiz", "") : (q.ExtraFees.First(k => k.Category == (int)ExtraFeeCategoryType.Insurance).ExtraFeeName.Contains("Ek Ücret") ? q.ExtraFees.First(k => k.Category == (int)ExtraFeeCategoryType.Insurance).ExtraFeeName.Replace("Ek Ücret", "").Replace("Ücretsiz", "") : q.ExtraFees.First(k => k.Category == (int)ExtraFeeCategoryType.Insurance).ExtraFeeName))))
                        : string.Empty
                }).ToList()
            };

            return await this.RenderViewAsync(direction, viewModel);
        }

        private string PrinterInsurance(string policyName, DateTime entryDate)
        {
            int policyDays = 0;
            int numberInPolicyName = ExtractNumberFromPolicyName(policyName);

            // Check for "Service Fee" policies
            if (policyName.Contains("SERVICE FEE", StringComparison.OrdinalIgnoreCase) ||
                policyName.Contains("Online Service Fee", StringComparison.OrdinalIgnoreCase) ||
                policyName.Contains("Offline Service Fee", StringComparison.OrdinalIgnoreCase) ||
                policyName.Contains("Service Fee", StringComparison.OrdinalIgnoreCase))
            {
                if (policyName.Equals("SERVICE FEE", StringComparison.OrdinalIgnoreCase) ||
                    policyName.Equals("Service Fee", StringComparison.OrdinalIgnoreCase))
                {
                    policyDays = 364;
                }
                else if (policyName.Equals("Online Service Fee", StringComparison.OrdinalIgnoreCase) ||
                         policyName.Equals("Offline Service Fee", StringComparison.OrdinalIgnoreCase))
                {
                    policyDays = 179;
                }
                else
                {
                    policyDays = GetDaysBasedOnNumber(numberInPolicyName);
                }
            }

            // Check for "Year" or "Month" policies
            else if (policyName.Contains("Yıl", StringComparison.OrdinalIgnoreCase) ||
                     policyName.Contains("Year", StringComparison.OrdinalIgnoreCase) ||
                     policyName.Contains("Ay", StringComparison.OrdinalIgnoreCase) ||
                     policyName.Contains("Month", StringComparison.OrdinalIgnoreCase))
            {
                if (policyName.Contains("Yıl", StringComparison.OrdinalIgnoreCase) ||
                    policyName.Contains("Year", StringComparison.OrdinalIgnoreCase))
                {
                    policyDays = 364 * numberInPolicyName;
                }
                else
                {
                    policyDays = GetDaysBasedOnNumber(numberInPolicyName);
                }
            }

            // Check for "Days" policies
            else if (policyName.Contains("Günlük", StringComparison.OrdinalIgnoreCase) ||
                     policyName.Contains("Days", StringComparison.OrdinalIgnoreCase))
            {
                policyDays = GetDaysPolicyDays(numberInPolicyName);
            }

            var exitDate = entryDate.AddDays(policyDays).ToShortDateString();

            return exitDate;
        }

        private int ExtractNumberFromPolicyName(string policyName)
        {
            var numberInPolicyNameString = string.Join("", policyName.ToCharArray().Where(char.IsDigit));
            int.TryParse(numberInPolicyNameString, out int numberInPolicyName);
            return numberInPolicyName;
        }

        private int GetDaysBasedOnNumber(int numberInPolicyName)
        {
            switch (numberInPolicyName)
            {
                case 1: return 29;
                case 2: return 59;
                case 3: return 89;
                case 6: return 179;
                case 12: return 364;
                default: return 0;
            }
        }

        private int GetDaysPolicyDays(int numberInPolicyName)
        {
            switch (numberInPolicyName)
            {
                case 18: return 8;       //1-8
                case 915: return 15;      //9-15
                case 1631: return 31;    //16-31
                case 3262: return 62;    //32-62
                case 6392: return 92;    //63-92
                case 93180: return 180;  //93-180
                case 181365: return 365; //181-365
                default: return 0;
            }
        }

        public async Task<IActionResult> GetCargoBarcode(string encryptedId)
        {
            if (string.IsNullOrEmpty(encryptedId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var applicationId = encryptedId.ToDecryptInt();

            var apiCargoShipmentResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<GetCargoShipmentResponse>>
                (ApiMethodName.Appointment.GetCargoShipment + applicationId, AppSettings.PrintData.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiCargoShipmentResponse.Validate(out ResultModel cargoShipmentResult).ConfigureAwait(false))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<GetApplicationForCargoBarcodeApiResponse>>
                ($"{ApiMethodName.Appointment.GetApplicationForCargoBarcode + applicationId}/{LanguageId}", AppSettings.PrintData.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            if (!apiResponse.Data.IsCargoIntegrationExists)
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.CargoFeeIsNotSelected, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            if (apiResponse.Data.AreaId == null && apiResponse.Data.CargoProviderId is (int)CargoProviderType.LastMile)
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingAddressAreaInformation, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            if (apiCargoShipmentResponse.Data.IsExist)
            {
                if (apiResponse.Data.CargoProviderId == (byte)CargoProviderType.UPS && (_environment.EnvironmentName == "Production" || _environment.EnvironmentName == "Production-K8S"))
                {
                    var apiCargoLabelRecoveryResponse = await RestHttpClient.Create().Get<ShipmentResponse>(
                        AppSettings.Cargo.BaseApiUrl + CargoEndpoint.LabelRecovery + applicationId,
                        QMSApiDefaultRequestHeaders);

                    if (apiCargoLabelRecoveryResponse?.Status != "SUCCESS")
                        return Json(new
                        {
                            Message = apiCargoLabelRecoveryResponse?.Message ?? $"{ResultMessage.ErrorOccurred.ToDescription()}",
                            ResultType = ResultType.Warning
                        });

                    return View("UPSCargoBarcode", new BarcodeViewModel
                    {
                        ApplicationId = applicationId,
                        CargoProviderId = apiResponse.Data.CargoProviderId,
                        TrackingNumber = apiCargoLabelRecoveryResponse.Data.TrackingNumber,
                        GraphicImage = apiCargoLabelRecoveryResponse.Data.GraphicImage,
                    });
                }

                var labelRecoveryViewModel = apiResponse.Data.CargoProviderId is (int)CargoProviderType.UPS
                    ? new BarcodeViewModel
                    {
                        ApplicationId = applicationId,
                        CargoProviderId = apiResponse.Data.CargoProviderId,
                        TrackingNumber = apiCargoShipmentResponse.Data.TrackingNumber,
                        GraphicImage = apiCargoShipmentResponse.Data.GraphicImage
                    } : new BarcodeViewModel
                    {
                        ApplicationId = applicationId,
                        CargoProviderId = apiResponse.Data.CargoProviderId,
                        TrackingNumber = apiCargoShipmentResponse.Data.TrackingNumber,
                        GraphicImage = apiCargoShipmentResponse.Data.GraphicImage,
                        BranchName = apiResponse.Data.BranchName,
                        BranchPhoneNumber = apiResponse.Data.BranchTelephone,
                        Applicants = new List<BarcodeViewModel.Applicant>
                        {
                            new BarcodeViewModel.Applicant
                            {
                                Name = apiResponse.Data.Name,
                                Surname = apiResponse.Data.Surname,
                                PhoneNumber1 = apiResponse.Data.PhoneNumber1,
                                PhoneNumber2 = apiResponse.Data.PhoneNumber2,
                                Address = $"{apiResponse.Data.AreaName} / {apiResponse.Data.Address}",
                                PassportNumber = apiResponse.Data.PassportNumber
                            }
                        }
                    };

                return View(apiResponse.Data.CargoProviderId is (int)CargoProviderType.LastMile
                    ? "LastMileCargoBarcode"
                    : "UPSCargoBarcode", labelRecoveryViewModel);
            }

            var request = new ShipmentRequest
            {
                ApplicationId = applicationId,
                CargoProviderId = apiResponse.Data.CargoProviderId.GetValueOrDefault(),
                EnvironmentType = _environment.EnvironmentName,
                AreaId = apiResponse.Data.AreaId,
                Branch = new Branch
                {
                    Phone = apiResponse.Data.BranchTelephone,
                    BranchAddress = apiResponse.Data.BranchAddress,
                    BranchCityName = apiResponse.Data.BranchCityName,
                    BranchCountryCode = apiResponse.Data.BranchCountryCode
                },
                Application = new Application
                {
                    Name = apiResponse.Data.Name,
                    Surname = apiResponse.Data.Surname,
                    Email = apiResponse.Data.Email,
                    Address = apiResponse.Data.Address,
                    City = apiResponse.Data.City,
                    PostalCode = apiResponse.Data.PostalCode,
                    Phone1 = apiResponse.Data.PhoneNumber1,
                    Phone2 = apiResponse.Data.PhoneNumber2,
                    PassportNumber = apiResponse.Data.PassportNumber,
                    NameOfSecondContactPerson = apiResponse.Data.NameOfSecondContactPerson
                }
            };

            var apiCargoResponse = await RestHttpClient.Create().Post<ShipmentResponse>(
                AppSettings.Cargo.BaseApiUrl + CargoEndpoint.Shipment, QMSApiDefaultRequestHeaders, request);

            if (apiCargoResponse?.Status != "SUCCESS")
                return Json(new { Message = apiCargoResponse?.Message ?? $"{ResultMessage.ErrorOccurred.ToDescription()}", ResultType = ResultType.Warning });

            var viewModel = apiResponse.Data.CargoProviderId is (int)CargoProviderType.UPS
                ? new BarcodeViewModel
                {
                    ApplicationId = applicationId,
                    CargoProviderId = apiResponse.Data.CargoProviderId,
                    TrackingNumber = apiCargoResponse.Data.TrackingNumber,
                    GraphicImage = apiCargoResponse.Data.GraphicImage
                } : new BarcodeViewModel
                {
                    ApplicationId = applicationId,
                    TrackingNumber = apiCargoResponse.Data.TrackingNumber,
                    GraphicImage = apiCargoResponse.Data.GraphicImage,
                    CargoProviderId = apiResponse.Data.CargoProviderId,
                    BranchName = apiResponse.Data.BranchName,
                    BranchPhoneNumber = apiResponse.Data.BranchTelephone,
                    Applicants = new List<BarcodeViewModel.Applicant>
                    {
                        new BarcodeViewModel.Applicant
                        {
                            Name = apiResponse.Data.Name,
                            Surname = apiResponse.Data.Surname,
                            PhoneNumber1 = apiResponse.Data.PhoneNumber1,
                            PhoneNumber2 = apiResponse.Data.PhoneNumber2,
                            Address = $"{apiResponse.Data.AreaName} / {apiResponse.Data.Address}",
                            PassportNumber = apiResponse.Data.PassportNumber
                        }
                    }
                };

            return View(apiResponse.Data.CargoProviderId is (int)CargoProviderType.LastMile
                ? "LastMileCargoBarcode"
                : "UPSCargoBarcode", viewModel);
        }

        public async Task<IActionResult> GetEsimQrCode(string encryptedApplicationId)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var applicationId = encryptedApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<EsimQrCodeApiResponse>>
                    ($"{ApiMethodName.Appointment.GetEsimQrCode + applicationId}", AppSettings.PrintData.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var viewModel = new EsimQrCodeViewModel
            {
                ApplicationId = applicationId,
                QrCodes = apiResponse.Data.QrCodes
            };

            return View("EsimQrCode", viewModel);
        }

        public IActionResult CreateBarcode(string encryptedId)
        {
            return File(Convert.FromBase64String(CreateBarcode_(encryptedId)), "image/png");
        }

        public string CreateBarcode_(string encryptedId)
        {
            int applicationId = encryptedId.ToDecryptInt();

            return Convert.ToBase64String(BarcodeClient.CreateBarcodeByteArray(applicationId.ToString("0000000000000")));
        }

        #region Print All

        public async Task<IActionResult> ApplicationPrintAll(int EncryptedApplicationId, int relationalId, bool ICR_, int ICR_Count, int ICRType, bool entryInformationFormForTurkey_, int entryInformationFormForTurkey_Count, int entryInformationFormType, bool cargo_, int cargo_Count, int cargoType, bool applicationPage_, int applicationPage_Count, int applicationPageType, bool isAuthorizedPrintAll, string branchCountryIso3)
        {
            if (EncryptedApplicationId == 0)
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var finalStr = new StringBuilder();

            var view = await this.RenderViewAsync("ApplicationPrintAll", new ApplicationPrintAllViewModel { isAuthorizedPrintAll = isAuthorizedPrintAll, ICR = ICR_, entryInformationFormForTurkey = entryInformationFormForTurkey_, cargo = cargo_, applicationPage = applicationPage_ });

            finalStr.Append(view);

            var apiRequest = new GetApplicationPrintAllApiRequest
            {
                BranchId = UserSession.BranchId.GetValueOrDefault(),
                ApplicationId = EncryptedApplicationId,
                RelationalApplicationId = relationalId,
                Icr = ICR_,
                EntryInformationFormForTurkey = entryInformationFormForTurkey_,
                Cargo = cargo_,
                ApplicationPage = applicationPage_,
                ApplicationPageLanguage = applicationPage_ ? applicationPageType : 0,
                PrintAll = true,
            };

            var taxInclude = false;
            var includeAll = false;
            apiRequest.IcrLanguage = (int)ICRLanguage.English;
            if (ICR_)
            {
                if (relationalId == 0)
                {
                    TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                    return RedirectToAction("List", "Application", new { Area = "Appointment" });
                }

                if (ICRType == (int)PDFDocumentTypes.English || ICRType == (int)PDFDocumentTypes.EnglishTaxed || ICRType == (int)PDFDocumentTypes.EnglishAllItemsTaxed)
                {
                    apiRequest.IcrLanguage = (int)ICRLanguage.English;
                    if (ICRType == (int)PDFDocumentTypes.EnglishTaxed)
                        taxInclude = true;
                    else if (ICRType == (int)PDFDocumentTypes.EnglishAllItemsTaxed)
                    {
                        taxInclude = true;
                        includeAll = true;
                    }
                }
                else if (ICRType == (int)PDFDocumentTypes.Arabic || ICRType == (int)PDFDocumentTypes.ArabicTaxed || ICRType == (int)PDFDocumentTypes.ArabicAllItemsTaxed)
                {
                    apiRequest.IcrLanguage = (int)ICRLanguage.Arabic;
                    if (ICRType == (int)PDFDocumentTypes.ArabicTaxed)
                        taxInclude = true;
                    else if (ICRType == (int)PDFDocumentTypes.ArabicAllItemsTaxed)
                    {
                        taxInclude = true;
                        includeAll = true;
                    }
                }
                else if (ICRType == (int)PDFDocumentTypes.Turkmen || ICRType == (int)PDFDocumentTypes.TurkmenTaxed || ICRType == (int)PDFDocumentTypes.TurkmenAllItemsTaxed)
                {
                    apiRequest.IcrLanguage = (int)ICRLanguage.Turkmen;
                    if (ICRType == (int)PDFDocumentTypes.TurkmenTaxed)
                        taxInclude = true;
                    else if (ICRType == (int)PDFDocumentTypes.TurkmenAllItemsTaxed)
                    {
                        taxInclude = true;
                        includeAll = true;
                    }
                }
                else if (ICRType == (int)PDFDocumentTypes.Russian || ICRType == (int)PDFDocumentTypes.RussianTaxed || ICRType == (int)PDFDocumentTypes.RussianAllItemsTaxed)
                {
                    apiRequest.IcrLanguage = (int)ICRLanguage.Russian;
                    if (ICRType == (int)PDFDocumentTypes.RussianTaxed)
                        taxInclude = true;
                    else if (ICRType == (int)PDFDocumentTypes.RussianAllItemsTaxed)
                    {
                        taxInclude = true;
                        includeAll = true;
                    }
                }
                else if (ICRType == (int)PDFDocumentTypes.French || ICRType == (int)PDFDocumentTypes.FrenchTaxed || ICRType == (int)PDFDocumentTypes.FrenchAllItemsTaxed)
                {
                    apiRequest.IcrLanguage = (int)ICRLanguage.French;
                    if (ICRType == (int)PDFDocumentTypes.FrenchTaxed)
                        taxInclude = true;
                    else if (ICRType == (int)PDFDocumentTypes.FrenchAllItemsTaxed)
                    {
                        taxInclude = true;
                        includeAll = true;
                    }
                }
            }

            if (cargo_ && relationalId == 0)
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var enApiRequestHeader = PortalGatewayApiDefaultRequestHeaders;
            enApiRequestHeader.Remove("languageId");
            enApiRequestHeader.Add("languageId", (apiRequest.IcrLanguage).ToString());

            var apiResponse = await PortalHttpClientHelper
            .PostAsJsonAsync<ApiResponse<ApplicationPrintAllApiResponse>>
                (apiRequest, ApiMethodName.Appointment.GetApplicationPrintAll, AppSettings.PrintData.BaseApiUrl, enApiRequestHeader)
            .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            if (ICR_)
            {
                var icr = await GetPrintAllICR(apiResponse.Data.GetIcrResponse, relationalId.ToEncrypt(), taxInclude, apiRequest.IcrLanguage, includeAll, true, false);

                var icrActionResultType = icr.GetType();

                if (icrActionResultType.Name == "RedirectToActionResult" || icrActionResultType
                        .GetProperty("Model")?
                        .GetValue(icr) is not ICRViewModel icrModel)
                {
                    TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                    return RedirectToAction("List", "Application", new { Area = "Appointment" });
                }

                var viewIcr = await this.RenderViewAsync("GetICR", icrModel);

                for (int i = 0; i < ICR_Count; i++)
                {
                    finalStr.Append(viewIcr);
                    finalStr.Append("<div style=\"break-after:page\"></div>");
                }
            }

            if (entryInformationFormForTurkey_)
            {
                var page = "GetEntryInformationFormEn";
                var entryInformationFormForTurkeyLanguage = EntryFormLanguage.English.ToString();
                if (entryInformationFormType == (int)PDFDocumentTypes.French)
                {
                    page = "GetEntryInformationFormFr";
                    entryInformationFormForTurkeyLanguage = EntryFormLanguage.French.ToString();
                }
                else if (entryInformationFormType == (int)PDFDocumentTypes.Arabic)
                {
                    if (apiRequest.BranchId == 1)
                        page = "GetEntryInformationFormArKuwait";
                    else
                        page = "GetEntryInformationFormAr";

                    entryInformationFormForTurkeyLanguage = EntryFormLanguage.Arabic.ToString();
                }
                else if (entryInformationFormType == (int)PDFDocumentTypes.Turkish)
                {
                    page = "GetEntryInformationFormTr";
                    entryInformationFormForTurkeyLanguage = EntryFormLanguage.Turkish.ToString();
                }
                else if (entryInformationFormType == (int)PDFDocumentTypes.Russian)
                {
                    page = "GetEntryInformationFormRu";
                    entryInformationFormForTurkeyLanguage = EntryFormLanguage.Russian.ToString();
                }
                else if (entryInformationFormType == (int)PDFDocumentTypes.TkmEnglish)
                {
                    page = "GetEntryInformationFormTkmEn";
                    entryInformationFormForTurkeyLanguage = EntryFormLanguage.TkmEnglish.ToString();
                }
                else if (entryInformationFormType == (int)PDFDocumentTypes.TkmTurkish)
                {
                    page = "GetEntryInformationFormTkmTr";
                    entryInformationFormForTurkeyLanguage = EntryFormLanguage.TkmTurkish.ToString();
                }
                else if (entryInformationFormType == (int)PDFDocumentTypes.TkmRussian)
                {
                    page = "GetEntryInformationFormTkmRu";
                    entryInformationFormForTurkeyLanguage = EntryFormLanguage.TkmRussian.ToString();
                }
                else if (entryInformationFormType == (int)PDFDocumentTypes.Turkmen)
                {
                    page = "GetEntryInformationFormTm";
                    entryInformationFormForTurkeyLanguage = EntryFormLanguage.Turkmen.ToString();
                }

                var entryInformationForm = GetPrintAllEntryInformationForm(apiResponse.Data.GetApplicationEntryFormResponse, entryInformationFormForTurkeyLanguage, true).Result;

                var entryInformationFormActionResultType = entryInformationForm.GetType();

                if (entryInformationFormActionResultType.Name == "RedirectToActionResult" || entryInformationFormActionResultType
                        .GetProperty("Model")?
                        .GetValue(entryInformationForm) is not EntryInformationFormViewModel entryInformationFormViewModel)
                {
                    TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                    return RedirectToAction("List", "Application", new { Area = "Appointment" });
                }

                var viewEntryInformationForm = await this.RenderViewAsync(page, entryInformationFormViewModel);

                for (int i = 0; i < entryInformationFormForTurkey_Count; i++)
                {
                    finalStr.Append(viewEntryInformationForm);
                    finalStr.Append("<div style=\"break-after:page\"></div>");
                }
            }

            if (cargo_)
            {
                var page = "GetCargoReceiptEn";
                var cargoLanguage = "English";
                if (cargoType == (int)PDFDocumentTypes.Arabic)
                {
                    if (branchCountryIso3 == "DZA")
                    {
                        page = "GetCargoReceiptAlgerianAr";
                        cargoLanguage = "AlgerianArabic";
                    }
                    else
                    {
                        page = "GetCargoReceiptAr";
                        cargoLanguage = "Arabic";
                    }
                }
                else if (cargoType == (int)PDFDocumentTypes.Russian)
                {
                    page = "GetCargoReceiptRus";
                    cargoLanguage = "Russian";
                }
                else if (cargoType == (int)PDFDocumentTypes.French)
                {
                    if (branchCountryIso3 == "DZA")
                    {
                        page = "GetCargoReceiptFr";
                        cargoLanguage = "AlgerianFrench";
                    }
                }

                var cargo = GetPrintAllCargoReceipt(apiResponse.Data.GetCargoResponse, relationalId, EncryptedApplicationId, cargoLanguage, true).Result;
                var cargoActionResultType = cargo.GetType();

                if (cargoActionResultType.Name == "RedirectToActionResult" || cargoActionResultType
                        .GetProperty("Model")?
                        .GetValue(cargo) is not CargoReceiptViewModel cargoModel)
                {
                    TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                    return RedirectToAction("List", "Application", new { Area = "Appointment" });
                }

                var viewCargo = await this.RenderViewAsync(page, cargoModel);

                for (int i = 0; i < cargo_Count; i++)
                {
                    finalStr.Append(viewCargo);
                    finalStr.Append("<div style=\"break-after:page\"></div>");
                }
            }

            if (applicationPage_)
            {
                var applicationPage = await PrintAllPrintOut(apiResponse.Data.GetApplicationPageResponse);

                var applicationPageActionResultType = applicationPage.GetType();

                if (applicationPageActionResultType.Name == "RedirectToActionResult" || applicationPageActionResultType
                        .GetProperty("Model")?
                        .GetValue(applicationPage) is not ApplicationViewModel applicationPageViewModel)
                {
                    TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                    return RedirectToAction("List", "Application", new { Area = "Appointment" });
                }

                var pageLanguage = "en-US";
                if (applicationPageType == (int)PDFDocumentTypes.Turkish)
                    pageLanguage = "tr-TR";

                var viewApplicationPage = await this.RenderViewAsync("_PrintOutPDF", applicationPageViewModel, false, pageLanguage);

                for (int i = 0; i < applicationPage_Count; i++)
                {
                    finalStr.Append(viewApplicationPage);
                }
            }

            return base.Content(finalStr.ToString(), "text/html", Encoding.UTF8);
        }

        public async Task<IActionResult> GetPrintAllICR(ApplicationPrintAllApiResponse.GetIcrApiResponse apiResponse, string encryptedId, bool taxInclude, int icrLanguage, bool includeAll = false, bool printAll = false, bool printer = false)
        {
            if (apiResponse.RelationalApplicationsForIcr == null || apiResponse.BranchIcr == null || apiResponse.ApplicationOrder == 0)
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var tlvFormatCountries = EnumHelper.GetEnumAsDictionary(typeof(TLVFormatQRCodeCountries)).Select(p => new
            {
                Value = p.Key
            }).ToList();

            var visaCategories = await GetCachedVisaCategories();

            var viewModel = new ICRViewModel
            {
                TaxInclude = taxInclude,
                EncryptedId = apiResponse.RelationalApplicationsForIcr.RelationalApplicationId.ToEncrypt(),
                IsTLVFormat = tlvFormatCountries.Any(q => q.Value == apiResponse.RelationalApplicationsForIcr.BranchCountryId),
                BranchName = apiResponse.RelationalApplicationsForIcr.BranchCorporateName,
                BranchCountryId = apiResponse.RelationalApplicationsForIcr.BranchCountryId,
                InvoiceNumber = apiResponse.RelationalApplicationsForIcr.InvoiceNumber,
                Phone = apiResponse.RelationalApplicationsForIcr.Phone,
                Email = apiResponse.RelationalApplicationsForIcr.Email,
                Address = apiResponse.RelationalApplicationsForIcr.BranchAddress,
                AppointmentTime = apiResponse.RelationalApplicationsForIcr.ApplicationTime.DateTime,
                DateTimeNow = DateTime.Now.ToShortDateString() + ", " + DateTime.Now.ToShortTimeString(),
                IcrType = apiResponse.BranchIcr.Type,
                InvoiceNo = apiResponse.BranchIcr.SapBranch.TrimStart('0') + "-" + apiResponse.RelationalApplicationsForIcr.ApplicationTime.DateTime.ToString("yyyyMMdd") + "-" + apiResponse.RelationalApplicationsForIcr.RelationalApplicationId,
                CreatedBy = apiResponse.RelationalApplicationsForIcr.CreatedBy,
                ApplicationExtraFeesTotal = new List<TotalExtraFeeViewModel>(),
                QrBranchName = apiResponse.RelationalApplicationsForIcr.QrCorporateName, //used for qr decodinBg
                QrInvoiceNumber = apiResponse.RelationalApplicationsForIcr.QrInvoiceNumber,
                PrintAll = printAll,
                BranchCityName = apiResponse.RelationalApplicationsForIcr.BranchCityName,
                KSAICR = apiResponse.RelationalApplicationsForIcr.KSAICR,
                Applicants = apiResponse.RelationalApplicationsForIcr.Applicants.Where(q => (q.ApplicantTypeId == (int)ApplicantType.Family || q.ApplicantTypeId == (int)ApplicantType.Group) ? q.StatusId != (int)ApplicationStatus.Cancelled : 1 == 1).Select(p => new ICRViewModel.ApplicantViewModel
                {
                    IcrLanguage = icrLanguage,
                    PhotoBoothCheck = p.ExtraFees.Any(q => q.ExtraFeeNameTr.Contains("Fotoğraf")) && p.PhotoBoothId != 0,
                    ApplicationNumber = p.Id.ToApplicationNumber(),
                    VisaCategoryId = p.VisaCategoryId,
                    VisaCategory = visaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId) == null ? string.Empty :
                    visaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == icrLanguage) == null ?
                    visaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId).Translations.FirstOrDefault().Name :
                    visaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == icrLanguage).Name,
                    VisaCategoryEn = visaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId) == null ? string.Empty :
                    visaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == (int)Language.English) == null ?
                    visaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId).Translations.FirstOrDefault().Name :
                    visaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == (int)Language.English).Name,
                    VisaCategoryAr = visaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId) == null ? string.Empty :
                    visaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == (int)ICRLanguage.Arabic) == null ?
                    visaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId).Translations.FirstOrDefault().Name :
                    visaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == (int)ICRLanguage.Arabic).Name,
                    Name = p.Name,
                    VisaNo = p.VisaNo,
                    BarcodeICR = !string.IsNullOrEmpty(p.VisaNo) ? CreateBarcodeICR_(p.VisaNo) : "",
                    PhotoBoothBarcodeICR = (p.ExtraFees.Any(q => q.ExtraFeeNameTr.Contains("Fotoğraf")) && p.PhotoBoothId != 0) ? CreateBarcodePhotobooth(p.PhotoBoothId.ToEncrypt()).Result : "",
                    Surname = p.Surname,
                    PassportNumber = p.PassportNumber,
                    PhoneNumber = p.PhoneNumber,
                    Nationality = p.Nationality,
                    PhotoBoothEncryptedId = p.PhotoBoothId.ToEncrypt(),
                    ApplicationAdress = p.Address,
                    ApplicantExtraFeesTotal = p.ExtraFees.Where(q => includeAll || q.ShowInICR).GroupBy(e => new { e.CurrencyId, e.TaxRatio }).Select(e => new TotalExtraFeeViewModel()
                    {
                        Currency = GetCurrencyConverterByLanguageId(icrLanguage, e.Key.CurrencyId),
                        Price = e.Sum(q => q.Price * q.Quantity),
                        BasePrice = e.Sum(q => q.BasePrice * q.Quantity),
                        Tax = e.Sum(q => q.Tax * q.Quantity),
                        TaxRatio = e.Key.TaxRatio,
                        ServiceTax = e.Sum(q => q.ServiceTax * q.Quantity),
                    }).GroupBy(e => new { e.Currency, e.TaxRatio }).Select(e => new TotalExtraFeeViewModel()
                    {
                        Currency = e.Key.Currency,
                        Price = e.Sum(q => q.Price),
                        BasePrice = e.Sum(q => q.BasePrice),
                        Tax = e.Sum(q => q.Tax),
                        TaxRatio = e.Key.TaxRatio,
                        ServiceTax = e.Sum(q => q.ServiceTax),
                    }).OrderBy(z => z.Currency).ThenBy(z => z.TaxRatio).ToList(),
                    ExtraFees = p.ExtraFees.Where(q => includeAll || q.ShowInICR).GroupBy(e => new { e.CurrencyId, e.TaxRatio, e.ExtraFeeName, e.ExtraFeeNameAr, e.IsGroupInIcr }).Select(e => new ICRViewModel.ApplicantViewModel.ApplicantExtraFeeViewModel
                    {
                        Name = e.Key.IsGroupInIcr ? GetExtraFeeNameConverterByLanguageId(icrLanguage, apiResponse.RelationalApplicationsForIcr.BranchCountryId) : e.Key.ExtraFeeName,
                        NameAr = e.Key.ExtraFeeNameAr,
                        IsGroupInIcr = e.Key.IsGroupInIcr,
                        Currency = GetCurrencyConverterByLanguageId(icrLanguage, e.Key.CurrencyId),
                        Price = e.Sum(q => q.Price * q.Quantity),
                        BasePrice = e.Sum(q => q.BasePrice * q.Quantity),
                        Tax = e.Sum(q => q.Tax * q.Quantity),
                        TaxRatio = e.Key.TaxRatio,
                        PriceZero = e.Where(q => q.TaxRatio == 0 && q.Category == 4).Select(q => q.Price * q.Quantity).Sum(),
                        ServiceTax = e.Sum(q => q.ServiceTax * q.Quantity)
                    }).GroupBy(e => new { e.Name, e.NameAr, e.Currency, e.IsGroupInIcr, Value = e.IsGroupInIcr ? new decimal(1) : e.TaxRatio }).Select(e => new ICRViewModel.ApplicantViewModel.ApplicantExtraFeeViewModel
                    {
                        Name = e.Key.Name,
                        NameAr = e.Key.NameAr,
                        IsGroupInIcr = e.Key.IsGroupInIcr,
                        Currency = e.Key.Currency,
                        Price = e.Sum(q => q.Price),
                        BasePrice = e.Sum(q => q.BasePrice),
                        Tax = e.Sum(q => q.Tax),
                        TaxRatio = e.Key.IsGroupInIcr ? e.Where(q => q.TaxRatio != 0).Select(q => q.TaxRatio).FirstOrDefault() : e.Key.Value,
                        PriceZero = e.Sum(q => q.PriceZero),
                        ServiceTax = e.Sum(q => q.ServiceTax)
                    }).OrderBy(z => z.Currency).ThenBy(z => z.TaxRatio).ToList()
                }).ToList()
            };
            String branchPreviousNotes = "";
            if (viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0) != null)
            {
                if (icrLanguage == (int)ICRLanguage.Russian)
                {
                    branchPreviousNotes = viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).PriceZero.ToString("0.00") + " " + viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).Currency + " это визовый сбор, который не облагается налогом.";
                }
                else if (icrLanguage == (int)ICRLanguage.Arabic)
                {
                    branchPreviousNotes = viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).PriceZero.ToString("0.00") + " " + viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).Currency + " درهم إماراتي هي رسوم التأشيرة معفات من الضريبة. ";
                }
                else if (icrLanguage == (int)ICRLanguage.English)
                {
                    branchPreviousNotes = viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).Currency + " " + viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).PriceZero.ToString("0.00") + " is the Visa fee and its exempted from tax.";
                }
                else if (icrLanguage == (int)ICRLanguage.French)
                {
                    branchPreviousNotes = viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).Currency + " " + viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).PriceZero.ToString("0.00") + " est le frais de visa et son exonération d'impôt.";
                }
                else if (icrLanguage == (int)ICRLanguage.Turkmen)
                {
                    branchPreviousNotes = viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).Currency + " " + viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).PriceZero.ToString("0.00") + " wiza tölegidir we salgytdan boşadylýar.";
                }
                else if (icrLanguage != (int)ICRLanguage.EnglishAndArabic)
                {
                    branchPreviousNotes = viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).Currency + " " + viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).PriceZero.ToString("0.00") + " is the Visa fee and its exempted from tax.";
                }
            }

            viewModel.BranchIcrNotes = GetPrintAllIcrNoteByLanguageId(icrLanguage, apiResponse, branchPreviousNotes, viewModel.BranchCountryId).Item2;
            viewModel.IcrName = GetPrintAllIcrNoteByLanguageId(icrLanguage, apiResponse, branchPreviousNotes, viewModel.BranchCountryId).Item1;

            foreach (var applicant in viewModel.Applicants)
            {
                foreach (var extraFeesPerCurrency in applicant.ApplicantExtraFeesTotal)
                {
                    if (viewModel.ApplicationExtraFeesTotal.Any(p => p.Currency == extraFeesPerCurrency.Currency && p.TaxRatio == extraFeesPerCurrency.TaxRatio))
                    {
                        viewModel.ApplicationExtraFeesTotal.FirstOrDefault(p => p.Currency == extraFeesPerCurrency.Currency && p.TaxRatio == extraFeesPerCurrency.TaxRatio).Price += extraFeesPerCurrency.Price;
                        viewModel.ApplicationExtraFeesTotal.FirstOrDefault(p => p.Currency == extraFeesPerCurrency.Currency && p.TaxRatio == extraFeesPerCurrency.TaxRatio).Tax += extraFeesPerCurrency.Tax;
                        viewModel.ApplicationExtraFeesTotal.FirstOrDefault(p => p.Currency == extraFeesPerCurrency.Currency && p.TaxRatio == extraFeesPerCurrency.TaxRatio).ServiceTax += extraFeesPerCurrency.ServiceTax;
                        viewModel.ApplicationExtraFeesTotal.FirstOrDefault(p => p.Currency == extraFeesPerCurrency.Currency && p.TaxRatio == extraFeesPerCurrency.TaxRatio).BasePrice += extraFeesPerCurrency.BasePrice;
                    }
                    else
                    {
                        viewModel.ApplicationExtraFeesTotal.Add(new TotalExtraFeeViewModel() { Currency = extraFeesPerCurrency.Currency, Price = extraFeesPerCurrency.Price, Tax = extraFeesPerCurrency.Tax, TaxRatio = extraFeesPerCurrency.TaxRatio, ServiceTax = extraFeesPerCurrency.ServiceTax, BasePrice = extraFeesPerCurrency.BasePrice });
                    }
                }
            }
            viewModel.ApplicationExtraFeesTotal.OrderBy(z => z.Currency).ThenBy(z => z.TaxRatio);

            var vatAmount = string.Empty;
            var totalAmount = string.Empty;
            if (viewModel.IsTLVFormat)
            {
                vatAmount = String.Format("{0:0.00}", viewModel.ApplicationExtraFeesTotal.Sum(q => q.Tax));
                totalAmount = String.Format("{0:0.00}", viewModel.ApplicationExtraFeesTotal.Sum(q => q.Price));
            }
            viewModel.CreateICR = CreateICR_(viewModel.BranchName, viewModel.InvoiceNumber, vatAmount, totalAmount, viewModel.TaxInclude, viewModel.AppointmentTime, viewModel.IsTLVFormat);
            viewModel.ApplicationExtraFeesGrandTotal = viewModel.ApplicationExtraFeesTotal.GroupBy(q => new { q.Currency }).Select(q => new GrandTotalExtraFeeViewModel()
            {
                Currency = q.Key.Currency,
                Price = q.Sum(p => p.Price),
            }).ToList();

            if (viewModel.BranchCountryId == 77)//India
                viewModel.Title = "Tax Invoice";
            else
                viewModel.Title = viewModel.BranchName + " ICR";

            try
            {
                var htmlStr = IcrRendererByLanguage(icrLanguage, viewModel, printer);

                ViewData["ICR_html"] = htmlStr.Result;

                return View("GetICR");
            }
            catch (Exception e)
            {
                TempData.Put("Notification", new ResultModel { Message = e.Message, ResultType = ResultType.Danger });
                return RedirectToAction("Detail", "Application", new { Area = "Appointment", encryptedApplicationId = encryptedId });
            }
        }

        public async Task<IActionResult> GetPrintAllIndividualICR(ApplicationPrintAllApiResponse.GetIndividualIcrApiResponse apiResponse, string encryptedId, bool taxInclude, int icrLanguage, bool includeAll = false, bool printAll = false, bool printer = false)
        {
            if (apiResponse.Application == null || apiResponse.BranchIcr == null || apiResponse.ApplicationOrder == 0)
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var tlvFormatCountries = EnumHelper.GetEnumAsDictionary(typeof(TLVFormatQRCodeCountries)).Select(p => new
            {
                Value = p.Key
            }).ToList();

            var visaCategories = await GetCachedVisaCategories();

            var viewModel = new ICRViewModel
            {
                TaxInclude = taxInclude,
                EncryptedId = encryptedId,
                IsTLVFormat = tlvFormatCountries.Any(q => q.Value == apiResponse.Application.BranchCountryId),
                BranchName = apiResponse.Application.BranchCorporateName,
                BranchCountryId = apiResponse.Application.BranchCountryId,
                InvoiceNumber = apiResponse.Application.InvoiceNumber,
                Phone = apiResponse.Application.Phone,
                Email = apiResponse.Application.Email,
                Address = apiResponse.Application.BranchAddress,
                AppointmentTime = apiResponse.Application.ApplicationTime.DateTime,
                DateTimeNow = DateTime.Now.ToShortDateString() + ", " + DateTime.Now.ToShortTimeString(),
                IcrType = apiResponse.BranchIcr.Type,
                InvoiceNo = apiResponse.BranchIcr.SapBranch.TrimStart('0') + "-" + apiResponse.Application.ApplicationTime.DateTime.ToString("yyyyMMdd") + "-" + apiResponse.Application.Id,
                CreatedBy = apiResponse.Application.CreatedBy,
                ApplicationExtraFeesTotal = new List<TotalExtraFeeViewModel>(),
                QrBranchName = apiResponse.Application.QrCorporateName, //used for qr decodinBg
                QrInvoiceNumber = apiResponse.Application.QrInvoiceNumber,
                PrintAll = printAll,
                BranchCityName = apiResponse.Application.BranchCityName,
                Applicants = new List<ICRViewModel.ApplicantViewModel>{ new ICRViewModel.ApplicantViewModel
                {
                    IcrLanguage = icrLanguage,
                    PhotoBoothCheck = apiResponse.Application.Applicant.ExtraFees.Any(q => q.ExtraFeeNameTr.Contains("Fotoğraf")) && apiResponse.Application.Applicant.PhotoBoothId != 0,
                    ApplicationNumber = apiResponse.Application.Applicant.Id.ToApplicationNumber(),
                    VisaCategoryId = apiResponse.Application.Applicant.VisaCategoryId,
                    VisaCategory = visaCategories.FirstOrDefault(s => s.Id == apiResponse.Application.Applicant.VisaCategoryId) == null ? string.Empty :
                    visaCategories.FirstOrDefault(s => s.Id == apiResponse.Application.Applicant.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == icrLanguage) == null ?
                    visaCategories.FirstOrDefault(s => s.Id == apiResponse.Application.Applicant.VisaCategoryId).Translations.FirstOrDefault().Name :
                    visaCategories.FirstOrDefault(s => s.Id == apiResponse.Application.Applicant.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == icrLanguage).Name,
                    VisaCategoryEn = visaCategories.FirstOrDefault(s => s.Id == apiResponse.Application.Applicant.VisaCategoryId) == null ? string.Empty :
                    visaCategories.FirstOrDefault(s => s.Id == apiResponse.Application.Applicant.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == (int)Language.English) == null ?
                    visaCategories.FirstOrDefault(s => s.Id == apiResponse.Application.Applicant.VisaCategoryId).Translations.FirstOrDefault().Name :
                    visaCategories.FirstOrDefault(s => s.Id == apiResponse.Application.Applicant.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == (int)Language.English).Name,
                    VisaCategoryAr = visaCategories.FirstOrDefault(s => s.Id == apiResponse.Application.Applicant.VisaCategoryId) == null ? string.Empty :
                    visaCategories.FirstOrDefault(s => s.Id == apiResponse.Application.Applicant.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == (int)ICRLanguage.Arabic) == null ?
                    visaCategories.FirstOrDefault(s => s.Id == apiResponse.Application.Applicant.VisaCategoryId).Translations.FirstOrDefault().Name :
                    visaCategories.FirstOrDefault(s => s.Id == apiResponse.Application.Applicant.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == (int)ICRLanguage.Arabic).Name,
                    Name = apiResponse.Application.Applicant.Name,
                    VisaNo = apiResponse.Application.Applicant.VisaNo,
                    BarcodeICR = !string.IsNullOrEmpty(apiResponse.Application.Applicant.VisaNo) ? CreateBarcodeICR_(apiResponse.Application.Applicant.VisaNo) : "",
                    PhotoBoothBarcodeICR = (apiResponse.Application.Applicant.ExtraFees.Any(q => q.ExtraFeeNameTr.Contains("Fotoğraf")) && apiResponse.Application.Applicant.PhotoBoothId != 0) ? CreateBarcodePhotobooth(apiResponse.Application.Applicant.PhotoBoothId.ToEncrypt()).Result : "",
                    Surname = apiResponse.Application.Applicant.Surname,
                    PassportNumber = apiResponse.Application.Applicant.PassportNumber,
                    PhoneNumber = apiResponse.Application.Applicant.PhoneNumber,
                    Nationality = apiResponse.Application.Applicant.Nationality,
                    PhotoBoothEncryptedId = apiResponse.Application.Applicant.PhotoBoothId.ToEncrypt(),
                    ApplicationAdress = apiResponse.Application.Applicant.Address,
                    ApplicantExtraFeesTotal = apiResponse.Application.Applicant.ExtraFees.Where(q => includeAll || q.ShowInICR).GroupBy(e => new { e.CurrencyId, e.TaxRatio }).Select(e => new TotalExtraFeeViewModel()
                    {
                        Currency = GetCurrencyConverterByLanguageId(icrLanguage, e.Key.CurrencyId),
                        Price = e.Sum(q => q.Price * q.Quantity),
                        BasePrice = e.Sum(q => q.BasePrice * q.Quantity) ?? 0,
                        Tax = e.Sum(q => q.Tax * q.Quantity),
                        TaxRatio = e.Key.TaxRatio,
                        ServiceTax = e.Sum(q => q.ServiceTax * q.Quantity) ?? 0,
                    }).GroupBy(e => new { e.Currency, e.TaxRatio }).Select(e => new TotalExtraFeeViewModel()
                    {
                        Currency = e.Key.Currency,
                        Price = e.Sum(q => q.Price),
                        BasePrice = e.Sum(q => q.BasePrice),
                        Tax = e.Sum(q => q.Tax),
                        TaxRatio = e.Key.TaxRatio,
                        ServiceTax = e.Sum(q => q.ServiceTax),
                    }).OrderBy(z => z.Currency).ThenBy(z => z.TaxRatio).ToList(),
                    ExtraFees = apiResponse.Application.Applicant.ExtraFees.Where(q => includeAll || q.ShowInICR).GroupBy(e => new { e.CurrencyId, e.TaxRatio, e.ExtraFeeName, e.ExtraFeeNameAr, e.IsGroupInIcr }).Select(e => new ICRViewModel.ApplicantViewModel.ApplicantExtraFeeViewModel
                    {
                        Name = e.Key.IsGroupInIcr ? GetExtraFeeNameConverterByLanguageId(icrLanguage, apiResponse.Application.BranchCountryId) : e.Key.ExtraFeeName,
                        NameAr = e.Key.ExtraFeeNameAr,
                        IsGroupInIcr = e.Key.IsGroupInIcr,
                        Currency = GetCurrencyConverterByLanguageId(icrLanguage, e.Key.CurrencyId),
                        Price = e.Sum(q => q.Price * q.Quantity),
                        BasePrice = e.Sum(q => q.BasePrice * q.Quantity) ?? 0,
                        Tax = e.Sum(q => q.Tax * q.Quantity),
                        TaxRatio = e.Key.TaxRatio,
                        PriceZero = e.Where(q => q.TaxRatio == 0 && q.Category == 4).Select(q => q.Price * q.Quantity).Sum(),
                        ServiceTax = e.Sum(q => q.ServiceTax * q.Quantity) ?? 0
                    }).GroupBy(e => new { e.Name, e.NameAr, e.Currency, e.IsGroupInIcr, Value = e.IsGroupInIcr ? new decimal(1) : e.TaxRatio }).Select(e => new ICRViewModel.ApplicantViewModel.ApplicantExtraFeeViewModel
                    {
                        Name = e.Key.Name,
                        NameAr = e.Key.NameAr,
                        IsGroupInIcr = e.Key.IsGroupInIcr,
                        Currency = e.Key.Currency,
                        Price = e.Sum(q => q.Price),
                        BasePrice = e.Sum(q => q.BasePrice),
                        Tax = e.Sum(q => q.Tax),
                        TaxRatio = e.Key.IsGroupInIcr ? e.Where(q => q.TaxRatio != 0).Select(q => q.TaxRatio).FirstOrDefault() : e.Key.Value,
                        PriceZero = e.Sum(q => q.PriceZero),
                        ServiceTax = e.Sum(q => q.ServiceTax)
                    }).OrderBy(z => z.Currency).ThenBy(z => z.TaxRatio).ToList()
                }}.ToList()
            };
            String branchPreviousNotes = "";
            if (viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0) != null)
            {
                if (icrLanguage == (int)ICRLanguage.Russian)
                {
                    branchPreviousNotes = viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).PriceZero.ToString("0.00") + " " + viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).Currency + " это визовый сбор, который не облагается налогом.";
                }
                else if (icrLanguage == (int)ICRLanguage.Arabic)
                {
                    branchPreviousNotes = viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).PriceZero.ToString("0.00") + " " + viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).Currency + " درهم إماراتي هي رسوم التأشيرة معفات من الضريبة. ";
                }
                else if (icrLanguage == (int)ICRLanguage.English)
                {
                    branchPreviousNotes = viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).Currency + " " + viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).PriceZero.ToString("0.00") + " is the Visa fee and its exempted from tax.";
                }
                else if (icrLanguage == (int)ICRLanguage.French)
                {
                    branchPreviousNotes = viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).Currency + " " + viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).PriceZero.ToString("0.00") + " est le frais de visa et son exonération d'impôt.";
                }
                else if (icrLanguage == (int)ICRLanguage.Turkmen)
                {
                    branchPreviousNotes = viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).Currency + " " + viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).PriceZero.ToString("0.00") + " wiza tölegidir we salgytdan boşadylýar.";
                }
                else if (icrLanguage != (int)ICRLanguage.EnglishAndArabic)
                {
                    branchPreviousNotes = viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).Currency + " " + viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).PriceZero.ToString("0.00") + " is the Visa fee and its exempted from tax.";
                }
            }

            viewModel.BranchIcrNotes = GetPrintAllIndividualIcrNoteByLanguageId(icrLanguage, apiResponse, branchPreviousNotes, viewModel.BranchCountryId).Item2;
            viewModel.IcrName = GetPrintAllIndividualIcrNoteByLanguageId(icrLanguage, apiResponse, branchPreviousNotes, viewModel.BranchCountryId).Item1;

            foreach (var applicant in viewModel.Applicants)
            {
                foreach (var extraFeesPerCurrency in applicant.ApplicantExtraFeesTotal)
                {
                    if (viewModel.ApplicationExtraFeesTotal.Any(p => p.Currency == extraFeesPerCurrency.Currency && p.TaxRatio == extraFeesPerCurrency.TaxRatio))
                    {
                        viewModel.ApplicationExtraFeesTotal.FirstOrDefault(p => p.Currency == extraFeesPerCurrency.Currency && p.TaxRatio == extraFeesPerCurrency.TaxRatio).Price += extraFeesPerCurrency.Price;
                        viewModel.ApplicationExtraFeesTotal.FirstOrDefault(p => p.Currency == extraFeesPerCurrency.Currency && p.TaxRatio == extraFeesPerCurrency.TaxRatio).Tax += extraFeesPerCurrency.Tax;
                        viewModel.ApplicationExtraFeesTotal.FirstOrDefault(p => p.Currency == extraFeesPerCurrency.Currency && p.TaxRatio == extraFeesPerCurrency.TaxRatio).ServiceTax += extraFeesPerCurrency.ServiceTax;
                        viewModel.ApplicationExtraFeesTotal.FirstOrDefault(p => p.Currency == extraFeesPerCurrency.Currency && p.TaxRatio == extraFeesPerCurrency.TaxRatio).BasePrice += extraFeesPerCurrency.BasePrice;
                    }
                    else
                    {
                        viewModel.ApplicationExtraFeesTotal.Add(new TotalExtraFeeViewModel() { Currency = extraFeesPerCurrency.Currency, Price = extraFeesPerCurrency.Price, Tax = extraFeesPerCurrency.Tax, TaxRatio = extraFeesPerCurrency.TaxRatio, ServiceTax = extraFeesPerCurrency.ServiceTax, BasePrice = extraFeesPerCurrency.BasePrice });
                    }
                }
            }
            viewModel.ApplicationExtraFeesTotal.OrderBy(z => z.Currency).ThenBy(z => z.TaxRatio);

            var vatAmount = string.Empty;
            var totalAmount = string.Empty;
            if (viewModel.IsTLVFormat)
            {
                vatAmount = String.Format("{0:0.00}", viewModel.ApplicationExtraFeesTotal.Sum(q => q.Tax));
                totalAmount = String.Format("{0:0.00}", viewModel.ApplicationExtraFeesTotal.Sum(q => q.Price));
            }
            viewModel.CreateICR = CreateICR_(viewModel.BranchName, viewModel.InvoiceNumber, vatAmount, totalAmount, viewModel.TaxInclude, viewModel.AppointmentTime, viewModel.IsTLVFormat);
            viewModel.ApplicationExtraFeesGrandTotal = viewModel.ApplicationExtraFeesTotal.GroupBy(q => new { q.Currency }).Select(q => new GrandTotalExtraFeeViewModel()
            {
                Currency = q.Key.Currency,
                Price = q.Sum(p => p.Price),
            }).ToList();


            viewModel.Title = viewModel.BranchName + " ICR";

            try
            {
                var htmlStr = IndividualIcrRendererByLanguage(icrLanguage, viewModel, printer);

                ViewData["ICR_html"] = htmlStr.Result;

                return View("GetICR");
            }
            catch (Exception e)
            {
                TempData.Put("Notification", new ResultModel { Message = e.Message, ResultType = ResultType.Danger });
                return RedirectToAction("Detail", "Application", new { Area = "Appointment", encryptedApplicationId = encryptedId });
            }
        }

        public Task<IActionResult> GetPrintAllEntryInformationForm(ApplicationPrintAllApiResponse.GetApplicationEntryFormApiResponse apiResponse, string languageId, bool printAll = false)
        {
            if (apiResponse.ApplicationEntryForm == null)
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return Task.FromResult<IActionResult>(RedirectToAction("List", "Application", new { Area = "Appointment" }));
            }

            var viewModel = new EntryInformationFormViewModel()
            {
                Name = apiResponse.ApplicationEntryForm.Name,
                Surname = apiResponse.ApplicationEntryForm.Surname,
                Nationality = apiResponse.ApplicationEntryForm.Nationality,
                BirthDate = apiResponse.ApplicationEntryForm.BirthDate,
                PassportNumber = apiResponse.ApplicationEntryForm.PassportNumber,
                DateOfArrival = apiResponse.ApplicationEntryForm.ArrivalDate,
                BranchId = apiResponse.ApplicationEntryForm.BranchId,
                PrintAll = printAll
            };

            if (languageId == EntryFormLanguage.English.ToString())
                return Task.FromResult<IActionResult>(View("GetEntryInformationFormEn", viewModel));
            if (languageId == EntryFormLanguage.French.ToString())
                return Task.FromResult<IActionResult>(View("GetEntryInformationFormFr", viewModel));
            if (languageId == EntryFormLanguage.Arabic.ToString())
                return Task.FromResult<IActionResult>(View("GetEntryInformationFormAr", viewModel));
            if (languageId == EntryFormLanguage.Turkish.ToString())
                return Task.FromResult<IActionResult>(View("GetEntryInformationFormTr", viewModel));
            if (languageId == EntryFormLanguage.Russian.ToString())
                return Task.FromResult<IActionResult>(View("GetEntryInformationFormRu", viewModel));
            if (languageId == EntryFormLanguage.TkmEnglish.ToString())
                return Task.FromResult<IActionResult>(View("GetEntryInformationFormTkmEn", viewModel));
            if (languageId == EntryFormLanguage.TkmTurkish.ToString())
                return Task.FromResult<IActionResult>(View("GetEntryInformationFormTkmTr", viewModel));
            if (languageId == EntryFormLanguage.TkmRussian.ToString())
                return Task.FromResult<IActionResult>(View("GetEntryInformationFormTkmRu", viewModel));
            if (languageId == EntryFormLanguage.Turkmen.ToString())
                return Task.FromResult<IActionResult>(View("GetEntryInformationFormTm", viewModel));
            TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
            return Task.FromResult<IActionResult>(RedirectToAction("List", "Application", new { Area = "Appointment" }));
        }

        public Task<IActionResult> GetPrintAllCargoReceipt(ApplicationPrintAllApiResponse.GetCargoApiResponse apiResponse, int relationalApplicationId, int applicationId, string language, bool printAll = false)
        {
            if (apiResponse.RelationalApplications == null)
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return Task.FromResult<IActionResult>(RedirectToAction("List", "Application", new { Area = "Appointment" }));
            }

            var appId = (apiResponse.RelationalApplications.Applicants.First().ApplicantTypeId == (int)ApplicantType.Individual || apiResponse.RelationalApplications.Applicants.First().ApplicantTypeId == (int)ApplicantType.Representative) ? applicationId : relationalApplicationId;
            var isCargoAvailable = apiResponse.RelationalApplications.Applicants.Count() == 1 ?
                apiResponse.RelationalApplications.Applicants.First().ExtraFees.Any(q => q.Category == ExtraFeeCategoryType.Cargo.ToInt()) :
                apiResponse.RelationalApplications.Applicants.First(q => q.Id == appId).ExtraFees.Any(q => q.Category == ExtraFeeCategoryType.Cargo.ToInt());

            if (!isCargoAvailable)
            {
                TempData.Put("Notification", new ResultModel { Message = SiteResources.CargoIsNotAvailable, ResultType = ResultType.Danger });
                return Task.FromResult<IActionResult>(RedirectToAction("List", "Application", new { Area = "Appointment" }));
            }

            var mainApp = apiResponse.RelationalApplications.Applicants.Count() == 1
                ? apiResponse.RelationalApplications.Applicants.First()
                : apiResponse.RelationalApplications.Applicants.First(q => q.Id == apiResponse.RelationalApplications.RelationalApplicationId);

            var viewModel = new CargoReceiptViewModel
            {
                ApplicationTime = apiResponse.RelationalApplications.ApplicationTime.AddHours(apiResponse.RelationalApplications.BranchCountryTimeOffset).DateTime,
                CityName = apiResponse.RelationalApplications.BranchCityName,
                IsCargoIntegrationActive = apiResponse.RelationalApplications.IsCargoIntegrationActive,
                CargoProviderId = apiResponse.RelationalApplications.CargoProviderId.GetValueOrDefault(),
                MainAppAddress = mainApp.Address,
                MainAppEmail = mainApp.Email,
                MainAppPhone1 = mainApp.PhoneNumber,
                MainAppPhone2 = mainApp.PhoneNumber2,
                MainApplicant = $"{mainApp.Name} {mainApp.Surname}",
                MainApplicantPassportNumber = mainApp.PassportNumber,
                MainAppSecondContactPerson = mainApp.NameOfSecondContactPerson,
                CreatedBy = apiResponse.RelationalApplications.CreatedBy,
                PrintAll = printAll,
                Applicants = apiResponse.RelationalApplications.Applicants
                .Where(q => q.StatusId != (int)ApplicationStatus.Cancelled && q.ExtraFees.Any(a => a.Category == (int)ExtraFeeCategoryType.Cargo))
                .Select(p => new CargoReceiptViewModel.ApplicantViewModel
                {
                    Name = p.Name,
                    Surname = p.Surname,
                    PassportNumber = p.PassportNumber,
                }).ToList()
            };

            switch (language)
            {
                case "English":
                    return Task.FromResult<IActionResult>(View("GetCargoReceiptEn", viewModel));
                case "Arabic":
                    return Task.FromResult<IActionResult>(View("GetCargoReceiptAr", viewModel));
                case "Russian":
                    return Task.FromResult<IActionResult>(View("GetCargoReceiptRus", viewModel));
                case "AlgerianFrench":
                    return Task.FromResult<IActionResult>(View("GetCargoReceiptFr", viewModel));
                case "AlgerianArabic":
                    return Task.FromResult<IActionResult>(View("GetCargoReceiptAlgerianAr", viewModel));
                default:
                    TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

                    return Task.FromResult<IActionResult>(RedirectToAction("List", "Application", new { Area = "Appointment" }));
            }
        }

        public async Task<IActionResult> PrintAllPrintOut(ApplicationPrintAllApiResponse.GetApplicationPageApiResponse apiResponse)
        {
            if (apiResponse.ApplicationPage == null)
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var visaCategories = await GetCachedVisaCategories();

            var viewModel = new ApplicationViewModel
            {
                EncryptedId = apiResponse.ApplicationPage.Id.ToEncrypt(),
                BranchApplicationCountryId = apiResponse.ApplicationPage.BranchApplicationCountryId,
                BranchApplicationCountry = apiResponse.ApplicationPage.BranchApplicationCountry,
                CountryId = apiResponse.ApplicationPage.CountryId,
                CountryName = apiResponse.ApplicationPage.CountryName,
                ApplicantTypeId = apiResponse.ApplicationPage.ApplicantTypeId,
                ApplicationTypeId = apiResponse.ApplicationPage.ApplicationTypeId,
                PassportNumber = apiResponse.ApplicationPage.PassportNumber,
                PassportExpireDate = apiResponse.ApplicationPage.PassportExpireDate,
                ApplicationPassportStatusId = apiResponse.ApplicationPage.ApplicationPassportStatusId,
                TitleId = apiResponse.ApplicationPage.TitleId,
                Name = apiResponse.ApplicationPage.Name,
                Surname = apiResponse.ApplicationPage.Surname,
                BirthDate = apiResponse.ApplicationPage.BirthDate,
                GenderId = apiResponse.ApplicationPage.GenderId,
                MaritalStatusId = apiResponse.ApplicationPage.MaritalStatusId,
                NationalityId = apiResponse.ApplicationPage.NationalityId,
                Nationality = apiResponse.ApplicationPage.Nationality,
                ResidenceNumber = apiResponse.ApplicationPage.ResidenceNumber,
                MaidenName = apiResponse.ApplicationPage.MaidenName,
                FatherName = apiResponse.ApplicationPage.FatherName,
                MotherName = apiResponse.ApplicationPage.MotherName,
                IsCargoIntegrationActive = apiResponse.ApplicationPage.IsCargoIntegrationActive,

                ShowCityDropdown = apiResponse.ApplicationPage.ShowCityDropdown,
                Email = apiResponse.ApplicationPage.Email,
                PhoneNumber1 = apiResponse.ApplicationPage.PhoneNumber1,
                PhoneNumber2 = apiResponse.ApplicationPage.PhoneNumber2,
                Address = apiResponse.ApplicationPage.Address,
                Note = apiResponse.ApplicationPage.Note,
                RelationalApplicationId = apiResponse.ApplicationPage.RelationalApplicationId,
                ApplicationTime = apiResponse.ApplicationPage.ApplicationTime.AddHours(apiResponse.ApplicationPage.TimeZoneOffset),
                CreatedBy = apiResponse.ApplicationPage.CreatedBy,
                CreatedByNameSurname = apiResponse.ApplicationPage.CreatedByNameSurname,
                StatusId = apiResponse.ApplicationPage.StatusId,

                Insurance = new ApplicationViewModel.ApplicationInsurance()
                {
                    Number = apiResponse.ApplicationPage.Insurance == null ? string.Empty : apiResponse.ApplicationPage.Insurance.Number[0]
                },

                Document = new ApplicationViewModel.ApplicationDocument
                {
                    TotalYearInCountry = apiResponse.ApplicationPage.Document.TotalYearInCountry,
                    ReimbursementTypeId = apiResponse.ApplicationPage.Document.ReimbursementTypeId,
                    ReimbursementSponsorDetail = apiResponse.ApplicationPage.Document.ReimbursementSponsorDetail,
                    Job = apiResponse.ApplicationPage.Document.Job != null ? apiResponse.ApplicationPage.Document.Job : EnumHelper.GetEnumDescription(typeof(DataOccupationType), apiResponse.ApplicationPage.Document.OccupationId.ToString()),
                    CompanyName = apiResponse.ApplicationPage.Document.CompanyName,
                    TotalYearInCompany = apiResponse.ApplicationPage.Document.TotalYearInCompany,
                    MonthlySalary = apiResponse.ApplicationPage.Document.MonthlySalary,
                    MonthlySalaryCurrencyId = apiResponse.ApplicationPage.Document.MonthlySalaryCurrencyId,
                    HasBankAccount = apiResponse.ApplicationPage.Document.HasBankAccount,
                    BankBalance = apiResponse.ApplicationPage.Document.BankBalance,
                    BankBalanceCurrencyId = apiResponse.ApplicationPage.Document.BankBalanceCurrencyId,
                    HasDeed = apiResponse.ApplicationPage.Document.HasDeed,
                    VisaCategoryId = apiResponse.ApplicationPage.Document.VisaCategoryId,
                    VisaCategory = GetVisaCategoryNameFromId(apiResponse.ApplicationPage.Document.VisaCategoryId, visaCategories),
                    NumberOfEntryId = EnumHelper.GetEnumDescription(typeof(NumberOfEntryType), apiResponse.ApplicationPage.Document.NumberOfEntryId.ToString()),
                    HasEntryBan = apiResponse.ApplicationPage.Document.HasEntryBan,
                    EntryDate = apiResponse.ApplicationPage.Document.EntryDate,
                    ExitDate = apiResponse.ApplicationPage.Document.ExitDate,
                    CityName = apiResponse.ApplicationPage.Document.CityName,
                    AccomodationDetail = apiResponse.ApplicationPage.Document.AccomodationDetail,
                    HasRelativeAbroad = apiResponse.ApplicationPage.Document.HasRelativeAbroad,
                    RelativeLocation = apiResponse.ApplicationPage.Document.RelativeLocation,
                    PersonTravelWith = apiResponse.ApplicationPage.Document.PersonTravelWith,
                    PersonTravelWithHasVisa = apiResponse.ApplicationPage.Document.PersonTravelWithHasVisa,
                    ProvidedWithHasRelatedInsurance = apiResponse.ApplicationPage.Document.ProvidedWithHasRelatedInsurance,
                    ApplicationTogether = apiResponse.ApplicationPage.Document.ApplicationTogether,
                    ApplicationTogetherFiftyYearCount = apiResponse.ApplicationPage.Document.ApplicationTogetherFiftyYearCount,
                    ApplicationTogetherFifteenYearCount = apiResponse.ApplicationPage.Document.ApplicationTogetherFifteenYearCount,
                    HasPersonVisitedTurkeyBefore = apiResponse.ApplicationPage.Document.HasPersonVisitedTurkeyBefore,
                    ResidenceApplicationToBeMade = apiResponse.ApplicationPage.Document.ResidenceApplicationToBeMade
                },

                VisaHistories = apiResponse.ApplicationPage.VisaHistories.Select(p => new ApplicationViewModel.ApplicationVisaHistory
                {
                    CountryId = p.CountryId,
                    CountryName = p.CountryName,
                    FromDate = p.FromDate,
                    UntilDate = p.UntilDate,
                    IsUsed = p.IsUsed,
                    NumberOfEntryId = p.NumberOfEntryId,
                    NumberOfEntry = EnumHelper.GetEnumDescription(typeof(NumberOfEntryType), p.NumberOfEntryId.ToString()),
                    VisaIsUsedYear = p.VisaIsUsedYear,
                    VisaIsUsedYearString = EnumHelper.GetEnumDescription(typeof(VisaIsUsedYear), p.VisaIsUsedYear.ToString()),
                    OldVisaDecision = p.OldVisaDecisionId == null ? "" : EnumHelper.GetEnumDescription(typeof(OldVisaDecisionType), p.OldVisaDecisionId.ToString())
                }).ToList(),

                ExtraFees = apiResponse.ApplicationPage.ExtraFees.Where(q => q.ShowInSummary).Select(p => new ApplicationViewModel.ApplicationExtraFee
                {
                    TypeId = p.TypeId,
                    ExtraFeeName = p.ExtraFeeName,
                    Price = p.Price,
                    Tax = p.Tax,
                    CurrencyId = p.CurrencyId,
                    Quantity = p.Quantity,
                    Category = p.Category,
                    PaymentMethod = p.PaymentMethod
                }).ToList(),

                StatusHistories = apiResponse.ApplicationPage.StatusHistories.Select(p => new ApplicationViewModel.ApplicationStatusHistory()
                {
                    Order = p.Order,
                    Status = p.Status,
                    NameSurname = p.NameSurname,
                    ApplicationCreatedAt = apiResponse.ApplicationPage.ApplicationTime,
                    StatusDate = p.StatusDate.AddHours(apiResponse.ApplicationPage.TimeZoneOffset)
                }).ToList(),

                ApplicationHistories = apiResponse.ApplicationPage.ApplicationHistories.Select(p => new ApplicationViewModel.ApplicationHistory
                {
                    ApplicationHistoryId = p.ApplicationHistoryId,
                    PropertyName = p.PropertyName,
                    PreviousValue = p.PreviousValue,
                    CurrentValue = p.CurrentValue,
                    CreatedBy = p.CreatedBy,
                    CreatedByNameSurname = p.CreatedByNameSurname,
                    CreatedAt = p.CreatedAt
                }).ToList(),
                PrintAll = apiResponse.ApplicationPage.PrintAll,
                IsInquiryActive = apiResponse.ApplicationPage.IsInquiryActive,
                Inquiries = apiResponse.ApplicationPage.Inquiries.Select(s => new InquiryViewModel()
                {
                    Id = s.Id,
                    BranchId = s.BranchId,
                    Name = s.Name,
                    Observation = s.Observation,
                    InquiryQuestions = s.InquiryQuestions.Select(q => new InquiryQuestionViewModel()
                    {
                        Id = q.Id,
                        Name = q.Name,
                        QuestionTypeId = q.QuestionTypeId,
                        IsMultiSelectable = q.IsMultiSelectable,
                        IsRequired = q.IsRequired,
                        IsDescriptionIncluded = q.IsDescriptionIncluded,
                        ElectiveAnswer = q.ElectiveAnswer == null ? new InquiryElectiveAnswerViewModel() : new InquiryElectiveAnswerViewModel()
                        {
                            Description = q.ElectiveAnswer?.Description,
                            Ids = q.ElectiveAnswer?.Ids
                        },
                        ExplanationAnswers = q.ExplanationAnswers == null ? new List<InquiryExplanationAnswerViewModel>() : q.ExplanationAnswers.Select(s => new InquiryExplanationAnswerViewModel()
                        {
                            Explanation = s.Explanation,
                            Id = s.Id
                        }).ToList(),
                        Choices = q.Choices.Select(c => new InquiryQuestionChoiceViewModel()
                        {
                            Id = c.Id,
                            Name = c.Name
                        }).ToList()
                    }).ToList()
                }).ToList()
            };

            foreach (var item in viewModel.ApplicationHistories)
            {
                item.PropertyNameDetail = GetApplicationHistoryPropertyDetail(item.PropertyName);
                item.PreviousValueDetail = GetApplicationHistoryValueDetail(item.PropertyName, item.PreviousValue, visaCategories);
                item.CurrentValueDetail = GetApplicationHistoryValueDetail(item.PropertyName, item.CurrentValue, visaCategories);
            }

            List<int> currency = new List<int>();
            foreach (var item in viewModel.ExtraFees)
            {
                if (!currency.Contains(item.CurrencyId))
                {
                    currency.Add(item.CurrencyId);
                }
            }
            var allTotal = new List<ApplicationViewModel.Total>();
            foreach (var item in currency)
            {
                var totalCount = new ApplicationViewModel.Total()
                {
                    CurrencyId = item,
                    Price = viewModel.ExtraFees.Where(q => q.CurrencyId == item).Sum(q => q.Price * q.Quantity),
                    Tax = viewModel.ExtraFees.Where(q => q.CurrencyId == item).Sum(q => q.Tax),
                    Count = viewModel.ExtraFees.Where(q => q.CurrencyId == item).Sum(q => q.Quantity)
                };
                allTotal.Add(totalCount);
            }
            viewModel.TotalCount = allTotal;

            return View("_PrintOutPDF", viewModel);
        }

        public Tuple<string, string> GetPrintAllIcrNoteByLanguageId(int languageId, ApplicationPrintAllApiResponse.GetIcrApiResponse apiResponse, String previousNote, int branchCountryId)
        {
            if (branchCountryId == 80 || branchCountryId == 181 || branchCountryId == 100 || branchCountryId == 93)
            {
                switch (languageId)
                {
                    case (int)ICRLanguage.English:
                        return Tuple.Create(apiResponse.BranchIcr.Name, apiResponse.BranchIcr.Note);
                    case (int)ICRLanguage.Arabic:
                        return Tuple.Create(apiResponse.BranchIcr.NameAr, apiResponse.BranchIcr.NoteAr);
                    case (int)ICRLanguage.Turkmen:
                        return Tuple.Create(apiResponse.BranchIcr.NameTm, apiResponse.BranchIcr.NoteTm);
                    case (int)ICRLanguage.Russian:
                        return Tuple.Create(apiResponse.BranchIcr.NameRu, apiResponse.BranchIcr.NoteRu);
                    default:
                        return Tuple.Create(apiResponse.BranchIcr.Name, apiResponse.BranchIcr.Note);
                }
            }

            switch (languageId)
            {
                case (int)ICRLanguage.English:
                    return Tuple.Create(apiResponse.BranchIcr.Name, previousNote + apiResponse.BranchIcr.Note);
                case (int)ICRLanguage.Arabic:
                    return Tuple.Create(apiResponse.BranchIcr.NameAr, previousNote.Replace(" ", "&nbsp;") + apiResponse.BranchIcr.NoteAr);
                case (int)ICRLanguage.Turkmen:
                    return Tuple.Create(apiResponse.BranchIcr.NameTm, previousNote + apiResponse.BranchIcr.NoteTm);
                case (int)ICRLanguage.Russian:
                    return Tuple.Create(apiResponse.BranchIcr.NameRu, previousNote + apiResponse.BranchIcr.NoteRu);
                case (int)ICRLanguage.French:
                    return Tuple.Create(apiResponse.BranchIcr.NameFr, previousNote + apiResponse.BranchIcr.NoteFr);
                default:
                    return Tuple.Create(apiResponse.BranchIcr.Name, previousNote + apiResponse.BranchIcr.Note);
            }
        }

        public Tuple<string, string> GetPrintAllIndividualIcrNoteByLanguageId(int languageId, ApplicationPrintAllApiResponse.GetIndividualIcrApiResponse apiResponse, String previousNote, int branchCountryId)
        {
            if (branchCountryId == 80 || branchCountryId == 181 || branchCountryId == 100 || branchCountryId == 93)
            {
                switch (languageId)
                {
                    case (int)ICRLanguage.English:
                        return Tuple.Create(apiResponse.BranchIcr.Name, apiResponse.BranchIcr.Note);
                    case (int)ICRLanguage.Arabic:
                        return Tuple.Create(apiResponse.BranchIcr.NameAr, apiResponse.BranchIcr.NoteAr);
                    case (int)ICRLanguage.Turkmen:
                        return Tuple.Create(apiResponse.BranchIcr.NameTm, apiResponse.BranchIcr.NoteTm);
                    case (int)ICRLanguage.Russian:
                        return Tuple.Create(apiResponse.BranchIcr.NameRu, apiResponse.BranchIcr.NoteRu);
                    default:
                        return Tuple.Create(apiResponse.BranchIcr.Name, apiResponse.BranchIcr.Note);
                }
            }

            switch (languageId)
            {
                case (int)ICRLanguage.English:
                    return Tuple.Create(apiResponse.BranchIcr.Name, previousNote + apiResponse.BranchIcr.Note);
                case (int)ICRLanguage.Arabic:
                    return Tuple.Create(apiResponse.BranchIcr.NameAr, previousNote.Replace(" ", "&nbsp;") + apiResponse.BranchIcr.NoteAr);
                case (int)ICRLanguage.Turkmen:
                    return Tuple.Create(apiResponse.BranchIcr.NameTm, previousNote + apiResponse.BranchIcr.NoteTm);
                case (int)ICRLanguage.Russian:
                    return Tuple.Create(apiResponse.BranchIcr.NameRu, previousNote + apiResponse.BranchIcr.NoteRu);
                case (int)ICRLanguage.French:
                    return Tuple.Create(apiResponse.BranchIcr.NameFr, previousNote + apiResponse.BranchIcr.NoteFr);
                default:
                    return Tuple.Create(apiResponse.BranchIcr.Name, previousNote + apiResponse.BranchIcr.Note);
            }
        }

        #endregion

        private string GetApplicationHistoryPropertyDetail(string propertyName)
        {
            var result = propertyName;

            switch (propertyName)
            {
                case "ApplicantTypeId":
                    result = SiteResources.ApplicantType;
                    break;
                case "ApplicationTypeId":
                    result = SiteResources.ApplicationType;
                    break;
                case "PassportNumber":
                    result = SiteResources.PassportNumber;
                    break;
                case "PassportExpireDate":
                    result = SiteResources.PassportExpireDate;
                    break;
                case "ApplicationPassportStatusId":
                    result = SiteResources.ApplicationPassportStatus;
                    break;
                case "TitleId":
                    result = SiteResources.Title;
                    break;
                case "Name":
                    result = SiteResources.Name;
                    break;
                case "Surname":
                    result = SiteResources.Surname;
                    break;
                case "BirthDate":
                    result = SiteResources.BirthDate;
                    break;
                case "GenderId":
                    result = SiteResources.Gender;
                    break;
                case "MaritalStatusId":
                    result = SiteResources.MaritalStatus;
                    break;
                case "NationalityId":
                    result = SiteResources.Nationality;
                    break;
                case "MaidenName":
                    result = SiteResources.MaidenName;
                    break;
                case "FatherName":
                    result = SiteResources.FatherName;
                    break;
                case "MotherName":
                    result = SiteResources.MotherName;
                    break;
                case "Email":
                    result = SiteResources.Email;
                    break;
                case "PhoneNumber1":
                    result = SiteResources.PhoneNumber;
                    break;
                case "PhoneNumber2":
                    result = SiteResources.PhoneNumber;
                    break;
                case "Address":
                    result = SiteResources.Address;
                    break;
                case "ApplicationStatusId":
                    result = SiteResources.ApplicationStatus;
                    break;
                case "Note":
                    result = SiteResources.Notes;
                    break;
                case "StatusId":
                    result = SiteResources.Status;
                    break;
                //ApplicationDocument
                case "TotalYearInCountry":
                    result = SiteResources.TotalYearInCountry;
                    break;
                case "ReimbursementTypeId":
                    result = SiteResources.ReimbursementType;
                    break;
                case "ReimbursementSponsorDetail":
                    result = SiteResources.ReimbursementSponsorDetail;
                    break;
                case "Job":
                    result = SiteResources.Job;
                    break;
                case "CompanyName":
                    result = SiteResources.CompanyName;
                    break;
                case "TotalYearInCompany":
                    result = SiteResources.TotalYearInCompany;
                    break;
                case "MonthlySalary":
                    result = SiteResources.MonthlySalary;
                    break;
                case "MonthlySalaryCurrencyId":
                    result = $"{SiteResources.Currency} ({SiteResources.MonthlySalary})";
                    break;
                case "HasBankAccount":
                    result = SiteResources.HasBankAccount;
                    break;
                case "BankBalance":
                    result = SiteResources.BankBalance;
                    break;
                case "BankBalanceCurrencyId":
                    result = $"{SiteResources.Currency} ({SiteResources.BankBalance})";
                    break;
                case "HasDeed":
                    result = SiteResources.HasDeed;
                    break;
                case "VisaCategoryId":
                    result = SiteResources.VisaCategory;
                    break;
                case "HasEntryBan":
                    result = SiteResources.HasEntryBan;
                    break;
                case "EntryDate":
                    result = SiteResources.EntryDate;
                    break;
                case "ExitDate":
                    result = SiteResources.ExitDate;
                    break;
                case "CityName":
                    result = SiteResources.CityToVisit;
                    break;
                case "AccomodationDetail":
                    result = SiteResources.AccomodationDetail;
                    break;
                case "HasRelativeAbroad":
                    result = SiteResources.HasRelativeAbroad;
                    break;
                case "RelativeLocation":
                    result = SiteResources.RelativeLocation;
                    break;
                case "PersonTravelWith":
                    result = SiteResources.PersonTravelWith;
                    break;
                case "PersonTravelWithHasVisa":
                    result = SiteResources.PersonTravelWithHasVisa;
                    break;
                case "ProvidedWithHasRelatedInsurance":
                    result = SiteResources.ProvidedWithHasRelatedInsurance;
                    break;
                case "ApplicationTogether":
                    result = SiteResources.ApplicationTogether;
                    break;
                default:
                    break;
            }

            return result;
        }

        private string GetApplicationHistoryValueDetail(string propertyName, string value, List<BranchApplicationCountryVisaCategoriesApiResponse.VisaCategory> visaCategoryDataList)
        {
            var result = value;
            switch (propertyName)
            {
                case "ApplicantTypeId":
                    result = EnumHelper.GetEnumDescription(typeof(ApplicantType), value);
                    break;
                case "ApplicationTypeId":
                    result = EnumHelper.GetEnumDescription(typeof(ApplicationType), value);
                    break;
                case "ApplicationPassportStatusId":
                    result = EnumHelper.GetEnumDescription(typeof(ApplicationPassportStatus), value);
                    break;
                case "TitleId":
                    result = EnumHelper.GetEnumDescription(typeof(Title), value);
                    break;
                case "GenderId":
                    result = EnumHelper.GetEnumDescription(typeof(Gender), value);
                    break;
                case "MaritalStatusId":
                    result = EnumHelper.GetEnumDescription(typeof(MaritalStatus), value);
                    break;
                case "NationalityId":
                    //TODO: get country list and use the related value
                    break;
                case "ApplicationStatusId":
                    //TODO: use the value taken by ApplicationStatus table
                    break;
                case "StatusId":
                    result = EnumHelper.GetEnumDescription(typeof(ApplicationStatus), value);
                    break;
                //ApplicationDocument
                case "ReimbursementTypeId":
                    result = EnumHelper.GetEnumDescription(typeof(ReimbursementType), value);
                    break;
                case "MonthlySalaryCurrencyId":
                    result = EnumHelper.GetEnumDescription(typeof(CurrencyType), value);
                    break;
                case "HasBankAccount":
                    result = EnumHelper.GetEnumDescription(typeof(YesNo), value);
                    break;
                case "BankBalanceCurrencyId":
                    result = EnumHelper.GetEnumDescription(typeof(CurrencyType), value);
                    break;
                case "HasDeed":
                    result = EnumHelper.GetEnumDescription(typeof(YesNo), value);
                    break;
                case "VisaCategoryId":
                    result = GetVisaCategoryNameFromId(value.ToInt(), visaCategoryDataList);
                    break;
                case "HasEntryBan":
                    result = EnumHelper.GetEnumDescription(typeof(YesNo), value);
                    break;
                case "HasRelativeAbroad":
                    result = EnumHelper.GetEnumDescription(typeof(YesNo), value);
                    break;
                case "PersonTravelWithHasVisa":
                    result = EnumHelper.GetEnumDescription(typeof(YesNo), value);
                    break;
                case "ProvidedWithHasRelatedInsurance":
                    result = EnumHelper.GetEnumDescription(typeof(YesNo), value);
                    break;
                default:
                    break;
            }
            return result;
        }

        public async Task<IActionResult> GetIndividualICR(string encryptedApplicationId, bool taxInclude, int icrLanguage, bool includeAll = false, bool printAll = false, bool printer = false)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var apiRequest = new GetApplicationPrintAllApiRequest
            {
                BranchId = UserSession.BranchId.GetValueOrDefault(),
                ApplicationId = encryptedApplicationId.ToDecryptInt(),
                RelationalApplicationId = encryptedApplicationId.ToDecryptInt(),
                IcrLanguage = icrLanguage,
                Icr = true,
                EntryInformationFormForTurkey = false,
                Cargo = false,
                ApplicationPage = false,
            };

            var enApiRequestHeader = PortalGatewayApiDefaultRequestHeaders;
            enApiRequestHeader.Remove("languageId");
            enApiRequestHeader.Add("languageId", ((int)ICRLanguage.English).ToString());

            var apiResponse = await PortalHttpClientHelper
            .PostAsJsonAsync<ApiResponse<GetIndividualIcrApiResponse>>
                (apiRequest, ApiMethodName.Appointment.GetApplicationIndividualIcr, AppSettings.PrintData.BaseApiUrl, enApiRequestHeader)
            .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                return Json(new
                {
                    Message = apiResponse.Message.ToDescription(),
                    ResultType = ResultType.Warning
                });
            }

            var icr = await GetPrintAllIndividualICR(apiResponse.Data, encryptedApplicationId.ToEncrypt(), taxInclude, icrLanguage, includeAll, false, printer);

            var icrActionResultType = icr.GetType();

            if (icrActionResultType.Name == "RedirectToActionResult" || icrActionResultType
                    .GetProperty("Model")?
                    .GetValue(icr) is not ICRViewModel icrModel)
            {
                return Json(new
                {
                    Message = ResultMessage.MissingOrInvalidData.ToDescription(),
                    ResultType = ResultType.Warning
                });
            }

            var viewIcr = await this.RenderViewAsync("GetICR", icrModel);

            return base.Content(viewIcr.ToString(), "text/html", Encoding.UTF8);
        }

        public async Task<IActionResult> GetICR(string encryptedApplicationId, bool taxInclude, int icrLanguage, bool includeAll = false, bool printAll = false, bool printer = false)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var apiRequest = new GetApplicationPrintAllApiRequest
            {
                BranchId = UserSession.BranchId.GetValueOrDefault(),
                ApplicationId = encryptedApplicationId.ToDecryptInt(),
                RelationalApplicationId = encryptedApplicationId.ToDecryptInt(),
                IcrLanguage = icrLanguage,
                Icr = true,
                EntryInformationFormForTurkey = false,
                Cargo = false,
                ApplicationPage = false,
            };

            var enApiRequestHeader = PortalGatewayApiDefaultRequestHeaders;
            enApiRequestHeader.Remove("languageId");
            enApiRequestHeader.Add("languageId", ((int)ICRLanguage.English).ToString());

            var apiResponse = await PortalHttpClientHelper
            .PostAsJsonAsync<ApiResponse<GetIcrApiResponse>>
                (apiRequest, ApiMethodName.Appointment.GetApplicationIcr, AppSettings.PrintData.BaseApiUrl, enApiRequestHeader)
            .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                return Json(new
                {
                    Message = apiResponse.Message.ToDescription(),
                    ResultType = ResultType.Warning
                });
            }

            var icr = await GetPrintAllICR(apiResponse.Data, encryptedApplicationId.ToEncrypt(), taxInclude, icrLanguage, includeAll, false, printer);

            var icrActionResultType = icr.GetType();

            if (icrActionResultType.Name == "RedirectToActionResult" || icrActionResultType
                    .GetProperty("Model")?
                    .GetValue(icr) is not ICRViewModel icrModel)
            {
                return Json(new
                {
                    Message = ResultMessage.MissingOrInvalidData.ToDescription(),
                    ResultType = ResultType.Warning
                });
            }

            var viewIcr = await this.RenderViewAsync("GetICR", icrModel);

            return base.Content(viewIcr.ToString(), "text/html", Encoding.UTF8);
        }

        public Tuple<string, string> GetIcrNoteByLanguageId(int languageId, ApiResponse<BranchIcrApiResponse> branchIcr, String previousNote, int branchCountryId)
        {
            if (branchCountryId == 80 || branchCountryId == 181 || branchCountryId == 100 || branchCountryId == 93)
            {
                switch (languageId)
                {
                    case (int)ICRLanguage.English:
                        return Tuple.Create(branchIcr.Data.Name, branchIcr.Data.Note);
                    case (int)ICRLanguage.Arabic:
                        return Tuple.Create(branchIcr.Data.NameAr, branchIcr.Data.NoteAr);
                    case (int)ICRLanguage.Turkmen:
                        return Tuple.Create(branchIcr.Data.NameTm, branchIcr.Data.NoteTm);
                    case (int)ICRLanguage.Russian:
                        return Tuple.Create(branchIcr.Data.NameRu, branchIcr.Data.NoteRu);
                    default:
                        return Tuple.Create(branchIcr.Data.Name, branchIcr.Data.Note);
                }
            }
            else
            {
                switch (languageId)
                {
                    case (int)ICRLanguage.English:
                        return Tuple.Create(branchIcr.Data.Name, previousNote + branchIcr.Data.Note);
                    case (int)ICRLanguage.Arabic:
                        return Tuple.Create(branchIcr.Data.NameAr, previousNote.Replace(" ", "&nbsp;") + branchIcr.Data.NoteAr);
                    case (int)ICRLanguage.Turkmen:
                        return Tuple.Create(branchIcr.Data.NameTm, previousNote + branchIcr.Data.NoteTm);
                    case (int)ICRLanguage.Russian:
                        return Tuple.Create(branchIcr.Data.NameRu, previousNote + branchIcr.Data.NoteRu);
                    case (int)ICRLanguage.French:
                        return Tuple.Create(branchIcr.Data.NameFr, previousNote + branchIcr.Data.NoteFr);
                    default:
                        return Tuple.Create(branchIcr.Data.Name, previousNote + branchIcr.Data.Note);
                }
            }
        }

        public string GetCurrencyConverterByLanguageId(int languageId, int currencyId)
        {
            switch (languageId)
            {
                case (int)ICRLanguage.English:
                    return EnumHelper.GetEnumDescription(typeof(CurrencyType), currencyId.ToString());
                case (int)ICRLanguage.Arabic:
                    return CurrencyNameHelper.CurrencyConverterArabic(currencyId);
                case (int)ICRLanguage.Turkmen:
                    return CurrencyNameHelper.CurrencyConverterTurkmen(currencyId);
                case (int)ICRLanguage.Russian:
                    return CurrencyNameHelper.CurrencyConverterRussian(currencyId);
                case (int)ICRLanguage.French:
                    return CurrencyNameHelper.CurrencyConverterFrench(currencyId);
                default:
                    return EnumHelper.GetEnumDescription(typeof(CurrencyType), currencyId.ToString());
            }
        }
        public string GetExtraFeeNameConverterByLanguageId(int languageId, int branchCountryId)
        {
            if (branchCountryId == 77)
            {
                switch (languageId)
                {
                    case (int)ICRLanguage.Turkish:
                        return "Türkiye Vize Başvuru İşlem Bedeli";
                    case (int)ICRLanguage.English:
                        return "Türkiye Visa Application Processing Fee";
                    case (int)ICRLanguage.Arabic:
                        return "رسوم الحصول على تأشيرة تركيا"; //bunları sonra değiştir
                    case (int)ICRLanguage.Turkmen:
                        return "Türkiye Visa Application Processing Fee";//bunları sonra değiştir
                    case (int)ICRLanguage.Russian:
                        return "Türkiye Visa Application Processing Fee"; //bunları sonra değiştir
                    default:
                        return "Türkiye Vize Başvuru İşlem Bedeli";
                }
            }
            else
            {
                switch (languageId)
                {
                    case (int)ICRLanguage.Turkish:
                        return "Türkiye Vize Başvuru Bedeli";
                    case (int)ICRLanguage.English:
                        return "Turkiye Visa Application Fee";
                    case (int)ICRLanguage.Arabic:
                        return "تكلفة طلب التأشيرة إلى تركيا";
                    case (int)ICRLanguage.Turkmen:
                        return "Turkiye Wizasynyn Arza Bahasy";
                    case (int)ICRLanguage.Russian:
                        return "СТОИМОСТЬ ПРИМЕНЕНИЕ ВИЗЫ В ТУРЦИЮ";
                    case (int)ICRLanguage.French:
                        return "Frais de Demande de Visa Turquie";
                    default:
                        return "Türkiye Vize Bedeli";
                }
            }
        }
        public async Task<string> IndividualIcrRendererByLanguage(int languageId, ICRViewModel viewModel, bool printer)
        {
            string viewStr;
            string viewApplicant;
            int applicantCount;
            var viewApplicantStr = new List<string>();

            switch (languageId)
            {
                case (int)ICRLanguage.English:
                    applicantCount = viewModel.Applicants.Count;

                    for (int i = 0; i < applicantCount; i = i + 3)
                    {
                        viewModel.Order = i;
                        viewApplicantStr.Add(await this.RenderViewAsync("_IndividualICRApplicantsAlgeriaEnglish", viewModel));
                    }

                    viewApplicant = string.Join(" ", viewApplicantStr.Select(q => q));
                    return viewApplicant;
                case (int)ICRLanguage.Arabic:
                    applicantCount = viewModel.Applicants.Count;

                    for (int i = 0; i < applicantCount; i = i + 3)
                    {
                        viewModel.Order = i;
                        viewApplicantStr.Add(await this.RenderViewAsync("_IndividualICRApplicantsAlgeriaArabic", viewModel));
                    }

                    viewApplicant = string.Join(" ", viewApplicantStr.Select(q => q));
                    return viewApplicant;
                case (int)ICRLanguage.French:

                    applicantCount = viewModel.Applicants.Count;

                    for (var i = 0; i < applicantCount; i += 3)
                    {
                        viewModel.Order = i;
                        viewApplicantStr.Add(await this.RenderViewAsync("_IndividualICRApplicantsAlgeriaFrench", viewModel));
                    }

                    viewApplicant = string.Join(" ", viewApplicantStr.Select(q => q));
                    return viewApplicant;
                default:
                    return null;
            }
        }
        public async Task<string> IcrRendererByLanguage(int languageId, ICRViewModel viewModel, bool printer)
        {
            string viewStr;
            string viewApplicant;
            int applicantCount;
            var viewApplicantStr = new List<string>();

            switch (languageId)
            {
                case (int)ICRLanguage.English:
                    if (printer)
                    {
                        if (viewModel.BranchCountryId == 3)
                        {
                            viewApplicant = await this.RenderViewAsync("_ICRApplicantsAlgeriaEnglish_Printer", viewModel);
                            return viewApplicant;
                        }
                        else
                        {
                            viewApplicant = await this.RenderViewAsync("_ICR_Printer", viewModel);
                            return viewApplicant;
                        }
                    }
                    else
                    {
                        if (viewModel.BranchCountryId == 3)
                        {
                            applicantCount = viewModel.Applicants.Count;

                            for (int i = 0; i < applicantCount; i = i + 3)
                            {
                                viewModel.Order = i;
                                viewApplicantStr.Add(await this.RenderViewAsync("_ICRApplicantsAlgeriaEnglish", viewModel));
                            }

                            viewApplicant = string.Join(" ", viewApplicantStr.Select(q => q));
                            return viewApplicant;
                        }
                        else
                        {
                            viewStr = await this.RenderViewAsync("_ICR", viewModel);
                            applicantCount = viewModel.Applicants.Count;

                            for (int i = 0; i < applicantCount; i = i + 3)
                            {
                                viewModel.Order = i;
                                viewApplicantStr.Add(await this.RenderViewAsync("_ICRApplicants", viewModel));
                            }

                            viewApplicant = string.Join(" ", viewApplicantStr.Select(q => q));
                            return viewStr.Replace("<progress></progress>", viewApplicant);
                        }
                    }
                case (int)ICRLanguage.Arabic:
                    if (viewModel.BranchCountryId == 3)
                    {
                        applicantCount = viewModel.Applicants.Count;

                        for (int i = 0; i < applicantCount; i = i + 3)
                        {
                            viewModel.Order = i;
                            viewApplicantStr.Add(await this.RenderViewAsync("_ICRApplicantsAlgeriaArabic", viewModel));
                        }

                        viewApplicant = string.Join(" ", viewApplicantStr.Select(q => q));
                        return viewApplicant;
                    }
                    else
                    {
                        viewStr = await this.RenderViewAsync("_ICRArabic", viewModel);
                        applicantCount = viewModel.Applicants.Count;

                        for (int i = 0; i < applicantCount; i = i + 3)
                        {
                            viewModel.Order = i;
                            viewApplicantStr.Add(await this.RenderViewAsync("_ICRApplicantsArabic", viewModel));
                        }

                        var viewApplicantsRTL = string.Join(" ", viewApplicantStr.Select(q => q));
                        return viewStr.Replace("<progress></progress>", viewApplicantsRTL);
                    }
                case (int)ICRLanguage.French:

                    applicantCount = viewModel.Applicants.Count;

                    for (var i = 0; i < applicantCount; i += 3)
                    {
                        viewModel.Order = i;
                        viewApplicantStr.Add(await this.RenderViewAsync("_ICRApplicantsAlgeriaFrench", viewModel));
                    }

                    viewApplicant = string.Join(" ", viewApplicantStr.Select(q => q));
                    return viewApplicant;

                case (int)ICRLanguage.Turkmen:
                    viewStr = await this.RenderViewAsync("_ICRTurkmen", viewModel);
                    applicantCount = viewModel.Applicants.Count;

                    for (int i = 0; i < applicantCount; i = i + 3)
                    {
                        viewModel.Order = i;
                        viewApplicantStr.Add(await this.RenderViewAsync("_ICRApplicantsTurkmen", viewModel));
                    }

                    viewApplicant = string.Join(" ", viewApplicantStr.Select(q => q));
                    return viewStr.Replace("<progress></progress>", viewApplicant);

                case (int)ICRLanguage.Russian:

                    viewStr = await this.RenderViewAsync("_ICRRussian", viewModel);
                    applicantCount = viewModel.Applicants.Count;

                    for (int i = 0; i < applicantCount; i = i + 3)
                    {
                        viewModel.Order = i;
                        viewApplicantStr.Add(await this.RenderViewAsync("_ICRApplicantsRussian", viewModel));
                    }

                    viewApplicant = string.Join(" ", viewApplicantStr.Select(q => q));
                    return viewStr.Replace("<progress></progress>", viewApplicant);

                case (int)ICRLanguage.EnglishAndArabic:

                    viewStr = await this.RenderViewAsync("_ICR_KSA", viewModel);
                    applicantCount = viewModel.Applicants.Count;

                    for (int i = 0; i < applicantCount; i = i + 3)
                    {
                        viewModel.Order = i;
                        viewApplicantStr.Add(await this.RenderViewAsync("_ICRApplicants_KSA", viewModel));
                    }

                    viewApplicant = string.Join(" ", viewApplicantStr.Select(q => q));
                    return viewStr.Replace("<progress></progress>", viewApplicant);

                default:
                    return null;
            }
        }

        public async Task<IActionResult> CreateICR(string createdBy, string invoiceNumber, string vatAmount, string totalAmount, bool taxInclude, DateTime invoiceTime, bool isTLV)
        {
            var qrNote = string.Empty;
            if (taxInclude && isTLV)
            {
                var formattedDateTime = invoiceTime.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss' 'tt", CultureInfo.CreateSpecificCulture("en-US"));
                var seller = $"01-{createdBy.Length.ToString("X").PadLeft(2, '0')}-{BitConverter.ToString(Encoding.Default.GetBytes(createdBy))}";
                var varNumber = $"02-{invoiceNumber.Length.ToString("X").PadLeft(2, '0')}-{BitConverter.ToString(Encoding.Default.GetBytes(invoiceNumber))}";
                var timeStamp = $"03-{formattedDateTime.Length.ToString("X").PadLeft(2, '0')}-{BitConverter.ToString(Encoding.Default.GetBytes(formattedDateTime))}";
                var invoiceTotal = $"04-{totalAmount.Length.ToString("X").PadLeft(2, '0')}-{BitConverter.ToString(Encoding.Default.GetBytes(totalAmount.Replace(",", ".")))}";
                var vatTotal = $"05-{vatAmount.Length.ToString("X").PadLeft(2, '0')}-{BitConverter.ToString(Encoding.Default.GetBytes(vatAmount.Replace(",", ".")))}";
                var hex = $"{seller}-{varNumber}-{timeStamp}-{invoiceTotal}-{vatTotal}";
                var hexString = ConverterHelper.FromHex(hex);
                qrNote = Convert.ToBase64String(hexString);
            }

            var qrReturnUrl = AppSettings.IcrReturnUrl;
            var content = taxInclude && isTLV ? qrNote : qrReturnUrl;
            using var generator = new QRCodeGenerator();

            // Generate QrCode
            var qr = generator.CreateQrCode(content, ECCLevel.Q);

            // Render to canvas
            var info = new SKImageInfo(512, 512);
            using var surface = SKSurface.Create(info);
            var canvas = surface.Canvas;
            canvas.Render(qr, info.Width, info.Height);

            // Output to Stream -> File
            using var image = surface.Snapshot();
            using var data = image.Encode(SKEncodedImageFormat.Png, 100);

            return File(data.ToArray(), "image/png");
        }

        public string CreateICR_(string createdBy, string invoiceNumber, string vatAmount, string totalAmount, bool taxInclude, DateTime invoiceTime, bool isTLV)
        {
            var qrNote = string.Empty;
            if (taxInclude && isTLV)
            {
                var formattedDateTime = invoiceTime.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss' 'tt", CultureInfo.CreateSpecificCulture("en-US"));
                var seller = $"01-{createdBy.Length.ToString("X").PadLeft(2, '0')}-{BitConverter.ToString(Encoding.Default.GetBytes(createdBy))}";
                var varNumber = $"02-{invoiceNumber.Length.ToString("X").PadLeft(2, '0')}-{BitConverter.ToString(Encoding.Default.GetBytes(invoiceNumber))}";
                var timeStamp = $"03-{formattedDateTime.Length.ToString("X").PadLeft(2, '0')}-{BitConverter.ToString(Encoding.Default.GetBytes(formattedDateTime))}";
                var invoiceTotal = $"04-{totalAmount.Length.ToString("X").PadLeft(2, '0')}-{BitConverter.ToString(Encoding.Default.GetBytes(totalAmount.Replace(",", ".")))}";
                var vatTotal = $"05-{vatAmount.Length.ToString("X").PadLeft(2, '0')}-{BitConverter.ToString(Encoding.Default.GetBytes(vatAmount.Replace(",", ".")))}";
                var hex = $"{seller}-{varNumber}-{timeStamp}-{invoiceTotal}-{vatTotal}";
                var hexString = ConverterHelper.FromHex(hex);
                qrNote = Convert.ToBase64String(hexString);
            }

            var qrReturnUrl = AppSettings.IcrReturnUrl;

            var content = taxInclude && isTLV ? qrNote : qrReturnUrl;
            using var generator = new QRCodeGenerator();

            // Generate QrCode
            var qr = generator.CreateQrCode(content, ECCLevel.Q);

            // Render to canvas
            var info = new SKImageInfo(512, 512);
            using var surface = SKSurface.Create(info);
            var canvas = surface.Canvas;
            canvas.Render(qr, info.Width, info.Height);

            // Output to Stream -> File
            using var image = surface.Snapshot();
            using var data = image.Encode(SKEncodedImageFormat.Png, 100);

            string str = Convert.ToBase64String(data.ToArray());
            return str;
        }

        public IActionResult CreateBarcodeICR(string visaNo)
        {
            return File(Convert.FromBase64String(CreateBarcodeICR_(visaNo)), "image/png");
        }

        public string CreateBarcodeICR_(string visaNo)
        {
            return Convert.ToBase64String(BarcodeClient.CreateBarcodeByteArray(visaNo, new SKColor(33, 37, 41), BarcodeClient.DefaultWidth, BarcodeClient.ICRHeight));
        }

        public async Task<string> CreateBarcodePhotobooth(string encryptedId)
        {
            int photoBoothId = encryptedId.ToDecryptInt();
            string newPhotoBoothId = "";
            var enLanguageId = (int)Contracts.Entities.Enums.Language.English;
            var enApiRequestHeader = PortalGatewayApiDefaultRequestHeaders;
            enApiRequestHeader.Remove("languageId");
            enApiRequestHeader.Add("languageId", enLanguageId.ToString());

            var apiResponseGetPhotoBooth = await PortalHttpClientHelper
                .GetAsync<ApiResponse<PhotoBoothApiResponse>>
                (ApiMethodName.Appointment.GetPhotoBooth + photoBoothId, AppSettings.PortalGatewayApiUrl, enApiRequestHeader)
                .ConfigureAwait(false);

            newPhotoBoothId = photoBoothId.ToString();
            DateTime today = DateTime.Today.Date;
            if (today == apiResponseGetPhotoBooth.Data.ExpireDateTime.Value.Date && apiResponseGetPhotoBooth.Data.Status == (int)Portal.Gateway.Contracts.Entities.Enums.PhotoBoothStatus.Expired)
            {

                var apiAddPhotoBoothRequest = new AddPhotoBoothApiRequest
                {
                    BranchId = UserSession.BranchId.Value,
                    ApplicationId = apiResponseGetPhotoBooth.Data.ApplicationId,
                    Name = apiResponseGetPhotoBooth.Data.Name,
                    Surname = apiResponseGetPhotoBooth.Data.Surname,
                    PassportNumber = apiResponseGetPhotoBooth.Data.PassportNumber,
                    Price = (decimal)apiResponseGetPhotoBooth.Data.Price,
                    CurrencyId = (int)apiResponseGetPhotoBooth.Data.CurrencyId,
                    Status = (int)PhotoBoothStatus.Expired,
                    RequestedBy = UserSession.UserId,
                };

                var apiAddPhotoBoothResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                    (apiAddPhotoBoothRequest, ApiMethodName.Appointment.AddPhotoBooth, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                var apiResponseUpdatePhotoBoothApplicationStatus = await PortalHttpClientHelper
                           .GetAsync<ApiResponse<UpdateApiResponse>>
                           ($"{ApiMethodName.Appointment.UpdatePhotoBoothApplicationStatus + photoBoothId}/{(int)PhotoBoothStatus.Expired}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                           .ConfigureAwait(false);

                newPhotoBoothId = apiAddPhotoBoothResponse.Data.Id.ToString();

            }

            return Convert.ToBase64String(BarcodeClient.CreateBarcodeByteArray(Int32.Parse(newPhotoBoothId).ToString("9000000000000"), new SKColor(33, 37, 41), BarcodeClient.DefaultWidth, BarcodeClient.PhotoboothHeight));
        }
        public async Task<IActionResult> GetCargoReceipt(string encryptedApplicationId, string applicationId, string language, bool printAll = false)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var apiRequest = new GetApplicationPrintAllApiRequest
            {
                BranchId = UserSession.BranchId.GetValueOrDefault(),
                ApplicationId = encryptedApplicationId.ToDecryptInt(),
                RelationalApplicationId = encryptedApplicationId.ToDecryptInt(),
                IcrLanguage = 2,
                Icr = false,
                EntryInformationFormForTurkey = false,
                Cargo = true,
                ApplicationPage = false,
                PrintAll = printAll,
            };

            var enApiRequestHeader = PortalGatewayApiDefaultRequestHeaders;
            enApiRequestHeader.Remove("languageId");
            enApiRequestHeader.Add("languageId", ((int)ICRLanguage.English).ToString());

            var apiResponse = await PortalHttpClientHelper
            .PostAsJsonAsync<ApiResponse<GetCargoApiResponse>>
                (apiRequest, ApiMethodName.Appointment.GetCargoReceipt, AppSettings.PrintData.BaseApiUrl, enApiRequestHeader)
            .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var cargo = GetPrintAllCargoReceipt(apiResponse.Data, encryptedApplicationId.ToDecryptInt(), applicationId.ToDecryptInt(), language, printAll).Result;

            var cargoActionResultType = cargo.GetType();

            if (cargoActionResultType.Name == "RedirectToActionResult" || cargoActionResultType
                    .GetProperty("Model")?
                    .GetValue(cargo) is not CargoReceiptViewModel cargoModel)
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var page = "GetCargoReceiptEn";
            switch (language)
            {
                case "English":
                    page = "GetCargoReceiptEn";
                    break;
                case "Arabic":
                    page = "GetCargoReceiptAr";
                    break;
                case "Russian":
                    page = "GetCargoReceiptRus";
                    break;
                case "AlgerianFrench":
                    page = "GetCargoReceiptFr";
                    break;
                case "AlgerianArabic":
                    page = "GetCargoReceiptAlgerianAr";
                    break;
                default:
                    TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                    return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var viewCargo = await this.RenderViewAsync(page, cargoModel);

            return base.Content(viewCargo.ToString(), "text/html", Encoding.UTF8);
        }

        public async Task<IActionResult> GetEntryInformationForm(string encryptedApplicationId, string languageId, bool printAll = false)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var apiRequest = new GetApplicationPrintAllApiRequest
            {
                BranchId = UserSession.BranchId.GetValueOrDefault(),
                ApplicationId = encryptedApplicationId.ToDecryptInt(),
                RelationalApplicationId = encryptedApplicationId.ToDecryptInt(),
                IcrLanguage = 2,
                Icr = false,
                EntryInformationFormForTurkey = true,
                Cargo = false,
                ApplicationPage = false,
                PrintAll = printAll,
            };

            var enApiRequestHeader = PortalGatewayApiDefaultRequestHeaders;
            enApiRequestHeader.Remove("languageId");
            enApiRequestHeader.Add("languageId", ((int)ICRLanguage.English).ToString());

            var apiResponse = await PortalHttpClientHelper
            .PostAsJsonAsync<ApiResponse<GetApplicationEntryFormApiResponse>>
                (apiRequest, ApiMethodName.Appointment.GetEntryInformationForm, AppSettings.PrintData.BaseApiUrl, enApiRequestHeader)
            .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var entryInformationForm = GetPrintAllEntryInformationForm(apiResponse.Data, languageId, printAll).Result;

            var entryInformationFormActionResultType = entryInformationForm.GetType();

            if (entryInformationFormActionResultType.Name == "RedirectToActionResult" || entryInformationFormActionResultType
                    .GetProperty("Model")?
                    .GetValue(entryInformationForm) is not EntryInformationFormViewModel entryInformationFormViewModel)
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var page = "GetEntryInformationFormEn";
            if (languageId == EntryFormLanguage.English.ToString())
                page = "GetEntryInformationFormEn";
            else if (languageId == EntryFormLanguage.French.ToString())
                page = "GetEntryInformationFormFr";
            else if (languageId == EntryFormLanguage.Arabic.ToString() && apiRequest.BranchId == 1)
                page = "GetEntryInformationFormArKuwait";
            else if (languageId == EntryFormLanguage.Arabic.ToString() && apiRequest.BranchId != 1)
                page = "GetEntryInformationFormAr";
            else if (languageId == EntryFormLanguage.Turkish.ToString())
                page = "GetEntryInformationFormTr";
            else if (languageId == EntryFormLanguage.Russian.ToString())
                page = "GetEntryInformationFormRu";
            else if (languageId == EntryFormLanguage.TkmEnglish.ToString())
                page = "GetEntryInformationFormTkmEn";
            else if (languageId == EntryFormLanguage.TkmTurkish.ToString())
                page = "GetEntryInformationFormTkmTr";
            else if (languageId == EntryFormLanguage.TkmRussian.ToString())
                page = "GetEntryInformationFormTkmRu";
            else if (languageId == EntryFormLanguage.Turkmen.ToString())
                page = "GetEntryInformationFormTm";
            else
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var viewEntryInformationForm = await this.RenderViewAsync(page, entryInformationFormViewModel);

            return base.Content(viewEntryInformationForm.ToString(), "text/html", Encoding.UTF8);
        }

        public async Task<IActionResult> GetWithInsuranceExplicitConsentText(string encryptedApplicationId, string branchCountryIso3, int branchId, string languageId)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            int applicationId = encryptedApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<GetExplicitConsentTextApiResponse>>
                ($"{ApiMethodName.Appointment.GetExplicitConsentText + applicationId}", AppSettings.PrintData.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var viewModel = new ExplicitConsentTextViewModel()
            {
                NameAndSurname = apiResponse.Data.NameAndSurname,
                Date = DateTime.Now.Date.ToShortDateString(),
                PassportNumber = apiResponse.Data.PassportNumber,
                ContactInformation = apiResponse.Data.ContactInformation,
                BranchCountryIso3 = branchCountryIso3,
                BranchId = branchId
            };

            if (languageId == ExplicitConsentLanguage.English.ToString())
                return View("GetExplicitConsentTextWithInsuranceEn", viewModel);
            else if (languageId == ExplicitConsentLanguage.French.ToString())
                return View("GetExplicitConsentTextWithInsuranceFr", viewModel);
            else if (languageId == ExplicitConsentLanguage.Arabic.ToString())
                return View("GetExplicitConsentTextWithInsuranceAr", viewModel);
            else if (languageId == ExplicitConsentLanguage.Turkish.ToString())
                return View("GetExplicitConsentTextWithInsuranceTr", viewModel);
            else if (languageId == ExplicitConsentLanguage.Russian.ToString())
                return View("GetExplicitConsentTextWithInsuranceRu", viewModel);
            else if (languageId == ExplicitConsentLanguage.IraqArabic.ToString())
                return View("GetExplicitConsentTextWithInsuranceIraqAr", viewModel);
            else if (languageId == ExplicitConsentLanguage.IraqEnglish.ToString())
                return View("GetExplicitConsentTextWithInsuranceIraqEn", viewModel);
            else
                return View("GetExplicitConsentTextWithInsuranceTrkm", viewModel);

            TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
            return RedirectToAction("List", "Application", new { Area = "Appointment" });
        }

        public async Task<IActionResult> GetWithOutInsuranceExplicitConsentText(string encryptedApplicationId, string branchCountryIso3, string languageId)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            int applicationId = encryptedApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<GetExplicitConsentTextApiResponse>>
                ($"{ApiMethodName.Appointment.GetExplicitConsentText + applicationId}", AppSettings.PrintData.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var viewModel = new ExplicitConsentTextViewModel()
            {
                NameAndSurname = apiResponse.Data.NameAndSurname,
                Date = DateTime.Now.Date.ToShortDateString(),
                PassportNumber = apiResponse.Data.PassportNumber,
                BranchCountryIso3 = branchCountryIso3
            };

            if (languageId == ExplicitConsentLanguage.English.ToString())
                return View("GetExplicitConsentTextWithOutInsuranceEn", viewModel);
            else if (languageId == ExplicitConsentLanguage.French.ToString())
                return View("GetExplicitConsentTextWithOutInsuranceFr", viewModel);
            else if (languageId == ExplicitConsentLanguage.Arabic.ToString())
                return View("GetExplicitConsentTextWithOutInsuranceAr", viewModel);
            else if (languageId == ExplicitConsentLanguage.Turkish.ToString())
                return View("GetExplicitConsentTextWithOutInsuranceTr", viewModel);
            else if (languageId == ExplicitConsentLanguage.Russian.ToString())
                return View("GetExplicitConsentTextWithOutInsuranceRu", viewModel);
            else if (languageId == ExplicitConsentLanguage.IraqEnglish.ToString())
                return View("GetExplicitConsentTextWithOutInsuranceIraqEn", viewModel);
            else if (languageId == ExplicitConsentLanguage.IraqArabic.ToString())
                return View("GetExplicitConsentTextWithOutInsuranceIraqAr", viewModel);
            else
                return View("GetExplicitConsentTextWithOutInsuranceTrkm", viewModel);

            TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
            return RedirectToAction("List", "Application", new { Area = "Appointment" });
        }

        public async Task<IActionResult> ApplicationPage(string encryptedApplicationId, int relationalId, bool? printAll = false)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var apiRequest = new GetApplicationPrintAllApiRequest
            {
                BranchId = UserSession.BranchId.GetValueOrDefault(),
                ApplicationId = encryptedApplicationId.ToDecryptInt(),
                RelationalApplicationId = relationalId == 0 ? encryptedApplicationId.ToDecryptInt() : relationalId,
                IcrLanguage = 2,
                Icr = false,
                EntryInformationFormForTurkey = false,
                Cargo = false,
                ApplicationPage = true,
                PrintAll = printAll,
            };

            var enApiRequestHeader = PortalGatewayApiDefaultRequestHeaders;
            enApiRequestHeader.Remove("languageId");
            enApiRequestHeader.Add("languageId", (LanguageId).ToString());

            var apiResponse = await PortalHttpClientHelper
            .PostAsJsonAsync<ApiResponse<GetApplicationPageApiResponse>>
                (apiRequest, ApiMethodName.Appointment.GetApplicationPage, AppSettings.PrintData.BaseApiUrl, enApiRequestHeader)
            .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var applicationPage = await PrintAllPrintOut(apiResponse.Data);

            var applicationPageActionResultType = applicationPage.GetType();

            if (applicationPageActionResultType.Name == "RedirectToActionResult" || applicationPageActionResultType
                    .GetProperty("Model")?
                    .GetValue(applicationPage) is not ApplicationViewModel applicationPageViewModel)
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var page = await this.RenderViewAsync("_PrintOutPDF", applicationPageViewModel);

            return base.Content(page.ToString(), "text/html", Encoding.UTF8);
        }

        public async Task<IActionResult> ApplicationPage_(string encryptedApplicationId, int relationalId)
        {
            var applicants = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationIdsApiResponse>>
                ($"{ApiMethodName.Appointment.GetApplicationIds + encryptedApplicationId.ToDecryptInt()}", AppSettings.PrintData.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            var appPage = new List<string>();
            foreach (var id in applicants.Data.Applications)
            {
                var view = await ApplicationPage(id.Id.ToEncrypt(), id.RelationalApplicationId, false) as ContentResult;
                appPage.Add(view.Content);
            }

            return Json(appPage);
        }

        public async Task<IActionResult> GetComplementaryServices(string encryptedApplicationId)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            int applicationId = encryptedApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<GetComplementaryServicesApiResponse>>
                ($"{ApiMethodName.Appointment.GetComplementaryServices + applicationId}/{LanguageId}", AppSettings.PrintData.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var viewModel = new ComplementaryServicesViewModel()
            {
                ApplicantName = apiResponse.Data.ApplicantName,
                PassportNo = apiResponse.Data.PassportNo,
                ComplementaryServicesStartDate = apiResponse.Data.ComplementaryServicesStartDate.ToShortDateString(),
                ComplementaryServicesEndDate = apiResponse.Data.ComplementaryServicesEndDate.ToShortDateString(),
            };

            return View("ComplementaryServicesIndia", viewModel);
        }

        [Action(IsMenuItem = true)]
        public IActionResult AccountingICRAppointmentsFromExcel()
        {
            var viewModel = new ICRDataFromExcelViewModel()
            {
                FileSessionId = Guid.NewGuid().ToString(),
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> SessionUploadFile([FromForm] IFormFile file, string fileSessionId)
        {
            if (file != null && !string.IsNullOrEmpty(fileSessionId))
            {
                using (var ms = new MemoryStream())
                {
                    file.CopyTo(ms);
                    await _fileStorage.SaveFileAsync(FileStorage.FileStorageBucketName, $"{FileStorage.SessionUploadFileFolderName}/{fileSessionId}", ms, "");
                }

                var fileModel = FileHelper.GetFileInfo(file);
                Extensions.SessionExtensions.Set(HttpContext.Session, $"SessionUploadFile{fileSessionId}", fileModel);
            }

            return Content("");
        }

        public IActionResult SessionRemoveFile([FromForm] IFormFile file, string fileSessionId)
        {
            if (file != null && !string.IsNullOrEmpty(fileSessionId))
            {
                var fileModel = Extensions.SessionExtensions.Get<FileModel>(HttpContext.Session, $"SessionUploadFile{fileSessionId}");
                Extensions.SessionExtensions.Set(HttpContext.Session, $"SessionUploadFile{fileSessionId}", fileModel);
            }

            return Content("");
        }

        [HttpPost]
        public async Task<IActionResult> GetAccountingICRApplications(string rows, int ICRTypeId)
        {
            if (rows.IsNullOrWhitespace())
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Warning });

            try
            {
                var viewModel = JsonSerializer.Deserialize<List<ICRDataFromExcelViewModel>>(rows);

                if (viewModel == null || !viewModel.Any())
                    return Json(new ResultModel { Message = EnumResources.AppointmentNotFound, ResultType = ResultType.Warning });

                var appointmentIds = viewModel.Select(s => s.ApplicationNumber).Distinct().ToList();
                if (!appointmentIds.Any())
                    return Json(new ResultModel { Message = EnumResources.AppointmentNotFound, ResultType = ResultType.Warning });

                var apiRequest = new GetAccountingICRApiRequest
                {
                    SapBranchIds = viewModel.Select(s => s.SapBranchId).Distinct().ToList()
                };

                var enApiRequestHeader = PortalGatewayApiDefaultRequestHeaders;
                enApiRequestHeader.Remove("languageId");
                enApiRequestHeader.Add("languageId", ICRLanguage.English.ToString());

                var apiResponse = await PortalHttpClientHelper
                                .PostAsJsonAsync<ApiResponse<GetAccountingICRApiResponse>>
                                    (apiRequest, ApiMethodName.Appointment.GetAccountingICR, AppSettings.PrintData.BaseApiUrl, enApiRequestHeader)
                                .ConfigureAwait(false);

                if (apiResponse.Data == null)
                    return Json(new ResultModel { Message = EnumResources.ErrorOccurred, ResultType = ResultType.Warning });


                var finalStr = new StringBuilder();
                var count = 0;

                var tlvFormatCountries = EnumHelper.GetEnumAsDictionary(typeof(TLVFormatQRCodeCountries)).Select(p => new
                {
                    Value = p.Key
                }).ToList();

                var visaCategories = await GetCachedVisaCategories();

                foreach (var id in appointmentIds)
                {
                    count++;
                    var view = viewModel.Where(s => s.ApplicationNumber == id).Select(s => new ICRViewModel
                    {
                        TaxInclude = false,
                        EncryptedId = s.ApplicationNumber.ToEncrypt(),
                        IsTLVFormat = tlvFormatCountries.Exists(q => q.Value == (apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == s.SapBranchId)?.CountryId ?? 0)),
                        BranchName = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == s.SapBranchId)?.QrBranchName ?? string.Empty,
                        BranchCountryId = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == s.SapBranchId)?.BranchCountryId ?? 0,
                        InvoiceNumber = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == s.SapBranchId)?.BranchInvoiceNumber ?? string.Empty,
                        Phone = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == s.SapBranchId)?.BranchPhone ?? string.Empty,
                        Email = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == s.SapBranchId)?.BranchEmail ?? string.Empty,
                        Address = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == s.SapBranchId)?.BranchAddress ?? string.Empty,
                        AppointmentTime = new DateTime(s.AppointmentTime.Year, s.AppointmentTime.Month, s.AppointmentTime.Day, 13, 0, 0),
                        DateTimeNow = DateTime.Now.ToShortDateString() + ", " + DateTime.Now.ToShortTimeString(),
                        InvoiceNo = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == s.SapBranchId)?.SapBranchId.TrimStart('0') + "-" + s.AppointmentTime.ToString("yyyyMMdd") + "-" + s.ApplicationNumber,
                        IcrType = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == s.SapBranchId)?.BranchIcrNoteType ?? 0,
                        SapBranchId = s.SapBranchId,
                        CreatedBy = UserSession.FullName,
                        ApplicationExtraFeesTotal = new List<TotalExtraFeeViewModel>(),
                        QrBranchName = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == s.SapBranchId)?.QrBranchName ?? string.Empty,
                        QrInvoiceNumber = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == s.SapBranchId)?.BranchInvoiceNumber ?? string.Empty,
                        PrintAll = false,
                        KSAICR = string.Empty,
                        Applicants = viewModel.Where(s => s.ApplicationNumber == id).Select(a => new ICRViewModel.ApplicantViewModel
                        {
                            IcrLanguage = 2,
                            PhotoBoothCheck = false,
                            ApplicationNumber = a.ApplicationNumber.ToInt().ToApplicationNumber(),
                            VisaCategoryId = 0,
                            VisaCategory = visaCategories.FirstOrDefault(s => s.Id == 0).Translations.FirstOrDefault(s => s.LanguageId == (int)Language.English).Name,
                            VisaCategoryEn = visaCategories.FirstOrDefault(s => s.Id == 0).Translations.FirstOrDefault(s => s.LanguageId == (int)Language.English).Name,
                            VisaCategoryAr = visaCategories.FirstOrDefault(s => s.Id == 0).Translations.FirstOrDefault(s => s.LanguageId == (int)ICRLanguage.Arabic).Name,
                            Name = a.Name,
                            Surname = a.Surname,
                            VisaNo = a.VisaNo,
                            BarcodeICR = CreateBarcodeICR_(a.VisaNo),
                            PhotoBoothBarcodeICR = string.Empty,
                            PassportNumber = a.PassportNumber,
                            PhoneNumber = string.Empty,
                            Nationality = string.Empty,
                            PhotoBoothEncryptedId = string.Empty,
                            ApplicationAdress = string.Empty,
                            ApplicantExtraFeesTotal = viewModel.Where(s => s.ApplicationNumber == id).Select(t => new TotalExtraFeeViewModel()
                            {
                                Currency = t.ExtraFeeCurrency,
                                Price = t.ExtraFeePrice,
                                BasePrice = t.ExtraFeeBasePrice,
                                Tax = 0,
                                TaxRatio = 0,
                                ServiceTax = 0
                            }).ToList(),
                            ExtraFees = viewModel.Where(s => s.ApplicationNumber == id).Select(e => new ICRViewModel.ApplicantViewModel.ApplicantExtraFeeViewModel()
                            {
                                Name = e.ExtraFeeName,
                                NameAr = e.ExtraFeeName,
                                Currency = e.ExtraFeeCurrency,
                                Price = e.ExtraFeePrice,
                                BasePrice = e.ExtraFeeBasePrice,
                                Tax = 0,
                                TaxRatio = 0,
                                ServiceTax = 0
                            }).ToList()
                        }).DistinctBy(d => d.ApplicationNumber).ToList(),
                    }).FirstOrDefault();
                    String branchPreviousNotes = "";
                    if (view.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0) != null)
                    {
                        if (ICRTypeId == (int)ICRLanguage.Russian)
                        {
                            branchPreviousNotes = view.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).PriceZero.ToString("0.00") + " " + view.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).Currency + " это визовый сбор, который не облагается налогом.";
                        }
                        else if (ICRTypeId == (int)ICRLanguage.Arabic)
                        {
                            branchPreviousNotes = view.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).PriceZero.ToString("0.00") + " " + view.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).Currency + " درهم إماراتي هي رسوم التأشيرة معفات من الضريبة. ";
                        }
                        else if (ICRTypeId == (int)ICRLanguage.English)
                        {
                            branchPreviousNotes = view.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).Currency + " " + view.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).PriceZero.ToString("0.00") + " is the Visa fee and its exempted from tax.";
                        }
                        else if (ICRTypeId == (int)ICRLanguage.French)
                        {
                            branchPreviousNotes = view.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).Currency + " " + view.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).PriceZero.ToString("0.00") + " est le frais de visa et son exonération d'impôt.";
                        }
                        else if (ICRTypeId == (int)ICRLanguage.Turkmen)
                        {
                            branchPreviousNotes = view.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).Currency + " " + view.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).PriceZero.ToString("0.00") + " wiza tölegidir we salgytdan boşadylýar.";
                        }
                        else if (ICRTypeId != (int)ICRLanguage.EnglishAndArabic)
                        {
                            branchPreviousNotes = view.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).Currency + " " + view.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0).PriceZero.ToString("0.00") + " is the Visa fee and its exempted from tax.";
                        }
                    }

                    view.BranchIcrNotes = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrNote ?? string.Empty;
                    view.IcrName = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrName ?? string.Empty;

                    if (view.BranchCountryId == 80 || view.BranchCountryId == 181 || view.BranchCountryId == 100 || view.BranchCountryId == 93)
                    {
                        if (ICRTypeId == (int)ICRLanguage.English)
                        {
                            view.BranchIcrNotes = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrNote ?? string.Empty;
                            view.IcrName = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrName ?? string.Empty;
                        }
                        else if (ICRTypeId == (int)ICRLanguage.Arabic)
                        {
                            view.BranchIcrNotes = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrNoteAr ?? string.Empty;
                            view.IcrName = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrNameAr ?? string.Empty;
                        }
                        else if (ICRTypeId == (int)ICRLanguage.Turkmen)
                        {
                            view.BranchIcrNotes = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrNoteTm ?? string.Empty;
                            view.IcrName = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrNameTm ?? string.Empty;
                        }
                        else if (ICRTypeId == (int)ICRLanguage.Russian)
                        {
                            view.BranchIcrNotes = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrNoteRu ?? string.Empty;
                            view.IcrName = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrNameRu ?? string.Empty;
                        }
                    }
                    else
                    {
                        if (ICRTypeId == (int)ICRLanguage.English)
                        {
                            view.BranchIcrNotes = branchPreviousNotes + apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrNote ?? string.Empty;
                            view.IcrName = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrName ?? string.Empty;
                        }
                        else if (ICRTypeId == (int)ICRLanguage.Arabic)
                        {
                            view.BranchIcrNotes = branchPreviousNotes.Replace(" ", "&nbsp;") + apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrNoteAr ?? string.Empty;
                            view.IcrName = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrNameAr ?? string.Empty;
                        }
                        else if (ICRTypeId == (int)ICRLanguage.Turkmen)
                        {
                            view.BranchIcrNotes = branchPreviousNotes + apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrNoteTm ?? string.Empty;
                            view.IcrName = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrNameTm ?? string.Empty;
                        }
                        else if (ICRTypeId == (int)ICRLanguage.Russian)
                        {
                            view.BranchIcrNotes = branchPreviousNotes + apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrNoteRu ?? string.Empty;
                            view.IcrName = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrNameRu ?? string.Empty;
                        }
                        else if (ICRTypeId == (int)ICRLanguage.French)
                        {
                            view.BranchIcrNotes = branchPreviousNotes + apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrNoteFr ?? string.Empty;
                            view.IcrName = apiResponse.Data.Branches.FirstOrDefault(f => f.SapBranchId == view.SapBranchId)?.BranchIcrNameFr ?? string.Empty;
                        }
                    }

                    foreach (var applicant in view.Applicants)
                    {
                        foreach (var extraFeesPerCurrency in applicant.ApplicantExtraFeesTotal)
                        {
                            if (view.ApplicationExtraFeesTotal.Any(p => p.Currency == extraFeesPerCurrency.Currency && p.TaxRatio == extraFeesPerCurrency.TaxRatio))
                            {
                                view.ApplicationExtraFeesTotal.FirstOrDefault(p => p.Currency == extraFeesPerCurrency.Currency && p.TaxRatio == extraFeesPerCurrency.TaxRatio).Price += extraFeesPerCurrency.Price;
                                view.ApplicationExtraFeesTotal.FirstOrDefault(p => p.Currency == extraFeesPerCurrency.Currency && p.TaxRatio == extraFeesPerCurrency.TaxRatio).Tax += extraFeesPerCurrency.Tax;
                                view.ApplicationExtraFeesTotal.FirstOrDefault(p => p.Currency == extraFeesPerCurrency.Currency && p.TaxRatio == extraFeesPerCurrency.TaxRatio).ServiceTax += extraFeesPerCurrency.ServiceTax;
                                view.ApplicationExtraFeesTotal.FirstOrDefault(p => p.Currency == extraFeesPerCurrency.Currency && p.TaxRatio == extraFeesPerCurrency.TaxRatio).BasePrice += extraFeesPerCurrency.BasePrice;
                            }
                            else
                            {
                                view.ApplicationExtraFeesTotal.Add(new TotalExtraFeeViewModel() { Currency = extraFeesPerCurrency.Currency, Price = extraFeesPerCurrency.Price, Tax = extraFeesPerCurrency.Tax, TaxRatio = extraFeesPerCurrency.TaxRatio, ServiceTax = extraFeesPerCurrency.ServiceTax, BasePrice = extraFeesPerCurrency.BasePrice });
                            }
                        }
                    }
                    view.ApplicationExtraFeesTotal.OrderBy(z => z.Currency);

                    var vatAmount = string.Empty;
                    var totalAmount = string.Empty;
                    if (view.IsTLVFormat)
                    {
                        vatAmount = String.Format("{0:0.00}", view.ApplicationExtraFeesTotal.Sum(q => q.Tax));
                        totalAmount = String.Format("{0:0.00}", view.ApplicationExtraFeesTotal.Sum(q => q.Price));
                    }
                    view.CreateICR = CreateICR_(view.BranchName, view.InvoiceNumber, vatAmount, totalAmount, view.TaxInclude, view.AppointmentTime, view.IsTLVFormat);
                    view.ApplicationExtraFeesGrandTotal = view.ApplicationExtraFeesTotal.GroupBy(q => new { q.Currency }).Select(q => new GrandTotalExtraFeeViewModel()
                    {
                        Currency = q.Key.Currency,
                        Price = q.Sum(p => p.Price),
                    }).ToList();

                    if (view.BranchCountryId == 77)//India
                        view.Title = "Tax Invoice";
                    else
                        view.Title = view.BranchName + " ICR";

                    var htmlStr = IcrRendererByLanguage(ICRTypeId, view, false);
                    finalStr.Append(htmlStr.Result);
                    if (count != appointmentIds.Count)
                        finalStr.Append("<div style=\"break-after:page\"></div>");
                }
                var html = finalStr.ToString();

                if (!string.IsNullOrEmpty(html))
                    return Json(new { success = true, message = EnumResources.OperationIsSuccessful, htmlContent = html });
                else
                    return Json(new { success = false, message = EnumResources.ErrorOccurred });
            }
            catch (Exception)
            {
                return Json(new ResultModel { Message = EnumResources.ErrorOccurred, ResultType = ResultType.Warning });
            }
        }

        [HttpPost]
        public async Task<IActionResult> GetICRDataFromExcel([FromForm] IFormFile uploadFile, [FromServices] IWebHostEnvironment environment)
        {
            if (uploadFile == null || uploadFile.FileName.IsNullOrWhitespace())
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Warning });

            var dirName = Path.Combine(environment.WebRootPath, "documents", "AccountingICRAppointments");

            if (!Directory.Exists(dirName))
                Directory.CreateDirectory(dirName);

            var fileName = Path.Combine(environment.WebRootPath, "documents", "AccountingICRAppointments", uploadFile.FileName);

            try
            {
                await using (var fileStream = System.IO.File.Create(fileName))
                {
                    await uploadFile.CopyToAsync(fileStream);
                    fileStream.Flush();
                }

                var applications = await GetApplicationsFromFile(fileName);

                return applications.Item2 != EnumResources.OperationIsSuccessful ?
                    Json(new ResultModel { Message = applications.Item2, ResultType = ResultType.Warning }) :
                    Json(new ResultModel { Data = applications.Item1 });
            }
            catch (Exception)
            {
                return Json(new ResultModel { Message = EnumResources.ErrorOccurred, ResultType = ResultType.Warning });
            }
            finally
            {
                if (System.IO.File.Exists(fileName))
                    System.IO.File.Delete(fileName);
            }
        }

        public async Task<IActionResult> GetInquiry(string encryptedApplicationId, int inquiryId)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            int id = encryptedApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
            .GetAsync<ApiResponse<GetApplicationInquiryApiResponse>>
                (ApiMethodName.Appointment.GetApplicationInquiry + id + "/" + LanguageId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var viewModel = new AddUpdateApplicationInquiryViewModel()
            {
                EncryptedApplicationId = encryptedApplicationId,
                InquiryId = inquiryId,
                Observation = apiResponse.Data.Inquiries.FirstOrDefault(s => s.Id == inquiryId)?.Observation,
                Name = apiResponse.Data.Inquiries.FirstOrDefault(s => s.Id == inquiryId)?.Name,
                InquiryQuestions = apiResponse.Data.Inquiries.FirstOrDefault(s => s.Id == inquiryId).InquiryQuestions.Select(q => new InquiryQuestionViewModel()
                {
                    Id = q.Id,
                    Name = q.Name,
                    QuestionTypeId = q.QuestionTypeId,
                    IsMultiSelectable = q.IsMultiSelectable,
                    IsRequired = q.IsRequired,
                    IsDescriptionIncluded = q.IsDescriptionIncluded,
                    ColSpan = q.ColSpan,
                    DescriptionColSpan = q.DescriptionColSpan,
                    ElectiveAnswer = q.ElectiveAnswer == null ? new InquiryElectiveAnswerViewModel() : new InquiryElectiveAnswerViewModel()
                    {
                        Description = q.ElectiveAnswer?.Description,
                        Ids = q.ElectiveAnswer?.Ids
                    },
                    ExplanationAnswers = q.ExplanationAnswers == null ? new List<InquiryExplanationAnswerViewModel>() : q.ExplanationAnswers.Select(s => new InquiryExplanationAnswerViewModel()
                    {
                        Explanation = s.Explanation,
                        Id = s.Id
                    }).ToList(),
                    Choices = q.Choices.Select(c => new InquiryQuestionChoiceViewModel()
                    {
                        Id = c.Id,
                        Name = c.Name,
                        ColSpan = c.ColSpan,
                        ChoiceTypeId = c.ChoiceTypeId,
                        IsChecked = q.ElectiveAnswer.Ids.Contains(c.Id),
                        SelectionsText = q.SelectionAnswers is null ? string.Empty : string.Join(", ", c.Selections.Where(k => q.SelectionAnswers.Select(r => r.Id).Contains(k.Id)).Select(s => s.Name)
                            .ToList()),
                        Selections = q.SelectionAnswers is null ? new List<int>() : c.Selections.Where(k => q.SelectionAnswers.Select(r => r.Id).Contains(k.Id)).Select(s => s.Id).ToList()
                    }).ToList()
                }).ToList()
            };

            var viewInquiry = await this.RenderViewAsync("Inquiry", viewModel);

            return base.Content(viewInquiry, "text/html", Encoding.UTF8);
        }

        private async Task<Tuple<IEnumerable<ICRDataFromExcelViewModel>, string>> GetApplicationsFromFile(string fName)
        {
            var appointments = new List<ICRDataFromExcelViewModel>();

            try
            {
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

                await using var stream = System.IO.File.Open(fName, FileMode.Open, FileAccess.Read);

                using var reader = ExcelReaderFactory.CreateReader(stream);

                do
                {
                    while (reader.Read())
                    { }
                }
                while (reader.NextResult());

                var result = reader.AsDataSet();
                var dataTable = result.Tables[0];

                for (var i = 1; i < dataTable.Rows.Count; i++)
                {
                    var appointment = new ICRDataFromExcelViewModel
                    {
                        AppointmentTime = DateTime.TryParse(dataTable.Rows[i][0].ToString()?.Trim(), out var appointmentTime) ? appointmentTime.Date : DateTime.MinValue,
                        SapBranchId = dataTable.Rows[i][1].ToString().Trim(),
                        BranchName = dataTable.Rows[i][2].ToString().Trim(),
                        VisaNo = dataTable.Rows[i][3].ToString().Trim(),
                        ApplicationNumber = dataTable.Rows[i][4].ToString().Trim(),
                        Name = dataTable.Rows[i][5].ToString().Trim(),
                        Surname = dataTable.Rows[i][6].ToString().Trim(),
                        PassportNumber = dataTable.Rows[i][7].ToString().Trim(),
                        ExtraFeeName = dataTable.Rows[i][8].ToString().Trim(),
                        ExtraFeeCurrency = dataTable.Rows[i][9].ToString().Trim(),
                        ExtraFeePrice = (decimal.TryParse(dataTable.Rows[i][10].ToString()?.Trim(), out var price) ? price : 0) * (decimal.TryParse(dataTable.Rows[i][11].ToString()?.Trim(), out var quantity) ? quantity : 0),
                        ExtraFeeBasePrice = decimal.TryParse(dataTable.Rows[i][10].ToString()?.Trim(), out var basePrice) ? basePrice : 0
                    };

                    appointments.Add(appointment);
                }

                return new Tuple<IEnumerable<ICRDataFromExcelViewModel>, string>(appointments, EnumResources.OperationIsSuccessful);
            }
            catch (Exception)
            {
                return new Tuple<IEnumerable<ICRDataFromExcelViewModel>, string>(appointments, EnumResources.MissingOrInvalidData);
            }
        }
    }
}

