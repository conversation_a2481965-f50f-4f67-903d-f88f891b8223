﻿using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Management.ClientDevice;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.ApiModel.Responses.Management.ClientDevice;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.ClientDevice;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class ClientDeviceController : BaseController<ClientDeviceController>
    {
        public ClientDeviceController(
                IOptions<AppSettings> appSettings,
                ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        { }

        #region Add

        public IActionResult PartialAddClientDevice()
        {
            var viewModel = new AddClientDeviceViewModel()
            {
                IsActive = true
            };

            return PartialView("_AddClientDevice", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddClientDevice(AddClientDeviceViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddClientDeviceApiRequest
            {
                Name = viewModel.Name,
                Description = viewModel.Description,
                BranchId = viewModel.BranchId,
                IsActive = viewModel.IsActive
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Management.AddClientDevice, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateClientDevice(string encryptedClientDeviceId)
        {
            int id = encryptedClientDeviceId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ClientDeviceApiResponse>>
                (ApiMethodName.Management.GetClientDevice + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new ClientDeviceViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Description = apiResponse.Data.Description,
                Name = apiResponse.Data.Name,
                BranchId = apiResponse.Data.BranchId,
                IsActive = apiResponse.Data.IsActive
            };

            return PartialView("_UpdateClientDevice", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateClientDevice(ClientDeviceViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateClientDeviceApiRequest
            {
                Id = viewModel.EncryptedId.ToDecryptInt(),
                BranchId = viewModel.BranchId,
                Name = viewModel.Name,
                Description = viewModel.Description,
                IsActive = viewModel.IsActive
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateClientDevice, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteClientDevice(string encryptedClientDeviceId)
        {
            int id = encryptedClientDeviceId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Management.DeleteClientDevice + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> PartialClientDevice(string encryptedClientDeviceId)
        {
            int id = encryptedClientDeviceId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ClientDeviceApiResponse>>
                (ApiMethodName.Management.GetClientDevice + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var branchList = await CacheHelper.GetBranchesAsync();

            var viewModel = new ClientDeviceViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Name = apiResponse.Data.Name,
                Description = apiResponse.Data.Description,
                BranchId = apiResponse.Data.BranchId,
                IsActive = apiResponse.Data.IsActive,
                BranchName = branchList.Branches.First(t => t.Id == apiResponse.Data.BranchId).BranchTranslations.Any(t => t.LanguageId == LanguageId) ?
                            branchList.Branches.First(t => t.Id == apiResponse.Data.BranchId).BranchTranslations.First(t => t.LanguageId == LanguageId).Name :
                            branchList.Branches.First(t => t.Id == apiResponse.Data.BranchId).BranchTranslations.FirstOrDefault().Name
            };

            return PartialView("_ClientDevice", viewModel);
        }

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedClientDevices([DataSourceRequest] DataSourceRequest request, FilterClientDeviceViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new GetPaginatedClientDevicesApiRequest
            {
                Name = filterViewModel.FilterName,
                BranchId = filterViewModel.FilterBranchId,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedClientDevicesApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedClientDevices, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var branchList = await CacheHelper.GetBranchesAsync();

            var paginatedData = new List<ClientDeviceViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().ClientDevices
                    .Select(p => new ClientDeviceViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        Name = p.Name,
                        Description = p.Description,
                        BranchId = p.BranchId,
                        BranchName = branchList.Branches.First(t => t.Id == p.BranchId).BranchTranslations.Any(t => t.LanguageId == LanguageId) ?
                            branchList.Branches.First(t => t.Id == p.BranchId).BranchTranslations.First(t => t.LanguageId == LanguageId).Name :
                            branchList.Branches.First(t => t.Id == p.BranchId).BranchTranslations.FirstOrDefault().Name,
                        IsActive = p.IsActive
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #endregion

        public async Task<IActionResult> ClientDeviceInventory(string encryptedClientDeviceId)
        {
            var apiResponse = await PortalHttpClientHelper
                                    .GetAsync<ApiResponse<ClientDeviceInventoryApiResponse>>
                                    (ApiMethodName.Management.GetClientDeviceInventory + encryptedClientDeviceId.ToDecrypt(), AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                                    .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", result);
                return RedirectToAction("/");
            }

            var viewModel = new ClientDeviceInventoryViewModel()
            {
                EncryptedClientDeviceId = encryptedClientDeviceId,
                ClientDeviceInventories = apiResponse.Data.ClientDeviceInventories
                                .Select(p => new ClientDeviceInventoriesViewModel()
                                {
                                    Inventory = new ViewModels.Inventory.InventoryViewModel()
                                    {
                                        EncryptedId = p.Id.ToEncrypt(),
                                        IsActive = p.IsActive,
                                        Name = p.Name
                                    }
                                }).ToList() ?? null
            };

            return View(viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> AddUpdateClientDeviceInventory(ClientDeviceInventoryViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddUpdateClientDeviceInventoryApiRequest
            {
                ClientDeviceId = viewModel.EncryptedClientDeviceId.ToDecryptInt(),
                ClientDeviceInventories = viewModel.ClientDeviceInventories.Select(p => new AddUpdateClientDeviceInventoriesApiRequest()
                {
                    InventoryId = p.Inventory.EncryptedId.ToDecryptInt(),
                    IsActive = p.Inventory.IsActive
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Management.AddUpdateClientDeviceInventory, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }
    }
}
