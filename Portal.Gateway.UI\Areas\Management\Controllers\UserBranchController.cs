﻿using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.Management.UserBranch;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Management.User;
using Portal.Gateway.ApiModel.Responses.Management.UserBranch;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.User;
using Portal.Gateway.UI.Areas.Management.ViewModels.UserBranch;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class UserBranchController : BaseController<UserBranchController>
    {
        public UserBranchController(
                IOptions<AppSettings> appSettings,
                ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        #region Add

        public IActionResult PartialAddUserBranch(string encryptedUserId)
        {
            var viewModel = new AddUserBranchViewModel
            {
                EncryptedUsedId = encryptedUserId
            };

            return PartialView("_AddUserBranch", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddUserBranch(AddUserBranchViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddUserBranchApiRequest
            {
                UserId = viewModel.EncryptedUsedId.ToDecryptInt(),
                BranchIds = viewModel.BranchIds
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Management.AddUserBranch, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteUserBranch(string encryptedUserBranchId)
        {
            int id = encryptedUserBranchId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Management.DeleteUserBranch + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> List(string encryptedUserId)
        {
            if (string.IsNullOrEmpty(encryptedUserId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "User", new { Area = "Management" });
            }

            int userId = encryptedUserId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<UserApiResponse>>
                (ApiMethodName.Management.GetUser + userId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", result);
                return RedirectToAction("List", "User", new { Area = "Management" });
            }

            var userViewModel = new UserViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Name = apiResponse.Data.Name,
                Surname = apiResponse.Data.Surname,
                Email = apiResponse.Data.Email,
                PhoneNumber = apiResponse.Data.PhoneNumber,
                IsActive = apiResponse.Data.IsActive
            };

            ViewData["UserBranch_List_User"] = userViewModel;
            return View();
        }

        public async Task<IActionResult> GetUserBranches([DataSourceRequest] DataSourceRequest request, string encryptedUserId)
        {
            var apiRequest = new UserBranchesApiRequest
            {
                UserId = encryptedUserId.ToDecryptInt()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UserBranchesApiResponse>>
                (apiRequest, ApiMethodName.Management.GetUserBranches, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var data = new List<UserBranchViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.UserBranches.Any())
            {
                data = apiResponse.Data.UserBranches.Select(p => new UserBranchViewModel
                {
                    EncryptedId = p.Id.ToEncrypt(),
                    UserId = p.UserId,
                    CountryId = p.CountryId,
                    BranchId = p.BranchId,
                    CountryName = p.CountryName,
                    BranchName = p.BranchName,
                    IsActive = p.IsActive
                }).ToList();
            }

            return Json(data.ToDataSourceResult(request));
        }

        #endregion
    }
}
