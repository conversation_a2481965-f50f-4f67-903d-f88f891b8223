﻿using System;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Accounting;
using Portal.Gateway.ApiModel.Requests.Appointment.Application;
using Portal.Gateway.ApiModel.Requests.Insurance;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Appointment.Application;
using Portal.Gateway.ApiModel.Responses.Insurance;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.Application;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using iTextSharp.text.pdf.parser;
using Microsoft.AspNetCore.Http;
using Portal.Gateway.ApiModel.Responses.Management.RoleAction;
using Portal.Gateway.UI.Constants;
using static Portal.Gateway.ApiModel.Responses.Appointment.Application.ApplicationsWithExemptInsuranceRelationApiResponse;

namespace Portal.Gateway.UI.Areas.Appointment.Controllers
{
    [Area("Appointment")]
    public class ApplicationCancellationController : BaseController<ApplicationCancellationController>
    {
        public ApplicationCancellationController(
            ICacheHelper cacheHelper,
            IOptions<AppSettings> appSettings)
            : base(appSettings, cacheHelper)
        {
        }

        #region Add

        [HttpPost]
        public async Task<IActionResult> AddApplicationCancellation(AddApplicationCancellationViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            if (viewModel.CancellationTypeId == (int)ApplicationCancellationType.Cancellation &&
                viewModel.RelatedInsuranceApplicationCancellationTypeId.HasValue &&
                !viewModel.HasPermissionToCancelRelatedInsuranceApplication)
            {
                return Json(new ResultModel { Message = SiteResources.HasNoPermissionForOperation, ResultType = ResultType.Danger });
            }

            var apiRequest = new AddApplicationCancellationApiRequest
            {
                ApplicationId = viewModel.EncryptedApplicationId.ToDecryptInt(),
                CancellationTypeId = viewModel.CancellationTypeId,
                CancellationReasonId = viewModel.CancellationReasonId,
                RelatedInsuranceApplicationCancellationTypeId = viewModel.RelatedInsuranceApplicationCancellationTypeId,
                CreatedBy = UserSession.UserId
            };

            if (viewModel.CancellationTypeId == (int)ApplicationCancellationType.PartialRefund)
            {
                var isAnySelectedRelatedInsurance = viewModel.ApplicationCancellationExtraFees.Any(a =>
                    a.CategoryTypeId == (int)ExtraFeeCategoryType.RelatedInsurance && a.IsChecked);

                if (!viewModel.HasPermissionToPartialRefundRelatedInsuranceApplication && isAnySelectedRelatedInsurance)
                    return Json(new ResultModel { Message = SiteResources.HasNoPermissionForOperation, ResultType = ResultType.Danger });

                apiRequest.ApplicationCancellationExtraFees = viewModel.ApplicationCancellationExtraFees
                                                                .Where(p => p.IsChecked).Select(p => new AddApplicationCancellationApiRequest.ApplicationCancellationExtraFee
                                                                {
                                                                    ApplicationExtraFeeId = p.ApplicationExtraFeeId
                                                                }).ToList();


            }

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Appointment.AddApplicationCancellation, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        public async Task<IActionResult> PartialAddApplicationCancellation(string encryptedApplicationId, int applicationCancellationTypeId)
        {
            int id = encryptedApplicationId.ToDecryptInt();

            var viewModel = new AddApplicationCancellationViewModel
            {
                EncryptedApplicationId = encryptedApplicationId,
                CancellationTypeId = applicationCancellationTypeId
            };

            var apiResponse = await PortalHttpClientHelper
            .GetAsync<ApiResponse<ApplicationExtraFeeApiResponse>>
            (ApiMethodName.Appointment.GetSanitizedApplicationExtraFees + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
            .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);
            
            var applicationTime = apiResponse.Data.ApplicationTime.Date;

            switch (applicationCancellationTypeId)
            {
                case (int)ApplicationCancellationType.Cancellation:
                    viewModel.HasPermissionToCancelRelatedInsuranceApplication =
                        apiResponse.Data.ExtraFees.Any(fee => fee.Category == (int)ExtraFeeCategoryType.RelatedInsurance) &&
                        await HasPermissionForCancellationOrRefund(applicationCancellationTypeId, applicationTime);
                    break;
                // check permissions for cancellation
                case (int)ApplicationCancellationType.PartialRefund:
                    viewModel.ApplicationCancellationExtraFees = apiResponse.Data.ExtraFees.Select(p =>
                        new AddApplicationCancellationViewModel.ApplicationCancellationExtraFee
                        {
                            ApplicationExtraFeeId = p.ApplicationExtraFeeId,
                            ExtraFeeId = p.ExtraFeeId,
                            ExtraFeeName = p.ExtraFeeName,
                            BranchCountryId = p.BranchCountryId,
                            CategoryTypeId = p.Category
                        }).ToList();

                    // check permissions for partial refund
                    viewModel.HasPermissionToPartialRefundRelatedInsuranceApplication =
                        apiResponse.Data.ExtraFees.Any(fee => fee.Category == (int)ExtraFeeCategoryType.RelatedInsurance) &&
                        await HasPermissionForCancellationOrRefund(applicationCancellationTypeId, applicationTime);
                    break;
            }


            return PartialView("_AddApplicationCancellation", viewModel);
        }

        #region PrivateMethods

        private async Task<bool> HasPermissionForCancellationOrRefund(int applicationCancellationTypeId, DateTime applicationTime)
        {
            var roleActions = await CacheHelper.GetRoleActionsAsync();

            var actionMethodName = applicationTime == DateTime.Today.Date
                ? GetActionMethodForSameDay(applicationCancellationTypeId)
                : GetActionMethodForPastDays(applicationCancellationTypeId);

            return UserHasRolePermission(roleActions, actionMethodName);
        }

        private string GetActionMethodForSameDay(int applicationCancellationTypeId) =>
            applicationCancellationTypeId switch
            {
                (int)ApplicationCancellationType.Cancellation => ActionMethods.CanCancelRelatedInsuredApplicationsOnSameDay,
                _ => ActionMethods.CanPartialRefundRelatedInsuredApplicationsOnSameDay
            };

        private string GetActionMethodForPastDays(int applicationCancellationTypeId) =>
            applicationCancellationTypeId switch
            {
                (int)ApplicationCancellationType.Cancellation => ActionMethods.CanCancelRelatedInsuredApplicationsAfterCreatedDay,
                _ => ActionMethods.CanPartialRefundRelatedInsuredApplicationsAfterCreatedDay
            };

        private bool UserHasRolePermission(RoleActionApiResponse roleActions, string actionMethodName) =>
            roleActions.RoleActionSites
                .Where(site => UserSession.RoleIds.Any(roleId => roleId == site.Role.Id))
                .Any(site => site.RoleActions
                    .Any(action => action.Action.Method == actionMethodName && action.Action.IsActive));

        #endregion

        #endregion

        [HttpGet]
        public async Task<IActionResult> GetExistingPhotoboothApplicationUsedCheck(string encryptedApplicationId)
        {
            var applicationId = encryptedApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ValidateApiResponse>>
                (ApiMethodName.Appointment.GetExistingPhotoboothApplicationUsedCheck + applicationId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (apiResponse == null)
                return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });

            return Json(new ResultModel { Data = apiResponse.Data, Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }



        #region Update

        [HttpPut]
        public async Task<IActionResult> UpdateApplicationCancellationStatus(UpdateApplicationCancellationStatusViewModel viewModel)
        {
            string sapGroupId = "";
            int applicationId = 0;
            int relationalApplicationId = 0;
            bool sendSapCheck = false;

            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var applicationCancellationId = viewModel.EncryptedApplicationCancellationId.ToDecryptInt();

            var updateApplicationCancellationStatus = false;

            IList<ApplicationCancellationApiResponse.ApplicationInsurance> getInsuranceNumbers = new List<ApplicationCancellationApiResponse.ApplicationInsurance>();
            List<string> insuranceNumbers = new List<string>();
            List<string> releatedInsuranceNumbers = new List<string>();
            bool insurancePartialRefundCheck = false;
            bool releatedInsurancePartialRefundCheck = false;


            var apiResponseGetApplicationCancellation = await PortalHttpClientHelper
               .GetAsync<ApiResponse<ApplicationCancellationApiResponse>>
               (ApiMethodName.Appointment.GetApplicationCancellation + applicationCancellationId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
               .ConfigureAwait(false);


            if (viewModel.ApplicationCancellationStatusId == (int)ApplicationCancellationStatus.Approved)
            {

                applicationId = apiResponseGetApplicationCancellation.Data.ApplicationId;
                relationalApplicationId = apiResponseGetApplicationCancellation.Data.RelationalApplicationId != null ? (int)apiResponseGetApplicationCancellation.Data.RelationalApplicationId : 0;
                getInsuranceNumbers = apiResponseGetApplicationCancellation.Data.Insurance;
                
                var hasPermissionForCancellationOrRefund = apiResponseGetApplicationCancellation.Data.ReleatedInsuranceCheck &&
                                                           await HasPermissionForCancellationOrRefund(apiResponseGetApplicationCancellation.Data.CancellationTypeId, apiResponseGetApplicationCancellation.Data.ApplicationTime.DateTime);

                if (!await apiResponseGetApplicationCancellation.Validate(out ResultModel resultGetApplicationCancellation).ConfigureAwait(false))
                    return Json(resultGetApplicationCancellation);

                var applicationCancellation = apiResponseGetApplicationCancellation.Data;

                if (apiResponseGetApplicationCancellation.Data.SapOrder == null)
                {
                    if (applicationCancellation.CancellationTypeId == (int)ApplicationCancellationType.PartialRefund)
                    {
                        if (apiResponseGetApplicationCancellation.Data.CancellationExtraFees.Any(q =>
                                q != null ? (q.ExtraFeeName.Contains("Fotoğraf") || (q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy"))) : false))
                        {
                            var apiResponseUpdatePhotoBoothApplicationDelete = await PortalHttpClientHelper
                                .GetAsync<ApiResponse<UpdateApiResponse>>
                                ($"{ApiMethodName.Appointment.UpdatePhotoBoothApplicationDelete + applicationId}/{false}",
                                    AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                                .ConfigureAwait(false);


                            if (!await apiResponseUpdatePhotoBoothApplicationDelete
                                    .Validate(out ResultModel resultUpdatePhotoBoothApplicationDelete)
                                    .ConfigureAwait(false))
                                return Json(resultUpdatePhotoBoothApplicationDelete);
                        }
                        
                        if(applicationCancellation.CancellationExtraFees.Any(p => p.Category == ExtraFeeCategoryType.Insurance.ToInt()))
                        {
                            insurancePartialRefundCheck = true;
                            insuranceNumbers = getInsuranceNumbers.Where(q => q.RelatedIndividualInsuraneId == 0)
                                .Select(q => q.Number).ToList();
                        }
                        if(applicationCancellation.CancellationExtraFees.Any(p => p.Category == ExtraFeeCategoryType.RelatedInsurance.ToInt()))
                        {
                            releatedInsurancePartialRefundCheck = true;
                            releatedInsuranceNumbers = getInsuranceNumbers.Where(q => q.RelatedIndividualInsuraneId != 0)
                                .Select(q => q.Number).ToList();
                        } 
                    }
                    else if (applicationCancellation.CancellationTypeId == (int)ApplicationCancellationType.Cancellation)
                    {
                        if (apiResponseGetApplicationCancellation.Data.ApplicationExtraFeeNames.Any(q =>
                                q != null ? q.Contains("Fotoğraf") : false))
                        {
                            var apiResponseUpdatePhotoBoothApplicationDelete = await PortalHttpClientHelper
                                .GetAsync<ApiResponse<UpdateApiResponse>>
                                ($"{ApiMethodName.Appointment.UpdatePhotoBoothApplicationDelete + applicationId}/{false}",
                                    AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                                .ConfigureAwait(false);


                            if (!await apiResponseUpdatePhotoBoothApplicationDelete
                                    .Validate(out ResultModel resultUpdatePhotoBoothApplicationDelete)
                                    .ConfigureAwait(false))
                                return Json(resultUpdatePhotoBoothApplicationDelete);
                        }
                        
                        if(getInsuranceNumbers.Count() > 0)
                        {
                            insuranceNumbers = getInsuranceNumbers.Where(q => q.RelatedIndividualInsuraneId == 0)
                                .Select(q => q.Number).ToList();
                        }


                        if (apiResponseGetApplicationCancellation.Data.RelatedInsuranceApplicationCancellationTypeId.HasValue ? apiResponseGetApplicationCancellation.Data.RelatedInsuranceApplicationCancellationTypeId.Value == (int)RelatedInsuranceApplicationCancellationType.OnlyNormalApplications : (applicationCancellation.CancellationTypeId == (int)ApplicationCancellationType.Cancellation && !hasPermissionForCancellationOrRefund && applicationCancellation.ReleatedInsuranceCheck))
                        {
                            // Yeni ilgili sigorta başvurularını oluştur
                            var createdNewRelatedInsuranceApplications =
                                await CreateRelatedInsuranceApplicationsForCancelledApplication(applicationId);

                            // Eğer yeni başvurular oluşturulmadıysa hata mesajı döndür
                            if (createdNewRelatedInsuranceApplications == null ||
                                !createdNewRelatedInsuranceApplications.Any())
                            {
                                return Json(new ResultModel
                                {
                                    Message = ResultMessage.MissingOrInvalidData.ToDescription(),
                                    ResultType = ResultType.Danger
                                });
                            }

                            // Başvuru iptal durumunu güncelle
                            var cancellationOperation = await UpdateApplicationCancellationStatus(
                                applicationCancellationId, viewModel.ApplicationCancellationStatusId,
                                apiResponseGetApplicationCancellation.Data.CancellationTypeId, sendSapCheck, sapGroupId,
                                applicationId, relationalApplicationId, insuranceNumbers, releatedInsuranceNumbers,
                                apiResponseGetApplicationCancellation.Data.ApplicantTypeId, insurancePartialRefundCheck,
                                releatedInsurancePartialRefundCheck);

                            // İptal işlemi başarılıysa yeni başvuruları işle
                            if (cancellationOperation.ResultType == ResultType.Success)
                            {
                                return Json(await ProcessNewApplications(createdNewRelatedInsuranceApplications));
                            }
                            else
                            {
                                // İptal işlemi başarısızsa hata mesajı döndür
                                return Json(cancellationOperation);
                            }
                        }
                        else if (getInsuranceNumbers.Count() > 0)
                        {
                            releatedInsuranceNumbers = getInsuranceNumbers
                                .Where(q => q.RelatedIndividualInsuraneId != 0)
                                .Select(q => q.Number).ToList();
                        }
                    }

                    if (apiResponseGetApplicationCancellation.Data.CountryId == 152) // Saudi Arabia
                    {
                        var apiRequestKsaIcrPartialOrCancelEInvoice = new SendKsaIcrGenerateEInvoiceApiRequest
                        {
                            ApplicationId = applicationCancellation.ApplicationId,
                            CreatedAt = DateTime.Now,
                            CreatedBy = UserSession.UserId,
                            ExtraFeeIds = new List<int>()
                        };
                        if (applicationCancellation.CancellationTypeId == (int)ApplicationCancellationType.PartialRefund)
                        {
                            apiRequestKsaIcrPartialOrCancelEInvoice.CancellationTypeId =
                                (int)ApplicationCancellationType.PartialRefund;
                            apiRequestKsaIcrPartialOrCancelEInvoice.ExtraFeeIds = applicationCancellation
                                .CancellationExtraFees.Select(p => p.ExtraFeeId).ToList();
                        }
                        else if (applicationCancellation.CancellationTypeId == (int)ApplicationCancellationType.Cancellation)
                        {
                            apiRequestKsaIcrPartialOrCancelEInvoice.CancellationTypeId =
                                (int)ApplicationCancellationType.Cancellation;
                            apiRequestKsaIcrPartialOrCancelEInvoice.ExtraFeeIds = applicationCancellation
                                .ApplicationExtraFeeIds;
                        }

                        var apiResponseSendKsaIcrPartialOrCancelEInvoice = await PortalHttpClientHelper
                            .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                                (apiRequestKsaIcrPartialOrCancelEInvoice, ApiMethodName.Appointment.SendKsaIcrPartialOrCancelEInvoice, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                            .ConfigureAwait(false);

                        if (!await apiResponseSendKsaIcrPartialOrCancelEInvoice.Validate(out ResultModel resultKsaIcrPartialOrCancel).ConfigureAwait(false))
                            return Json(resultKsaIcrPartialOrCancel);

                    }

                    // SAP not created yet, so no need to cancel from accounting service
                    return Json(await UpdateApplicationCancellationStatus(applicationCancellationId, viewModel.ApplicationCancellationStatusId, apiResponseGetApplicationCancellation.Data.CancellationTypeId, sendSapCheck, sapGroupId, applicationId, relationalApplicationId, insuranceNumbers,releatedInsuranceNumbers, apiResponseGetApplicationCancellation.Data.ApplicantTypeId,insurancePartialRefundCheck,releatedInsurancePartialRefundCheck));
                }

                sendSapCheck = true;
                var apiRequestCancelOrder = new CancelOrderApiRequest
                {
                    AppointmentId = applicationCancellation.ApplicationId,
                    CancelOrderReasonId = applicationCancellation.CancellationReasonId,
                    ExtraFeeIds = new List<int>()
                };

                
                
                if (applicationCancellation.CancellationTypeId == (int)ApplicationCancellationType.PartialRefund)
                {
                    if (apiResponseGetApplicationCancellation.Data.PhotoBoothUsedCheck && applicationCancellation.CancellationExtraFees.Any(q => q != null ? (q.ExtraFeeName.Contains("Fotoğraf") || (q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy"))) : false))
                    {
                        return Json(new ResultModel { Message = EnumResources.PhotoBoothPartialRefund, ResultType = ResultType.Danger });
                    }
                    else if (applicationCancellation.CancellationExtraFees.Any(q => q != null ? (q.ExtraFeeName.Contains("Fotoğraf") || (q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy"))) : false))
                    {

                        apiRequestCancelOrder.ExtraFeeIds = applicationCancellation.CancellationExtraFees.Select(p => p.ExtraFeeId).ToList();

                    }
                    else
                    {
                        apiRequestCancelOrder.ExtraFeeIds = applicationCancellation.CancellationExtraFees.Select(p => p.ExtraFeeId).ToList();
                    }
                    
                    if(applicationCancellation.CancellationExtraFees.Any(p => p.Category == ExtraFeeCategoryType.Insurance.ToInt()))
                    {
                        insurancePartialRefundCheck = true;
                        insuranceNumbers = getInsuranceNumbers.Where(q => q.RelatedIndividualInsuraneId == 0)
                            .Select(q => q.Number).ToList();
                    }
                    if(applicationCancellation.CancellationExtraFees.Any(p => p.Category == ExtraFeeCategoryType.RelatedInsurance.ToInt()))
                    {
                        releatedInsurancePartialRefundCheck = true;
                        releatedInsuranceNumbers = getInsuranceNumbers.Where(q => q.RelatedIndividualInsuraneId != 0)
                            .Select(q => q.Number).ToList();
                    } 
                }
                if (applicationCancellation.CancellationTypeId == (int)ApplicationCancellationType.Cancellation)
                {
                    if (apiResponseGetApplicationCancellation.Data.PhotoBoothUsedCheck && applicationCancellation.ApplicationExtraFeeNames.Any(q => q != null ? q.Contains("Fotoğraf") : false))
                    {
                        IList<int> extraFeesId = new List<int>() { };
                        foreach (var extraFeeId in applicationCancellation.ApplicationExtraFeeIds)
                        {
                            if (extraFeeId != 130 && extraFeeId != 131 && extraFeeId != 46 && extraFeeId != 18 && extraFeeId != 182)
                                extraFeesId.Add(extraFeeId);
                        }
                        apiRequestCancelOrder.ExtraFeeIds = extraFeesId;

                    }
                    
                     if(getInsuranceNumbers.Count() > 0) {
                    
                        insuranceNumbers = getInsuranceNumbers.Where(q => q.RelatedIndividualInsuraneId == 0)
                            .Select(q => q.Number).ToList();
                    }
                    
                }

                var apiResponseCancelOrder = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequestCancelOrder, ApiMethodName.Accounting.CancelOrder, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

                if (!await apiResponseCancelOrder.Validate(out ResultModel resultCancelOrder).ConfigureAwait(false))
                    return Json(resultCancelOrder);
                
                if (apiResponseGetApplicationCancellation.Data.RelatedInsuranceApplicationCancellationTypeId.HasValue ? apiResponseGetApplicationCancellation.Data.RelatedInsuranceApplicationCancellationTypeId.Value == (int)RelatedInsuranceApplicationCancellationType.OnlyNormalApplications : (applicationCancellation.CancellationTypeId == (int)ApplicationCancellationType.Cancellation && !hasPermissionForCancellationOrRefund && applicationCancellation.ReleatedInsuranceCheck))
                {
                    // Yeni ilgili sigorta başvurularını oluştur
                    var createdNewRelatedInsuranceApplications = await CreateRelatedInsuranceApplicationsForCancelledApplication(applicationId);

                    // Eğer yeni başvurular oluşturulmadıysa hata mesajı döndür
                    if (createdNewRelatedInsuranceApplications == null || !createdNewRelatedInsuranceApplications.Any())
                    {
                        return Json(new ResultModel { Message = ResultMessage.MissingOrInvalidData.ToDescription(), ResultType = ResultType.Danger });
                    }

                    // Başvuru iptal durumunu güncelle
                    var cancellationOperation = await UpdateApplicationCancellationStatus(
                        applicationCancellationId, viewModel.ApplicationCancellationStatusId,
                        apiResponseGetApplicationCancellation.Data.CancellationTypeId, sendSapCheck, sapGroupId,
                        applicationId, relationalApplicationId, insuranceNumbers,releatedInsuranceNumbers,
                        apiResponseGetApplicationCancellation.Data.ApplicantTypeId,insurancePartialRefundCheck,releatedInsurancePartialRefundCheck);

                    // İptal işlemi başarılıysa yeni başvuruları işle
                    if (cancellationOperation.ResultType == ResultType.Success)
                    {
                        return Json(await ProcessNewApplications(createdNewRelatedInsuranceApplications));
                    }
                    else
                    {
                        // İptal işlemi başarısızsa hata mesajı döndür
                        return Json(cancellationOperation);
                    }
                } 
                else if(getInsuranceNumbers.Count() > 0) {
                    
                        releatedInsuranceNumbers = getInsuranceNumbers.Where(q => q.RelatedIndividualInsuraneId != 0)
                            .Select(q => q.Number).ToList();
                }
                
               
                if (applicationCancellation.CancellationTypeId == (int)ApplicationCancellationType.PartialRefund)
                {
                    if (applicationCancellation.CancellationExtraFees.Any(q => q != null ? (q.ExtraFeeName.Contains("Fotoğraf") || (q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy"))) : false))
                    {
                        var apiResponseUpdatePhotoBoothApplicationDelete = await PortalHttpClientHelper
                       .GetAsync<ApiResponse<UpdateApiResponse>>
                       ($"{ApiMethodName.Appointment.UpdatePhotoBoothApplicationDelete + applicationId}/{false}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                       .ConfigureAwait(false);

                        if (!await apiResponseUpdatePhotoBoothApplicationDelete.Validate(out ResultModel resultUpdatePhotoBoothApplicationDelete).ConfigureAwait(false))
                            return Json(resultUpdatePhotoBoothApplicationDelete);
                    }
                }

                if (applicationCancellation.CancellationTypeId == (int)ApplicationCancellationType.Cancellation)
                {
                    if (apiResponseGetApplicationCancellation.Data.PhotoBoothUsedCheck && applicationCancellation.ApplicationExtraFeeNames.Any(q => q != null ? q.Contains("Fotoğraf") : false))
                    {
                        var apiResponseUpdatePhotoBoothApplicationDelete = await PortalHttpClientHelper
                           .GetAsync<ApiResponse<UpdateApiResponse>>
                           ($"{ApiMethodName.Appointment.UpdatePhotoBoothApplicationDelete + applicationId}/{true}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                           .ConfigureAwait(false);


                        if (!await apiResponseUpdatePhotoBoothApplicationDelete.Validate(out ResultModel resultUpdatePhotoBoothApplicationDelete).ConfigureAwait(false))
                            return Json(resultUpdatePhotoBoothApplicationDelete);
                    }
                    else if (applicationCancellation.ApplicationExtraFeeNames.Any(q => q != null ? q.Contains("Fotoğraf") : false))
                    {
                        var apiResponseUpdatePhotoBoothApplicationDelete = await PortalHttpClientHelper
                           .GetAsync<ApiResponse<UpdateApiResponse>>
                           ($"{ApiMethodName.Appointment.UpdatePhotoBoothApplicationDelete + applicationId}/{false}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                           .ConfigureAwait(false);


                        if (!await apiResponseUpdatePhotoBoothApplicationDelete.Validate(out ResultModel resultUpdatePhotoBoothApplicationDelete).ConfigureAwait(false))
                            return Json(resultUpdatePhotoBoothApplicationDelete);
                    }
                }

                updateApplicationCancellationStatus = apiResponseCancelOrder.Data.Result;
                sapGroupId = apiResponseCancelOrder.Data.CancelSapGroupId;
            }
            else if (viewModel.ApplicationCancellationStatusId == (int)ApplicationCancellationStatus.Rejected)
            {
                updateApplicationCancellationStatus = true;
            }
            else
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            if (updateApplicationCancellationStatus)
            {
                return Json(await UpdateApplicationCancellationStatus(applicationCancellationId, viewModel.ApplicationCancellationStatusId, apiResponseGetApplicationCancellation.Data.CancellationTypeId, sendSapCheck, sapGroupId, applicationId, relationalApplicationId, insuranceNumbers,releatedInsuranceNumbers, apiResponseGetApplicationCancellation.Data.ApplicantTypeId,insurancePartialRefundCheck,releatedInsurancePartialRefundCheck));
            }
            else
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
        }

        private async Task<List<AddUpdateApplicationApiRequest>> CreateRelatedInsuranceApplicationsForCancelledApplication(int cancelledMainApplicationId)
        {
            var cancelledMainApplication = await GetCancelledMainApplication(cancelledMainApplicationId);
            if (cancelledMainApplication == null) return new List<AddUpdateApplicationApiRequest>();

            var relatedInsuranceApplications = await GetRelatedInsuranceApplications(cancelledMainApplicationId);
            if (!relatedInsuranceApplications.Any()) return new List<AddUpdateApplicationApiRequest>();

            return relatedInsuranceApplications.Select(relatedInsuranceApplicant => CreateApplicationModel(cancelledMainApplication, relatedInsuranceApplicant)).ToList();

            //return await ProcessNewApplications(newApplicationModels);
        }

        private async Task<ApplicationSummaryApiResponse> GetCancelledMainApplication(int cancelledMainApplicationId)
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationSummaryApiResponse>>
                    (ApiMethodName.Appointment.GetSanitizedApplicationSummary + cancelledMainApplicationId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return null;

            return apiResponse.Data;
        }

        private async Task<List<RelatedIndividualInsuranceApiResponseDto>> GetRelatedInsuranceApplications(int cancelledMainApplicationId)
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationsWithExemptInsuranceRelationApiResponse>>($"{ApiMethodName.Appointment.GetApplicationsWithExemptInsuranceRelation + cancelledMainApplicationId}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return null;

            return apiResponse.Data.RelatedInsuranceApplications;
        }

        private async Task<ResultModel> ProcessNewApplications(List<AddUpdateApplicationApiRequest> newApplicationModels)
        {
            if (!newApplicationModels.Any())
                return new ResultModel { Message = "No new applications to process.", ResultType = ResultType.Warning };

            foreach (var applicationApiRequest in newApplicationModels)
            {
                var addApplicationApiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<AddApiResponse>>(applicationApiRequest, ApiMethodName.Appointment.AddApplication, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                if (!await addApplicationApiResponse.Validate(out ResultModel addApplicationApiResponseResult).ConfigureAwait(false))
                    return addApplicationApiResponseResult;
            }

            return new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success };
        }

        private AddUpdateApplicationApiRequest CreateApplicationModel(ApplicationSummaryApiResponse cancelledMainApplication, RelatedIndividualInsuranceApiResponseDto relatedInsuranceApplicant)
        {
            return new AddUpdateApplicationApiRequest
            {
                BranchApplicationCountryId = cancelledMainApplication.BranchApplicationCountryId,
                ApplicantTypeId = (int)ApplicantType.Individual,
                ApplicantCount = null,
                ApplicationTypeId = (int)ApplicationType.NonApplicationRelatedInsurance,
                PassportNumber = relatedInsuranceApplicant.PassportNumber,
                PassportExpireDate = DateTime.MinValue,
                ApplicationPassportStatusId = (int)ApplicationPassportStatus.WithPassport,
                AgencyId = cancelledMainApplication.AgencyId,
                CustomerId = null,
                Reason = null,
                IsAllowDeniedPassport = cancelledMainApplication.IsAllowDeniedPassport,
                TitleId = 1,
                Name = relatedInsuranceApplicant.Name,
                Surname = relatedInsuranceApplicant.Surname,
                BirthDate = relatedInsuranceApplicant.BirthDate,
                GenderId = 1,
                MaritalStatusId = null,
                NationalityId = relatedInsuranceApplicant.NationalityId,
                ResidenceNumber = cancelledMainApplication.ResidenceNumber,
                MaidenName = null,
                FatherName = cancelledMainApplication.Name,
                MotherName = cancelledMainApplication.Name,
                Email = relatedInsuranceApplicant.Email,
                PhoneNumber1 = relatedInsuranceApplicant.PhoneNumber1,
                Address = cancelledMainApplication.Address,
                ForeignCityId = cancelledMainApplication.ForeignCityId,
                PostalCode = cancelledMainApplication.PostalCode,
                NameOfSecondContactPerson = cancelledMainApplication.NameOfSecondContactPerson,
                AreaId = cancelledMainApplication.AreaId,
                AreaName = cancelledMainApplication.AreaName,
                GovernorateId = cancelledMainApplication.GovernorateId,
                GovernorateName = cancelledMainApplication.GovernorateName,
                IsCargoIntegrationSelected = cancelledMainApplication.IsCargoIntegrationSelected,
                RelationalApplicationId = null,
                RelationShipId = (int)ApplicantIndividualRelationship.HimselfHerself,
                Note = null,
                RequestedBy = UserSession.UserId,
                Document = new AddUpdateApplicationApiRequest.ApplicationDocument
                {
                    TotalYearInCountry = null,
                    ReimbursementTypeId = null,
                    ReimbursementSponsorDetail = null,
                    Job = null,
                    OccupationId = null,
                    CompanyName = null,
                    TotalYearInCompany = null,
                    MonthlySalary = null,
                    MonthlySalaryCurrencyId = null,
                    HasBankAccount = false,
                    BankBalance = null,
                    BankBalanceCurrencyId = null,
                    HasDeed = false,
                    VisaCategoryId = cancelledMainApplication.VisaCategoryId,
                    AdditionalServiceTypeId = null,
                    NumberOfEntryId = null,
                    HasEntryBan = false,
                    EntryDate = DateTime.Now,
                    ExitDate = DateTime.Now,
                    CityName = null,
                    AccomodationDetail = null,
                    HasRelativeAbroad = false,
                    RelativeLocation = null,
                    PersonTravelWith = null,
                    PersonTravelWithHasVisa = false,
                    ProvidedWithHasRelatedInsurance = false,
                    ApplicationTogether = false,
                    ApplicationTogetherFiftyYearCount = null,
                    ApplicationTogetherFifteenYearCount = null,
                    HasPersonVisitedTurkeyBefore = null,
                    VehicleTypeId = null,
                    BrandModelId = null,
                    PlateNo = null,
                    ModelYear = null,
                    ChassisNumber = null,
                    BrandText = null,
                    ModelText = null,
                    ResidenceApplicationToBeMade = null
                },
                VisaHistories = new List<AddUpdateApplicationApiRequest.ApplicationVisaHistory>(),
                ExtraFees = cancelledMainApplication.ExtraFees
                            .Where(p => p.Category == (int)ExtraFeeCategoryType.RelatedInsurance).Select(p => new AddUpdateApplicationApiRequest.ApplicationExtraFee
                            {
                                ExtraFeeId = p.ExtraFeeId,
                                Quantity = 1,
                                CreatedBy = UserSession.UserId,
                                CreatedAt = DateTime.Now,
                            }).ToList()
            };
        }

        public async Task<ResultModel> UpdateApplicationCancellationStatus(int cancellationId, int cancellationStatusId, int cancellationTypeId, bool sendSapCheck, string sapGroupId, int applicationId, int relationalApplicationId, List<string> insuranceNumbers,List<string> releatedInsuranceNumbers, int applicantTypeId,bool insurancePartialRefundCheck,bool releatedInsurancePartialRefundCheck)
        {
            var apiRequestUpdateApplicationCancellationStatus = new UpdateApplicationCancellationStatusApiRequest
            {
                ApplicationCancellationId = cancellationId,
                ApplicationCancellationStatusId = cancellationStatusId,
                UpdatedBy = UserSession.UserId
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequestUpdateApplicationCancellationStatus, ApiMethodName.Appointment.UpdateApplicationCancellationStatus, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (cancellationStatusId == (int)ApplicationCancellationStatus.Approved && insuranceNumbers.Count > 0 && (cancellationTypeId == (int)ApplicationCancellationType.Cancellation || insurancePartialRefundCheck))
            {

                for (int i = 0; i < insuranceNumbers.Count; i++)
                {
                    var apiRequest = new CancelPolicyApiRequest
                    {
                        PolicyNo = insuranceNumbers[i],
                        AppointmentId = applicationId,
                        UserId = UserSession.UserId
                    };

                    await PortalHttpClientHelper
                        .PostAsJsonAsync<ApiResponse<CancelPolicyApiResponse>>
                        (apiRequest, ApiMethodName.Insurance.CancelPolicy, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                        .ConfigureAwait(false);
                    
                     await PortalHttpClientHelper
                        .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                        (null,
                            $"{ApiMethodName.Appointment.UpdateOldInsurancePassive + applicationId}/{insuranceNumbers[i]}",
                            AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                        .ConfigureAwait(false);
                }
            }
            if (cancellationStatusId == (int)ApplicationCancellationStatus.Approved && releatedInsuranceNumbers.Count > 0 && (cancellationTypeId == (int)ApplicationCancellationType.Cancellation || releatedInsurancePartialRefundCheck))
            {

                for (int i = 0; i < releatedInsuranceNumbers.Count; i++)
                {
                    var apiRequest = new CancelPolicyApiRequest
                    {
                        PolicyNo = releatedInsuranceNumbers[i],
                        AppointmentId = applicationId,
                        UserId = UserSession.UserId,
                        ProviderId = ProviderType.EmaaRi.ToInt()
                    };

                    await PortalHttpClientHelper
                        .PostAsJsonAsync<ApiResponse<CancelPolicyApiResponse>>
                            (apiRequest, ApiMethodName.Insurance.CancelPolicy, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                        .ConfigureAwait(false);
                    
                    await PortalHttpClientHelper
                        .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                        (null,
                            $"{ApiMethodName.Appointment.UpdateOldInsurancePassive + applicationId}/{releatedInsuranceNumbers[i]}",
                            AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                        .ConfigureAwait(false);
                }
            }
            
            if (sendSapCheck && (applicantTypeId == (int)Contracts.Entities.Enums.ApplicantType.Family || applicantTypeId == (int)Contracts.Entities.Enums.ApplicantType.Group) && sapGroupId == "")
            {
                int id;
                id = relationalApplicationId != 0 ? relationalApplicationId : applicationId;

                var createApiResponse = await PortalHttpClientHelper
                    .GetAsync<ApiResponse<AddApiResponse>>
                    (ApiMethodName.Accounting.CreateOrder + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);
            }


            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return result;
            if (!string.IsNullOrEmpty(sapGroupId))
                return new ResultModel { Message = sapGroupId, ResultType = ResultType.Info };
            else
                return new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success };
        }

        #endregion

        #region Get

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult List()
        {
            var viewModel = new FilterApplicationCancellationViewModel()
            {
                FilterCancellationStatusId = (int)ApplicationCancellationStatus.Pending
            };

            return View(viewModel);
        }

        public async Task<IActionResult> GetPaginatedApplicationCancellations([DataSourceRequest] DataSourceRequest request, FilterApplicationCancellationViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedApplicationCancellationsApiRequest
            {
                BranchId = UserSession.BranchId,
                CancellationTypeId = filterViewModel.FilterCancellationTypeId,
                CancellationReasonId = filterViewModel.FilterCancellationReasonId,
                CancellationStatusId = filterViewModel.FilterCancellationStatusId,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<ApplicationCancellationsApiResponse>>>
                (apiRequest, ApiMethodName.Appointment.GetPaginatedApplicationCancellations, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<ApplicationCancellationViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().ApplicationCancellations
                    .Select(p => new ApplicationCancellationViewModel
                    {
                        BranchApplicationCountryId = p.BranchApplicationCountryId,
                        BranchId = p.BranchId,

                        EncryptedId = p.Id.ToEncrypt(),
                        CancellationTypeId = p.CancellationTypeId,
                        CancellationReasonId = p.CancellationReasonId,
                        CancellationStatusId = p.CancellationStatusId,
                        CreatedBy = p.CreatedBy,
                        CreatedByNameSurname = p.CreatedByNameSurname,
                        CreatedAt = p.CreatedAt,

                        ApplicationId = p.ApplicationId,
                        ApplicantTypeId = p.ApplicantTypeId,
                        ApplicationTypeId = p.ApplicationTypeId,
                        PassportNumber = p.PassportNumber,
                        Name = p.Name,
                        Surname = p.Surname,
                        ApplicationStatusId = p.ApplicationStatusId,
                        ApplicationStatus = p.ApplicationStatus,
                        ApplicationTime = p.ApplicationTime
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #endregion

    }
}
