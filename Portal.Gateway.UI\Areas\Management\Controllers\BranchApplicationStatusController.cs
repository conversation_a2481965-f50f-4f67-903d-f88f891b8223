﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.General;
using Portal.Gateway.ApiModel.Requests.Management.BranchApplicationStatus;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.BranchApplicationStatus;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class BranchApplicationStatusController : BaseController<BranchApplicationStatusController>
    {
        public BranchApplicationStatusController(
            IOptions<AppSettings> appSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        [HttpGet]
        public async Task<IActionResult> List(string encryptedBranchId)
        {
            if (string.IsNullOrEmpty(encryptedBranchId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("/");
            }

            var apiRequest = new BranchApplicationStatusesApiRequest
            {
                BranchId = encryptedBranchId.ToDecryptInt()
            };

            var apiResponse = await PortalHttpClientHelper
                              .PostAsJsonAsync<ApiResponse<BranchApplicationStatusesApiResponse>>(apiRequest, ApiMethodName.Parameter.GetBranchApplicationStatuses, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                              .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", result);
                return RedirectToAction("/");
            }

            var branchList = await CacheHelper.GetBranchesAsync();
            var branchDetail = string.Empty;
            var branch = branchList.Branches?.FirstOrDefault(q => q.Id == encryptedBranchId.ToDecryptInt());

            if (branch != null)
                branchDetail = $"{branch.BranchTranslations.FirstOrDefault(q => q.LanguageId == LanguageId)?.Name} ({branch.Country?.Name})";

            var viewModel = new BranchApplicationStatusViewModel
            {
                EncryptedBranchId = encryptedBranchId,
                BranchDetail = branchDetail,
                BranchApplicationStatuses = apiResponse.Data.BranchApplicationStatuses.Select(p => new BranchApplicationStatusListViewModel
                {
                    EncryptedBranchApplicationStatusId = p.Id.ToEncrypt(),
                    EncryptedApplicationStatusId = p.ApplicationStatusId.ToEncrypt(),
                    IsActive = p.IsActive,
                    Name = p.Name,
                    IsApplicationUpdateAllowed = p.IsApplicationUpdateAllowed,
                    IsNewApplicationWithSamePassportNumberBlocked = p.IsNewApplicationWithSamePassportNumberBlocked,
                    IsApplicationStatusHidden = p.IsApplicationStatusHidden,
                    IsRestrictChangeContactInformation = p.IsRestrictChangeContactInformation,
                    IsEsimBeSent = p.IsEsimBeSent
                }).ToList()
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateBranchApplicationStatus(BranchApplicationStatusViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateBranchApplicationStatusesApiRequest
            {
                BranchId = viewModel.EncryptedBranchId.ToDecryptInt(),
                BranchApplicationStatuses = viewModel.BranchApplicationStatuses
                                            .Select(p => new UpdateBranchApplicationStatusesApiRequest.BranchApplicationStatus
                                            {
                                                IsActive = p.IsActive,
                                                ApplicationStatusId = p.EncryptedApplicationStatusId.ToDecryptInt(),
                                                BranchApplicationStatusId = p.EncryptedBranchApplicationStatusId.ToDecryptInt(),
                                                IsApplicationUpdateAllowed = p.IsApplicationUpdateAllowed,
                                                IsNewApplicationWithSamePassportNumberBlocked = p.IsNewApplicationWithSamePassportNumberBlocked,
                                                IsApplicationStatusHidden = p.IsApplicationStatusHidden,
                                                IsRestrictChangeContactInformation = p.IsRestrictChangeContactInformation,
                                                IsEsimBeSent = p.IsEsimBeSent
                                            })
                                            .ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateBranchApplicationStatuses, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }
    }
}