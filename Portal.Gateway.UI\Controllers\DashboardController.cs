﻿using Elastic.Clients.Elasticsearch.Nodes;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.Dashboard;
using Portal.Gateway.ApiModel.Responses.Dashboard;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.ApiModel.Responses.Management.RoleAction;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.QMS.ViewModels.Reports;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using Portal.Gateway.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Controllers
{
    public class DashboardController : BaseController<DashboardController>
    {
        private readonly CacheSettings _cacheSettings;
        private readonly ILogger<DashboardController> _logger;

        public DashboardController(
            ILogger<DashboardController> logger,
            IOptions<AppSettings> appSettings,
            IOptions<CacheSettings> cacheSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {
            _cacheSettings = cacheSettings.Value;
            _logger = logger;
        }

        [ActionAttribute(IsMenuItem = true)]
        public async Task<IActionResult> Index()
        {
            var activeDashboards = new List<int>();

            var roleActions = await CacheHelper.GetRoleActionsAsync();

            var dashboards = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
                .Select(p => p.RoleActions.Where(q => q.Action.Controller == "Dashboard" && q.Action.IsActive)
                .Select(t => new { ActionName = t.Action.Method })).ToList();

            if (dashboards.Any(p => p.Any(q => q.ActionName == "Chairman")))
                activeDashboards.Add((int)DashboardTypes.Chairman);

            if (dashboards.Any(p => p.Any(q => q.ActionName == "Ministry")))
                activeDashboards.Add((int)DashboardTypes.Ministry);

            if (dashboards.Any(p => p.Any(q => q.ActionName == "Consular")) && UserSession.BranchIds.Count() > 0)
                activeDashboards.Add((int)DashboardTypes.Consular);

            if (dashboards.Any(p => p.Any(q => q.ActionName == "OfficeManager")) && UserSession.BranchId.HasValue)
                activeDashboards.Add((int)DashboardTypes.OfficeManager);

            if (dashboards.Any(p => p.Any(q => q.ActionName == "Officer")) && UserSession.BranchId.HasValue)
                activeDashboards.Add((int)DashboardTypes.Officer);

            ViewData["ActiveDashboards"] = activeDashboards.FirstOrDefault();
            ViewBag.EnableQuarterCategoryStatsGraph = AppSettings.EnableQuarterCategoryStatsGraph;

            return View();
        }

        [Action(IsMenuItem = true)]
        public IActionResult BranchGeneralSituation()
        {
            ViewBag.EnableQuarterCategoryStatsGraph = AppSettings.EnableQuarterCategoryStatsGraph;

            return View("_ChairManBranchGeneralSituation");
        }

        #region General

        [HttpGet]
        public async Task<IActionResult> GetCountriesWithBranches()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = await CacheHelper.GetActiveCountriesAsync();

            var branchList = await CacheHelper.GetBranchesAsync();

            try
            {
                var model = apiResponse.CountryList.Select(p => new
                {
                    CountryName = p.CountryName,
                    CountryCode = p.CountryCode,
                    BranchCount = p.BranchList.Count(),
                    BranchList = p.BranchList?.Select(q => new
                    {
                        Name = branchList.Branches.First(t => t.Id == q.BranchId).BranchTranslations.Any(t => t.LanguageId == LanguageId) ?
                            branchList.Branches.First(t => t.Id == q.BranchId).BranchTranslations.First(t => t.LanguageId == LanguageId).Name :
                            branchList.Branches.First(t => t.Id == q.BranchId).BranchTranslations.FirstOrDefault().Name
                    }).ToList()
                }).ToList();

                return Json(new ResultModel { Data = model, Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
            }
            catch { }

            return Json(new ResultModel { Data = null, Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger });
        }

        #endregion

        #region Officer

        [HttpGet]
        public async Task<IActionResult> GetOfficerDailyStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await GetDailyApplicationsAsync(UserSession.BranchIds, UserSession.UserId)).Data;

            var cachedBranches = await CacheHelper.GetBranchesAsync();

            var timeZoneOffset = cachedBranches.Branches.FirstOrDefault(q => q.Id == UserSession.BranchId)?.TimeZoneOffset ?? 0;

            try
            {
                var count = apiResponse?.BranchList
                        .FirstOrDefault(p => p.BranchId == UserSession.BranchId)?.AgentApplicationStats
                        .FirstOrDefault(p => p.Id == UserSession.UserId)?.Value ?? 0;

                return Json(new ResultModel
                {
                    Data = new
                    {
                        Count = count,
                        Date = (int)timeZoneOffset - timeZoneOffset == 0 ? apiResponse.StatsDate.AddHours((int)timeZoneOffset).ToString("dd/MM/yyyy hh:mm") : (int)timeZoneOffset - timeZoneOffset < 0 ?
                        apiResponse.StatsDate.AddHours((int)timeZoneOffset).AddMinutes(30).ToString("dd/MM/yyyy hh:mm") : apiResponse.StatsDate.AddHours((int)timeZoneOffset).AddMinutes(-30).ToString("dd/MM/yyyy hh:mm")
                    },
                    Message = ResultMessage.OperationIsSuccessful.ToDescription(),
                    ResultType = ResultType.Success
                });
            }
            catch
            {
                return Json(jsonResult);
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetOfficerStats(string type)
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetPeriodicApplicationStatsAsync()).Data;

            try
            {
                var responseType = EnumHelper.GetEnumAsDictionary(typeof(DashboardRangeTypes)).FirstOrDefault(p => p.Value == type).Key;

                switch (responseType)
                {
                    case (int)DashboardRangeTypes.Weekly:
                        var weeklyReport = apiResponse.Stats
                            .FirstOrDefault(p => p.StatsType == responseType)?.BranchList
                            .FirstOrDefault(p => p.BranchId == UserSession.BranchId)?.AgentList
                            .FirstOrDefault(p => p.AgentId == UserSession.UserId)?.ApplicationStats
                            .Select(p => new
                            {
                                Date = p.Date.ToString("dd/MM/yyyy"),
                                Count = p.Value
                            }).ToList();

                        if (weeklyReport != null)
                        {
                            jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                            jsonResult.Data = weeklyReport;
                            jsonResult.ResultType = ResultType.Success;
                        }
                        break;

                    case (int)DashboardRangeTypes.Quarter:
                        var monthlyReport = apiResponse.Stats
                            .FirstOrDefault(p => p.StatsType == responseType)?.BranchList
                            .FirstOrDefault(p => p.BranchId == UserSession.BranchId)?.AgentList
                            .FirstOrDefault(p => p.AgentId == UserSession.UserId)?.ApplicationStats
                            .Select(p => new
                            {
                                Date = Convert.ToDateTime(p.Date.ToShortDateString()),
                                Count = p.Value
                            }).ToList();

                        if (monthlyReport != null)
                        {
                            jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                            jsonResult.Data = monthlyReport;
                            jsonResult.ResultType = ResultType.Success;
                        }
                        break;

                    default:
                        break;
                }
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetOfficerExtraFeeStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetExtraFeeStatsAsync()).Data;

            try
            {
                var quarterReport = apiResponse.Stats
                            .FirstOrDefault()?.BranchList
                            .FirstOrDefault(p => p.BranchId == UserSession.BranchId)?.AgentList
                            .FirstOrDefault(p => p.AgentId == UserSession.UserId)?.Stats
                            .Select(p => new
                            {
                                ExtraFee = p.Name,
                                Count = p.Value
                            }).ToList();

                if (quarterReport != null)
                {
                    jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                    jsonResult.Data = quarterReport;
                    jsonResult.ResultType = ResultType.Success;
                }
            }
            catch { }

            return Json(jsonResult);
        }

        #endregion

        #region Supervisor

        [HttpGet]
        public async Task<IActionResult> GetOfficeManagerDailyStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await GetDailyApplicationsAsync(branchIds: UserSession.BranchIds)).Data;

            var cachedBranches = await CacheHelper.GetBranchesAsync();

            var timeZoneOffset = cachedBranches.Branches.FirstOrDefault(q => q.Id == UserSession.BranchId)?.TimeZoneOffset ?? 0;

            try
            {
                var count = apiResponse?.BranchList
                    .FirstOrDefault(p => p.BranchId == UserSession.BranchId)?.ApplicationCount ?? 0;

                return Json(new ResultModel
                {
                    Data = new
                    {
                        Count = count,
                        Date = (int)timeZoneOffset - timeZoneOffset == 0 ? apiResponse.StatsDate.AddHours((int)timeZoneOffset).ToString("dd/MM/yyyy hh:mm") : (int)timeZoneOffset - timeZoneOffset < 0 ?
                        apiResponse.StatsDate.AddHours((int)timeZoneOffset).AddMinutes(30).ToString("dd/MM/yyyy hh:mm") : apiResponse.StatsDate.AddHours((int)timeZoneOffset).AddMinutes(-30).ToString("dd/MM/yyyy hh:mm")
                    },
                    Message = ResultMessage.OperationIsSuccessful.ToDescription(),
                    ResultType = ResultType.Success
                });
            }
            catch
            {
                return Json(jsonResult);
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetOfficeManagerStats(string type)
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetPeriodicApplicationStatsAsync()).Data;

            try
            {
                var responseType = EnumHelper.GetEnumAsDictionary(typeof(DashboardRangeTypes)).FirstOrDefault(p => p.Value == type).Key;

                switch (responseType)
                {
                    case (int)DashboardRangeTypes.Weekly:
                        var weeklyReport = apiResponse?.Stats
                            .FirstOrDefault(p => p.StatsType == responseType)?.BranchList
                            .FirstOrDefault(p => p.BranchId == UserSession.BranchId)?.ApplicationStats
                            .Select(p => new
                            {
                                Date = p.Date.ToString("dd/MM/yyyy"),
                                Count = p.Value
                            }).ToList();

                        if (weeklyReport != null)
                        {
                            jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                            jsonResult.Data = weeklyReport;
                            jsonResult.ResultType = ResultType.Success;
                        }
                        break;

                    case (int)DashboardRangeTypes.Quarter:
                        var monthlyReport = apiResponse?.Stats
                            .FirstOrDefault(p => p.StatsType == responseType)?.BranchList
                            .FirstOrDefault(p => p.BranchId == UserSession.BranchId)?.ApplicationStats
                            .Select(p => new
                            {
                                Date = Convert.ToDateTime(p.Date.ToShortDateString()),
                                Count = p.Value
                            }).ToList();

                        if (monthlyReport != null)
                        {
                            jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                            jsonResult.Data = monthlyReport;
                            jsonResult.ResultType = ResultType.Success;
                        }
                        break;

                    default:
                        break;
                }
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetOfficeManagerExtraFeeStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetExtraFeeStatsAsync()).Data;

            try
            {
                var quarterReport = apiResponse?.Stats
                            .FirstOrDefault()?.BranchList
                            ?.FirstOrDefault(p => p.BranchId == UserSession.BranchId)?.Stats
                            .Select(p => new
                            {
                                ExtraFee = p.Name,
                                Count = p.Value
                            }).ToList().OrderBy(p => p.ExtraFee);

                if (quarterReport != null)
                {
                    jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                    jsonResult.Data = quarterReport;
                    jsonResult.ResultType = ResultType.Success;
                }
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetOfficeManagerDeliveryStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetDailyPassportDeliveriesAsync()).Data;

            try
            {
                var statusList = apiResponse?.Stats
                    .Select(p => new
                    {
                        Status = EnumHelper.GetEnumDescription(typeof(ApplicationStatusType), p.ApplicationStatusType.ToString()),
                        Count = p.Value
                    }).ToList().OrderBy(p => p.Status);

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = statusList;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetOfficeManagerStatsFromBeginning()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetStatsFromBeginningAsync()).Data;

            try
            {
                var statBranches = (UserSession.BranchId.HasValue) ? apiResponse.BranchList.Where(q => q.BranchId == UserSession.BranchId.Value).ToList()
                    : apiResponse.BranchList;

                var combinedResult = new
                {
                    AllApplications = statBranches.FirstOrDefault(p => p.BranchId == UserSession.BranchId)?.Value,
                    DeclinedRatio = Convert.ToInt32(100 *
                        statBranches
                            .FirstOrDefault(p => p.BranchId == UserSession.BranchId)?.StatusList
                            .FirstOrDefault(p => p.TypeId == (int)ApplicationStatusType.Rejection ||
                                            p.TypeId == (int)ApplicationStatusType.RejectionRefundDone ||
                                            p.TypeId == (int)ApplicationStatusType.RejectedPassportDeliveredToCourier ||
                                            p.TypeId == (int)ApplicationStatusType.RejectionWithCountryEntryBanned ||
                                            p.TypeId == (int)ApplicationStatusType.IstizanRejection)?.Value /
                        statBranches.FirstOrDefault(p => p.BranchId == UserSession.BranchId)?.Value),
                    PendingApproval = statBranches
                        .FirstOrDefault(p => p.BranchId == UserSession.BranchId)?.StatusList
                        .FirstOrDefault(p => p.TypeId == (int)ApplicationStatusType.Istizan)?.Value,
                    EntryBanned = statBranches.FirstOrDefault(p => p.BranchId == UserSession.BranchId)?.EntryBannedApplications
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = combinedResult;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetOfficeManagerInsuranceStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetBranchInsuranceStatsAsync()).Data;

            try
            {
                var branches = (UserSession.BranchId.HasValue) ? apiResponse.Branches.Where(q => q.BranchId == UserSession.BranchId.Value).ToList()
                    : apiResponse.Branches;

                var model = new
                {
                    TotalCount = branches.Sum(q => q.Total),
                    ActiveCount = branches.Sum(q => q.Active),
                    Ratio = Convert.ToDouble(String.Format("{0:0.00}", (branches.Sum(q => q.Active) / branches.Sum(q => q.Total))))
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetOfficeManagerPreviousDayGeneralStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetOfficeManagerPreviousDayGeneralStatsAsync()).Data;

            try
            {
                var statBranches = (UserSession.BranchId.HasValue) ? apiResponse.BranchList.Where(q => q.BranchId == UserSession.BranchId.Value).ToList()
                    : apiResponse.BranchList;

                var branches = await CacheHelper.GetBranchesAsync();

                var model = new
                {
                    Name = $"{SiteResources.AllBranches.ToTitleCase()} - {apiResponse.StatsDate.ToString("dd/MM/yyyy")}",
                    ApplicantTypes = statBranches.First().ApplicationTypes.Select(q => new
                    {
                        Type = EnumHelper.GetEnumDescription(typeof(ApplicationType), q.Type.ToString()),
                        Value = statBranches.Sum(w => w.ApplicationTypes.First(e => e.Type == q.Type).Count)
                    }).ToList()
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }


        #region Modals

        public async Task<IActionResult> PartialOfficeManagerDailyApplicationTypeStats()
        {
            var branchList = await CacheHelper.GetBranchesAsync();

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Branches.Where(q => UserSession.BranchIds.Any(p => p == q.Id)).Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_OfficeManager/Modals/_DailyApplicationTypeStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalOfficeManagerDailyApplicationTypeStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetOfficeManagerPreviousDayGeneralStatsAsync()).Data;

            var branchList = await CacheHelper.GetBranchesAsync();

            try
            {
                var model = new
                {
                    Date = DateTime.Today.AddDays(-1).ToString("dd/MM/yyyy"),
                    BranchList = branchList.Branches.Where(q => UserSession.BranchIds.Any(p => p == q.Id)).Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        TypeList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id)?.ApplicationTypes.Select(w => new
                        {
                            Type = EnumHelper.GetEnumDescription(typeof(ApplicationType), w.Type.ToString()),
                            Value = w.Count
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        #endregion

        #endregion

        #region Chairman

        #region General Picture

        [HttpGet]
        public async Task<IActionResult> GetOfficerCount()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetOfficersAsync()).Data;

            try
            {
                var model = new
                {
                    TotalCount = apiResponse?.TotalCount
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetChairmanAllBranchesDaily()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await GetDailyApplicationsAsync()).Data;

            var branches = await CacheHelper.GetBranchesAsync();

            try
            {
                var model = new
                {
                    LastUpdate = apiResponse.StatsDate.ToLocalTime().ToString("dd/MM/yyyy hh:mm"),
                    BranchList = branches.Branches.Select(p => new
                    {
                        CountryId = p.Country.Id,
                        Count = apiResponse.BranchList.Any(q => q.BranchId == p.Id) ?
                        apiResponse.BranchList.FirstOrDefault(q => q.BranchId == p.Id).ApplicationCount : 0,
                        Branch = p.BranchTranslations.Any(q => q.LanguageId == LanguageId) ?
                        p.BranchTranslations.FirstOrDefault(q => q.LanguageId == LanguageId).Name :
                        p.BranchTranslations.FirstOrDefault().Name,
                    }).ToList().OrderBy(p => p.Branch)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetChairmanStatsFromBeginning()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetStatsFromBeginningAsync()).Data;

            try
            {
                var combinedResult = new
                {
                    AllApplications = apiResponse.BranchList.Sum(p => p.Value),
                    DeclinedRatio = Convert.ToInt32(100 *
                        apiResponse.BranchList
                            .Sum(p => p.StatusList.FirstOrDefault(t => t.TypeId == (int)ApplicationStatusType.Rejection ||
                                            t.TypeId == (int)ApplicationStatusType.RejectionRefundDone ||
                                            t.TypeId == (int)ApplicationStatusType.RejectedPassportDeliveredToCourier ||
                                            t.TypeId == (int)ApplicationStatusType.RejectionWithCountryEntryBanned ||
                                            t.TypeId == (int)ApplicationStatusType.IstizanRejection).Value) /
                        apiResponse.BranchList
                            .Sum(p => p.Value)),
                    PendingApproval = apiResponse.BranchList
                        .Sum(p => p.StatusList.FirstOrDefault(t => t.TypeId == (int)ApplicationStatusType.Istizan).Value),
                    Completed = apiResponse.BranchList
                        .Sum(p => p.StatusList
                        .FirstOrDefault(t =>
                            t.TypeId == (int)ApplicationStatusType.DeliveredToApplicant ||
                            t.TypeId == (int)ApplicationStatusType.RejectionRefundDone ||
                            t.TypeId == (int)ApplicationStatusType.OutscanToCourrier ||
                            t.TypeId == (int)ApplicationStatusType.HandDeliveredToApplicantAtEmbassy ||
                            t.TypeId == (int)ApplicationStatusType.RejectionWithCountryEntryBanned ||
                            t.TypeId == (int)ApplicationStatusType.RejectedPassportDeliveredToCourier).Value),
                    PieChartName = SiteResources.AllCountries,
                    EntryBanned = apiResponse.BranchList.Sum(p => p.EntryBannedApplications)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = combinedResult;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetQuarterApplicationStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetQuarterApplicationSummaryAsync()).Data;

            try
            {
                var model = new
                {
                    StartDate = apiResponse.StartDate.ToString("dd/MM/yyyy"),
                    EndDate = apiResponse.EndDate.ToString("dd/MM/yyyy"),
                    Name = SiteResources.AllBranches.ToTitleCase(),
                    List = branchList.Branches.Select(p => new
                    {
                        Branch = p.BranchTranslations.Any(q => q.LanguageId == LanguageId) ?
                            p.BranchTranslations.First(q => q.LanguageId == LanguageId).Name :
                            p.BranchTranslations.FirstOrDefault().Name,
                        Count = apiResponse.BranchList.FirstOrDefault(q => q.BranchId == p.Id)?.TotalApplications ?? 0
                    }).ToList().OrderBy(p => p.Branch)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetQuarterApplicationCategoryStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetQuarterApplicationSummaryAsync()).Data;

            var applicationCategoryList = await GetListedVisaCategories();

            try
            {
                var model = new
                {
                    StartDate = apiResponse.StartDate.ToString("dd/MM/yyyy"),
                    EndDate = apiResponse.EndDate.ToString("dd/MM/yyyy"),
                    Name = SiteResources.AllBranches,
                    BranchList = branchList.Branches.OrderBy(o => o.Name).Select(p => new
                    {
                        Branch = p.BranchTranslations.Any(q => q.LanguageId == LanguageId) ?
                            p.BranchTranslations.First(q => q.LanguageId == LanguageId).Name :
                            p.BranchTranslations.FirstOrDefault().Name,
                    }).ToList(),
                    CategoryList = applicationCategoryList.Select(q => new
                    {
                        Category = q.Text,
                        Values = branchList.Branches.OrderBy(o => o.Name).Select(p => new
                        {
                            Value = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == p.Id)?
                                .VisaCategoryList.FirstOrDefault(w => w.VişaCategoryId == Convert.ToInt32(q.Value))?.Total ?? 0
                        }).ToList()
                    }).ToList()
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetPreviousDayFreeApplicationsStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetPreviousDayGeneralStatsAsync()).Data;

            try
            {
                var model = new
                {
                    Name = $"{SiteResources.AllBranches.ToTitleCase()} - {apiResponse.StatsDate.ToString("dd/MM/yyyy")}",
                    FreeApplications = apiResponse.BranchList.First().FreeApplications.Select(q => new
                    {
                        Type = EnumHelper.GetEnumDescription(typeof(ReportFreeApplicationType), q.Type.ToString()),
                        Value = apiResponse.BranchList.Sum(w => w.FreeApplications.First(e => e.Type == q.Type).Count)
                    }).ToList()
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }
        #endregion

        #region Insurance

        [HttpGet]
        public async Task<IActionResult> GetQuarterInsuranceStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetInsuranceStatsAsync()).Data;

            try
            {
                var model = new
                {
                    StartDate = apiResponse.StartDate.ToString("dd/MM/yyyy"),
                    EndDate = apiResponse.EndDate.ToString("dd/MM/yyyy"),
                    Name = SiteResources.AllBranches,
                    BranchList = branchList.Branches.OrderBy(o => o.Name).Select(p => new
                    {
                        Branch = p.BranchTranslations.Any(q => q.LanguageId == LanguageId) ?
                            p.BranchTranslations.First(q => q.LanguageId == LanguageId).Name :
                            p.BranchTranslations.FirstOrDefault().Name,
                    }).ToList(),
                    InsuranceList = apiResponse.InsuranceTypes.Select(q => new
                    {
                        InsuranceType = q.InsuranceName,
                        Values = branchList.Branches.OrderBy(o => o.Name).Select(p => new
                        {
                            Value = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == p.Id)?
                                .InsuranceList.FirstOrDefault(w => w.InsuranceId == q.InsuranceId)?.Count ?? 0
                        }).ToList()
                    }).ToList()
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetDailyInsuranceStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetInsuranDailyInsuranceStatsAsync()).Data;

            try
            {
                var model = new
                {
                    StartDate = apiResponse.StartDate.ToString("dd/MM/yyyy"),
                    EndDate = apiResponse.EndDate.ToString("dd/MM/yyyy"),
                    Name = SiteResources.AllBranches,
                    BranchList = branchList.Branches.OrderBy(o => o.Name).Select(p => new
                    {
                        Branch = p.BranchTranslations.Any(q => q.LanguageId == LanguageId) ?
                            p.BranchTranslations.First(q => q.LanguageId == LanguageId).Name :
                            p.BranchTranslations.FirstOrDefault().Name,
                    }).ToList(),
                    InsuranceList = apiResponse.InsuranceTypes.Select(q => new
                    {
                        InsuranceType = q.InsuranceName,
                        Values = branchList.Branches.OrderBy(o => o.Name).Select(p => new
                        {
                            Value = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == p.Id)?
                                .InsuranceList.FirstOrDefault(w => w.InsuranceId == q.InsuranceId)?.Count ?? 0
                        }).ToList()
                    }).ToList()
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetTotalInsuranceStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetInsuranceStatsAsync()).Data;

            try
            {
                var model = new
                {
                    Name = SiteResources.AllBranches,
                    BranchList = apiResponse.BranchList.Select(q => new
                    {
                        Branch = branchList.Branches.FirstOrDefault(p => p.Id == q.BranchId).BranchTranslations.Any(p => p.LanguageId == LanguageId) ?
                            branchList.Branches.FirstOrDefault(p => p.Id == q.BranchId)?.BranchTranslations.First(p => p.LanguageId == LanguageId).Name :
                            branchList.Branches.FirstOrDefault(p => p.Id == q.BranchId)?.BranchTranslations.FirstOrDefault().Name,
                        Value = q.InsuranceList.Sum(p => p.Count)
                    }).ToList()
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetActiveInsuranceStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetInsuranceStatsAsync()).Data;

            try
            {
                var model = new
                {
                    Name = SiteResources.AllBranches,
                    BranchList = branchList.Branches.OrderBy(o => o.Name).Select(p => new
                    {
                        Branch = p.BranchTranslations.Any(q => q.LanguageId == LanguageId) ?
                            p.BranchTranslations.First(q => q.LanguageId == LanguageId).Name :
                            p.BranchTranslations.FirstOrDefault().Name,
                    }).ToList(),
                    InsuranceList = apiResponse.InsuranceTypes.Select(q => new
                    {
                        InsuranceType = q.InsuranceName,
                        Values = branchList.Branches.OrderBy(o => o.Name).Select(p => new
                        {
                            Value = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == p.Id)?
                                .InsuranceList.FirstOrDefault(w => w.InsuranceId == q.InsuranceId)?.Count ?? 0
                        }).ToList()
                    }).ToList()
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetMonthyInsuranceSalesStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetMonthComparativeInsuranceStatsAsync()).Data;

            try
            {
                var model = new
                {
                    Name = SiteResources.AllBranches,
                    MonthList = apiResponse.Branches.First().Months.Select(q => new
                    {
                        Month = EnumHelper.GetEnumDescription(typeof(Months), q.Month.ToString())
                    }).ToList(),
                    BranchList = apiResponse.Branches.Select(q => new
                    {
                        Branch = branchList.Branches.FirstOrDefault(p => p.Id == q.BranchId).BranchTranslations.Any(p => p.LanguageId == LanguageId) ?
                            branchList.Branches.FirstOrDefault(p => p.Id == q.BranchId)?.BranchTranslations.First(p => p.LanguageId == LanguageId).Name :
                            branchList.Branches.FirstOrDefault(p => p.Id == q.BranchId)?.BranchTranslations.FirstOrDefault().Name,
                        MonthList = q.Months.Select(p => new
                        {

                            Value = p.Total
                        }).ToList()
                    }).ToList()
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        #endregion

        #region Statistics

        [HttpGet]
        public async Task<IActionResult> GetPreviousDayGeneralStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetPreviousDayGeneralStatsAsync()).Data;

            var branchList = await CacheHelper.GetBranchesAsync();

            try
            {
                var model = new
                {
                    Name = $"{SiteResources.AllBranches.ToTitleCase()} - {apiResponse.StatsDate.ToString("dd/MM/yyyy")}",
                    ApplicantTypes = apiResponse.BranchList.First().ApplicationTypes.Select(q => new
                    {
                        Type = EnumHelper.GetEnumDescription(typeof(ApplicationType), q.Type.ToString()),
                        Value = apiResponse.BranchList.Sum(w => w.ApplicationTypes.First(e => e.Type == q.Type).Count)
                    }).ToList(),
                    ApplicantAgeRanges = apiResponse.BranchList.First().ApplicantAgeRanges.Select(q => new
                    {
                        Type = EnumHelper.GetEnumDescription(typeof(ReportAgeRanges), q.Type.ToString()),
                        Value = apiResponse.BranchList.Sum(w => w.ApplicantAgeRanges.First(e => e.Type == q.Type).Count)
                    }).ToList(),
                    ApplicantGenders = apiResponse.BranchList.First().ApplicantGenders.OrderBy(q => q.Type).Select(q => new
                    {
                        Type = EnumHelper.GetEnumDescription(typeof(Gender), q.Type.ToString()),
                        Value = apiResponse.BranchList.Sum(w => w.ApplicantGenders.First(e => e.Type == q.Type).Count)
                    }).ToList(),
                    ApplicationProcessTypes = apiResponse.BranchList.First().ApplicationProcessTypes.Select(q => new
                    {
                        Type = EnumHelper.GetEnumDescription(typeof(ReportApplicationProcessTypes), q.Type.ToString()),
                        Value = apiResponse.BranchList.Sum(w => w.ApplicationProcessTypes.First(e => e.Type == q.Type).Count)
                    }).ToList(),
                    TotalApplications = apiResponse.BranchList.Select(q => new
                    {
                        Branch = branchList.Branches.First(t => t.Id == q.BranchId).BranchTranslations.Any(t => t.LanguageId == LanguageId) ?
                            branchList.Branches.First(t => t.Id == q.BranchId).BranchTranslations.First(t => t.LanguageId == LanguageId).Name :
                            branchList.Branches.First(t => t.Id == q.BranchId).BranchTranslations.FirstOrDefault().Name,
                        Value = q.Total
                    }).ToList()
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetMonthylExtraFeeStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetMonthlyExtraFeeStatsAsync()).Data;

            try
            {
                var model = new
                {
                    StartDate = apiResponse.StartDate.ToString("dd/MM/yyyy"),
                    EndDate = apiResponse.EndDate.ToString("dd/MM/yyyy"),
                    Name = SiteResources.AllBranches,
                    ExtraFeeList = apiResponse.ExtraFees.Select(q => new
                    {
                        ExtraFee = q.Name,
                        Values = branchList.Branches.OrderBy(o => o.Name).Select(p => new
                        {
                            Value = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == p.Id)?
                                .Stats.FirstOrDefault(w => w.ExtraFeeId == q.ExtraFeeId)?.Value ?? 0
                        }).ToList()
                    }).ToList(),
                    BranchList = branchList.Branches.OrderBy(o => o.Name).Select(p => new
                    {
                        Branch = p.BranchTranslations.Any(q => q.LanguageId == LanguageId) ?
                            p.BranchTranslations.First(q => q.LanguageId == LanguageId).Name :
                            p.BranchTranslations.FirstOrDefault().Name,
                    }).ToList(),
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetPreviousDayChangeStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetPreviousDayChangeStatsAsync()).Data;

            var branchList = await CacheHelper.GetBranchesAsync();

            var changeTypes = EnumHelper.GetEnumAsDictionary(typeof(DashboardApplicationChangeTypes)).Select(p => new
            {
                Text = p.Value,
                Value = p.Key
            }).ToList().OrderBy(q => q.Text);

            try
            {
                int totalCount = 0;

                foreach (var branch in apiResponse.BranchList)
                {
                    foreach (var changeType in branch.ChangeTypes)
                    {
                        totalCount += changeType.Count;
                    }
                }

                var isDataExists = totalCount;

                var model = new
                {
                    Name = $"{SiteResources.AllBranches.ToTitleCase()} - {apiResponse.StatsDate.ToString("dd/MM/yyyy")}",
                    TypeList = isDataExists > 0 ? changeTypes.Select(q => new
                    {
                        Type = q.Text,
                        Value = apiResponse.BranchList.Sum(w => w.ChangeTypes.First(e => e.Type == q.Value).Count)
                    }).ToList() : null
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetAverageIstizanResultTime()
        {

            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetAverageIstizanResultTimeAsync()).Data;

            try
            {
                var combinedResult = new
                {
                    AverageIstizanResultTime = apiResponse.AverageIstizanResultTime

                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = combinedResult;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetAverageNormalApplicationResultTime()
        {

            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetAverageNormalApplicationResultTimeAsync()).Data;

            try
            {
                var combinedResult = new
                {
                    AverageNormalApplicationResultTime = apiResponse.AverageNormalApplicationResultTime

                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = combinedResult;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        #endregion

        #region Modals

        public async Task<IActionResult> PartialInsuranceStats()
        {
            var branchList = await CacheHelper.GetBranchesAsync();

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Branches.Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_ChairMan/_Modals/_InsuranceStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalInsuranceStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetInsuranceStatsAsync()).Data;

            try
            {
                var model = new
                {
                    StartDate = apiResponse.StartDate.ToString("dd/MM/yyyy"),
                    EndDate = apiResponse.EndDate.ToString("dd/MM/yyyy"),
                    BranchList = branchList.Branches.Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        InsuranceList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id)?.InsuranceList.Select(w => new
                        {
                            Insurance = apiResponse.InsuranceTypes.First(t => t.InsuranceId == w.InsuranceId).InsuranceName,
                            Value = w.Count
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        public async Task<IActionResult> PartialActiveInsuranceStats()
        {
            var branchList = await CacheHelper.GetBranchesAsync();

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Branches.Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_ChairMan/_Modals/_ActiveInsuranceStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalActiveInsuranceStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetInsuranceStatsAsync()).Data;

            try
            {
                var model = new
                {
                    StartDate = apiResponse.StartDate.ToString("dd/MM/yyyy"),
                    EndDate = apiResponse.EndDate.ToString("dd/MM/yyyy"),
                    BranchList = branchList.Branches.Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        InsuranceList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id)?.InsuranceList.Select(w => new
                        {
                            Insurance = apiResponse.InsuranceTypes.First(t => t.InsuranceId == w.InsuranceId).InsuranceName,
                            Value = w.Count
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        public async Task<IActionResult> PartialQuarterExtraFeeStats()
        {
            var branchList = await CacheHelper.GetBranchesAsync();

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Branches.Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_ChairMan/_Modals/_QuarterExtraFeeStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalQuarterExtraFeeStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetQuarterExtraFeeStatsAsync()).Data;

            try
            {
                var model = new
                {
                    StartDate = apiResponse.StartDate.ToString("dd/MM/yyyy"),
                    EndDate = apiResponse.EndDate.ToString("dd/MM/yyyy"),
                    BranchList = branchList.Branches.Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        ExtraFeeList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id)?.Stats.Select(w => new
                        {
                            ExtraFee = apiResponse.ExtraFees.First(t => t.ExtraFeeId == w.ExtraFeeId).Name,
                            Value = w.Value
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        public async Task<IActionResult> PartialDailyApplicationProcessTypeStats()
        {
            var branchList = await CacheHelper.GetBranchesAsync();

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Branches.Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_ChairMan/_Modals/_DailyApplicationProcessTypeStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalDailyApplicationProcessTypeStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetPreviousDayGeneralStatsAsync()).Data;

            try
            {
                var model = new
                {
                    Date = DateTime.Today.AddDays(-1).ToString("dd/MM/yyyy"),
                    BranchList = branchList.Branches.Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        TypeList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id)?.ApplicationProcessTypes.Select(w => new
                        {
                            Type = EnumHelper.GetEnumDescription(typeof(ReportApplicationProcessTypes), w.Type.ToString()),
                            Value = w.Count
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        public async Task<IActionResult> PartialDailyApplicationTypeStats()
        {
            var branchList = await CacheHelper.GetBranchesAsync();

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Branches.Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_ChairMan/_Modals/_DailyApplicationTypeStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalDailyApplicationTypeStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetPreviousDayGeneralStatsAsync()).Data;

            try
            {
                var model = new
                {
                    Date = DateTime.Today.AddDays(-1).ToString("dd/MM/yyyy"),
                    BranchList = branchList.Branches.Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        TypeList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id)?.ApplicationTypes.Select(w => new
                        {
                            Type = EnumHelper.GetEnumDescription(typeof(ApplicationType), w.Type.ToString()),
                            Value = w.Count
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        public async Task<IActionResult> PartialDailyAgeRangeStats()
        {
            var branchList = await CacheHelper.GetBranchesAsync();

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Branches.Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_ChairMan/_Modals/_DailyAgeRangeStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalDailyAgeRangeStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetPreviousDayGeneralStatsAsync()).Data;

            try
            {
                var model = new
                {
                    Date = DateTime.Today.AddDays(-1).ToString("dd/MM/yyyy"),
                    BranchList = branchList.Branches.Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        TypeList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id)?.ApplicantAgeRanges.Select(w => new
                        {
                            Type = EnumHelper.GetEnumDescription(typeof(ReportAgeRanges), w.Type.ToString()),
                            Value = w.Count
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        public async Task<IActionResult> PartialDailyFreeApplicationStats()
        {
            var branchList = await CacheHelper.GetBranchesAsync(); ;

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Branches.Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_ChairMan/_Modals/_DailyFreeApplicationStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalDailyFreeApplicationStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetPreviousDayGeneralStatsAsync()).Data;

            try
            {
                var model = new
                {
                    Date = DateTime.Today.AddDays(-1).ToString("dd/MM/yyyy"),
                    BranchList = branchList.Branches.Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        TypeList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id && w.FreeApplications.Any(e => e.Count > 0))?.FreeApplications.Select(w => new
                        {
                            Type = EnumHelper.GetEnumDescription(typeof(ReportFreeApplicationType), w.Type.ToString()),
                            Value = w.Count
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        public async Task<IActionResult> PartialQuarterApplicationCategoryStats()
        {
            var branchList = await CacheHelper.GetBranchesAsync();

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Branches.Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_ChairMan/_Modals/_QuarterApplicationCategoryStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalQuarterApplicationCategoryStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetQuarterApplicationSummaryAsync()).Data;

            var visaCategories = await GetCachedVisaCategories();

            try
            {
                var model = new
                {
                    StartDate = apiResponse.StartDate.ToString("dd/MM/yyyy"),
                    EndDate = apiResponse.EndDate.ToString("dd/MM/yyyy"),
                    BranchList = branchList.Branches.Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        CategoryList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id)?.VisaCategoryList.Select(w => new
                        {
                            Category = GetVisaCategoryNameFromId(w.VişaCategoryId, visaCategories),
                            Value = w.Total
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        public async Task<IActionResult> PartialQuarterApplicationStats()
        {
            var branchList = await CacheHelper.GetBranchesAsync();

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Branches.Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_ChairMan/_Modals/_QuarterApplicationStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalQuarterApplicationStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var request = new GetStatsApiRequest();

            var apiResponse = (await CacheHelper.GetQuarterApplicationStatsAsync()).Data;

            try
            {
                var model = new
                {
                    StartDate = apiResponse.StartDate.ToString("dd/MM/yyyy"),
                    EndDate = apiResponse.EndDate.ToString("dd/MM/yyyy"),
                    BranchList = branchList.Branches.Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        DayList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id)?.ApplicationStats.Select(w => new
                        {
                            Day = Convert.ToDateTime(w.Date.ToShortDateString()),
                            Value = w.Value
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        public async Task<IActionResult> PartialPreviousDayChangeStats()
        {
            var branchList = await CacheHelper.GetBranchesAsync();

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Branches.Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_ChairMan/_Modals/_PreviousDayChangeStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalPreviousDayChangeStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetPreviousDayChangeStatsAsync()).Data;

            try
            {
                var model = new
                {
                    Date = DateTime.Today.AddDays(-1).ToString("dd/MM/yyyy"),
                    BranchList = branchList.Branches.Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        TypeList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id)?.ChangeTypes.Where(w => w.Count > 0).Select(w => new
                        {
                            Type = EnumHelper.GetEnumDescription(typeof(DashboardApplicationChangeTypes), w.Type.ToString()),
                            Value = w.Count
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        #endregion

        #endregion

        #region Consular

        // OK: Başlangıçtan itibaren alınan başvuru sayısı (şube bazlı) - Tamamlanan başvuru sayısı (şube bazlı) - Ret oranı (şube bazlı) - Onay bekleyen -istizan (şube bazlı)
        [HttpGet]
        public async Task<IActionResult> GetConsularStatsFromBeginning()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetStatsFromBeginningAsync()).Data;

            try
            {
                var statBranches = (UserSession.BranchId.HasValue) ? apiResponse.BranchList.Where(q => q.BranchId == UserSession.BranchId.Value).ToList()
                    : apiResponse.BranchList;

                var combinedResult = new
                {
                    AllApplications = statBranches.Sum(p => p.Value),
                    DeclinedRatio = Convert.ToInt32(100 *
                        statBranches
                            .Sum(p => p.StatusList.FirstOrDefault(t => t.TypeId == (int)ApplicationStatusType.Rejection ||
                                            t.TypeId == (int)ApplicationStatusType.RejectionRefundDone ||
                                            t.TypeId == (int)ApplicationStatusType.RejectedPassportDeliveredToCourier ||
                                            t.TypeId == (int)ApplicationStatusType.RejectionWithCountryEntryBanned ||
                                            t.TypeId == (int)ApplicationStatusType.IstizanRejection).Value) /
                        statBranches
                            .Sum(p => p.Value)),
                    PendingApproval = statBranches
                        .Sum(p => p.StatusList.FirstOrDefault(t => t.TypeId == (int)ApplicationStatusType.Istizan).Value),
                    Completed = statBranches
                        .Sum(p => p.StatusList
                        .FirstOrDefault(t =>
                            t.TypeId == (int)ApplicationStatusType.DeliveredToApplicant ||
                            t.TypeId == (int)ApplicationStatusType.RejectionRefundDone ||
                            t.TypeId == (int)ApplicationStatusType.OutscanToCourrier ||
                            t.TypeId == (int)ApplicationStatusType.HandDeliveredToApplicantAtEmbassy ||
                            t.TypeId == (int)ApplicationStatusType.RejectionWithCountryEntryBanned ||
                            t.TypeId == (int)ApplicationStatusType.RejectedPassportDeliveredToCourier).Value),
                    //PieChartName = SiteResources.AllCountries,
                    //EntryBanned = apiResponse.BranchList.Sum(p => p.EntryBannedApplications)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = combinedResult;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        // OK: Şubelerin günlük başvuru sayısı (şube bazlı)
        [HttpGet]
        public async Task<IActionResult> GetConsularAllBranchesDaily()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await GetDailyApplicationsAsync(branchIds: UserSession.BranchIds)).Data;

            var branches = await CacheHelper.GetBranchesAsync();

            var branchList = branches.Branches.Where(q => UserSession.BranchIds.Any(p => p == q.Id)).ToList();

            try
            {
                var model = new
                {
                    LastUpdate = apiResponse.StatsDate.ToLocalTime().ToString("dd/MM/yyyy hh:mm"),
                    BranchList = branchList.Select(p => new
                    {
                        CountryId = p.Country.Id,
                        Count = apiResponse.BranchList.Any(q => q.BranchId == p.Id) ?
                        apiResponse.BranchList.FirstOrDefault(q => q.BranchId == p.Id).ApplicationCount : 0,
                        Branch = p.BranchTranslations.Any(q => q.LanguageId == LanguageId) ?
                        p.BranchTranslations.FirstOrDefault(q => q.LanguageId == LanguageId).Name :
                        p.BranchTranslations.FirstOrDefault().Name,
                    }).ToList().OrderBy(p => p.Branch)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        // OK: Son çeyrekte yapılan şube bazlı başvurular 
        [HttpGet]
        public async Task<IActionResult> GetConsularQuarterApplicationStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branches = await CacheHelper.GetBranchesAsync();

            var branchList = branches.Branches.Where(q => UserSession.BranchIds.Any(p => p == q.Id)).ToList();

            var apiResponse = (await CacheHelper.GetQuarterApplicationSummaryAsync()).Data;

            try
            {
                var model = new
                {
                    StartDate = apiResponse.StartDate.ToString("dd/MM/yyyy"),
                    EndDate = apiResponse.EndDate.ToString("dd/MM/yyyy"),
                    Name = SiteResources.AllBranches.ToTitleCase(),
                    List = branchList.Select(p => new
                    {
                        Branch = p.BranchTranslations.Any(q => q.LanguageId == LanguageId) ?
                            p.BranchTranslations.First(q => q.LanguageId == LanguageId).Name :
                            p.BranchTranslations.FirstOrDefault().Name,
                        Count = apiResponse.BranchList.FirstOrDefault(q => q.BranchId == p.Id)?.TotalApplications ?? 0
                    }).ToList().OrderBy(p => p.Branch)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        // OK: Son çeyrek şube bazlı başvuru kategorileri 
        [HttpGet]
        public async Task<IActionResult> GetConsularQuarterApplicationCategoryStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branches = await CacheHelper.GetBranchesAsync();

            var branchList = branches.Branches.Where(q => UserSession.BranchIds.Any(p => p == q.Id)).ToList();

            var apiResponse = (await CacheHelper.GetQuarterApplicationSummaryAsync()).Data;

            var applicationCategoryList = await GetListedVisaCategories();

            try
            {
                var model = new
                {
                    StartDate = apiResponse.StartDate.ToString("dd/MM/yyyy"),
                    EndDate = apiResponse.EndDate.ToString("dd/MM/yyyy"),
                    Name = SiteResources.AllBranches,
                    BranchList = branchList.OrderBy(o => o.Name).Select(p => new
                    {
                        Branch = p.BranchTranslations.Any(q => q.LanguageId == LanguageId) ?
                            p.BranchTranslations.First(q => q.LanguageId == LanguageId).Name :
                            p.BranchTranslations.FirstOrDefault().Name,
                    }).ToList(),
                    CategoryList = applicationCategoryList.Select(q => new
                    {
                        Category = q.Text,
                        Values = branchList.OrderBy(o => o.Name).Select(p => new
                        {
                            Value = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == p.Id)?
                                .VisaCategoryList.FirstOrDefault(w => w.VişaCategoryId == Convert.ToInt32(q.Value))?.Total ?? 0
                        }).ToList()
                    }).ToList()
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        // Gunluk başvuru cinsiyet oranı (şube bazlı) - Gunluk başvuru yaş aralıkları (şube bazlı)
        [HttpGet]
        public async Task<IActionResult> GetConsularPreviousDayGeneralStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            // TODO: will be changed
            var apiResponse = (await CacheHelper.GetConsularPreviousDayGeneralStatsAsync()).Data;

            var branches = await CacheHelper.GetBranchesAsync();

            //var branchList = branches.Branches.Where(q => UserSession.BranchIds.Any(p => p == q.Id)).ToList();

            try
            {
                var model = new
                {
                    Name = $"{SiteResources.AllBranches.ToTitleCase()} - {apiResponse.StatsDate.ToString("dd/MM/yyyy")}",
                    ApplicantTypes = apiResponse.BranchList.First().ApplicationTypes.Where(e => e.Type == (int)ApplicationType.Turquois
                    || e.Type == (int)ApplicationType.TurquoisPremium || e.Type == (int)ApplicationType.TurquoisGratis)
                    .Select(q => new
                    {
                        Type = EnumHelper.GetEnumDescription(typeof(ApplicationType), q.Type.ToString()),
                        Value = apiResponse.BranchList.Sum(w => w.ApplicationTypes.First(e => e.Type == q.Type).Count)
                    }).ToList(),
                    ApplicantAgeRanges = apiResponse.BranchList.First().ApplicantAgeRanges.Select(q => new
                    {
                        Type = EnumHelper.GetEnumDescription(typeof(ReportAgeRanges), q.Type.ToString()),
                        Value = apiResponse.BranchList.Sum(w => w.ApplicantAgeRanges.First(e => e.Type == q.Type).Count)
                    }).ToList(),
                    ApplicantGenders = apiResponse.BranchList.First().ApplicantGenders.OrderBy(q => q.Type).Select(q => new
                    {
                        Type = EnumHelper.GetEnumDescription(typeof(Gender), q.Type.ToString()),
                        Value = apiResponse.BranchList.Sum(w => w.ApplicantGenders.First(e => e.Type == q.Type).Count)
                    }).ToList()
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        #region Modals

        public async Task<IActionResult> PartialConsularDailyAgeRangeStats()
        {
            var branches = await CacheHelper.GetBranchesAsync();

            var branchList = branches.Branches.Where(q => UserSession.BranchIds.Any(p => p == q.Id)).ToList();

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_Consular/_Modals/_DailyAgeRangeStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalConsularDailyAgeRangeStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branches = await CacheHelper.GetBranchesAsync();

            var branchList = branches.Branches.Where(q => UserSession.BranchIds.Any(p => p == q.Id)).ToList();

            var apiResponse = (await CacheHelper.GetConsularPreviousDayGeneralStatsAsync()).Data;

            try
            {
                var model = new
                {
                    Date = DateTime.Today.AddDays(-1).ToString("dd/MM/yyyy"),
                    BranchList = branchList.Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        TypeList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id)?.ApplicantAgeRanges.Select(w => new
                        {
                            Type = EnumHelper.GetEnumDescription(typeof(ReportAgeRanges), w.Type.ToString()),
                            Value = w.Count
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        public async Task<IActionResult> PartialConsularQuarterApplicationCategoryStats()
        {
            var branches = await CacheHelper.GetBranchesAsync();

            var branchList = branches.Branches.Where(q => UserSession.BranchIds.Any(p => p == q.Id)).ToList();

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_Consular/_Modals/_QuarterApplicationCategoryStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalConsularQuarterApplicationCategoryStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branches = await CacheHelper.GetBranchesAsync();

            var branchList = branches.Branches.Where(q => UserSession.BranchIds.Any(p => p == q.Id)).ToList();

            var apiResponse = (await CacheHelper.GetQuarterApplicationSummaryAsync()).Data;

            var visaCategories = await GetCachedVisaCategories();

            try
            {
                var model = new
                {
                    StartDate = apiResponse.StartDate.ToString("dd/MM/yyyy"),
                    EndDate = apiResponse.EndDate.ToString("dd/MM/yyyy"),
                    BranchList = branchList.Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        CategoryList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id)?.VisaCategoryList.Select(w => new
                        {
                            Category = GetVisaCategoryNameFromId(w.VişaCategoryId, visaCategories),
                            Value = w.Total
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        public async Task<IActionResult> PartialConsularQuarterApplicationStats()
        {
            var branches = await CacheHelper.GetBranchesAsync();

            var branchList = branches.Branches.Where(q => UserSession.BranchIds.Any(p => p == q.Id)).ToList();

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_Consular/_Modals/_QuarterApplicationStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalConsularQuarterApplicationStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branches = await CacheHelper.GetBranchesAsync();

            var branchList = branches.Branches.Where(q => UserSession.BranchIds.Any(p => p == q.Id)).ToList();

            var request = new GetStatsApiRequest();

            var apiResponse = (await CacheHelper.GetQuarterApplicationStatsAsync()).Data;

            try
            {
                var model = new
                {
                    StartDate = apiResponse.StartDate.ToString("dd/MM/yyyy"),
                    EndDate = apiResponse.EndDate.ToString("dd/MM/yyyy"),
                    BranchList = branchList.Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        DayList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id)?.ApplicationStats.Select(w => new
                        {
                            Day = Convert.ToDateTime(w.Date.ToShortDateString()),
                            Value = w.Value
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        public async Task<IActionResult> PartialConsularDailyApplicationTypeStats()
        {
            var branchList = await CacheHelper.GetBranchesAsync();

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Branches.Where(q => UserSession.BranchIds.Any(p => p == q.Id)).Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_Consular/_Modals/_DailyApplicationTypeStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalConsularDailyApplicationTypeStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetConsularPreviousDayGeneralStatsAsync()).Data;

            try
            {
                var model = new
                {
                    Date = DateTime.Today.AddDays(-1).ToString("dd/MM/yyyy"),
                    BranchList = branchList.Branches.Where(q => UserSession.BranchIds.Any(p => p == q.Id)).Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        TypeList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id)?.ApplicationTypes.Where(e => e.Type == (int)ApplicationType.Turquois || e.Type == (int)ApplicationType.TurquoisPremium || e.Type == (int)ApplicationType.TurquoisGratis).Select(w => new
                        {
                            Type = EnumHelper.GetEnumDescription(typeof(ApplicationType), w.Type.ToString()),
                            Value = w.Count
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }


        #endregion

        #endregion

        #region Ministry

        [HttpGet]
        public async Task<IActionResult> GetMinistryStatsFromBeginning()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetStatsFromBeginningAsync()).Data;

            try
            {
                var combinedResult = new
                {
                    AllApplications = apiResponse.BranchList.Sum(p => p.Value),
                    DeclinedRatio = Convert.ToInt32(100 *
                        apiResponse.BranchList
                            .Sum(p => p.StatusList.FirstOrDefault(t => t.TypeId == (int)ApplicationStatusType.Rejection ||
                                            t.TypeId == (int)ApplicationStatusType.RejectionRefundDone ||
                                            t.TypeId == (int)ApplicationStatusType.RejectedPassportDeliveredToCourier ||
                                            t.TypeId == (int)ApplicationStatusType.RejectionWithCountryEntryBanned ||
                                            t.TypeId == (int)ApplicationStatusType.IstizanRejection).Value) /
                        apiResponse.BranchList
                            .Sum(p => p.Value)),
                    PendingApproval = apiResponse.BranchList
                        .Sum(p => p.StatusList.FirstOrDefault(t => t.TypeId == (int)ApplicationStatusType.Istizan).Value),
                    Completed = apiResponse.BranchList
                        .Sum(p => p.StatusList
                        .FirstOrDefault(t =>
                            t.TypeId == (int)ApplicationStatusType.DeliveredToApplicant ||
                            t.TypeId == (int)ApplicationStatusType.RejectionRefundDone ||
                            t.TypeId == (int)ApplicationStatusType.OutscanToCourrier ||
                            t.TypeId == (int)ApplicationStatusType.HandDeliveredToApplicantAtEmbassy ||
                            t.TypeId == (int)ApplicationStatusType.RejectionWithCountryEntryBanned ||
                            t.TypeId == (int)ApplicationStatusType.RejectedPassportDeliveredToCourier).Value),
                    //PieChartName = SiteResources.AllCountries,
                    //EntryBanned = apiResponse.BranchList.Sum(p => p.EntryBannedApplications)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = combinedResult;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetMinistryAllBranchesDaily()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await GetDailyApplicationsAsync()).Data;

            var branches = await CacheHelper.GetBranchesAsync();

            try
            {
                var model = new
                {
                    LastUpdate = apiResponse.StatsDate.ToLocalTime().ToString("dd/MM/yyyy hh:mm"),
                    BranchList = branches.Branches.Select(p => new
                    {
                        CountryId = p.Country.Id,
                        Count = apiResponse.BranchList.Any(q => q.BranchId == p.Id) ?
                        apiResponse.BranchList.FirstOrDefault(q => q.BranchId == p.Id).ApplicationCount : 0,
                        Branch = p.BranchTranslations.Any(q => q.LanguageId == LanguageId) ?
                        p.BranchTranslations.FirstOrDefault(q => q.LanguageId == LanguageId).Name :
                        p.BranchTranslations.FirstOrDefault().Name,
                    }).ToList().OrderBy(p => p.Branch)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetMinistryQuarterApplicationStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetQuarterApplicationSummaryAsync()).Data;

            try
            {
                var model = new
                {
                    StartDate = apiResponse.StartDate.ToString("dd/MM/yyyy"),
                    EndDate = apiResponse.EndDate.ToString("dd/MM/yyyy"),
                    Name = SiteResources.AllBranches.ToTitleCase(),
                    List = branchList.Branches.Select(p => new
                    {
                        Branch = p.BranchTranslations.Any(q => q.LanguageId == LanguageId) ?
                            p.BranchTranslations.First(q => q.LanguageId == LanguageId).Name :
                            p.BranchTranslations.FirstOrDefault().Name,
                        Count = apiResponse.BranchList.FirstOrDefault(q => q.BranchId == p.Id)?.TotalApplications ?? 0
                    }).ToList().OrderBy(p => p.Branch)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetMinistryQuarterApplicationCategoryStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetQuarterApplicationSummaryAsync()).Data;

            var applicationCategoryList = await GetListedVisaCategories();

            try
            {
                var model = new
                {
                    StartDate = apiResponse.StartDate.ToString("dd/MM/yyyy"),
                    EndDate = apiResponse.EndDate.ToString("dd/MM/yyyy"),
                    Name = SiteResources.AllBranches,
                    BranchList = branchList.Branches.OrderBy(o => o.Name).Select(p => new
                    {
                        Branch = p.BranchTranslations.Any(q => q.LanguageId == LanguageId) ?
                            p.BranchTranslations.First(q => q.LanguageId == LanguageId).Name :
                            p.BranchTranslations.FirstOrDefault().Name,
                    }).ToList(),
                    CategoryList = applicationCategoryList.Select(q => new
                    {
                        Category = q.Text,
                        Values = branchList.Branches.OrderBy(o => o.Name).Select(p => new
                        {
                            Value = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == p.Id)?
                                .VisaCategoryList.FirstOrDefault(w => w.VişaCategoryId == Convert.ToInt32(q.Value))?.Total ?? 0
                        }).ToList()
                    }).ToList()
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        [HttpGet]
        public async Task<IActionResult> GetMinistryPreviousDayGeneralStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var apiResponse = (await CacheHelper.GetPreviousDayGeneralStatsAsync()).Data;

            var branchList = await CacheHelper.GetBranchesAsync();

            try
            {
                var model = new
                {
                    Name = $"{SiteResources.AllBranches.ToTitleCase()} - {apiResponse.StatsDate.ToString("dd/MM/yyyy")}",
                    ApplicantTypes = apiResponse.BranchList.First().ApplicationTypes.Where(e => e.Type == (int)ApplicationType.Turquois || e.Type == (int)ApplicationType.TurquoisPremium || e.Type == (int)ApplicationType.TurquoisGratis).Select(q => new
                    {
                        Type = EnumHelper.GetEnumDescription(typeof(ApplicationType), q.Type.ToString()),
                        Value = apiResponse.BranchList.Sum(w => w.ApplicationTypes.First(e => e.Type == q.Type).Count)
                    }).ToList(),
                    ApplicantAgeRanges = apiResponse.BranchList.First().ApplicantAgeRanges.Select(q => new
                    {
                        Type = EnumHelper.GetEnumDescription(typeof(ReportAgeRanges), q.Type.ToString()),
                        Value = apiResponse.BranchList.Sum(w => w.ApplicantAgeRanges.First(e => e.Type == q.Type).Count)
                    }).ToList(),
                    ApplicantGenders = apiResponse.BranchList.First().ApplicantGenders.OrderBy(q => q.Type).Select(q => new
                    {
                        Type = EnumHelper.GetEnumDescription(typeof(Gender), q.Type.ToString()),
                        Value = apiResponse.BranchList.Sum(w => w.ApplicantGenders.First(e => e.Type == q.Type).Count)
                    }).ToList(),
                    ApplicationProcessTypes = apiResponse.BranchList.First().ApplicationProcessTypes.Select(q => new
                    {
                        Type = EnumHelper.GetEnumDescription(typeof(ReportApplicationProcessTypes), q.Type.ToString()),
                        Value = apiResponse.BranchList.Sum(w => w.ApplicationProcessTypes.First(e => e.Type == q.Type).Count)
                    }).ToList(),
                    //TotalApplications = apiResponse.BranchList.Select(q => new
                    //{
                    //    Branch = branchList.Branches.First(t => t.Id == q.BranchId).BranchTranslations.Any(t => t.LanguageId == LanguageId) ?
                    //        branchList.Branches.First(t => t.Id == q.BranchId).BranchTranslations.First(t => t.LanguageId == LanguageId).Name :
                    //        branchList.Branches.First(t => t.Id == q.BranchId).BranchTranslations.FirstOrDefault().Name,
                    //    Value = q.Total
                    //}).ToList()
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        #region Modals

        public async Task<IActionResult> PartialMinistryQuarterApplicationStats()
        {
            var branchList = await CacheHelper.GetBranchesAsync();

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Branches.Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_Ministry/_Modals/_QuarterApplicationStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalMinistryQuarterApplicationStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var request = new GetStatsApiRequest();

            var apiResponse = (await CacheHelper.GetQuarterApplicationStatsAsync()).Data;

            try
            {
                var model = new
                {
                    StartDate = apiResponse.StartDate.ToString("dd/MM/yyyy"),
                    EndDate = apiResponse.EndDate.ToString("dd/MM/yyyy"),
                    BranchList = branchList.Branches.Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        DayList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id)?.ApplicationStats.Select(w => new
                        {
                            Day = Convert.ToDateTime(w.Date.ToShortDateString()),
                            Value = w.Value
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        public async Task<IActionResult> PartialMinistryQuarterApplicationCategoryStats()
        {
            var branchList = await CacheHelper.GetBranchesAsync();

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Branches.Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_Ministry/_Modals/_QuarterApplicationCategoryStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalMinistryQuarterApplicationCategoryStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetQuarterApplicationSummaryAsync()).Data;

            var visaCategories = await GetCachedVisaCategories();

            try
            {
                var model = new
                {
                    StartDate = apiResponse.StartDate.ToString("dd/MM/yyyy"),
                    EndDate = apiResponse.EndDate.ToString("dd/MM/yyyy"),
                    BranchList = branchList.Branches.Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        CategoryList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id)?.VisaCategoryList.Select(w => new
                        {
                            Category = GetVisaCategoryNameFromId(w.VişaCategoryId, visaCategories),
                            Value = w.Total
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        public async Task<IActionResult> PartialMinistryDailyAgeRangeStats()
        {
            var branchList = await CacheHelper.GetBranchesAsync();

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Branches.Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_Ministry/_Modals/_DailyAgeRangeStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalMinistryDailyAgeRangeStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetPreviousDayGeneralStatsAsync()).Data;

            try
            {
                var model = new
                {
                    Date = DateTime.Today.AddDays(-1).ToString("dd/MM/yyyy"),
                    BranchList = branchList.Branches.Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        TypeList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id)?.ApplicantAgeRanges.Select(w => new
                        {
                            Type = EnumHelper.GetEnumDescription(typeof(ReportAgeRanges), w.Type.ToString()),
                            Value = w.Count
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        public async Task<IActionResult> PartialMinistryDailyApplicationTypeStats()
        {
            var branchList = await CacheHelper.GetBranchesAsync();

            var viewModel = new DashboardBranchViewModel()
            {
                Branches = branchList.Branches.Select(q => new DashboardBranchViewModel.BranchViewModel()
                {
                    BranchId = q.Id,
                    BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                        q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                        q.BranchTranslations.FirstOrDefault().Name
                }).ToList().OrderBy(q => q.BranchName)
            };

            return PartialView("_Ministry/_Modals/_DailyApplicationTypeStats", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetModalMinistryDailyApplicationTypeStats()
        {
            var jsonResult = new ResultModel { Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger };

            var branchList = await CacheHelper.GetBranchesAsync();

            var apiResponse = (await CacheHelper.GetPreviousDayGeneralStatsAsync()).Data;

            try
            {
                var model = new
                {
                    Date = DateTime.Today.AddDays(-1).ToString("dd/MM/yyyy"),
                    BranchList = branchList.Branches.Select(q => new
                    {
                        BranchId = q.Id,
                        BranchName = q.BranchTranslations.Any(w => w.LanguageId == LanguageId) ?
                            q.BranchTranslations.First(w => w.LanguageId == LanguageId).Name :
                            q.BranchTranslations.FirstOrDefault().Name,
                        TypeList = apiResponse.BranchList.FirstOrDefault(w => w.BranchId == q.Id)?.ApplicationTypes.Where(e => e.Type == (int)ApplicationType.Turquois || e.Type == (int)ApplicationType.TurquoisPremium || e.Type == (int)ApplicationType.TurquoisGratis).Select(w => new
                        {
                            Type = EnumHelper.GetEnumDescription(typeof(ApplicationType), w.Type.ToString()),
                            Value = w.Count
                        }).ToList()
                    }).ToList().OrderBy(q => q.BranchName)
                };

                jsonResult.Message = ResultMessage.OperationIsSuccessful.ToString();
                jsonResult.Data = model;
                jsonResult.ResultType = ResultType.Success;
            }
            catch { }

            return Json(jsonResult);
        }

        #endregion

        #endregion

        private async Task<ApiResponse<DailyApplicationStatsApiResponse>> GetDailyApplicationsAsync(IList<int> branchIds = null, int? userId = null)
        {
            var apiRequest = new GetStatsApiRequest()
            {
                BranchIds = branchIds,
                UserId = userId,
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<DailyApplicationStatsApiResponse>>
                (apiRequest, ApiMethodName.Dashboard.GetDailyApplicationStats, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            return apiResponse;
        }
    }
}