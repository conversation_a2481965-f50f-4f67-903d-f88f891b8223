﻿using Gateway.Extensions;
using Gateway.Http;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.LineDepartmentConnection.RequestModels;
using Portal.Gateway.UI.Areas.Management.ViewModels.LineDepartmentConnection.Results;
using Portal.Gateway.UI.Areas.Management.ViewModels.LineDepartmentConnection.ViewModels;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class LineDepartmentConnectionController : BaseController<LineDepartmentConnectionController>
    {
        public LineDepartmentConnectionController(IOptions<AppSettings> appSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {
        }

        public IActionResult PartialAddLineDepartmentConnection(int lineDepartmentId)
        {
            if (!lineDepartmentId.IsNumericAndGreaterThenZero())
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var viewModel = new AddLineDepartmentConnectionViewModel
            {
                LineDepartmentId = lineDepartmentId,
            };

            return PartialView("_AddLineDepartmentConnection", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddLineDepartmentConnection(AddLineDepartmentConnectionViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var request = new AddLineDepartmentConnectionRequestModel
            {
                ButtonName = viewModel.ButtonName,
                LineId = viewModel.LineId,
                DepartmentId = viewModel.DepartmentId,
                ShowWaitingButton = viewModel.ShowWaitingButton,
                UserId = UserSession.UserId,
                BranchId = viewModel.BranchId
            };

            var apiResponse = await RestHttpClient.Create().Post<AddLineDepartmentConnectionResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.AddLineDepartmentConnection.Replace("{lineDepartmentId}", viewModel.LineDepartmentId.ToString()), QMSApiDefaultRequestHeaders, request);

            if (apiResponse.Data is null || !apiResponse.Data.IsNumericAndGreaterThenZero())
                return Json(new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #region Update

        public async Task<IActionResult> PartialUpdateLineDepartmentConnection(int lineDepartmentId,int lineDepartmentConnectionId)
        {
            if (!lineDepartmentId.IsNumericAndGreaterThenZero() || !lineDepartmentConnectionId.IsNumericAndGreaterThenZero())
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiResponse = await RestHttpClient.Create().Get<GetLineDepartmentConnectionResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.GetLineDepartmentConnection + lineDepartmentConnectionId, QMSApiDefaultRequestHeaders);

            var viewModel = new UpdateLineDepartmentConnectionViewModel()
            {
                LineDepartmentConnectionId = apiResponse.Data.Id,
                BranchId = apiResponse.Data.ConnectedBranch.Id,
                ButtonName = apiResponse.Data.ButtonName,
                DepartmentId = apiResponse.Data.ConnectedDepartment.Id,
                LineId = apiResponse.Data.ConnectedLine.Id,
                ShowWaitingButton = apiResponse.Data.ShowWaitingButton,
                LineDepartmentId = lineDepartmentId,
            };

            return PartialView("_UpdateLineDepartmentConnection", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateLineDepartmentConnection(UpdateLineDepartmentConnectionViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid) //buraya fluent val yazılıcak viewmodellere
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateLineDepartmentConnectionRequestModel
            {
                UserId = UserSession.UserId,
                ButtonName = viewModel.ButtonName,
                DepartmentId = viewModel.DepartmentId,
                LineId = viewModel.LineId,
                ShowWaitingButton = viewModel.ShowWaitingButton,
                LineDepartmentId = viewModel.LineDepartmentId,
                BranchId = viewModel.BranchId
            };

            var apiResponse = await RestHttpClient.Create().Put<UpdateLineDepartmentConnectionResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.UpdateLineDepartmentConnection.Replace("{lineDepartmentId}",viewModel.LineDepartmentId.ToString()) + viewModel.LineDepartmentConnectionId, QMSApiDefaultRequestHeaders, apiRequest);

            if (apiResponse.Data is null || !apiResponse.Data.IsNumericAndGreaterThenZero())
                return Json(new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> PartialLineDepartmentConnection(int lineDepartmentConnectionId)
        {
            if (!lineDepartmentConnectionId.IsNumericAndGreaterThenZero())
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiResponse = await RestHttpClient.Create().Get<GetLineDepartmentConnectionResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.GetLineDepartmentConnection + lineDepartmentConnectionId, QMSApiDefaultRequestHeaders);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            return PartialView("_LineDepartmentConnection", apiResponse.Data);
        }

        [Action(IsMenuItem = true)]
        public IActionResult List(int lineDepartmentId)
        {
            return View(new LineDepartmentConnectionViewModel
            {
                LineDepartmentId = lineDepartmentId,
            });
        }

        public async Task<IActionResult> GetLineDepartmentConnections([DataSourceRequest] DataSourceRequest request, int lineDepartmentId)
        {
            var apiResponse = await RestHttpClient.Create().Get<GetLineDepartmentConnectionsResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.GetLineDepartmentConnectionsByLineDepartment.Replace("{lineDepartmentId}",lineDepartmentId.ToString()), QMSApiDefaultRequestHeaders);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            return Json(new DataSourceResult { Data = apiResponse.Data });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteLineDepartmentConnection(int lineDepartmentConnectionId)
        {
            if (!lineDepartmentConnectionId.IsNumericAndGreaterThenZero())
                return Content(EnumResources.MissingOrInvalidData);

            var apiResponse = await RestHttpClient.Create().Delete<DeleteLineDepartmentConnectionResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.DeleteLineDepartmentConnection + lineDepartmentConnectionId, QMSApiDefaultRequestHeaders);

            if (apiResponse.Data is null || !apiResponse.Data.IsNumericAndGreaterThenZero())
                return Json(apiResponse.Message);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion
    }
}
