﻿using System.Collections.Generic;

namespace Gateway.External.Application.User.Dto
{
    public class PushNotificationDto
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string CreatedAt { get; set; }
        public string SendAt { get; set; }
        public string Subject { get; set; }
        public string Text { get; set; }
        public int? LocationId { get; set; }
        public int? NationalityId { get; set; }
        public LookupValue Status { get; set; }
        public List<PushNotificationHistoryDto> Histories { get; set; }

    }
}
