﻿using System;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Management.Branch;
using Portal.Gateway.ApiModel.Requests.Management.Inquiry;
using Portal.Gateway.ApiModel.Requests.Management.Slot;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.ApiModel.Responses.Management.Inquiry;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.Branch;
using Portal.Gateway.UI.Areas.Management.ViewModels.Inquiry;
using Portal.Gateway.UI.Areas.Management.ViewModels.Slot;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class InquiryController : BaseController<InquiryController>
    {
        public InquiryController(IOptions<AppSettings> appSettings, ICacheHelper cacheHelper) : base(appSettings, cacheHelper)
        {
        }

        #region Get

        [HttpGet]
        public async Task<IActionResult> List(string encryptedBranchId)
        {
            if (string.IsNullOrEmpty(encryptedBranchId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Branch", new { Area = "Management" });
            }

            var branchId = encryptedBranchId.ToDecryptInt();

            var branchList = await CacheHelper.GetBranchesAsync();

            var inquiryViewModel = new InquiryManagementViewModel()
            {
                EncryptedBranchId = encryptedBranchId,
                BranchName = branchList?.Branches?.FirstOrDefault(q => q.Id == branchId)?.BranchTranslations?.FirstOrDefault(q => q.LanguageId == LanguageId)?.Name
            };

            return View(inquiryViewModel);
        }

        public async Task<IActionResult> GetPaginatedInquiries([DataSourceRequest] DataSourceRequest request, FilterInquiryViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new GetPaginatedInquiriesApiRequest
            {
                BranchId = filterViewModel.EncryptedBranchId.ToDecryptInt(),
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<GetPaginatedInquiriesApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedInquiries, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var data = new List<InquiryManagementDto>();

            data = apiResponse.Data.Items.First().Inquiries
                .Select(p => new InquiryManagementDto
                {
                    EncryptedId = p.Id.ToEncrypt(),
                    Name = p.Name,
                    IsActive = p.IsActive,
                    CreatedAt = p.CreatedAt.ToString("dd/MM/yyyy HH:mm")
                }).ToList();

            return Json(new DataSourceResult { Data = data, Total = apiResponse.Data.TotalItemCount });
        }

        public async Task<IActionResult> GetPaginatedInquiryQuestions(string encryptedInquiryId)
        {
            int id = encryptedInquiryId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<GetInquiryApiResponse>>
                    (ApiMethodName.Management.GetInquiry + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var categories = EnumHelper.GetEnumAsDictionary(typeof(InquiryQuestionChoiceType))
                .Select(x => new InquiryQuestionChoiceTypeViewModel { Id = x.Key, Name = x.Value.ToTitleCase() });

            ViewData["categories"] = categories;
            ViewData["defaultCategory"] = categories.First();

            var viewModel = new UpdateInquiryViewModel()
            {
                EncryptedInquiryId = encryptedInquiryId.ToEncrypt(),
                Questions = apiResponse.Data.Questions.Select(s => new InquiryManagementQuestionViewModel
                {
                    EncryptedQuestionId = s.QuestionId.ToEncrypt(),
                    IsActive = s.IsActive,
                    Colspan = s.Colspan,
                    NameEng = s.Translations.Find(s => s.LanguageId == (int)Language.English) == null ? string.Empty : s.Translations.Find(s => s.LanguageId == (int)Language.English).Name,
                    NameTr = s.Translations.Find(s => s.LanguageId == (int)Language.Turkish) == null ? string.Empty : s.Translations.Find(s => s.LanguageId == (int)Language.Turkish).Name,
                }).ToList()
            };

            return Json(new DataSourceResult { Data = viewModel.Questions, Total = viewModel.Questions.Count });
        }

        #endregion

        #region Add

        public IActionResult PartialAddInquiry(string encryptedBranchId)
        {
            var viewModel = new AddUpdateInquiryViewModel()
            {
                EncryptedBranchId = encryptedBranchId,
                IsActive = false,
                Translations = new List<InquiryTranslationViewModel>()
            };

            foreach (var item in EnumHelper.GetEnumAsDictionary(typeof(InquiryTranslationEnum)))
            {
                viewModel.Translations.Add(new InquiryTranslationViewModel()
                {
                    LanguageId = item.Key,
                    Language = item.Value,
                });
            }

            return PartialView("_AddInquiry", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddInquiry(AddUpdateInquiryViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddInquiryApiRequest
            {
                BranchId = viewModel.EncryptedBranchId.ToDecryptInt(),
                IsActive = viewModel.IsActive,
                Translations = viewModel.Translations.Select(s => new InquiryTranslationDto()
                {
                    Name = s.Name,
                    LanguageId = s.LanguageId
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Management.AddInquiry, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }


        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateInquiryDefinition(string encryptedInquiryId)
        {
            int id = encryptedInquiryId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<GetInquiryApiResponse>>
                    (ApiMethodName.Management.GetInquiry + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var translations = EnumHelper.GetEnumAsDictionary(typeof(InquiryTranslationEnum)).ToList();

            var viewModel = new AddUpdateInquiryViewModel()
            {
                EncryptedInquiryId = encryptedInquiryId,
                IsActive = apiResponse.Data.IsActive,
                Translations = apiResponse.Data.Translation.Select(t => new InquiryTranslationViewModel
                {
                    LanguageId = t.LanguageId,
                    Language = translations.Find(s => s.Key == t.LanguageId).Value,
                    Name = t.Name
                }).ToList()
            };


            return PartialView("_UpdateInquiryManagement", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateInquiry(AddUpdateInquiryViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateInquiryApiRequest
            {
                InquiryId = viewModel.EncryptedInquiryId.ToDecryptInt(),
                BranchId = viewModel.EncryptedBranchId.ToDecryptInt(),
                IsActive = viewModel.IsActive,
                Translations = viewModel.Translations.Select(s => new InquiryTranslationDto()
                {
                    Name = s.Name,
                    LanguageId = s.LanguageId
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                    (apiRequest, ApiMethodName.Management.UpdateInquiry, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        public async Task<IActionResult> PartialUpdateInquiry(string encryptedInquiryId)
        {
            var categories = EnumHelper.GetEnumAsDictionary(typeof(InquiryQuestionChoiceType))
                .Select(x => new InquiryQuestionChoiceTypeViewModel { Id = x.Key, Name = x.Value.ToTitleCase() });

            ViewData["categories"] = categories;
            ViewData["defaultCategory"] = categories.First();

            var viewModel = new UpdateInquiryViewModel()
            {
                EncryptedInquiryId = encryptedInquiryId,
            };

            return PartialView("_UpdateInquiry", viewModel);
        }

        #endregion

        #region Order

        public async Task<IActionResult> PartialUpdateInquiryQuestionOrder(string encryptedInquiryId)
        {
            int id = encryptedInquiryId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<GetInquiryApiResponse>>
                    (ApiMethodName.Management.GetInquiry + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = apiResponse.Data.Questions.Select(s => new InquiryManagementQuestionViewModel
            {
                EncryptedQuestionId = s.QuestionId.ToEncrypt(),
                IsActive = s.IsActive,
                Colspan = s.Colspan,
                NameEng = s.Translations.Find(s => s.LanguageId == (int)Language.English) == null ? string.Empty : s.Translations.Find(s => s.LanguageId == (int)Language.English).Name,
                NameTr = s.Translations.Find(s => s.LanguageId == (int)Language.Turkish) == null ? string.Empty : s.Translations.Find(s => s.LanguageId == (int)Language.Turkish).Name,
            }).ToList();

            return PartialView("_UpdateInquiryOrder", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateInquiryOrder([FromBody] List<InquiryManagementQuestionViewModel> questions)
        {
            if (questions == null || !questions.Any())
                return Content(EnumResources.MissingOrInvalidData);

            var index = 1;

            var apiRequest = new UpdateInquiryOrderApiRequest
            {
                Questions = questions.Select(r => new UpdateInquiryOrderApiRequest.InquiryQuestionOrderDto()
                {
                    Id = r.EncryptedQuestionId.ToDecryptInt(),
                    Order = index++
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                    (apiRequest,ApiMethodName.Management.UpdateInquiryOrder, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);
            return Json(apiResponse.Data == null ? new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Danger } : new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Preview

        public async Task<IActionResult> PreviewInquiry(string encryptedInquiryId)
        {
            int id = encryptedInquiryId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<GetInquiryApiResponse>>
                    (ApiMethodName.Management.GetInquiry + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel =  new PreviewInquiryViewModel()
            {
                EncryptedInquiryId = encryptedInquiryId,
                InquiryName = apiResponse.Data.InquiryName,
                Observation = string.Empty,
                Questions = apiResponse.Data.Questions.Select(s => new PreviewInquiryManagementQuestionDto
                {
                    EncryptedQuestionId =s.QuestionId.ToEncrypt(),
                    Name = s.Translations.FirstOrDefault(s => s.LanguageId == LanguageId) != null ?
                        s.Translations.FirstOrDefault(s => s.LanguageId == LanguageId).Name
                        : s.Translations.FirstOrDefault().Name,
                    IsActive = s.IsActive,
                    Colspan = s.Colspan,
                    Order = s.Order,
                    QuestionType = s.QuestionType,
                    Choices = s.Choices.Select(c => new PreviewInquiryManagementChoiceDto
                    {
                        EncryptedChoiceId = c.ChoiceId.ToEncrypt(),
                        Name = c.Translations.FirstOrDefault(s => s.LanguageId == LanguageId) != null ?
                            c.Translations.FirstOrDefault(s => s.LanguageId == LanguageId).Name
                            : c.Translations.FirstOrDefault().Name,
                        Colspan = c.Colspan,
                        IsActive = c.IsActive,
                        ChoiceTypeId = c.ChoiceTypeId,
                    }).ToList()
                }).ToList()
            };

            return PartialView("_Preview", viewModel);
        }

        #endregion

        #region Grid Operations

        #region Inquiry Question

        [HttpPost]
        public async Task<IActionResult> AddInquiryQuestion([DataSourceRequest] DataSourceRequest request,
            [Bind(Prefix = "models")] IEnumerable<InquiryManagementQuestionViewModel> questions, string encryptedInquiryId)
        {
            if (questions != null && ModelState.IsValid)
            {
                var apiRequest = new AddUpdateInquiryQuestionApiRequest()
                {
                    IsUpdateOperation = false,
                    Questions = questions.Select(q => new InquiryQuestionGridOperationDto()
                    {
                        InquiryId = encryptedInquiryId.ToDecryptInt(),
                        IsActive = q.IsActive,
                        Colspan = q.Colspan,
                        Translations = new List<InquiryTranslationDto>
                       {
                           new InquiryTranslationDto()
                           {
                               Name = q.NameEng,
                               LanguageId = (int)Language.English
                           },
                           new InquiryTranslationDto()
                           {
                               Name = q.NameTr,
                               LanguageId = (int)Language.Turkish
                           }
                       },
                    }).ToList()
                };

                var apiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                        (apiRequest, ApiMethodName.Management.AddUpdateInquiryQuestion, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                {
                    Response.StatusCode = 400;
                    return Json(result);
                }
            }

            return Json(await questions.ToDataSourceResultAsync(request, ModelState));
        }

        [HttpPost]
        public async Task<IActionResult> UpdateInquiryQuestion([DataSourceRequest] DataSourceRequest request,
            [Bind(Prefix = "models")] IEnumerable<InquiryManagementQuestionViewModel> questions, string encryptedInquiryId)
        {
            if (questions != null && ModelState.IsValid)
            {
                var apiRequest = new AddUpdateInquiryQuestionApiRequest()
                {
                    IsUpdateOperation = true,
                    Questions = questions.Select(q => new InquiryQuestionGridOperationDto()
                    {
                        QuestionId = q.EncryptedQuestionId.ToDecryptInt(),
                        InquiryId = encryptedInquiryId.ToDecryptInt(),
                        Colspan = q.Colspan,
                        IsActive = q.IsActive,
                        Translations = new List<InquiryTranslationDto>
                        {
                            new InquiryTranslationDto()
                            {
                                Name = q.NameEng,
                                LanguageId = (int)Language.English
                            },
                            new InquiryTranslationDto()
                            {
                                Name = q.NameTr,
                                LanguageId = (int)Language.Turkish
                            }
                        },
                    }).ToList()
                };

                var apiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                        (apiRequest, ApiMethodName.Management.AddUpdateInquiryQuestion, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                {
                    Response.StatusCode = 400;
                    return Json(result);
                }
            }

            return Json(await questions.ToDataSourceResultAsync(request, ModelState));
        }

        [HttpPost]
        public async Task<IActionResult> DeleteInquiryQuestion([DataSourceRequest] DataSourceRequest request,
            [Bind(Prefix = "models")] IEnumerable<InquiryManagementQuestionViewModel> questions)
        {
            if (questions != null && ModelState.IsValid)
            {
                var question = questions.First();
                var apiRequest = new DeleteInquiryQuestionApiRequest()
                {
                    InquiryQuestionId = question.EncryptedQuestionId.ToDecryptInt()
                };

                var apiResponse = await PortalHttpClientHelper
                    .DeleteAsync<ApiResponse<DeleteApiResponse>>
                        (ApiMethodName.Management.DeleteInquiryQuestion + apiRequest.InquiryQuestionId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                {
                    Response.StatusCode = 400;
                    return Json(result);
                }

                if (!apiResponse.Data.Result)
                {
                    string alreadyExist = SiteResources.AlreadyExistDataMessage;

                    foreach (var questionData in questions)
                    {
                        questionData.CheckAlreadyExist = alreadyExist;
                    }

                    return Json(new { CheckAlreadyExist = alreadyExist });
                }
            }

            return Json(await questions.ToDataSourceResultAsync(request, ModelState));
        }

        #endregion

        #region Inquiry Question Choice

        public IActionResult GetInquiryQuestionChoiceType()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(InquiryQuestionChoiceType)).Select(x => new { Id = x.Key.ToString(), Name = x.Value.ToTitleCase() }));
        }

        public async Task<IActionResult> GetInquiryQuestionChoices([DataSourceRequest] DataSourceRequest request, string encryptedQuestionId)
        {
            var id = encryptedQuestionId.ToDecryptInt();

            if(id == 0)
                return Json(new DataSourceResult { Data = new List<InquiryManagementChoiceViewModel>() , Total = 0 });

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<GetInquiryQuestionChoicesApiResponse>>
                (ApiMethodName.Management.GetInquiryQuestionChoices + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            var categories = EnumHelper.GetEnumAsDictionary(typeof(InquiryQuestionChoiceType))
                .Select(x => new InquiryQuestionChoiceTypeViewModel { Id = x.Key, Name = x.Value.ToTitleCase() });

            ViewData["categories"] = categories;
            ViewData["defaultCategory"] = categories.First();

            var dataList = new List<InquiryManagementChoiceViewModel>();

            if (await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                dataList = apiResponse.Data.Choices.Select(s => new InquiryManagementChoiceViewModel()
                {
                    EncryptedChoiceId = s.ChoiceId.ToEncrypt(),
                    EncryptedQuestionId = encryptedQuestionId,
                    IsActive = s.IsActive,
                    Colspan = s.Colspan,
                    ChoiceType = new InquiryQuestionChoiceTypeViewModel()
                    {
                        Id = s.ChoiceTypeId,
                        Name = categories.First(c => c.Id == s.ChoiceTypeId).Name
                    },
                    ChoiceTypeId = s.ChoiceTypeId,
                    SelectionButton = s.ChoiceTypeId == 3 ? $"<div class=\"btn-group\">\n<button class=\"btn btn-primary font-weight-bold\" data-toggle=\"modal\" data-target=\"#modalUpdateChoiceSelection\" onclick=\"updateChoiceSelection('{s.ChoiceId.ToEncrypt()}')\">\nC\n</button>\n</div>" : "<span> </span>",
                    NameEng = s.Translations.Find(s => s.LanguageId == (int)Language.English) == null ? string.Empty : s.Translations.Find(s => s.LanguageId == (int)Language.English).Name,
                    NameTr = s.Translations.Find(s => s.LanguageId == (int)Language.Turkish) == null ? string.Empty : s.Translations.Find(s => s.LanguageId == (int)Language.Turkish).Name,
                }).ToList();
            }

            return Json(new DataSourceResult { Data = dataList, Total = dataList.Count });
        }

        [HttpPost]
        public async Task<IActionResult> AddInquiryQuestionChoice([DataSourceRequest] DataSourceRequest request,
            [Bind(Prefix = "models")] IEnumerable<InquiryManagementChoiceViewModel> choices, string encryptedQuestionId)
        {
            if (choices != null && ModelState.IsValid)
            {
                var apiRequest = new AddUpdateInquiryQuestionChoiceApiRequest()
                {
                    IsUpdateOperation = false,
                    Choices = choices.Select(q => new InquiryQuestionChoiceGridOperationDto()
                    {
                        ChoiceId = q.EncryptedChoiceId.ToDecryptInt(),
                        InquiryQuestionId = encryptedQuestionId.ToDecryptInt(),
                        ChoiceType = q.ChoiceType.Id,
                        Colspan = q.Colspan,
                        IsActive = q.IsActive,
                        Translations = new List<InquiryTranslationDto>
                        {
                            new InquiryTranslationDto()
                            {
                                Name = q.NameEng,
                                LanguageId = (int)Language.English
                            },
                            new InquiryTranslationDto()
                            {
                                Name = q.NameTr,
                                LanguageId = (int)Language.Turkish
                            }
                        },
                    }).ToList()
                };

                var apiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                        (apiRequest, ApiMethodName.Management.AddUpdateInquiryQuestionChoice, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                {
                    Response.StatusCode = 400;
                    return Json(result);
                }
            }

            return Json(await choices.ToDataSourceResultAsync(request, ModelState));
        }


        [HttpPost]
        public async Task<IActionResult> UpdateInquiryQuestionChoice([DataSourceRequest] DataSourceRequest request,
           [Bind(Prefix = "models")] IEnumerable<InquiryManagementChoiceViewModel> choices,string encryptedQuestionId)
        {
            if (choices != null && ModelState.IsValid)
            {
                var apiRequest = new AddUpdateInquiryQuestionChoiceApiRequest()
                {
                    IsUpdateOperation = true,
                    Choices = choices.Select(q => new InquiryQuestionChoiceGridOperationDto()
                    {
                        ChoiceId = q.EncryptedChoiceId.ToDecryptInt(),
                        InquiryQuestionId = encryptedQuestionId.ToDecryptInt(),
                        ChoiceType = q.ChoiceType.Id,
                        Colspan = q.Colspan,
                        IsActive = q.IsActive,
                        Translations = new List<InquiryTranslationDto>
                        {
                            new InquiryTranslationDto()
                            {
                                Name = q.NameEng,
                                LanguageId = (int)Language.English
                            },
                            new InquiryTranslationDto()
                            {
                                Name = q.NameTr,
                                LanguageId = (int)Language.Turkish
                            }
                        },
                    }).ToList()
                };

                var apiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                        (apiRequest, ApiMethodName.Management.AddUpdateInquiryQuestionChoice, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                {
                    Response.StatusCode = 400;
                    return Json(result);
                }
            }

            return Json(await choices.ToDataSourceResultAsync(request, ModelState));
        }

        [HttpPost]
        public async Task<IActionResult> DeleteInquiryQuestionChoice([DataSourceRequest] DataSourceRequest request,
            [Bind(Prefix = "models")] IEnumerable<InquiryManagementChoiceViewModel> questions, string encryptedQuestionId)
        {
            if (questions != null && ModelState.IsValid)
            {
                var question = questions.First();
                var apiRequest = new DeleteInquiryQuestionChoiceApiRequest()
                {
                    InquiryQuestionChoiceId = question.EncryptedChoiceId.ToDecryptInt()
                };

                var apiResponse = await PortalHttpClientHelper
                    .DeleteAsync<ApiResponse<DeleteApiResponse>>
                        (ApiMethodName.Management.DeleteInquiryQuestionChoice + apiRequest.InquiryQuestionChoiceId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                {
                    Response.StatusCode = 400;
                    return Json(result);
                }

                if (!apiResponse.Data.Result)
                {
                    string alreadyExist = SiteResources.AlreadyExistDataMessage;

                    foreach (var questionData in questions)
                    {
                        questionData.CheckAlreadyExist = alreadyExist;
                    }

                    return Json(new { CheckAlreadyExist = alreadyExist });
                }
            }

            return Json(await questions.ToDataSourceResultAsync(request, ModelState));
        }

        #endregion

        #region Inquiry Choice Selection

        public async Task<IActionResult> PartialUpdateInquiryChoiceSelection(string encryptedChoiceId)
        {
            var viewModel = new InquiryQuestionChoiceSelectionViewModel
            {
                EncryptedChoiceId = encryptedChoiceId
            };

            return PartialView("_UpdateInquiryChoiceSelection", viewModel);
        }

        public async Task<IActionResult> GetInquiryQuestionChoiceSelection([DataSourceRequest] DataSourceRequest request, string encryptedChoiceId)
        {
            var id = encryptedChoiceId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<GetInquiryChoiceSelectionApiResponse>>
                (ApiMethodName.Management.GetInquiryChoiceSelection + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            var dataList = new List<InquiryManagementChoiceSelectionViewModel>();

            if (await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                dataList = apiResponse.Data.Selections.Select(s => new InquiryManagementChoiceSelectionViewModel()
                {
                    EncryptedChoiceId = encryptedChoiceId,
                    EncryptedSelectionId = s.SelectionId.ToEncrypt(),
                    IsActive = s.IsActive,
                    NameEng = s.Translations.Find(s => s.LanguageId == (int)Language.English) == null ? string.Empty : s.Translations.Find(s => s.LanguageId == (int)Language.English).Name,
                    NameTr = s.Translations.Find(s => s.LanguageId == (int)Language.Turkish) == null ? string.Empty : s.Translations.Find(s => s.LanguageId == (int)Language.Turkish).Name,
                }).ToList();
            }

            return Json(new DataSourceResult { Data = dataList, Total = dataList.Count });
        }

        public async Task<IActionResult> GetInquiryQuestionChoiceSelectionSelectList([DataSourceRequest] DataSourceRequest request, string encryptedChoiceId)
        {
            var id = encryptedChoiceId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<GetInquiryChoiceSelectionApiResponse>>
                    (ApiMethodName.Management.GetInquiryChoiceSelection + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            var dataList = new List<SelectListItem>();

            if (await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                dataList = apiResponse.Data.Selections.Select(s => new SelectListItem()
                {
                    Value = s.SelectionId.ToString(),
                    Text = s.Translations.Find(s => s.LanguageId == LanguageId) == null ?
                        s.Translations.First().Name : s.Translations.Find(s => s.LanguageId == LanguageId).Name,
                }).ToList();
            }

            return Json(dataList);
        }

        [HttpPost]
        public async Task<IActionResult> AddInquiryQuestionChoiceSelection([DataSourceRequest] DataSourceRequest request,
            [Bind(Prefix = "models")] IEnumerable<InquiryManagementChoiceSelectionViewModel> selection, string encryptedChoiceId)
        {
            if (selection != null && ModelState.IsValid)
            {
                var apiRequest = new AddUpdateInquiryQuestionChoiceSelectionApiRequest()
                {
                    IsUpdateOperation = false,
                    Selections = selection.Select(q => new InquiryQuestionChoiceSelectionGridOperationDto()
                    {
                        ChoiceId = encryptedChoiceId.ToDecryptInt(),
                        SelectionId = q.EncryptedSelectionId.ToDecryptInt(),
                        IsActive = q.IsActive,
                        Translations = new List<InquiryTranslationDto>
                        {
                            new InquiryTranslationDto()
                            {
                                Name = q.NameEng,
                                LanguageId = (int)Language.English
                            },
                            new InquiryTranslationDto()
                            {
                                Name = q.NameTr,
                                LanguageId = (int)Language.Turkish
                            }
                        },
                    }).ToList()
                };

                var apiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                        (apiRequest, ApiMethodName.Management.AddUpdateInquiryQuestionChoiceSelection, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                {
                    Response.StatusCode = 400;
                    return Json(result);
                }
            }

            return Json(await selection.ToDataSourceResultAsync(request, ModelState));
        }


        [HttpPost]
        public async Task<IActionResult> UpdateInquiryQuestionChoiceSelection([DataSourceRequest] DataSourceRequest request,
           [Bind(Prefix = "models")] IEnumerable<InquiryManagementChoiceSelectionViewModel> selection, string encryptedChoiceId)
        {
            if (selection != null && ModelState.IsValid)
            {
                var apiRequest = new AddUpdateInquiryQuestionChoiceSelectionApiRequest()
                {
                    IsUpdateOperation = true,
                    Selections = selection.Select(q => new InquiryQuestionChoiceSelectionGridOperationDto()
                    {
                        ChoiceId = encryptedChoiceId.ToDecryptInt(),
                        SelectionId = q.EncryptedSelectionId.ToDecryptInt(),
                        IsActive = q.IsActive,
                        Translations = new List<InquiryTranslationDto>
                        {
                            new InquiryTranslationDto()
                            {
                                Name = q.NameEng,
                                LanguageId = (int)Language.English
                            },
                            new InquiryTranslationDto()
                            {
                                Name = q.NameTr,
                                LanguageId = (int)Language.Turkish
                            }
                        },
                    }).ToList()
                };

                var apiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                        (apiRequest, ApiMethodName.Management.AddUpdateInquiryQuestionChoiceSelection, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                {
                    Response.StatusCode = 400;
                    return Json(result);
                }
            }

            return Json(await selection.ToDataSourceResultAsync(request, ModelState));
        }

        [HttpPost]
        public async Task<IActionResult> DeleteInquiryQuestionChoiceSelection([DataSourceRequest] DataSourceRequest request,
            [Bind(Prefix = "models")] IEnumerable<InquiryManagementChoiceSelectionViewModel> selection, string encryptedChoiceId)
        {
            if (selection != null && ModelState.IsValid)
            {
                var question = selection.First();
                var apiRequest = new DeleteInquiryQuestionChoiceSelectionApiRequest()
                {
                    InquiryQuestionChoiceSelectionId  = question.EncryptedSelectionId.ToDecryptInt()
                };

                var apiResponse = await PortalHttpClientHelper
                    .DeleteAsync<ApiResponse<DeleteApiResponse>>
                        (ApiMethodName.Management.DeleteInquiryQuestionChoiceSelection + apiRequest.InquiryQuestionChoiceSelectionId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                {
                    Response.StatusCode = 400;
                    return Json(result);
                }

                if (!apiResponse.Data.Result)
                {
                    string alreadyExist = SiteResources.AlreadyExistDataMessage;

                    foreach (var questionData in selection)
                    {
                        questionData.CheckAlreadyExist = alreadyExist;
                    }

                    return Json(new { CheckAlreadyExist = alreadyExist });
                }
            }

            return Json(await selection.ToDataSourceResultAsync(request, ModelState));
        }


        #endregion


        #endregion
    }
}
