﻿@using Microsoft.AspNetCore.Builder
@using Microsoft.Extensions.Caching.Memory
@using Microsoft.Extensions.Options
@using Microsoft.AspNetCore.Http
@using Portal.Gateway.ApiModel.Responses.General
@using Portal.Gateway.UI.Constants
@inject IHttpContextAccessor HttpContextAccessor
@inject IOptions<RequestLocalizationOptions> RequestLocalizationOptions
@inject ICacheHelper cacheHelper
@{
    var currentUser = Portal.Gateway.UI.Extensions.SessionExtensions.Get<UserModel>(HttpContextAccessor.HttpContext.Session, SessionKeys.UserSession);
}

<style>
    .active-department {
    color: #fff !important;
    background-color: #663259 !important;
    border-color: #663259 !important;
    }

    .active-department .svg-icon svg g [fill] {
    fill: #fff !important;
    }
</style>

<div id="kt_quick_panel" class="offcanvas offcanvas-left p-10 quick-department" style="overflow-y: scroll;">
    <div class="offcanvas-header d-flex align-items-center justify-content-between pb-10">
        <h3 class="font-weight-bold m-0">
            @SiteResources.DepartmentCounterSelection.ToTitleCase()
        </h3>
        <a href="#" class="btn btn-xs btn-icon btn-light btn-hover-primary" id="kt_quick_actions_close">
            <i class="ki ki-close icon-xs text-muted"></i>
        </a>
    </div>
    <div class="offcanvas-content pr-5 mr-n5">
        <div class="form-group row mb-10 pr-5 pl-5">
            <label class="col-6 col-form-label" for="counter">@SiteResources.CounterNumber.ToTitleCase()</label>
            <select class="col-6 form-control" id="counter">
                <option value="0">@SiteResources.Select</option>
                @for (int i = 1; i <= 100; i++)
                {
                    if (currentUser.CounterId != null && i == currentUser.CounterId.Value)
                    {
                        <option value="@i" selected>@i</option>
                    }
                    else
                    {
                        <option value="@i">@i</option>
                    }
                }
            </select>
        </div>

        <div id="divDepartments" class="row gutter-b">

            @{
                if (currentUser.DepartmentIds != null)
                {
                    var currentDepartmentId = currentUser.DepartmentId;
                    var departmentList = new Dictionary<int, string>();
                    var departments = await cacheHelper.GetDepartmentsDictionaryAsync();

                    foreach (var item in currentUser.DepartmentIds)
                    {
                        var departmentName = "";
                        var department = departments[item];

                        if (department.Names.Any(p => p.Key == Html.CurrentLanguageId().ToString().ToInt()))
                            departmentName = department.Names.First(p => p.Key == Html.CurrentLanguageId().ToString().ToInt()).Value;
                        else
                            departmentName = department.Names.First().Value;

                        departmentList.Add(item, departmentName);
                    }
                    foreach (var item in departmentList.OrderBy(o => o.Value))
                    {
    <div class="col-12 mb-10">
        <a onclick="setDepartment('@(item.Key.ToEncrypt())');" class="btn btn-block btn-light btn-hover-primary text-dark-50 text-center py-5 px-5 @(currentDepartmentId == item.Key ? "active-department" : "")">
            <span class="d-block font-weight-bold font-size-h6 mt-2">@item.Value.ToTitleCase()</span>
        </a>
    </div> }
} }

        </div>
    </div>
</div>

<script>

    $(function () {
        checkDepartmentsVisibility();
    });

    $('#counter').change(function () {
        checkDepartmentsVisibility();
    });

    function checkDepartmentsVisibility() {
        $('#divDepartments').hide();
        var counter = $("#counter").val();
        if (counter > 0) {
            $('#divDepartments').show();
        }
    }

</script>