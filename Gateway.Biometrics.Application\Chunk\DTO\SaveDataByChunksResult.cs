﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.Chunk.DTO
{

    public class SaveDataByChunksResult : BaseServiceResult<SaveDataByChunksStatus>
    {
        public int RecordId { get; set; }
        public string UniqueId { get; set; }
        public int Part { get; set; }
        public int TotalPart { get; set; }        
        public bool FromOffline { get; set; }
    }

    public enum SaveDataByChunksStatus
    {
        Successful,
        ResourceExists,
        InvalidInput,
        ResourceNotFound,
        InternalError
    }
}
