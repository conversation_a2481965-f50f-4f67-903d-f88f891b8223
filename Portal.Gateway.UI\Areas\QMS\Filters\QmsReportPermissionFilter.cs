﻿using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Caching.Memory;
using Portal.Gateway.ApiModel.Responses.Management.RoleAction;
using Portal.Gateway.UI.Areas.QMS.Controllers;
using Portal.Gateway.UI.Areas.QMS.Models;
using Portal.Gateway.UI.Areas.QMS.ViewModels;
using Portal.Gateway.UI.Areas.QMS.ViewModels.Reports;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Models;
using System;
using System.Linq;
using Portal.Gateway.UI.Extensions;
using System.Collections.Generic;
using Portal.Gateway.UI.Helpers;

namespace Portal.Gateway.UI.Areas.QMS.Filters;

[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
public class QmsReportPermissionFilter : ActionFilterAttribute
{
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        if (ShouldSkipPermissionCheck(context))
        {
            base.OnActionExecuting(context);
            return;
        }

        if (context.Controller is QmsReportController controller)
        {
            var (startDate, endDate) = ExtractDateRangeFromArguments(context.ActionArguments.Values);

            if (!HasPermissionToViewReport(controller, startDate, endDate))
            {
                if (context.ActionArguments.Values.FirstOrDefault(a => a is DataSourceRequest) is DataSourceRequest request)
                {
                    context.Result = controller.Json(Enumerable.Empty<object>().ToDataSourceResult(request));
                }
            }
        }

        base.OnActionExecuting(context);
    }

    private bool ShouldSkipPermissionCheck(ActionExecutingContext context)
    {
        if (context.ActionDescriptor is not ControllerActionDescriptor actionDescriptor)
            return false;

        dynamic actionAttribute = actionDescriptor.MethodInfo
            .GetCustomAttributes(inherit: true)
            .FirstOrDefault(attr => attr.GetType().Name == "ActionAttribute");

        // Skip permission check for menu items
        return actionAttribute != null && actionAttribute.IsMenuItem;
    }

    private (DateTime? startDate, DateTime? endDate) ExtractDateRangeFromArguments(ICollection<object> values)
    {
        foreach (var arg in values)
        {
            switch (arg)
            {
                case FilterQmsReportViewModel f:
                    return (f.FilterStartDate, f.FilterEndDate);
                case FilterTokenReportViewModel f:
                    return (f.FilterStartDate, f.FilterEndDate);
                case FilterQmsAllReportsViewModel f:
                    return (f.FilterStartDate, f.FilterEndDate);
            }
        }
        return (null, null);
    }

    #region Permission Validation Methods
    private bool HasPermissionToViewReport(Controller controller, DateTime? startDate, DateTime? endDate)
    {
        var userSession = controller.HttpContext.Session.Get<UserModel>(SessionKeys.UserSession);
        if (userSession == null) return false;

        // Check permission
        var hasPermission = HasCurrentDayReportPermission(controller, userSession);

        if (!hasPermission || userSession.IsSysAdmin)
        {
            return true;
        }

        var isTodayReport = IsTodayReport(startDate, endDate);
        return isTodayReport;
    }

    private bool IsTodayReport(DateTime? startDate, DateTime? endDate)
    {
        return startDate?.Date == DateTime.Today &&
               endDate?.Date == DateTime.Today;
    }

    private bool HasCurrentDayReportPermission(Controller controller, UserModel userSession)
    {
        if (!(controller.HttpContext.RequestServices.GetService(typeof(ICacheHelper)) is ICacheHelper cacheHelper))
            return false;

        var roleActions = cacheHelper.GetRoleActionsAsync().GetAwaiter().GetResult();

        return roleActions.RoleActionSites
            .Where(site => userSession.RoleIds.Contains(site.Role.Id))
            .SelectMany(site => site.RoleActions)
            .Any(action =>
                action.Action.Method == ActionMethods.ShowCurrentDayQmsReports &&
                action.Action.IsActive);
    }
    #endregion
}