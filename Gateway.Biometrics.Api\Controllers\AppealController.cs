﻿using AutoMapper;
using Gateway.Biometrics.Api.Models;
using Gateway.Biometrics.Api.Models.Appeal;
using Gateway.Biometrics.Api.Models.Chunk;
using Gateway.Biometrics.Api.Models.DemographicInformation;
using Gateway.Biometrics.Application.Appeal;
using Gateway.Biometrics.Application.Appeal.DTO;
using Gateway.Biometrics.Application.Chunk.DTO;
using Gateway.Biometrics.Application.DemographicInformation.DTO;
using Gateway.Biometrics.Core.Context;
using Gateway.Biometrics.Resources;
using Gateway.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Api.Controllers
{
    [Authorize]
    [Route("api")]
    [ApiController]
    public class AppealController : Controller
    {
        private readonly IAppealService _appealService;
        private readonly IContext _context;
        private readonly IMapper _mapper;

        #region ctor

        public AppealController(IAppealService appealService,
            IContext context,
            IMapper mapper)
        {
            _appealService = appealService;
            _context = context;
            _mapper = mapper;
        }
        #endregion


        #region Public Methods

        /// <summary>
        /// Creates a new appeal
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Creates a new appeal",
            Description = "Creates a new appeal")]
        [HttpPost]
        [Route("appeals/create")]
        public async Task<IActionResult> CreateAppeal(CreateAppealRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<CreateAppealRequest>(request);
            serviceRequest.Context = _context;

            var result = await _appealService.CreateAppeal(serviceRequest);

            return AppealResponseFactory.CreateAppealResponse(result);
        }

        /// <summary>
        /// Inserts a new appeal with metadata
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Inserts a new appeal with metadata",
            Description = "Inserts a new appeal with metadata")]
        [HttpPost]
        [Route("appeals/insertWithMetaData")]
        public async Task<IActionResult> InsertAppealWithMetaData(InsertAppealWithMetaDataRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<InsertAppealWithMetaDataRequest>(request);
            serviceRequest.Context = _context;

            var result = await _appealService.InsertAppealWithMetaData(serviceRequest);

            return AppealResponseFactory.InsertAppealWithMetaDataResponse(result);
        }

        /// <summary>
        /// Inserts a new appeal with metadata
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Inserts a new appeal with metadata from offline data",
            Description = "Inserts a new appeal with metadata from offline data")]
        [HttpPost]
        [Route("appeals/insertWithMetaDataFromOffline")]
        public async Task<IActionResult> InsertAppealWithMetaDataFromOffline(InsertAppealWithMetaDataFromOfflineRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<InsertAppealWithMetaDataFromOfflineRequest>(request);
            serviceRequest.Context = _context;

            var result = await _appealService.InsertAppealWithMetaDataFromOffline(serviceRequest);

            return AppealResponseFactory.InsertAppealWithMetaDataFromOfflineResponse(result);
        }

        /// <summary>
        /// Inserts a new appeal with metadata entity only
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Inserts a new appeal with metadata entity only",
            Description = "Inserts a new appeal with metadata entity only")]
        [HttpPost]
        [Route("appeals/insertWithMetaDataFast")]
        public async Task<IActionResult> InsertAppealWithMetaDataFast(InsertAppealWithMetaDataFastRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<InsertAppealWithMetaDataFastRequest>(request);
            serviceRequest.Context = _context;

            var result = await _appealService.InsertAppealWithMetaDataFast(serviceRequest);

            return AppealResponseFactory.InsertAppealWithMetaDataFastResponse(result);
        }


        /// <summary>
        /// Save full data of metadata entity
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Save full data of metadata entity",
            Description = "Save full data of metadata entity")]
        [HttpPost]
        [Route("appeals/saveAppealMetaDataFull")]
        public async Task<IActionResult> SaveAppealMetaDataFull(SaveAppealMetaDataFullRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<SaveAppealMetaDataFullRequest>(request);
            serviceRequest.Context = _context;

            var result = await _appealService.SaveAppealMetaDataFull(serviceRequest);

            return AppealResponseFactory.SaveAppealMetaDataFullResponse(result);
        }


        /// <summary>
        /// Save metadata entity by chunks
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Save metadata entity by chunks",
            Description = "Save metadata entity by chunks")]
        [HttpPost]
        [Route("appeals/saveAppealMetaDataByChunks")]
        public async Task<IActionResult> SaveAppealMetaDataByChunks(SaveDataByChunksRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<SaveDataByChunksRequest>(request);
            serviceRequest.Context = _context;

            var result = await _appealService.SaveAppealMetaDataByChunks(serviceRequest);

            return AppealResponseFactory.SaveAppealMetaDataByChunksResponse(result);
        }

        /// <summary>
        /// Insert appeal and metadata by chunks from offline
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Insert appeal with metadata by chunks from offline",
            Description = "Insert appeal with metadata by chunks from offline")]
        [HttpPost]
        [Route("appeals/insertAppealWithMetaDataByChunksFromOffline")]
        public async Task<IActionResult> InsertAppealWithMetaDataByChunksFromOffline(SaveDataByChunksRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<SaveDataByChunksRequest>(request);
            serviceRequest.Context = _context;

            var result = await _appealService.InsertAppealWithMetaDataByChunksFromOffline(serviceRequest);

            return AppealResponseFactory.SaveAppealMetaDataByChunksResponse(result);
        }



        /// <summary>
        /// Inserts a child Appeal Meta Data
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [SwaggerOperation(Summary = "Inserts a new child appeal metadata",
        Description = "Inserts a new child appeal metadata")]
        [HttpPost]
        [Route("appeals/insertAppealMetaData")]
        public async Task<IActionResult> InsertAppealMetaData(InsertAppealMetaDataRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<InsertAppealMetaDataRequest>(request);
            serviceRequest.Context = _context;

            var result = await _appealService.InsertAppealMetaData(serviceRequest);

            return AppealResponseFactory.InsertAppealMetaDataResponse(result);
        }

        /// <summary>
        /// Gets Appeal list  by passport number and country
        /// </summary>
        /// <param name="passportNumber"></param>  
        /// <param name="countryId"></param>  
        [SwaggerOperation(Summary = "Gets Appeal list  by passport number and country",
            Description = "Gets Appeal list  by passport number and country")]
        [HttpGet]
        [Route("appeals/getListByPassportAndCountry/{passportNumber}/{countryId}")]
        public async Task<IActionResult> GetAppealsByPassportAndCountry(string passportNumber, string countryId)
        {
            if (passportNumber.IsNullOrWhitespace() || countryId.IsNullOrWhitespace())
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = new GetAppealsByPassportAndCountryRequest
            {
                PassportNumber = passportNumber,
                CountryId = Int32.Parse(countryId),
                Context = _context
            };

            var result = await _appealService.GetAppealsByPassportAndCountry(serviceRequest);

            return AppealResponseFactory.GetAppealsByPassportAndCountryResponse(result);
        }

        [SwaggerOperation(Summary = "Gets Appeal list  by xml",
            Description = "Gets Appeal list  by xml")]
        [HttpPost]
        [Route("appeals/getListByXml")]
        public async Task<IActionResult> GetAppealsByXml(GetAppealsByXmlRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetAppealsByXmlRequest>(request);
            serviceRequest.Context = _context;

            var result = await _appealService.GetAppealsByXml(serviceRequest);

            return AppealResponseFactory.GetAppealsByXmlResponse(result);
        }

        /// <summary>
        /// Gets full Appeal Metadata  by Id
        /// </summary>
        /// <param name="passportNumber"></param>  
        /// <param name="countryId"></param>  
        [SwaggerOperation(Summary = "Gets full Appeal Metadata  by Id",
            Description = "Gets full Appeal Metadata  by Id")]
        [HttpGet]
        [Route("appeals/getFullAppealMetaDataById/{appealMetaDataId}")]
        public async Task<IActionResult> GetFullAppealMetaDataById(string appealMetaDataId)
        {
            if (appealMetaDataId.IsNullOrWhitespace())
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = new GetFullAppealMetaDataRequest
            {
                AppealMetaDataId = appealMetaDataId,
                Context = _context
            };

            var result = await _appealService.GetFullAppealMetaDataById(serviceRequest);

            return AppealResponseFactory.GetFullAppealMetaDataResponse(result);
        }

        #endregion
    }
}
