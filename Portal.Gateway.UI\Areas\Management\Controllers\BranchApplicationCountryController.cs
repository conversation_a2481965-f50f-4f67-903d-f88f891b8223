﻿using Gateway.ObjectStoring;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.DocumentManagement;
using Portal.Gateway.ApiModel.Requests.Management.ApplicationFormElement;
using Portal.Gateway.ApiModel.Requests.Management.BranchApplicationCountry;
using Portal.Gateway.ApiModel.Requests.Management.BranchApplicationCountryFile;
using Portal.Gateway.ApiModel.Requests.Management.BranchApplicationCountryVisaCategory;
using Portal.Gateway.ApiModel.Requests.Management.BranchDigitalSignatureDocument;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.DocumentManagement;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.ApiModel.Responses.Management.ApplicationFormElement;
using Portal.Gateway.ApiModel.Responses.Management.BranchApplicationCountry;
using Portal.Gateway.ApiModel.Responses.Management.BranchApplicationCountryFile;
using Portal.Gateway.ApiModel.Responses.Management.BranchApplicationCountryVisaCategory;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.ApplicationFormElement;
using Portal.Gateway.UI.Areas.Management.ViewModels.BranchApplicationCountry;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class BranchApplicationCountryController : BaseController<BranchApplicationCountryController>
    {
        private readonly IFileStorage _fileStorage;

        public BranchApplicationCountryController(
            IOptions<AppSettings> appSettings,
            IFileStorage fileStorage,
            IConfiguration configuration,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {
            _fileStorage = fileStorage;
        }

        #region Add

        public IActionResult PartialAddBranchApplicationCountry()
        {
            var viewModel = new AddBranchApplicationCountryViewModel() { };

            return PartialView("_AddBranchApplicationCountry", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddBranchApplicationCountry(AddBranchApplicationCountryViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddBranchApplicationCountryApiRequest
            {
                CountryId = viewModel.CountryId,
                BranchId = viewModel.BranchId,
                Note = viewModel.Note
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Management.AddBranchApplicationCountry, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateBranchApplicationCountry(string encryptedBranchApplicationCountryId)
        {
            int id = encryptedBranchApplicationCountryId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BranchApplicationCountryApiResponse>>
                (ApiMethodName.Management.GetBranchApplicationCountry + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdateBranchApplicationCountryViewModel
            {
                Id = apiResponse.Data.Id,
                BranchId = apiResponse.Data.BranchId,
                CountryId = apiResponse.Data.CountryId,
                Note = apiResponse.Data.Note,
                IsActive = apiResponse.Data.IsActive
            };

            return PartialView("_UpdateBranchApplicationCountry", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateBranchApplicationCountry(UpdateBranchApplicationCountryViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateBranchApplicationCountryApiRequest
            {
                Id = viewModel.Id,
                BranchId = viewModel.BranchId,
                CountryId = viewModel.CountryId,
                IsActive = viewModel.IsActive,
                Note = viewModel.Note
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<EmptyApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateBranchApplicationCountry, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteBranchApplicationCountry(string encryptedBranchApplicationCountryId)
        {
            int id = encryptedBranchApplicationCountryId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Management.DeleteBranchApplicationCountry + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> PartialBranchApplicationCountry(string encryptedBranchApplicationCountryId)
        {
            int id = encryptedBranchApplicationCountryId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BranchApplicationCountryApiResponse>>
                (ApiMethodName.Management.GetBranchApplicationCountry + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new BranchApplicationCountryViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                BranchName = apiResponse.Data.BranchName,
                CountryName = apiResponse.Data.CountryName,
                IsActive = apiResponse.Data.IsActive,
                Note = apiResponse.Data.Note
            };

            return PartialView("_BranchApplicationCountry", viewModel);
        }

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult List()
        {
            var viewModel = new FilterBranchApplicationCountryViewModel();

            return View(viewModel);
        }

        public async Task<IActionResult> GetPaginatedBranchApplicationCountry([DataSourceRequest] DataSourceRequest request, FilterBranchApplicationCountryViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedBranchApplicationCountryApiRequest
            {
                CountryId = filterViewModel.FilterCountryId,
                BranchId = filterViewModel.FilterBranchId,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedBranchApplicationCountryApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedBranchApplicationCountry, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<BranchApplicationCountryItemViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().BranchApplicationCountries
                    .Select(p => new BranchApplicationCountryItemViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        BranchName = p.BranchName,
                        CountryName = p.CountryName,
                        IsActive = p.IsActive
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #endregion

        #region BranchApplicationCountryVisaCategory

        public async Task<IActionResult> PartialUpdateBranchApplicationCountryVisaCategory(string encryptedBranchApplicationCountryId)
        {
            int branchApplicationCountryId = encryptedBranchApplicationCountryId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BranchApplicationCountryVisaCategoryApiResponse>>
                (ApiMethodName.Management.GetBranchApplicationCountryVisaCategory + branchApplicationCountryId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(EnumResources.MissingOrInvalidData);


            var viewModel = new UpdateBranchApplicationCountryVisaCategoryViewModel
            {
                BranchName = apiResponse.Data.BranchName,
                BranchApplicationCountryId = apiResponse.Data.BranchApplicationCountryId,
                CountryName = apiResponse.Data.CountryName,
                VisaCategories = apiResponse.Data.VisaCategoryTypes.Select(t => new UpdateVisaCategoryViewModel()
                {
                    VisaCategoryName = t.Value,
                    VisaCategoryId = t.Key,
                    IsActive = apiResponse.Data.VisaCategories.Any(p => p.VisaCategoryId == t.Key) ? apiResponse.Data.VisaCategories.First(p => p.VisaCategoryId == t.Key).IsActive : false
                }).ToList()
            };

            return PartialView("_UpdateBranchApplicationCountryVisaCategory", viewModel);
        }

        public async Task<IActionResult> PartialUpdateBranchDigitalSignatureDocuments(string encryptedBranchApplicationCountryId)
        {
            int branchApplicationCountryId = encryptedBranchApplicationCountryId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<GetBranchDigitalSignatureDocumentsApiResponse>>
                    (ApiMethodName.Management.GetBranchDigitalSignatureDocuments + branchApplicationCountryId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(EnumResources.MissingOrInvalidData);

            Dictionary<int, string> documentTypes = EnumHelper.GetEnumAsDictionary(typeof(DigitalSignatureDocument));

            var languages = EnumHelper.GetEnumAsDictionary(typeof(DigitalSignatureDocumentLanguage))
                .Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() })
                .OrderBy(o => o.Text);

            var viewModel = new UpdateBranchDigitalSignatureDocumentViewModel()
            {
                BranchName = apiResponse.Data.BranchName,
                BranchApplicationCountryId = apiResponse.Data.BranchApplicationCountryId,
                CountryName = apiResponse.Data.CountryName,
                Languages = languages,
                DigitalSignatureDocuments = documentTypes.Select(t => new UpdateDigitalSignatureDocumentViewModel()
                {
                    DigitalSignatureDocumentName = t.Value,
                    DigitalSignatureDocumentId = t.Key,
                    UsesDigitalSignatureDocument = apiResponse.Data.DigitalSignatureDocuments.Any(p => p.DigitalSignatureDocumentId == t.Key) ? apiResponse.Data.DigitalSignatureDocuments.First(p => p.DigitalSignatureDocumentId == t.Key).UsesDigitalSignatureDocument : false,
                    Languages = apiResponse.Data.DigitalSignatureDocuments.Any(p => p.DigitalSignatureDocumentId == t.Key) ? apiResponse.Data.DigitalSignatureDocuments.First(p => p.DigitalSignatureDocumentId == t.Key).Languages : new List<int>(),
                }).OrderBy(o => o.DigitalSignatureDocumentName).ToList()
            };


            return PartialView("_UpdateBranchDigitalSignatureDocuments", viewModel);
        }

        [HttpPost]
        [AllowAnonymous]
        public async Task<IActionResult> UpdateBranchDigitalSignatureDocuments(UpdateBranchDigitalSignatureDocumentViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddOrUpdateBranchDigitalSignatureDocumentsApiRequest
            {
                BranchApplicationCountryId = viewModel.BranchApplicationCountryId,
                DigitalSignatureDocuments = viewModel.DigitalSignatureDocuments.Select(p => new AddOrUpdateBranchDigitalSignatureDocumentsApiRequest.AddBranchDigitalSignatureDocumentDto()
                {
                    DigitalSignatureDocumentId = p.DigitalSignatureDocumentId,
                    UsesDigitalSignatureDocument = p.UsesDigitalSignatureDocument,
                    Languages = p.Languages
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddOrUpdateBranchDigitalSignatureDocumentsApiResponse>>
                    (apiRequest, ApiMethodName.Management.AddOrUpdateBranchDigitalSignatureDocuments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpPut]
        public async Task<IActionResult> UpdateBranchApplicationCountryVisaCategory(UpdateBranchApplicationCountryVisaCategoryViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateBranchApplicationCountryVisaCategoryApiRequest
            {
                BranchApplicationCountryId = viewModel.BranchApplicationCountryId,
                VisaCategories = viewModel.VisaCategories.Select(p => new UpdateVisaCategoryApiRequest()
                {
                    VisaCategoryId = p.VisaCategoryId,
                    IsActive = p.IsActive
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<EmptyApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateBranchApplicationCountryVisaCategory, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region ApplicationFormElement

        public async Task<IActionResult> PartialUpdateApplicationFormElements(string encryptedBranchApplicationCountryId)
        {
            int branchApplicationCountryId = encryptedBranchApplicationCountryId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationFormElementsApiResponse>>
                (ApiMethodName.Management.GetApplicationFormElements + branchApplicationCountryId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(EnumResources.MissingOrInvalidData);

            Dictionary<int, string> enumListApplicationFormElements = EnumHelper.GetEnumAsDictionary(typeof(ApplicationFormElement));

            var viewModel = new UpdateApplicationFormElementsViewModel
            {
                BranchName = apiResponse.Data.BranchName,
                BranchApplicationCountryId = apiResponse.Data.BranchApplicationCountryId,
                CountryName = apiResponse.Data.CountryName,
                ApplicationFormElements = enumListApplicationFormElements.Select(p => new UpdateApplicationFormElementsViewModel.ApplicationFormElement()
                {
                    FormElementId = p.Key,
                    FormElementName = p.Value,
                    IsRequried = apiResponse.Data.ApplicationFormElements.Any(p2 => p2.FormElementId == p.Key) ? apiResponse.Data.ApplicationFormElements.First(p2 => p2.FormElementId == p.Key).IsRequired : false,
                    IsActive = apiResponse.Data.ApplicationFormElements.Any(p2 => p2.FormElementId == p.Key) ? apiResponse.Data.ApplicationFormElements.First(p2 => p2.FormElementId == p.Key).IsActive : false
                }).ToList(),
                InformationNotes = apiResponse.Data.InformationNotes
            };

            return PartialView("_UpdateApplicationFormElements", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateApplicationFormElements(UpdateApplicationFormElementsViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateApplicationFormElementsApiRequest
            {
                BranchApplicationCountryId = viewModel.BranchApplicationCountryId,
                ApplicationFormElements = viewModel.ApplicationFormElements.Select(p => new UpdateApplicationFormElementsApiRequest.ApplicationFormElement()
                {
                    FormElementId = p.FormElementId,
                    IsRequired = p.IsRequried,
                    IsActive = p.IsActive
                }).ToList(),
                InformationNotes = viewModel.InformationNotes
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateApplicationFormElements, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region BranchApplicationCountryFile

        public IActionResult PartialAddBranchApplicationCountryFile(string encryptedBranchApplicationCountryId)
        {
            var viewModel = new AddBranchApplicationCountryFileViewModel
            {
                EncryptedBranchApplicationCountryId = encryptedBranchApplicationCountryId,
                FileSessionId = Guid.NewGuid().ToString()
            };

            return PartialView("_AddBranchApplicationCountryFile", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> SessionUploadFile([FromForm] IFormFile file, string fileSessionId)
        {
            if (file != null && !string.IsNullOrEmpty(fileSessionId))
            {
                using (var ms = new MemoryStream())
                {
                    file.CopyTo(ms);
                    await _fileStorage.SaveFileAsync(FileStorage.FileStorageBucketName, $"{FileStorage.SessionUploadFileFolderName}/{fileSessionId}", ms, "");
                }

                var fileModel = FileHelper.GetFileInfo(file);
                Extensions.SessionExtensions.Set(HttpContext.Session, $"SessionUploadFile{fileSessionId}", fileModel);
            }

            return Content("");
        }

        [HttpPost]
        public async Task<IActionResult> AddBranchApplicationCountryFile(AddBranchApplicationCountryFileViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var fileModel = Extensions.SessionExtensions.Get<FileModel>(HttpContext.Session, $"{FileStorage.SessionUploadFileFolderName}/{viewModel.FileSessionId}");

            if (fileModel == null)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            byte[] bytes;
            using (var ms = new MemoryStream())
            {
                var stream = await _fileStorage.GetFileStreamAsync(FileStorage.FileStorageBucketName, $"{FileStorage.SessionUploadFileFolderName}/{viewModel.FileSessionId}");
                await stream.CopyToAsync(ms);
                bytes = ms.ToArray();
            }

            var addDocumentsApiRequest = new AddDocumentsApiRequest { Documents = new List<AddDocumentApiRequest>() };

            var document = new AddDocumentApiRequest
            {
                DocumentTypeId = (int)DocumentType.BranchApplicationCountry,
                ReferenceId = viewModel.EncryptedBranchApplicationCountryId.ToDecryptInt(),
                UploadPath = $"{DocumentType.BranchApplicationCountry}/{viewModel.EncryptedBranchApplicationCountryId}",
                FileName = viewModel.FileName,
                FileExtension = fileModel.FileExtension,
                FileContent = bytes,
                CreatedBy = UserSession.UserId
            };

            addDocumentsApiRequest.Documents.Add(document);

            var addDocumentsApiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddMultipleApiResponse>>
                (addDocumentsApiRequest, ApiMethodName.DocumentManagement.AddDocuments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await addDocumentsApiResponse.Validate(out ResultModel addDocumentsResult).ConfigureAwait(false))
                return Json(addDocumentsResult);

            var addBranchApplicationCountryFileApiRequest = new AddBranchApplicationCountryFileApiRequest
            {
                BranchApplicationCountryId = viewModel.EncryptedBranchApplicationCountryId.ToDecryptInt(),
                FileTypeId = viewModel.FileTypeId,
                DocumentId = addDocumentsApiResponse.Data.Ids.First()
            };

            var addBranchApplicationCountryFileApiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (addBranchApplicationCountryFileApiRequest, ApiMethodName.Management.AddBranchApplicationCountryFile, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await addBranchApplicationCountryFileApiResponse.Validate(out ResultModel addBranchApplicationCountryFileResult).ConfigureAwait(false))
                return Json(addBranchApplicationCountryFileResult);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        public async Task<IActionResult> FileList(string encryptedBranchApplicationCountryId)
        {
            if (string.IsNullOrEmpty(encryptedBranchApplicationCountryId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "BranchApplicationCountry", new { Area = "Management" });
            }

            int branchApplicationCountryId = encryptedBranchApplicationCountryId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BranchApplicationCountryApiResponse>>
                (ApiMethodName.Management.GetBranchApplicationCountry + branchApplicationCountryId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", result);
                return RedirectToAction("List", "BranchApplicationCountry", new { Area = "Management" });
            }

            var branchApplicationCountryViewModel = new BranchApplicationCountryViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                BranchName = apiResponse.Data.BranchName,
                CountryName = apiResponse.Data.CountryName
            };

            ViewData["BranchApplicationCountryFile_List"] = branchApplicationCountryViewModel;

            return View();
        }

        public async Task<IActionResult> GetPaginatedBranchApplicationCountryFiles([DataSourceRequest] DataSourceRequest request, FilterBranchApplicationCountryFileViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedBranchApplicationCountryFilesApiRequest
            {
                BranchApplicationCountryId = filterViewModel.EncryptedBranchApplicationCountryId.ToDecryptInt(),
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<BranchApplicationCountryFilesApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedBranchApplicationCountryFiles, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<BranchApplicationCountryFileViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().BranchApplicationCountryFiles
                    .Select(p => new BranchApplicationCountryFileViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        EncryptedBranchApplicationCountryId = p.BranchApplicationCountryId.ToEncrypt(),
                        FileTypeId = p.FileTypeId,
                        EncryptedDocumentId = p.DocumentId.ToEncrypt(),
                    }).ToList();
            }

            if (paginatedData.Select(p => p.EncryptedDocumentId).Any())
            {
                var documentsApiRequest = new DocumentsApiRequest
                {
                    Ids = paginatedData.Select(p => p.EncryptedDocumentId.ToDecryptInt()).ToList(),
                    IsFileContentIncluded = false
                };

                var getDocumentsApiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<DocumentsApiResponse>>
                    (documentsApiRequest, ApiMethodName.DocumentManagement.GetDocuments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                if (!await getDocumentsApiResponse.Validate(out ResultModel getDocumentsResult).ConfigureAwait(false))
                    return Json(getDocumentsResult);

                foreach (var item in paginatedData)
                {
                    var document = getDocumentsApiResponse.Data.Documents.FirstOrDefault(p => p.Id == item.EncryptedDocumentId.ToDecryptInt());

                    if (document != null)
                    {
                        item.OriginalFileName = document.OriginalFileName;
                        item.UniqueFileName = document.UniqueFileName;
                        item.FileExtension = document.FileExtension;
                    }
                }
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteBranchApplicationCountryFile(string encryptedBranchApplicationCountryFileId)
        {
            int id = encryptedBranchApplicationCountryFileId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Management.DeleteBranchApplicationCountryFile + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        public async Task<IActionResult> DownloadFile(string encryptedDocumentId)
        {
            var documentsApiRequest = new DocumentsApiRequest
            {
                Ids = new List<int> { encryptedDocumentId.ToDecryptInt() },
                IsFileContentIncluded = true
            };

            var getDocumentsApiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<DocumentsApiResponse>>
                (documentsApiRequest, ApiMethodName.DocumentManagement.GetDocuments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await getDocumentsApiResponse.Validate(out ResultModel getDocumentsResult).ConfigureAwait(false))
                return Json(null);

            var document = getDocumentsApiResponse.Data.Documents.FirstOrDefault();

            if (document == null || document.FileContent == null)
                return Json(null);

            return File(document.FileContent, "application/octet-stream", document.UniqueFileNameWithExtension);
        }

        #endregion
    }
}
