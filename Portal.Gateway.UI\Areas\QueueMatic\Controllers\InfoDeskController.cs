﻿using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Appointment.PreApplication;
using Portal.Gateway.ApiModel.Requests.QueueMatic.InfoDesk;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Appointment.Application;
using Portal.Gateway.ApiModel.Responses.Appointment.PreApplication;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.ApiModel.Responses.Management.BranchDepartment;
using Portal.Gateway.ApiModel.Responses.QueueMatic.InfoDesk;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.QueueMatic.Helpers.Scanners.Desko;
using Portal.Gateway.UI.Areas.QueueMatic.Models;
using Portal.Gateway.UI.Areas.QueueMatic.ViewModels.InfoDesk;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Hubs;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using Portal.Gateway.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.QueueMatic.Controllers
{
    [Area("QueueMatic")]
    public class InfoDeskController : BaseController<InfoDeskController>
    {
        private readonly IHubContext<QueueMaticHub> _hubContext;

        public InfoDeskController(
            IOptions<AppSettings> appSettings,
            IHubContext<QueueMaticHub> hubContext,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {
            _hubContext = hubContext;
        }

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult SelectQueueFlow()
        {
            if (UserSession.BranchId == null)
            {
                return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
            }

            return View();
        }

        public async Task<IActionResult> PartialAppointment(int preApplicationId)
        {
            if (preApplicationId == 0)
            {
                return Content(EnumResources.MissingOrInvalidData);
            }

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<PreApplicationApiResponse>>
                (ApiMethodName.Appointment.GetPreApplication + preApplicationId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            //if (apiResponse.Data.AppointmentTime.Date > DateTime.UtcNow.Date)
            //    return Content($"{SiteResources.EarlyAppointmentProcess} {apiResponse.Data.AppointmentTime.Date.ToString(SiteResources.DatePickerFormatView)}");
            //else if (apiResponse.Data.AppointmentTime.Date < DateTime.UtcNow.Date)
            //    return Content(SiteResources.AppointmentOutOfDate);

            //if (apiResponse.Data.BranchId != UserSession.BranchId)
            //{
            //    return Content(EnumResources.RecordNotFound);
            //}

            //if (apiResponse.Data.StatusId != ActivationStatusType.Active.ToInt())
            //{
            //    return Content(SiteResources.AppointmentNotActivated);
            //}

            var countries = await CacheHelper.GetCountriesAsync();

            var visaCategories = await GetCachedVisaCategories();

            var viewModel = new AppointmentViewModel
            {
                EncryptedPreApplicationId = apiResponse.Data.PreApplicationId.ToEncrypt(),
                BranchName = apiResponse.Data.BranchName,
                VisaCategoryId = apiResponse.Data.VisaCategoryId,
                VisaCategory = GetVisaCategoryNameFromId(apiResponse.Data.VisaCategoryId,visaCategories),
                ApplicantTypeId = apiResponse.Data.ApplicantTypeId,
                AppointmentTime = apiResponse.Data.AppointmentTime,
                ApplicationTypeId = apiResponse.Data.ApplicationTypeId,
                VasTypeId = apiResponse.Data.VasTypeId,
                Type = apiResponse.Data.SlotId != 0 ? InfoDeskTypes.Appointment : InfoDeskTypes.WalkIn,
                AgencyName = apiResponse.Data.AgencyName,
                Applicants = apiResponse.Data.Applicants.Select(p => new AppointmentViewModel.ApplicantVieModel()
                {
                    EncryptedApplicantId = p.ApplicantId.ToEncrypt(),
                    Name = p.Name,
                    Surname = p.Surname,
                    PassportNumber = p.PassportNumber,
                    PassportExpireDate = p.PassportExpireDate.GetValueOrDefault(),
                    BirthDate = p.BirthDate.GetValueOrDefault(),
                    GenderId = p.GenderId.GetValueOrDefault(),
                    Nationality = countries?.Countries.FirstOrDefault(q => q.Id == p.NationalityId).Name,
                    Email = p.Email,
                    PhoneNumber = p.PhoneNumber
                }).ToList()
            };

            return PartialView("_Appointment", viewModel);
        }

        public async Task<IActionResult> PartialApplication(string passportNumber, InfoDeskTypes type)
        {
            if (UserSession.BranchId == null)
            {
                return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
            }

            if (string.IsNullOrEmpty(passportNumber))
            {
                return Content(EnumResources.MissingOrInvalidData);
            }

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>
                (ApiMethodName.QueueMatic.GetApplication + passportNumber, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            if (apiResponse.Data.BranchId != UserSession.BranchId)
            {
                return Content(EnumResources.WrongBranchNotification);
            }

            var countries = await CacheHelper.GetCountriesAsync();

            var visaCategories = await GetCachedVisaCategories();

            var viewModel = new ApplicationViewModel
            {
                Type = type,
                EncryptedPreApplicationApplicantId = apiResponse.Data.PreApplicationApplicantId.ToEncrypt(),
                EncryptedApplicationId = apiResponse.Data.Id.ToEncrypt(),
                BranchName = apiResponse.Data.BranchName,
                VisaCategoryId = apiResponse.Data.Document.VisaCategoryId,
                VisaCategory = GetVisaCategoryNameFromId(apiResponse.Data.Document.VisaCategoryId, visaCategories),
                ApplicantTypeId = apiResponse.Data.ApplicantTypeId,
                IsVip = apiResponse.Data.IsVip,
                ApplicationTime = apiResponse.Data.ApplicationTime,
                Name = apiResponse.Data.Name,
                Surname = apiResponse.Data.Surname,
                PassportNumber = apiResponse.Data.PassportNumber,
                PassportExpireDate = apiResponse.Data.PassportExpireDate,
                BirthDate = apiResponse.Data.BirthDate,
                GenderId = apiResponse.Data.GenderId,
                Nationality = countries?.Countries?.FirstOrDefault(q => q.Id == apiResponse.Data.NationalityId)?.Name,
                Email = apiResponse.Data.Email,
                PhoneNumber = apiResponse.Data.PhoneNumber1,
                ApplicationTypeId = apiResponse.Data.ApplicationTypeId,

                StatusHistories = apiResponse.Data.StatusHistories.Select(p => new ApplicationViewModel.ApplicationStatusHistory
                {
                    Order = p.Order,
                    Status = p.Status,
                    NameSurname = p.NameSurname,
                    ApplicationCreatedAt = apiResponse.Data.ApplicationTime,
                    StatusDate = p.StatusDate
                }).ToList(),
            };

            return PartialView("_Application", viewModel);
        }

        public async Task<IActionResult> PartialSelectBranchDepartment(InfoDeskTypes type, string encryptedId, bool isGroupApp, int applicantCount)
        {
            if (UserSession.BranchId == null)
            {
                return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
            }

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BranchDepartmentsApiResponse>>
                (ApiMethodName.QueueMatic.GetBranchDepartments + UserSession.BranchId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new BranchDepartmentsViewModel()
            {
                TypeId = type,
                EncryptedReferenceNumber = encryptedId,
                IsGroupApp = isGroupApp,
                ApplicantCount = applicantCount,
                BranchDepartments = apiResponse.Data?.BranchDepartments?.Where(q => q.IsActive).Select(p => new BranchDepartmentsViewModel.BranchDepartment
                {
                    Name = p.Name,
                    EncryptedBranchDepartmentId = p.BranchDepartmentId.ToEncrypt(),
                    ProcessOrder = string.Join(" -> ", p.Departments.OrderBy(q => q.Order).Select(q => q.DepartmentName))
                }).ToList()
            };

            return PartialView("_SelectBranchDepartment", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AssignQueue(AssignBranchDepartmentViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddQueueMaticApiRequest
            {
                BranchDepartmentId = viewModel.EncryptedBranchDepartmentId.ToDecryptInt(),
                TypeId = (int)viewModel.TypeId,
                UserId = UserSession.UserId,
                IsGroupApp = viewModel.IsGroupApp,
                BranchId = UserSession.BranchId.GetValueOrDefault(),
                CounterId = UserSession.CounterId.GetValueOrDefault(),
                IsPriority = viewModel.IsPriority
            };

            if (viewModel.IsGroupApp)
            {
                apiRequest.PreApplicationId = viewModel.EncryptedId.ToDecryptInt();
            }
            else
            {
                apiRequest.PreApplicationApplicantId = viewModel.EncryptedId.ToDecryptInt();
            }

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<QueueSequenceApiResponse>>
                (apiRequest, ApiMethodName.QueueMatic.AddQueue, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            var model = new
            {
                ApplicantCount = viewModel.ApplicantCount,
                SequenceNumber = apiResponse.Data.SequenceNumber,
                ApplicationCountry = apiResponse.Data.ApplicationCountry
            };

            await _hubContext.Clients.All.SendAsync($"AssignQueue_{UserSession.BranchId.ToEncrypt()}");

            return Json(new ResultModel { Data = model, Message = $"{SiteResources.OrderNumber}: {apiResponse.Data.SequenceNumber}", ResultType = ResultType.Success });
        }

        [ActionAttribute(IsMenuItem = true)]
        public async Task<IActionResult> List(bool openModal = false)
        {
            if (UserSession.BranchId == null)
            {
                return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
            }

            var viewModel = new FilterQueueViewModel()
            {
                FilterBranchId = UserSession.BranchId,
                OpenModal = openModal
            };

            if (!UserSession.DepartmentId.HasValue)
            {
                return View(viewModel);
            }

            var departments = await CacheHelper.GetDepartmentsAsync();

            viewModel.FilterDepartmentId = UserSession.DepartmentId;
            viewModel.DepartmentName = departments.Departments.First(q => q.Id == UserSession.DepartmentId).NameTranslations.Any(q => q.LanguageId == LanguageId) ?
                            departments.Departments.First(q => q.Id == UserSession.DepartmentId).NameTranslations.First(q => q.LanguageId == LanguageId).Name :
                            departments.Departments.First(q => q.Id == UserSession.DepartmentId).NameTranslations.FirstOrDefault().Name;

            return View(viewModel);
        }

        public async Task<IActionResult> GetPaginatedQueues([DataSourceRequest] DataSourceRequest request, FilterQueueViewModel filterViewModel)
        {
            if (!filterViewModel.FilterBranchId.HasValue || !filterViewModel.FilterDepartmentId.HasValue)
                return Json(new DataSourceResult());

            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new GetPaginatedQueueApiRequest
            {
                BranchId = filterViewModel.FilterBranchId,
                BranchApplicationCountryId = filterViewModel.FilterBranchApplicationCountryId,
                QueueMaticTypeId = filterViewModel.FilterQueueMaticTypeId,
                DepartmentId = filterViewModel.FilterDepartmentId,
                PreApplicationApplicantId = filterViewModel.FilterPreApplicationApplicantId,
                PreApplicationId = filterViewModel.FilterPreApplicationId,
                ApplicationId = filterViewModel.FilterApplicationId,
                Name = filterViewModel.FilterName,
                Surname = filterViewModel.FilterSurname,
                PassportNumber = filterViewModel.FilterPassportNumber,
                QueueNumber = filterViewModel.FilterQueueNumber,
                IsCompleted = filterViewModel.FilterIsCompleted,
                QueueMaticStatusId = filterViewModel.FilterQueueMaticStatusId,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedQueueApiResponse>>>
                (apiRequest, ApiMethodName.QueueMatic.GetPaginatedQueueList, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var countries = await CacheHelper.GetCountriesAsync();

            var departments = await CacheHelper.GetDepartmentsAsync();

            var paginatedData = new List<QueueViewModel>();

            var visaCategories = await GetCachedVisaCategories();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().Appointments
                    .Select(p => new QueueViewModel
                    {
                        Order = p.QueueNumber,
                        SequenceId = p.SequenceId,
                        RelationalType = p.RelationalType,
                        EncryptedQueueMaticId = p.QueueMaticId.ToEncrypt(),
                        TypeId = p.QueueMaticTypeId,
                        DepartmentName = departments.Departments.FirstOrDefault(q => q.Id == p.DepartmentId).NameTranslations.Any(q => q.LanguageId == LanguageId) ?
                                            departments.Departments.FirstOrDefault(q => q.Id == p.DepartmentId).NameTranslations.First(q => q.LanguageId == LanguageId).Name :
                                            departments.Departments.FirstOrDefault(q => q.Id == p.DepartmentId).NameTranslations.FirstOrDefault().Name,
                        StatusId = p.StatusId,
                        Name = p.Name,
                        Surname = p.Surname,
                        PassportNumber = p.PassportNumber,
                        ApplicantTypeId = p.ApplicantTypeId,
                        ApplicationTypeId = p.ApplicationTypeId,
                        VisaCategoryId = p.VisaCategoryId,
                        VisaCategory = GetVisaCategoryNameFromId(p.VisaCategoryId, visaCategories),
                        VasTypeId = p.VasTypeId,
                        ApplicationCountry = countries.Countries.FirstOrDefault(q => q.Id == p.ApplicationCountryId).Name,
                        AppointmentTime = p.AppointmentTime,
                        LastUpdatedBy = p.LastUpdatedBy,
                        StandByTime = $"{p.StandByTime} {SiteResources.Minute}",
                        MainReferenceNumber = (p.ApplicantTypeId == (int)ApplicantType.Family || p.ApplicantTypeId == (int)ApplicantType.Group) ? p.MainReferenceNumber?.ToApplicationNumber() : null,
                        ReferenceNumber = p.ReferenceNumber.ToApplicationNumber(),
                        IsApplication = p.QueueMaticTypeId == (int)InfoDeskTypes.ReBiometry || p.QueueMaticTypeId == (int)InfoDeskTypes.PassportDelivery,
                        IsPriority = p.IsPriority
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        [HttpPost]
        public async Task<IActionResult> UpdateQueueStatus(UpdateQueueStatusViewModel viewModel)
        {
            if (string.IsNullOrEmpty(viewModel.EncryptedQueueMaticId) || viewModel.StatusId == 0)
            {
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
            }

            var apiResponse = new ApiResponse<UpdateApiResponse>();

            if (viewModel.RelationalType == (int)QueueApplicantRelationalType.Main)
            {
                var apiRequest = new UpdateRelationalQueueMaticApiRequest()
                {
                    UserId = UserSession.UserId,
                    DepartmentId = UserSession.DepartmentId.GetValueOrDefault(),
                    StatusId = viewModel.StatusId,
                    CounterId = UserSession.CounterId.GetValueOrDefault(),
                    SequenceId = viewModel.SequenceId
                };

                apiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                    (apiRequest, ApiMethodName.QueueMatic.UpdateRelationalQueueMatic, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);
            }
            else
            {
                var apiRequest = new UpdateQueueMaticApiRequest()
                {
                    UserId = UserSession.UserId,
                    StatusId = viewModel.StatusId,
                    DepartmentId = UserSession.DepartmentId.GetValueOrDefault(),
                    CounterId = UserSession.CounterId.GetValueOrDefault(),
                    QueueMaticId = viewModel.EncryptedQueueMaticId.ToDecryptInt()
                };

                apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.QueueMatic.UpdateQueueMatic, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);
            }

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                return Json(new ResultModel { Message = result.Message, ResultType = ResultType.Danger });
            }

            var model = new
            {
                showNotification = viewModel.StatusId == QueueMaticStatusTypes.InProgress.ToInt(),
                sequenceNumber = viewModel.SequenceNumber,
                counterId = UserSession.CounterId.GetValueOrDefault()
            };

            await _hubContext.Clients.All.SendAsync($"UpdateQueueStatus_{UserSession.BranchId.ToEncrypt()}", model);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        public async Task<IActionResult> PartialActiveQueues()
        {
            if (!UserSession.DepartmentId.HasValue)
            {
                return Content(EnumResources.MissingOrInvalidData);
            }

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<UserActiveQueuesApiResponse>>
                ($"{ApiMethodName.QueueMatic.GetUserQueues + UserSession.DepartmentId}/{UserSession.UserId}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var countries = await CacheHelper.GetCountriesAsync();

            var appointmentDepartments = EnumHelper.GetEnumAsDictionary(typeof(DepartmentSubmission)).Select(p => new
            {
                Value = p.Key
            }).ToList();

            var viewModel = new UserQueuesViewModel()
            {
                Status = EnumHelper.GetEnumAsDictionary(typeof(QueueMaticStatusTypes))
                            .Where(q => q.Key != QueueMaticStatusTypes.Completed.ToInt() && q.Key != QueueMaticStatusTypes.Created.ToInt() && q.Key != QueueMaticStatusTypes.InProgress.ToInt())
                            .Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }).ToList(),
                AppointmentRedirectUrl = AppSettings.PortalGatewayUiUrl,
                Applications = apiResponse.Data.Applications.Select(q => new UserQueuesViewModel.Application()
                {
                    PreApplicationId = q.PreApplicationId,
                    ApplicationTypeId = q.ApplicantTypeId,
                    EncryptedBranchApplicationCountryId = q.BranchApplicationCountryId.ToEncrypt(),
                    ApplicantTypeId = q.ApplicantTypeId,
                    SequenceId = q.SequenceId,
                    SequenceNumber = q.SequenceNumber,
                    StatusId = q.StatusId,
                    CounterId = q.CounterId,
                    QueueMaticTypeId = q.QueueMaticTypeId,
                    ApplicationCountry = countries?.Countries.FirstOrDefault(p => p.Id == q.ApplicationCountryId)?.Name,
                    IsPriority = q.IsPriority,
                    Applicants = q.Applicants.Select(p => new UserQueuesViewModel.Application.Applicant()
                    {
                        EncryptedQueueMaticId = p.QueueMaticId.ToEncrypt(),
                        PreApplicationApplicantId = p.PreApplicationApplicantId,
                        ApplicationId = p.ApplicationId,
                        Name = p.Name,
                        Surname = p.Surname,
                        PassportNumber = p.PassportNumber,
                        RelationalType = p.RelationalType,
                        DirectToApplication = !appointmentDepartments.Any(q => q.Value == UserSession.DepartmentId) ? false :
                                                (p.ApplicationId.HasValue ? false :
                                                    ((q.Applicants.Count() > 1 && p.RelationalType == (int)QueueApplicantRelationalType.Sub && !q.Applicants.First(t => t.RelationalType == (int)QueueApplicantRelationalType.Main).ApplicationId.HasValue) ? false : true))
                    }).ToList()
                }).ToList()
            };

            return PartialView("_ActiveQueues", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> ScanPassportAndUpdateInformation(string encryptedApplicantId, string passportData)
        {
            int id = encryptedApplicantId.ToDecryptInt();

            try
            {
                var mrzEntries = JsonConvert.DeserializeObject<Dictionary<string, string>>(passportData);

                if (mrzEntries == null || mrzEntries.Count == 0)
                    return Json(new ResultModel { Message = EnumResources.PassportCouldNotBeRead, ResultType = ResultType.Danger });

                var countries = await CacheHelper.GetCountriesAsync();
                MrzHelper.ParseMrzEntries(mrzEntries, countries, out PassportModel passportModel);

                if (passportModel == null)
                    return Json(new ResultModel { Message = EnumResources.PassportCouldNotBeRead, ResultType = ResultType.Danger });

                var apiRequest = new UpdatePreApplicationApplicantApiRequest
                {
                    Id = id,
                    PassportNumber = passportModel.PassportNumber,
                    PassportExpireDate = passportModel.PassportExpireDate,
                    Name = passportModel.Name.ToUpper(),
                    Surname = passportModel.Surname.ToUpper(),
                    BirthDate = passportModel.BirthDate,
                    GenderId = passportModel.GenderId,
                    NationalityId = passportModel.NationalityId
                };

                var apiResponse = await PortalHttpClientHelper
                    .PutAsJsonAsync<ApiResponse<EmptyApiResponse>>
                    (apiRequest, ApiMethodName.QueueMatic.UpdatePreApplicationApplicant, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                    return Json(result);

                return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
            }
            catch
            {
                return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });
            }
        }

        public async Task<IActionResult> WalkIn()
        {
            if (UserSession.BranchId == null)
            {
                return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
            }

            var viewModel = new WalkInViewModel()
            {
                Applicants = Enumerable.Range(0, 15).Select(p => new WalkInViewModel.Applicant()
                {
                    ApplicationEnabled = false
                }).ToList(),
                VisaCategoryId = VisaCategoryTypeEnum.Touristic.ToInt(),
                ApplicationTypeId = ApplicationType.Normal.ToInt(),
                NumberOfPerson = 1,
                SlotId = 0,
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> SaveWalkInAppointment(WalkInViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddUpdatePreApplicationApiRequest
            {
                ApplicantTypeId = viewModel.ApplicantTypeId,
                BranchApplicationCountryId = viewModel.BranchApplicationCountryId,
                VisaCategoryId = viewModel.VisaCategoryId,
                SlotId = viewModel.SlotId,
                VasTypeId = viewModel.VasTypeId,
                Date = DateTime.Now,
                ApplicationTypeId = viewModel.ApplicationTypeId,
                StatusId = ActivationStatusType.Active.ToInt(),
                CreatedBy = UserSession.UserId,
                CreatedAt = DateTime.Now,
                Applicants = viewModel.Applicants.Where(p => p.ApplicationEnabled).Select(p => new AddUpdatePreApplicationApiRequest.PreApplicationApplicants
                {
                    BirthDate = p.BirthDate.GetValueOrDefault(),
                    Email = string.IsNullOrEmpty(p.Email) ? "-" : p.Email,
                    GenderId = p.GenderId.GetValueOrDefault(),
                    Name = p.Name,
                    NationalityId = p.NationalityId.GetValueOrDefault(),
                    PassportExpireDate = p.PassportExpireDate.GetValueOrDefault(),
                    PassportNumber = p.PassportNumber,
                    PhoneNumber = string.IsNullOrEmpty(p.PhoneNumber) ? "0" : p.PhoneNumber,
                    Surname = p.Surname
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Appointment.AddPreApplication, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            var walkInResult = new
            {
                Type = InfoDeskTypes.WalkIn.ToInt(),
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                ApplicantCount = apiRequest.Applicants.Count()
            };

            return Json(new ResultModel { Data = walkInResult, Message = $"{ResultMessage.OperationIsSuccessful.ToDescription()} - {SiteResources.AppointmentNumberIs}: {apiResponse.Data.Id}", ResultType = ResultType.Success });
        }

        public async Task<IActionResult> PartialSelectSlot(int preApplicationId, string encryptedBranchApplicationCountryId, int applicantCount)
        {
            if (preApplicationId == 0 || string.IsNullOrEmpty(encryptedBranchApplicationCountryId))
            {
                return Content(EnumResources.MissingOrInvalidData);
            }

            var viewModel = new PostponePreApplicationViewModel
            {
                EncryptedPreApplicationId = preApplicationId.ToEncrypt(),
                PostponedPreApplicationId = preApplicationId,
                PostponedBranchApplicationCountryId = encryptedBranchApplicationCountryId.ToDecryptInt(),
                ApplicantCount = applicantCount,
                PostponedDate = DateTime.Today.AddDays(1)
            };

            return PartialView("_SelectSlot", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> PostponeAppointment(PostponePreApplicationViewModel viewModel)
        {
            if (viewModel.PostponedSlotId == 0 || viewModel.PostponedPreApplicationId == 0)
            {
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
            }

            var apiRequest = new PostponePreApplicationApiRequest()
            {
                PreApplicationId = viewModel.PostponedPreApplicationId,
                SlotId = viewModel.PostponedSlotId
            };

            var apiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                    (apiRequest, ApiMethodName.Appointment.PostponePreApplication, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                return Json(new ResultModel { Message = result.Message, ResultType = ResultType.Danger });
            }

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        public async Task<IActionResult> GetQueueNumber(int sequenceNumber, int applicantCount, string applicationCountry)
        {
            var TimeZoneOffset = new decimal(0);
            var cacheItem = await CacheHelper.GetBranchesAsync();
            TimeZoneOffset = cacheItem.Branches.FirstOrDefault(q => q.Id == UserSession.BranchId.GetValueOrDefault()).TimeZoneOffset;

            var viewModel = new QueueNumberViewModel()
            {
                ApplicationCountry = applicationCountry,
                ApplicantCount = applicantCount,
                SequenceNumber = sequenceNumber,
                TimeOffset = TimeZoneOffset
            };

            return View(viewModel);
        }

        [ActionAttribute(IsMenuItem = true)]
        public async Task<IActionResult> Routing(RoutingViewModel viewModel)
        {
            if (UserSession.BranchId == null)
            {
                return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
            }

            if (viewModel == null)
                viewModel = new RoutingViewModel();

            var branchId = UserSession.BranchId;

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BranchApiResponse>>
                (ApiMethodName.Management.GetBranch + branchId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            viewModel.EncryptedBranchId = branchId.ToEncrypt();
            viewModel.BranchDetail = $"{apiResponse.Data.Name} ({apiResponse.Data.Country.Name})";
            viewModel.TimeZoneOffset = apiResponse.Data.TimeZoneOffset;
            viewModel.QueueMaticPlaylistId = apiResponse.Data.QueueMaticPlaylistId;

            return View(viewModel);
        }

        public async Task<IActionResult> GetRoutingQueues([DataSourceRequest] DataSourceRequest request, FilterRoutingQueueViewModel filterViewModel)
        {
            var apiRequest = new GetRoutingQueuesApiRequest
            {
                BranchId = UserSession.BranchId,
                DepartmentId = UserSession.DepartmentId
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<RoutingQueuesApiResponse>>
                (apiRequest, ApiMethodName.QueueMatic.GetRoutingQueues, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var viewModel = apiResponse.Data.RoutingQueues.Select(p => new RoutingQueueViewModel
            {
                EncryptedQueueMaticId = p.QueueMaticId.ToEncrypt(),
                DepartmentId = p.DepartmentId,
                DepartmentName = p.DepartmentName,
                CounterId = p.CounterId,
                QueueNumber = p.QueueNumber,
                IsPriority = p.IsPriority
            }).ToList();

            if (filterViewModel.ItemSize != null && viewModel != null && viewModel.Count > filterViewModel.ItemSize)
                viewModel = viewModel.Take(filterViewModel.ItemSize.Value).ToList();

            return Json(viewModel.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task RecallQueueItem(RoutingQueueViewModel viewModel)
        {
            await _hubContext.Clients.All.SendAsync($"RecallQueueItem_{UserSession.BranchId.ToEncrypt()}", viewModel);
        }

        [HttpDelete]
        public async Task<IActionResult> DeletePreApplicationApplicant(string EncryptedPreApplicationApplicantId, string reason)
        {
            int id = EncryptedPreApplicationApplicantId.ToDecryptInt();

            var apiRequest = new DeletePreApplicationApplicantApiRequest
            {
                Id = id,
                UserId = UserSession.UserId,
                Reason = reason
            };

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (apiRequest, ApiMethodName.Appointment.DeletePreApplicationApplicant, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteQueueMaticToken(string EncryptedPreApplicationApplicantId, string EncryptedQueueMaticId)
        {
            int preApplicationApplicantId = EncryptedPreApplicationApplicantId.ToDecryptInt();
            int queueMaticId = EncryptedQueueMaticId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                ($"{ApiMethodName.QueueMatic.DeleteQueueMaticToken + preApplicationApplicantId}/{queueMaticId}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }
    }
}
