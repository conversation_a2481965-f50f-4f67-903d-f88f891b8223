﻿using Portal.Gateway.ApiModel.Requests.Dashboard;
using Portal.Gateway.ApiModel.Responses.Appointment.Application;
using Portal.Gateway.ApiModel.Responses.Dashboard;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.ApiModel.Responses.Management.RoleAction;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.UI.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Helpers
{
    public interface ICacheHelper
    {
        Task<bool> CacheAllAsync();
        Task<CountriesApiResponse> GetCountriesAsync();
        Task<ActiveCountriesApiResponse> GetActiveCountriesAsync();
        Task<ForeignCitiesApiResponse> GetForeignCitiesAsync();
        Task<RoleActionApiResponse> GetRoleActionsAsync();
        Task<BranchesApiResponse> GetBranchesAsync();
        Task<DepartmentsApiResponse> GetDepartmentsAsync();
        Task<List<BranchApplicationCountryVisaCategoriesApiResponse.VisaCategory>> GetVisaCategoriesAsync();
        Task<ApplicationStatusOrdersApiResponse> GetApplicationStatusOrderAsync();
        Task<Dictionary<int, DepartmentCacheModel>> GetDepartmentsDictionaryAsync();
        Task<BranchApplicationStatusesApiResponse> GetBranchApplicationStatusAsync();
        Task<ApiResponse<ApplicationStatsApiResponse>> GetPeriodicApplicationStatsAsync();
        Task<ApiResponse<ExtraFeeStatsApiResponse>> GetExtraFeeStatsAsync();
        Task<ApiResponse<DailyPassportDeliveryStatsApiResponse>> GetDailyPassportDeliveriesAsync();
        Task<ApiResponse<StatsFromBeginningApiResponse>> GetStatsFromBeginningAsync();
        Task<ApiResponse<BranchInsuranceStatsApiResonse>> GetBranchInsuranceStatsAsync();
        Task<ApiResponse<PreviousDayGeneralStatsApiResponse>> GetOfficeManagerPreviousDayGeneralStatsAsync();
        Task<ApiResponse<OfficersApiResponse>> GetOfficersAsync();
        Task<ApiResponse<QuarterApplicationSummaryApiResponse>> GetQuarterApplicationSummaryAsync();
        Task<ApiResponse<PreviousDayGeneralStatsApiResponse>> GetPreviousDayGeneralStatsAsync();
        Task<ApiResponse<InsuranceStatsApiResponse>> GetInsuranceStatsAsync();
        Task<ApiResponse<InsuranceStatsApiResponse>> GetInsuranDailyInsuranceStatsAsync();
        Task<ApiResponse<MonthComparativeInsuranceApiResponse>> GetMonthComparativeInsuranceStatsAsync();
        Task<ApiResponse<PeriodicExtraFeeStatsApiResponse>> GetMonthlyExtraFeeStatsAsync();
        Task<ApiResponse<PreviousDayChangeStatsApiReponse>> GetPreviousDayChangeStatsAsync();
        Task<ApiResponse<AverageIstizanResultTimeApiResponse>> GetAverageIstizanResultTimeAsync();
        Task<ApiResponse<AverageNormalApplicationResultTimeApiResponse>> GetAverageNormalApplicationResultTimeAsync();
        Task<ApiResponse<PeriodicExtraFeeStatsApiResponse>> GetQuarterExtraFeeStatsAsync();
        Task<ApiResponse<QuarterApplicationStatsApiResponse>> GetQuarterApplicationStatsAsync();
        Task<ApiResponse<PreviousDayGeneralStatsApiResponse>> GetConsularPreviousDayGeneralStatsAsync();
    }
}
