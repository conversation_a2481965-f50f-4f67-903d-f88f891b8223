﻿using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Portal.Gateway.ApiModel.Requests.Management.Tablet;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.UI.Validators;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Areas.Management.ViewModels.ApplicationSale;
using Portal.Gateway.ApiModel.Responses.Management.ApplicationSale;
using Portal.Gateway.Common.Utility.Helpers;
using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using Microsoft.AspNetCore.Http;
using Portal.Gateway.UI.Constants;
using System.IO;
using Gateway.ObjectStoring;
using Portal.Gateway.ApiModel.Requests.Appointment.Application;
using Portal.Gateway.ApiModel.Requests.DocumentManagement;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.Application;
using Portal.Gateway.Resources;
using Portal.Gateway.ApiModel.Requests.Management.ApplicationSale;
using System.Text;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class ApplicationSaleController : BaseController<ApplicationSaleController>
    {
        private readonly IFileStorage _fileStorage;
        public ApplicationSaleController(IFileStorage fileStorage, IOptions<AppSettings> appSettings, ICacheHelper cacheHelper) : base(appSettings, cacheHelper) 
        { 
            _fileStorage = fileStorage;
        }

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedApplicationSales([DataSourceRequest] DataSourceRequest request, FilterApplicationSaleViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new GetPaginatedApplicationSalesApiRequest
            {
                FilterName = filterViewModel.FilterName,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<ApplicationSalesApiResponse>>
                (apiRequest, ApiMethodName.Management.GetApplicationSales, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            var paginatedData = new List<ApplicationSaleViewModel>();

            if (apiResponse?.Data != null)
            {
                paginatedData = apiResponse.Data?.ApplicationSales
                    .Select(p => new ApplicationSaleViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        Name = p.Name,
                        AvailableCount = p.AvailableCount,
                        ReservedCount = p.ReservedCount,
                        UsedCount = p.UsedCount
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData });
        }

        public IActionResult PartialUploadEsim(string encryptedId)
        {
            var fileType = encryptedId.ToDecryptInt();

            var viewModel = new UploadEsimViewModel
            {
                EncryptedId = encryptedId,
                FileSessionId = Guid.NewGuid().ToString(),
                EsimTypes = EnumHelper.GetEnumAsDictionary(typeof(Contracts.Entities.Enums.EsimType)).Select(p => new SelectListItem()
                {
                    Text = p.Value.ToTitleCase(),
                    Value = p.Key.ToString()
                }).ToList(),
                FileTypeId = fileType,
                FileType = fileType.ToString()
            };

            return PartialView("_UploadEsim", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> SessionUploadEsimFile([FromForm] IFormFile file, string fileSessionId)
        {
            if (file != null && !string.IsNullOrEmpty(fileSessionId))
            {
                using (var ms = new MemoryStream())
                {
                    file.CopyTo(ms);
                    await _fileStorage.SaveFileAsync(FileStorage.FileStorageBucketName, $"{FileStorage.SessionUploadFileFolderName}/{fileSessionId}", ms, "");
                }

                var fileModel = FileHelper.GetFileInfo(file);
                Extensions.SessionExtensions.Set(HttpContext.Session, $"SessionUploadFile{fileSessionId}", fileModel);
            }

            return Content("");
        }

        [HttpPost]
        public async Task<IActionResult> AddApplicationEsim(UploadEsimViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var fileModel = Extensions.SessionExtensions.Get<FileModel>(HttpContext.Session, $"SessionUploadFile{viewModel.FileSessionId}");

            if (fileModel == null)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            byte[] bytes;
            using (var ms = new MemoryStream())
            {
                var stream = await _fileStorage.GetFileStreamAsync(FileStorage.FileStorageBucketName, $"{FileStorage.SessionUploadFileFolderName}/{viewModel.FileSessionId}");
                await stream.CopyToAsync(ms);
                bytes = ms.ToArray();
            }

            var apiRequest = new UploadEsimApiRequest
            {
                TypeId = viewModel.FileTypeId,
                FileExtension = fileModel.FileExtension,
                FileContent = bytes,
                CreatedBy = UserSession.UserId
            };

            var uploadApiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<UploadEsimApiResponse>>
                    (apiRequest, ApiMethodName.Management.UploadEsim, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

            if (!await uploadApiResponse.Validate(out ResultModel addDocumentsResult).ConfigureAwait(false))
                return Json(addDocumentsResult);

            if (uploadApiResponse.Data.Result.Any())
            {
                var message = new StringBuilder();

                foreach (var result in uploadApiResponse.Data.Result)
                {
                    message.Append($"{result.FileName} - {result.Description} <br/>");
                }

                return Json(new ResultModel { Message = message.ToString(), ResultType = ResultType.Warning });
            }
                
            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }
    }
}
