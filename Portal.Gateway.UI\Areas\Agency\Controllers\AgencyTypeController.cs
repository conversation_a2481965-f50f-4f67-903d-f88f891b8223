﻿using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Agency.AgencyType;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Agency.AgencyType;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Agency.ViewModels.AgencyType;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Agency.Controllers
{
    [Area("Agency")]
    public class AgencyTypeController : BaseController<AgencyTypeController>
    {

        public AgencyTypeController(
              IOptions<AppSettings> appSettings,
              ICacheHelper cacheHelper)
          : base(appSettings, cacheHelper)
        {

        }

        #region Add

        public IActionResult PartialAddAgencyType()
        {
            var viewModel = new AddAgencyTypeViewModel()
            {
                AgencyTypeTranslations = new List<AddAgencyTypeViewModel.AgencyTypeTranslationViewModel>()
            };

            foreach (var item in EnumHelper.GetEnumAsDictionary(typeof(Language)))
            {
                viewModel.AgencyTypeTranslations.Add(new AddAgencyTypeViewModel.AgencyTypeTranslationViewModel { LanguageId = item.Key });
            }

            return PartialView("_AddAgencyType", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddAgencyType(AddAgencyTypeViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddAgencyTypeApiRequest
            {
                AgencyTypeTranslations = viewModel.AgencyTypeTranslations.Select(p => new AddAgencyTypeApiRequest.AgencyTypeTranslationApiResponse
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                    Description = p.Description
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<EmptyApiResponse>>
                (apiRequest, ApiMethodName.Agency.AddAgencyType, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateAgencyType(string encryptedId)
        {
            int id = encryptedId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<AgencyTypeApiResponse>>
                (ApiMethodName.Agency.GetAgencyType + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdateAgencyTypeViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                IsActive = apiResponse.Data.IsActive,
                AgencyTypeTranslations = apiResponse.Data.AgencyTypeTranslations.Select(p => new UpdateAgencyTypeViewModel.AgencyTypeTranslationViewModel
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                    Description = p.Description
                    
                }).ToList()
            };

            return PartialView("_UpdateAgencyType", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateAgencyType(UpdateAgencyTypeViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateAgencyTypeApiRequest
            {
                Id = viewModel.EncryptedId.ToDecryptInt(),
                IsActive = viewModel.IsActive,
                AgencyTypeTranslations = viewModel.AgencyTypeTranslations.Select(p => new UpdateAgencyTypeApiRequest.AgencyTypeTranslationApiResponse
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                    Description = p.Description
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<EmptyApiResponse>>
                (apiRequest, ApiMethodName.Agency.UpdateAgencyType, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }
        
        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteAgencyType(string encryptedId)
        {
            int id = encryptedId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Agency.DeleteAgencyType + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> PartialAgencyType(string encryptedId)
        {
            int id = encryptedId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<AgencyTypeApiResponse>>
                (ApiMethodName.Agency.GetAgencyType + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new AgencyTypeViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                IsActive = apiResponse.Data.IsActive,
                AgencyTypeTranslations = apiResponse.Data.AgencyTypeTranslations.Select(q => new AgencyTypeViewModel.AgencyTypeTranslationApiResponse()
                {
                    LanguageId = q.LanguageId,
                    Name = q.Name,
                    Description = q.Description
                }).ToList()
            };

            return PartialView("_AgencyType", viewModel);
        }
        
        #endregion

        #region List

        public ActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedAgencyTypes([DataSourceRequest] DataSourceRequest request, FilterAgencyTypeViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedAgencyTypeApiRequest
            {
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedAgencyTypeApiResponse>>>
                (apiRequest, ApiMethodName.Agency.GetPaginatedAgencyTypes, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<AgencyTypeItemViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().AgencyTypes
                    .Select(p => new AgencyTypeItemViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        IsActive = p.IsActive,
                        Name = p.Name
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #endregion

    }
}
