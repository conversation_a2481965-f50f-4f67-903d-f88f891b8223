﻿using Gateway.Extensions;
using Gateway.Http;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.QmsFolder;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Biometrics.Models.ClientConfiguration.RequestModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.ClientConfiguration.Results;
using Portal.Gateway.UI.Areas.Biometrics.Models.ClientConfiguration.ViewModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.ClientConfigurationInventory.Results;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using System;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Biometrics.Controllers
{

    [Area("Biometrics")]
    public class ClientConfigurationController : BaseController<ClientConfigurationController>
    {
        public ClientConfigurationController(
            IOptions<AppSettings> appSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper) { }

        #region Add

        public async Task<IActionResult> PartialAddClientConfiguration()
        {
            var viewModel = new AddClientConfigurationViewModel();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ClientConfigurationInventoriesUnAssignedResponse>>(null, BiometricsEndPoint.ParameterGetClientConfigurationInventoriesUnAssigned, AppSettings.Biometrics.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (apiResponse.Data != null)
            {
                viewModel.UnAssignedClientConfigurationInventories = apiResponse.Data.ClientConfigurationInventories;
            }

            return PartialView("_AddClientConfiguration", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddClientConfiguration(AddClientConfigurationViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var request = new AddClientConfigurationRequestModel
            {
                Status = viewModel.Status,
                OfficeId = viewModel.OfficeId,
                CountryId = viewModel.CountryId,
                Description = viewModel.Description,
                CabinId = viewModel.CabinId,
                HostName = viewModel.HostName,
                LicenseId = viewModel.LicenseId,
                ProvinceId = viewModel.ProvinceId,
                NeurotecLicenseNumber = viewModel.NeurotecLicenseNumber,
                ClientConfigurationInventories = viewModel.ClientConfigurationInventories
            };

            var apiResponse = await RestHttpClient.Create().Post<AddClientConfigurationResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.AddClientConfiguration, headers, request);

            if (apiResponse.Data == null)
                return Json(new ResultModel { Message = apiResponse.Message + "\r\n" + apiResponse.ValidationMessages.ToPlainText(), ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateClientConfiguration(string encryptedClientConfigurationId)
        {
            if (encryptedClientConfigurationId.IsNullOrWhitespace())
            {
                return Content("Missing ClientConfiguration id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedClientConfigurationId.ToDecrypt());

            var apiResponse = await RestHttpClient.Create().Get<GetClientConfigurationResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetClientConfiguration + resourceId, headers);

            var viewModel = new UpdateClientConfigurationViewModel
            {
                Id = apiResponse.Data.Id,
                Status = apiResponse.Data.Status,
                OfficeId = apiResponse.Data.OfficeId,
                CountryId = apiResponse.Data.CountryId,
                Description = apiResponse.Data.Description,
                CabinId = apiResponse.Data.CabinId,
                HostName = apiResponse.Data.HostName,
                LicenseId = apiResponse.Data.LicenseId,
                NeurotecLicenseNumber = apiResponse.Data.NeurotecLicenseNumber,
                ProvinceId = apiResponse.Data.ProvinceId,
                ClientConfigurationInventories = apiResponse.Data.ClientConfigurationInventories,
                UnAssignedClientConfigurationInventories = apiResponse.Data.UnAssignedClientConfigurationInventories
            };

            return PartialView("_UpdateClientConfiguration", viewModel);
        }


        [HttpPost]
        public JsonResult UpdateClientConfigurationTest(UpdateClientConfigurationViewModel viewModel)
        {
            return null;
        }

        [HttpPost]
        public async Task<JsonResult> UpdateClientConfiguration(UpdateClientConfigurationViewModel viewModel)
        {

            if (viewModel == null || !ModelState.IsValid) //buraya fluent val yazılıcak viewmodellere
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var apiRequest = new UpdateClientConfigurationRequestModel
            {
                Status = viewModel.Status,
                OfficeId = viewModel.OfficeId,
                CountryId = viewModel.CountryId,
                Description = viewModel.Description,
                CabinId = viewModel.CabinId,
                HostName = viewModel.HostName,
                LicenseId = viewModel.LicenseId,
                NeurotecLicenseNumber = viewModel.NeurotecLicenseNumber,
                ClientConfigurationInventories = viewModel.ClientConfigurationInventories,

            };

            var apiResponse = await RestHttpClient.Create().Put<UpdateClientConfigurationResult>(
                AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.UpdateClientConfiguration + viewModel.Id, headers,
                apiRequest);


            if (apiResponse.Data == null)
                return Json(new ResultModel { Message = apiResponse.Message + "\r\n" + apiResponse.ValidationMessages.ToPlainText(), ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion


        #region Get

        public async Task<IActionResult> PartialDetailClientConfiguration(string encryptedClientConfigurationId)
        {
            if (encryptedClientConfigurationId.IsNullOrWhitespace())
            {
                return Content("Missing ClientConfiguration id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedClientConfigurationId.ToDecrypt());

            var apiResponse = await RestHttpClient.Create().Get<GetClientConfigurationResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetClientConfiguration + resourceId, headers);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var viewModel = new ClientConfigurationViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Status = apiResponse.Data.Status,
                OfficeId = apiResponse.Data.OfficeId,
                CountryId = apiResponse.Data.CountryId,
                Description = apiResponse.Data.Description,
                CabinId = apiResponse.Data.CabinId,
                HostName = apiResponse.Data.HostName,
                LicenseId = apiResponse.Data.LicenseId,
                NeurotecLicenseNumber = apiResponse.Data.NeurotecLicenseNumber,
                ProvinceId = apiResponse.Data.ProvinceId,
                ClientConfigurationInventories = apiResponse.Data.ClientConfigurationInventories,
                UnAssignedClientConfigurationInventories = apiResponse.Data.UnAssignedClientConfigurationInventories
            };

            return PartialView("_DetailClientConfiguration", viewModel);
        }


        [Action(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedClientConfigurations([DataSourceRequest] DataSourceRequest request, FilterClientConfigurationViewModel filterViewModel)
        {
            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedClientConfigurationApiRequest
            {
                FilterCountryId = filterViewModel.FilterCountryId,
                Pagination = new QmsPaginationApiRequest
                {
                    PageNumber = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = "Id",
                    Ascending = true
                }
            };

            var apiResponse = await RestHttpClient.Create().Post<GetPaginatedClientConfigurationsResult>(
                AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetPaginatedClientConfigurations, headers, apiRequest);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var paginatedData = apiResponse.Data.Select(p =>
                new ClientConfigurationViewModel
                {
                    EncryptedId = p.Id.ToEncrypt(),
                    Status = p.Status,
                    OfficeId = p.OfficeId,
                    CountryId = p.CountryId,
                    Description = p.Description,
                    CabinId = p.CabinId,
                    HostName = p.HostName,
                    LicenseId = p.LicenseId,
                    NeurotecLicenseNumber = p.NeurotecLicenseNumber.ToIntNullable(),
                    ProvinceId = p.ProvinceId,
                    Office = p.Office,
                    Cabin = p.Cabin,
                    ClientConfigurationInventories = p.ClientConfigurationInventories,

                }).ToList();

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.TotalNumberOfRecords });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteClientConfiguration(string encryptedClientConfigurationId)
        {
            if (encryptedClientConfigurationId.IsNullOrWhitespace())
            {
                return Content("Missing ClientConfiguration id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedClientConfigurationId.ToDecrypt());

            var apiResponse = await RestHttpClient.Create().Delete<DeleteClientConfigurationResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.DeleteClientConfiguration + resourceId, headers);

            if (apiResponse.Data == null)
                return Json(apiResponse.Message);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

    }

}
