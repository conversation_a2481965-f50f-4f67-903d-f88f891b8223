﻿using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.PerformanceManagement.PMS;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.PerformanceManagement.PMS;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.UI.Areas.PerformanceManagement.ViewModels.PMS;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DateTime = System.DateTime;

namespace Portal.Gateway.UI.Areas.PerformanceManagement.Controllers
{
    [Area("PerformanceManagement")]
    public class PMSController : BaseController<PMSController>
    {
        public PMSController(
            IOptions<AppSettings> appSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {
        }

        #region Get

        [Action(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        [Action(IsMenuItem = true)]
        public IActionResult ScoreCard()
        {
            return View();
        }
        
        public async Task<IActionResult> PartialUpdatePMS(int id)
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<PMSApiResponse>>
                (ApiMethodName.PerformanceManagementSystem.GetPMS + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdatePerformanceManagementViewModel
            {
                Id = id,
                CountryId = apiResponse.Data.CountryId ?? 0,
                BranchId = apiResponse.Data.BranchId ?? 0,
                UserId = apiResponse.Data.UserId ?? 0,
                PMSDataGroupId = apiResponse.Data.DataGroupId ?? 0,
                PMSDataTypeId = apiResponse.Data.DataTypeId ?? 0,
                PMSDate = apiResponse.Data.Date,
                PMSExplanation = apiResponse.Data.Explanation,
                PMSValue = apiResponse.Data.Value
            };

            return PartialView("_UpdatePMS", viewModel);
        }

        #endregion

        #region Post

        [HttpPost]
        public async Task<IActionResult> GetPaginatedPMSList([DataSourceRequest] DataSourceRequest request, PerformanceManagementViewModel filterViewModel)
        {
            var paginatedData = new List<PerformanceManagementListViewModel>();

            if (filterViewModel.UserId == 0)
                return Json(new DataSourceResult { Data = paginatedData, Total = 0 });

            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedPMSApiRequest
            {
                UserId = filterViewModel.UserId,
                User = filterViewModel.User,
                CountryId = filterViewModel.CountryId,
                Country = filterViewModel.Country,
                BranchId = filterViewModel.BranchId,
                Branch = filterViewModel.Branch,
                Date = filterViewModel.Date,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            if (filterViewModel.UserId == 0)
                return Json(new DataSourceResult { Data = paginatedData, Total = 0 });

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedPMSApiResponse>>>
                (apiRequest, ApiMethodName.PerformanceManagementSystem.GetPaginatedPMSList, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            if (apiResponse.Data != null && apiResponse.Data.Items.Count != 0)
            {
                paginatedData = apiResponse.Data.Items.First().PerformanceManagemet.Select(p => new PerformanceManagementListViewModel
                {
                    Id = p.Id ?? 0,
                    Date = p.Date.ToShortDateString(),
                    Country = p.Country,
                    Branch = p.Branch,
                    User = p.User,
                    DataGroup = p.DataGroup,
                    DataType = p.DataType,
                    Value = p.Value,
                    Explanation = p.Explanation,
                    CreatedBy = p.CreatedBy,
                    DataDate = p.DataDate.ToShortDateString()
                }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        public async Task<IActionResult> GetPaginatedPMSScoreCardList([DataSourceRequest] DataSourceRequest request, PerformanceManagementScoreCardViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedPMSApiRequest
            {
                UserId = filterViewModel.UserId,
                User = filterViewModel.User,
                CountryId = filterViewModel.CountryId,
                Country = filterViewModel.Country,
                BranchId = filterViewModel.BranchId,
                Branch = filterViewModel.Branch,
                StartDate = filterViewModel.FilterStartDate,
                EndDate = filterViewModel.FilterEndDate,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var paginatedData = new List<PerformanceManagementListViewModel>();

            if (filterViewModel.UserId == 0)
                return Json(new DataSourceResult { Data = paginatedData, Total = 0 });

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedPMSApiResponse>>>
                (apiRequest, ApiMethodName.PerformanceManagementSystem.GetPaginatedPMSScoreCardList, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            if (apiResponse.Data != null && apiResponse.Data.Items.Count != 0)
            {
                paginatedData = apiResponse.Data.Items.First().PerformanceManagemet.Select(p => new PerformanceManagementListViewModel
                {
                    Id = p.Id ?? default,
                    Date = p.Date.ToShortDateString(),
                    Country = p.Country,
                    Branch = p.Branch,
                    User = p.User,
                    DataGroup = p.DataGroup,
                    DataType = p.DataType,
                    Value = p.Value,
                    Explanation = p.Explanation,
                    CreatedBy = p.CreatedBy,
                    DataDate = p.DataDate.ToShortDateString()
                }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        public async Task<IActionResult> AddPMS(int userId, int countryId, int branchId, DateTime date, int dataGroupId, int dataTypeId, string explanation, int value)
        {
            var pmsApiRequest = new PMSApiRequest()
            {
                UserId = userId,
                CountryId = countryId,
                BranchId = branchId,
                Date = date,
                DataGroupId = dataGroupId,
                DataTypeId = dataTypeId,
                Explanation = explanation,
                Value = value
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                    (pmsApiRequest, ApiMethodName.PerformanceManagementSystem.AddPMS, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel addApplicationVisaDecisionResult).ConfigureAwait(false))
                return Json(addApplicationVisaDecisionResult);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        public async Task<IActionResult> UpdatePMS(UpdatePerformanceManagementViewModel viewModel)
        {
            if (((viewModel.PMSDataGroupId == (int)PMSDataGroup.Warnings && viewModel.PMSDataGroupId == (int)PMSMistakesDataType.Other) || viewModel.PMSDataGroupId == (int)PMSDataGroup.Warnings || viewModel.PMSDataGroupId == (int)PMSDataGroup.ThanksComplaints) && viewModel.PMSExplanation == null)
                return Json(new ResultModel { Message = ResultMessage.MissingOrInvalidData.ToDescription(), ResultType = ResultType.Warning });

            var apiRequest = new PMSApiRequest
            {
                Id = viewModel.Id,
                CountryId = viewModel.CountryId,
                BranchId = viewModel.BranchId,
                UserId = viewModel.UserId,
                DataGroupId = viewModel.PMSDataGroupId,
                DataTypeId = viewModel.PMSDataTypeId,
                Date = viewModel.PMSDate,
                Explanation = viewModel.PMSExplanation,
                Value = viewModel.PMSValue,
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<ValidateApiResponse>>
                (apiRequest, ApiMethodName.PerformanceManagementSystem.UpdatePMS, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }
        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeletePMS(int id)
        {
            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.PerformanceManagementSystem.DeletePMS + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion
    }
}