﻿using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Agency.AgencyTypeFile;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Agency.AgencyType;
using Portal.Gateway.ApiModel.Responses.Agency.AgencyTypeFile;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Agency.ViewModels.AgencyTypeFile;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Agency.Controllers
{

    [Area("Agency")]
    public class AgencyTypeFileController : BaseController<AgencyTypeFileController>
    {

        public AgencyTypeFileController(
              IOptions<AppSettings> appSettings,
              ICacheHelper cacheHelper)
          : base(appSettings, cacheHelper)
        {

        }

        public async Task<IActionResult> List(string encryptedAgencyTypeId)
        {
            if (string.IsNullOrEmpty(encryptedAgencyTypeId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("/");
            }

            int agencyTypeId = encryptedAgencyTypeId.ToDecryptInt();
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<AgencyTypeApiResponse>>
                (ApiMethodName.Agency.GetAgencyType + agencyTypeId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new FilterCountryByAgencyTypeViewModel 
            { 
                EncryptedAgencyTypeId = encryptedAgencyTypeId,
                AgencyTypeName = apiResponse.Data.AgencyTypeTranslations.Any(q => q.LanguageId == LanguageId) ?
                      apiResponse.Data.AgencyTypeTranslations.First(q => q.LanguageId == LanguageId).Name :
                      apiResponse.Data.AgencyTypeTranslations.FirstOrDefault().Name
            };

            return View(viewModel);
        }

        public async Task<IActionResult> GetPaginatedAgencyTypeFiles([DataSourceRequest] DataSourceRequest request, FilterCountryByAgencyTypeViewModel filterViewModel)
        {
            
            var paginationFilter = request.GetPaginationFilter(filterViewModel);
            int agencyTypeId = filterViewModel.EncryptedAgencyTypeId.ToDecryptInt();
            var apiRequest = new PaginatedCountryByAgencyTypeApiRequest
            {

                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                },
                AgencyTypeId = agencyTypeId
                

            };
            var apiResponse = await PortalHttpClientHelper
                                    .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedCountryByAgencyTypeApiResponse>>>
                                    (apiRequest,ApiMethodName.Agency.GetAgencyTypeFilesByAgencyType, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                                    .ConfigureAwait(false);

           


            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", result);
                return RedirectToAction("/");
            }

            var paginatedData = new List<CountryByAgencyTypeItemViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().Countries
                    .Select(p => new CountryByAgencyTypeItemViewModel
                    {
                        Country = p.Country,
                        EncryptedCountryId = p.CountryId.ToEncrypt(),
                        FileCount = p.FileCount
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });

         
        }

        [HttpGet]
        public async Task<IActionResult> ListByCountry(string encryptedAgencyTypeId, string encryptedCountryId)
        {
            if (string.IsNullOrEmpty(encryptedAgencyTypeId) || string.IsNullOrEmpty(encryptedCountryId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("/");
            }

            var apiRequest = new GetAgencyTypeFilesByAgencyTypeandCountryApiRequest
            {
                CountryId = encryptedCountryId.ToDecryptInt(),
                AgencyTypeId = encryptedAgencyTypeId.ToDecryptInt()
                

            };

            var apiResponse = await PortalHttpClientHelper
                                    .PostAsJsonAsync<ApiResponse<AgencyTypeFileListApiResponse>>
                                    (apiRequest,ApiMethodName.Agency.GetAgencyTypeFilesByAgencyTypeandCountry, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                                    .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", result);
                return RedirectToAction("/");
            }

            var viewModel = new AgencyTypeFileListViewModel()
            {
                EncryptedAgencyTypeId = encryptedAgencyTypeId,
                AgencyTypeName = apiResponse.Data.AgencyTypeName,
                EncryptedCountryId = encryptedCountryId,
                CountryName = apiResponse.Data.CountryName,
                AgencyTypeFiles = apiResponse.Data.AgencyTypeFiles.Select(p => new AgencyTypeFileListViewModel.AgencyTypeFileViewModel
                {
                    AgencyTypeFileTypeId = p.AgencyTypeFileId,
                    IsActive = p.IsActive
                })
                .ToList()

            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateAgencyTypeFiles(AgencyTypeFileListViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateAgencyTypeFilesApiRequest
            {
                AgencyTypeId = viewModel.EncryptedAgencyTypeId.ToDecryptInt(),
                CountryId = viewModel.EncryptedCountryId.ToDecryptInt(),
                AgencyTypeFiles = viewModel.AgencyTypeFiles.Select(p => new UpdateAgencyTypeFilesApiRequest.UpdateAgencyTypeFileApiRequest()
                {
                    AgencyTypeFileTypeId = p.AgencyTypeFileTypeId,
                    IsActive = p.IsActive
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Agency.UpdateAgencyTypeFiles, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

    }
}
