using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;

using Gateway.Http;
using Portal.Gateway.ApiModel.Responses.Management.RoleAction;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.QMS.Models.QMS.Responses;
using Portal.Gateway.UI.Areas.QMS.ViewModels.Dashboard;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;

namespace Portal.Gateway.UI.Areas.QMS.Controllers
{
    [Area("QMS")]
    public class QmsDashboardController : BaseController<QmsDashboardController>
    {
        #region ctor

        public QmsDashboardController(ICacheHelper cacheHelper, IOptions<AppSettings> appSettings) : base(appSettings, cacheHelper)
        {
        }

        #endregion

        #region Public Methods

        public async Task<IActionResult> Index()
        {
            var hasSelectedAuthorizedRole = false;

            var roleActions = await CacheHelper.GetRoleActionsAsync();

            hasSelectedAuthorizedRole = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
                .Any(p => p.RoleActions.Any(q => q.Action.Method == "IsAuthorizedForOfficerDashboard" && q.Action.IsActive));

            return View(UserSession.RoleIds.Any(r => hasSelectedAuthorizedRole) ?
                "~/Areas/QMS/Views/Dashboard/Officer/Index.cshtml" :
                "~/Areas/QMS/Views/Dashboard/Administration/Index.cshtml");
        }

        #region Administration

        [HttpPost]
        [Route("dashboard/load-waiting")]
        public async Task<IActionResult> LoadWaiting([FromBody] DashboardRequestViewModel request)
        {
            var viewModel = await GetWaitingData(request);

            return PartialView("~/Areas/QMS/Views/Dashboard/Administration/_WaitingSummary.cshtml", viewModel);
        }

        [HttpPost]
        [Route("dashboard/load-weekday")]
        public async Task<IActionResult> LoadWeekDay([FromBody] DashboardRequestViewModel request)
        {
            var viewModel = await GetWeekdayData(request);

            return PartialView("~/Areas/QMS/Views/Dashboard/Administration/_AverageTrafficByWeekdayView.cshtml", viewModel);
        }

        [HttpPost]
        [Route("dashboard/load-daily")]
        public async Task<IActionResult> LoadDaily([FromBody] DashboardRequestViewModel request)
        {
            var viewModel = await GetDailyData(request);

            return PartialView("~/Areas/QMS/Views/Dashboard/Administration/_AverageTrafficByDailyView.cshtml", viewModel);
        }

        [HttpPost]
        [Route("dashboard/load-hourly")]
        public async Task<IActionResult> LoadHourly([FromBody] DashboardRequestViewModel request)
        {
            var viewModel = await GetHourlyData(request);

            return PartialView("~/Areas/QMS/Views/Dashboard/Administration/_AverageTrafficByHourlyView.cshtml", viewModel);
        }

        [HttpPost]
        [Route("dashboard/applicationtype-chart")]
        public async Task<IActionResult> LoadAppointmentByApplicantType([FromBody] DashboardRequestViewModel request)
        {
            var viewModel = await GetAppointmentByApplicantTypeData(request);

            return PartialView("~/Areas/QMS/Views/Dashboard/Administration/_AppointmentByApplicantTypeSummary.cshtml", viewModel);
        }

        [HttpPost]
        [Route("dashboard/generatedtokenstatus-chart")]
        public async Task<IActionResult> LoadGeneratedTokenStatus([FromBody] DashboardRequestViewModel request)
        {
            var viewModel = await GetGeneratedTokenStatusData(request);

            return PartialView("~/Areas/QMS/Views/Dashboard/Administration/_GeneratedTokenStatusSummary.cshtml", viewModel);
        }

        [HttpPost]
        [Route("dashboard/totalapplicantsusedqms-chart")]
        public async Task<IActionResult> LoadTotalApplicantsUsedQms([FromBody] DashboardRequestViewModel request)
        {
            var viewModel = await GetTotalApplicantsUsedQmsData(request);

            return PartialView("~/Areas/QMS/Views/Dashboard/Administration/_TotalApplicantsUsedQMS.cshtml", viewModel);
        }

        #endregion

        #region Officer

        [HttpPost]
        [Route("dashboard/load-officer-total-application")]
        public async Task<IActionResult> LoadOfficerTotalApplicationTaken([FromBody] DashboardRequestViewModel request)
        {
            var viewModel = await GetOfficerTotalApplicationTakenData(request);

            return PartialView("~/Areas/QMS/Views/Dashboard/Officer/_TotalApplicationsTaken.cshtml", viewModel);
        }

        [HttpPost]
        [Route("dashboard/load-officer-total-actions")]
        public async Task<IActionResult> LoadOfficerTotalActionsApplied([FromBody] DashboardRequestViewModel request)
        {
            var viewModel = await GetOfficerTotalActionsAppliedData(request);

            return PartialView("~/Areas/QMS/Views/Dashboard/Officer/_TotalActionsApplied.cshtml", viewModel);
        }

        [HttpPost]
        [Route("dashboard/load-officer-hourly-service-time")]
        public async Task<IActionResult> LoadOfficerHourlyServiceTime([FromBody] DashboardRequestViewModel request)
        {
            var viewModel = await GetOfficerHourlyServiceTimeData(request);

            return PartialView("~/Areas/QMS/Views/Dashboard/Officer/_HourlyServiceTime.cshtml", viewModel);
        }

        #endregion

        #endregion

        #region Private Methods

        private async Task<OfficerHourlyServiceTimeViewModel> GetOfficerHourlyServiceTimeData(DashboardRequestViewModel request)
        {
            var apiResponse = await RestHttpClient.Create().Post<OfficerHourlyServiceTimeResult>($"{AppSettings.Qms.BaseApiUrl}{QmsEndPoint.DashboardOfficerHourlyServiceTime}",
                QMSApiDefaultRequestHeaders, request);

            if (apiResponse.Data == null)
                return new OfficerHourlyServiceTimeViewModel();

            var labels = new List<string>();
            var series = new List<double>();

            foreach (var data in apiResponse.Data)
            {
                labels.Add(data.ProcessHour < 10 ? $"0{data.ProcessHour}" : $"{data.ProcessHour}");
                series.Add(Math.Round(data.AverageMinutes,0));
            }

            var viewModel = new OfficerHourlyServiceTimeViewModel()
            {
                Series = series.ToArray(),
                Labels = labels.ToArray()
            };

            return viewModel;
        }

        private async Task<List<OfficerTotalActionsAppliedViewModel>> GetOfficerTotalActionsAppliedData(DashboardRequestViewModel request)
        {
            var apiResponse = await RestHttpClient.Create().Post<OfficerTotalActionsAppliedResult>($"{AppSettings.Qms.BaseApiUrl}{QmsEndPoint.DashboardOfficerTotalActionsApplied}",
                QMSApiDefaultRequestHeaders, request);

            if (apiResponse.Data == null)
                return new List<OfficerTotalActionsAppliedViewModel>();

            var labels = new List<string>
            {
                SiteResources.Assign,
                SiteResources.Cancelled,
                SiteResources.HoldOn,
                SiteResources.NotCompleted,
                SiteResources.Postpone,
                SiteResources.Recall,
                SiteResources.Completed
            };

            var countSeries = new List<int>
            {
                apiResponse.Data.AssignCount,
                apiResponse.Data.CancelledByApplicantCount,
                apiResponse.Data.HoldOnCount,
                apiResponse.Data.NotFoundCount,
                apiResponse.Data.PostponeCount,
                apiResponse.Data.RecallCount,
                apiResponse.Data.CompletedCount
            };

            var viewModel = new List<OfficerTotalActionsAppliedViewModel>();

            for (var i = 0; i < countSeries.Count; i++)
            {
                viewModel.Add(new OfficerTotalActionsAppliedViewModel()
                {
                    Category = labels[i],
                    Value = countSeries[i]
                });
            }

            return viewModel;
        }

        private async Task<OfficerTotalApplicationsTakenViewModel> GetOfficerTotalApplicationTakenData(
            DashboardRequestViewModel request)
        {
            var apiResponse = await RestHttpClient.Create().Post<OfficerTotalApplicationTakenResult>($"{AppSettings.Qms.BaseApiUrl}{QmsEndPoint.DashboardOfficerTotalApplicationTaken}",
                QMSApiDefaultRequestHeaders, request);

            if (apiResponse.Data == null)
                return new OfficerTotalApplicationsTakenViewModel();

            var labels = new List<DateTime>();
            var qmsSeries = new List<int>();
            var portalSeries = new List<int>();

            foreach (var data in apiResponse.Data.OrderBy(r => DateTime.ParseExact(r.Date,"dd/MM/yyyy", CultureInfo.InvariantCulture)))
            {
                labels.Add(DateTime.Parse(data.Date));
                qmsSeries.Add(data.QmsTotalApplicationCount);
                portalSeries.Add(data.PortalTotalApplicationCount);
            }

            var viewModel = new OfficerTotalApplicationsTakenViewModel
            {
                QmsSeries = qmsSeries.ToArray(),
                PortalSeries = portalSeries.ToArray(),
                Labels = labels.ToArray()
            };

            return viewModel;
        }

        private async Task<DashboardWaitingData> GetWaitingData(DashboardRequestViewModel request)
        {
            var hasAuthorizedForAllBranches = false;

            var roleActions = await CacheHelper.GetRoleActionsAsync();

            hasAuthorizedForAllBranches = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
                .Any(p => p.RoleActions.Any(q => q.Action.Method == "IsAuthorizedForAllBranchWaitingCounts" && q.Action.IsActive));

            if (!hasAuthorizedForAllBranches)
            {
                request.CountryId = 0;
                request.BranchId = (int)UserSession.BranchId;
            }

            var apiResponse = await RestHttpClient.Create().Post<DashboardWaitingResult>($"{AppSettings.Qms.BaseApiUrl}{QmsEndPoint.DashboardWaitingData}",
                QMSApiDefaultRequestHeaders, request);

            if (apiResponse.Data == null)
                return new DashboardWaitingData();

            var callNextSeries = new List<int>();
            var holdOnSeries = new List<int>();
            var labels = new List<string>();

            foreach (var data in apiResponse.Data)
            {
                callNextSeries.Add(data.CallNextWaiting);   
                holdOnSeries.Add(data.HoldOnWaiting);   
                labels.Add(data.BranchName);   
            }

            return new DashboardWaitingData
            {
                Label = labels,
                CallNextSeries = callNextSeries,
                HoldOnSeries = holdOnSeries
            };
        }

        private async Task<AverageTrafficByWeekday> GetWeekdayData(DashboardRequestViewModel request)
        {
            var apiResponse = await RestHttpClient.Create().Post<DashboardWeekdayResult>($"{AppSettings.Qms.BaseApiUrl}{QmsEndPoint.DashboardLoadWeekDay}",
                QMSApiDefaultRequestHeaders, request);

            if (apiResponse.Data == null)
                return new AverageTrafficByWeekday();

            var weekDayLabels = new List<string>
            {
                "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"
            };

            var weekDaySeries = new List<int>
            {
                apiResponse.Data.Sunday,
                apiResponse.Data.Monday,
                apiResponse.Data.Tuesday,
                apiResponse.Data.Wednesday,
                apiResponse.Data.Thursday,
                apiResponse.Data.Friday,
                apiResponse.Data.Saturday
            };

            var viewModel = new AverageTrafficByWeekday
            {
                Series = weekDaySeries.ToArray(),
                Labels = weekDayLabels.ToArray()
            };

            return viewModel;
        }

        private async Task<AverageTrafficByHour> GetHourlyData(DashboardRequestViewModel request)
        {
            var apiResponse = await RestHttpClient.Create().Post<DashboardHourResult>($"{AppSettings.Qms.BaseApiUrl}{QmsEndPoint.DashboardLoadHourly}",
                QMSApiDefaultRequestHeaders, request);

            if (apiResponse.Data == null)
                return new AverageTrafficByHour();

            var hourlyLabels = new List<string>
            {
                "00", "01", "02", "03", "04", "05","06",
                "07","08", "09", "10", "11", "12", "13",
                "14", "15", "16", "17", "18", "19", "20",
                "21", "22", "23"
            };

            var hourlySeries = new List<int>
            {
                apiResponse.Data.Hour0,
                apiResponse.Data.Hour1,
                apiResponse.Data.Hour2,
                apiResponse.Data.Hour3,
                apiResponse.Data.Hour4,
                apiResponse.Data.Hour5,
                apiResponse.Data.Hour6,
                apiResponse.Data.Hour7,
                apiResponse.Data.Hour8,
                apiResponse.Data.Hour9,
                apiResponse.Data.Hour10,
                apiResponse.Data.Hour11,
                apiResponse.Data.Hour12,
                apiResponse.Data.Hour13,
                apiResponse.Data.Hour14,
                apiResponse.Data.Hour15,
                apiResponse.Data.Hour16,
                apiResponse.Data.Hour17,
                apiResponse.Data.Hour18,
                apiResponse.Data.Hour19,
                apiResponse.Data.Hour20,
                apiResponse.Data.Hour21,
                apiResponse.Data.Hour22,
                apiResponse.Data.Hour23

            };

            var viewModel = new AverageTrafficByHour
            {
                Series = hourlySeries.ToArray(),
                Labels = hourlyLabels.ToArray()
            };

            return viewModel;
        }

        private async Task<AverageTrafficByDaily> GetDailyData(DashboardRequestViewModel request)
        {
            var apiResponse = await RestHttpClient.Create().Post<DashboardDailyResult>($"{AppSettings.Qms.BaseApiUrl}{QmsEndPoint.DashboardLoadDaily}",
                QMSApiDefaultRequestHeaders, request);

            if (apiResponse.Data == null)
                return new AverageTrafficByDaily();

            var dailyLabels = new List<DateTime>();
            var dailySeries = new List<int>();

            foreach (var item in apiResponse.Data)
            {
                dailyLabels.Add(item.Date);
                dailySeries.Add(item.DailyCount);
            }

            var viewModel = new AverageTrafficByDaily
            {
                Series = dailySeries.ToArray(),
                Labels = dailyLabels.ToArray()
            };

            return viewModel;
        }

        private async Task<AppointmentByApplicantTypeData> GetAppointmentByApplicantTypeData(DashboardRequestViewModel request)
        {
            var apiResponse = await RestHttpClient.Create().Post<DashboardAppointmentByApplicantTypeResult>($"{AppSettings.Qms.BaseApiUrl}{QmsEndPoint.DashboardAppointmentByApplicantType}",
                QMSApiDefaultRequestHeaders, request);

            if (apiResponse.Data == null)
                return new AppointmentByApplicantTypeData();

            var labels = new List<string>
            {
                SiteResources.Individual,
                SiteResources.Family,
                SiteResources.Group
            };

            var series = new List<int>
            {
                apiResponse.Data.IndividualCount,
                apiResponse.Data.FamilyCount,
                apiResponse.Data.GroupCount
            };

            var viewModel = new AppointmentByApplicantTypeData
            {
                Series = series.ToArray(),
                Labels = labels.ToArray()
            };

            return viewModel;
        }

        private async Task<GeneratedTokenStatusData> GetGeneratedTokenStatusData(DashboardRequestViewModel request)
        {
            var apiResponse = await RestHttpClient.Create().Post<DashboardGeneratedTokenStatusResult>($"{AppSettings.Qms.BaseApiUrl}{QmsEndPoint.DashboardGeneratedTokenStatus}",
                QMSApiDefaultRequestHeaders, request);

            if (apiResponse.Data == null)
                return new GeneratedTokenStatusData();

            var labels = new List<string>
            {
                SiteResources.Assign,
                SiteResources.Cancelled,
                SiteResources.HoldOn,
                SiteResources.NotCompleted,
                SiteResources.Postpone,
                SiteResources.Recall,
                SiteResources.Completed
            };

            var series = new List<int>
            {
                apiResponse.Data.AssignCount,
                apiResponse.Data.CancelledByApplicantCount,
                apiResponse.Data.HoldOnCount,
                apiResponse.Data.NotFoundCount,
                apiResponse.Data.PostponeCount,
                apiResponse.Data.RecallCount,
                apiResponse.Data.CompletedCount
            };

            var viewModel = new GeneratedTokenStatusData
            {
                Series = series.ToArray(),
                Labels = labels.ToArray()
            };

            return viewModel;
        }

        private async Task<TotalApplicantsUsedQmsData> GetTotalApplicantsUsedQmsData(DashboardRequestViewModel request)
        {
            var apiResponse = await RestHttpClient.Create().Post<DashboardTotalApplicantsUsedQmsResult>($"{AppSettings.Qms.BaseApiUrl}{QmsEndPoint.DashboardTotalApplicantsUsedQms}",
                QMSApiDefaultRequestHeaders, request);

            if (apiResponse.Data == null)
                return new TotalApplicantsUsedQmsData();

            var groupedSeries = apiResponse.Data.GroupBy(r => r.BranchName);

            var series = new List<DirectionSeries>();

            series.AddRange(groupedSeries.Select(r => new DirectionSeries
            {
                Name = r.Key,
                Data = r.Select(k => new SeriesData()
                {
                    X = DateTime.SpecifyKind(DateTime.Parse(k.Date),DateTimeKind.Utc),
                    Y = k.TotalApplicantCount
                }).OrderBy(o => o.X).ToList()
            }));

            var viewModel = new TotalApplicantsUsedQmsData
            {
                Series = series,
            };

            return viewModel;
        }

        //[HttpGet]
        //[Route("dashboard/realtime-update")]
        //public async Task<RefreshData> GetRealTimeWaitingData(string branchId)
        //{
        //    var request = new DashboardRequestViewModel { BranchId = Convert.ToInt32(branchId) };

        //    var apiResponse = await RestHttpClient.Create().Post<DashboardWaitingResult>($"{AppSettings.Qms.BaseApiUrl}{QmsEndPoint.DashboardWaitingData}",
        //        QMSApiDefaultRequestHeaders, request);

        //    if (apiResponse.Data == null)
        //        return new RefreshData();

        //    var callNextSeries = new List<int>();
        //    var holdOnSeries = new List<int>();
        //    var labels = new List<string>();

        //    foreach (var data in apiResponse.Data)
        //    {
        //        callNextSeries.Add(data.CallNextWaiting);
        //        holdOnSeries.Add(data.HoldOnWaiting);
        //        labels.Add(data.BranchName);
        //    }

        //    return new RefreshData
        //    {
        //        Label = labels.ToArray(),
        //        CallNextSeries = callNextSeries.ToArray(),
        //        HoldOnSeries = holdOnSeries.ToArray()
        //    };
        //}

        #endregion
    }
}