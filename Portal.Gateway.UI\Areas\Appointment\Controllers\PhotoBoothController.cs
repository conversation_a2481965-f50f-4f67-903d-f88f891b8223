﻿using BarcodeStandard;
using ClosedXML.Excel;
using Gateway.Barcode;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Appointment.Application;
using Portal.Gateway.ApiModel.Requests.Appointment.PhotoBooth;
using Portal.Gateway.ApiModel.Requests.Report;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Appointment.Application;
using Portal.Gateway.ApiModel.Responses.Appointment.PhotoBooth;
using Portal.Gateway.ApiModel.Responses.Management.RoleAction;
using Portal.Gateway.ApiModel.Responses.Report;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Dto.Report.Common.Report_11;
using Portal.Gateway.Contracts.Entities.Dto.Report.Common.Report_3;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.Application;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.PhotoBooth;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace Portal.Gateway.UI.Areas.Appointment.Controllers
{
	[Area("Appointment")]
	public class PhotoBoothController : BaseController<PhotoBoothController>
	{
        public PhotoBoothController(
				IOptions<AppSettings> appSettings,
				ICacheHelper cacheHelper)
			: base(appSettings, cacheHelper)
		{
        }

		#region Add

		public async Task<IActionResult> PartialAddPhotoBooth(string encryptedApplicationId, int BranchId)
		{
			var viewModel = new AddPhotoBoothViewModel();
			if (BranchId == 33 || BranchId == 8 || BranchId == 5 || BranchId == 3 || BranchId == 42 || BranchId == 9 || BranchId == 7 || BranchId == 11 || BranchId == 41 || BranchId == 40 || BranchId == 4 || BranchId == 13 || BranchId == 6 || BranchId == 10)
			{ // Al Saydiya, Basra, Duhok, Erbil, Karbala, Karrada, Kirkuk, Musul, Najaf, Ramadi, Suleymaniyah, Test, Waziriyah, Zakho
				viewModel.CurrencyList = EnumHelper.GetEnumAsDictionary(typeof(CurrencyType)).Select(p => new SelectListItem()
				{
					Text = p.Value,
					Value = p.Key.ToString()
				}).Where(p => p.Value == "2" || p.Value == "6").ToList();
			}
			else
			{
				viewModel.CurrencyList = EnumHelper.GetEnumAsDictionary(typeof(CurrencyType)).Select(p => new SelectListItem()
				{
					Text = p.Value,
					Value = p.Key.ToString()
				}).ToList();
			}

			var apiResponse = new ApiResponse<ApplicationSummaryApiResponse>();
			if (!string.IsNullOrEmpty(encryptedApplicationId))
			{
				int applicationId = encryptedApplicationId.ToDecryptInt();

				apiResponse = await PortalHttpClientHelper
					.GetAsync<ApiResponse<ApplicationSummaryApiResponse>>
					(ApiMethodName.Appointment.GetSanitizedApplicationSummary + applicationId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				viewModel.ApplicationId = apiResponse.Data.Id;
				viewModel.Name = apiResponse.Data.Name;
				viewModel.Surname = apiResponse.Data.Surname;
				viewModel.PassportNumber = apiResponse.Data.PassportNumber;
			}

			viewModel.CurrencyList.Insert(0, new SelectListItem() { Text = SiteResources.Select, Value = "" });

			return PartialView("_AddPhotoBooth", viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> AddPhotoBooth(AddPhotoBoothViewModel viewModel)
		{
			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var apiRequest = new AddPhotoBoothApiRequest
			{
				BranchId = UserSession.BranchId.Value,
				ApplicationId = viewModel.ApplicationId,
				Name = viewModel.Name,
				Surname = viewModel.Surname,
				PassportNumber = viewModel.PassportNumber,
				Price = viewModel.Price,
				CurrencyId = viewModel.CurrencyId,
				Status = (int)PhotoBoothStatus.Ordered,
				RequestedBy = UserSession.UserId,
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<AddApiResponse>>
				(apiRequest, ApiMethodName.Appointment.AddPhotoBooth, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		#endregion

		#region Delete

		[HttpDelete]
		public async Task<IActionResult> DeletePhotoBooth(string encryptedId)
		{
			int id = encryptedId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.DeleteAsync<ApiResponse<DeleteApiResponse>>
				(ApiMethodName.Appointment.DeletePhotoBooth + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		[HttpDelete]
		public async Task<IActionResult> DeleteOrderedPhotoBooth(string encryptedId)
		{
			//created for role problem in ui
			int id = encryptedId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.DeleteAsync<ApiResponse<DeleteApiResponse>>
					(ApiMethodName.Appointment.DeletePhotoBooth + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		#endregion

		#region BadShot

		[HttpPost]
		public async Task<IActionResult> BadShotPhotoBooth(string encryptedId)
		{
			int photoBoothId = encryptedId.ToDecryptInt();
			var enLanguageId = (int)Language.English;
			var enApiRequestHeader = PortalGatewayApiDefaultRequestHeaders;
			enApiRequestHeader.Remove("languageId");
			enApiRequestHeader.Add("languageId", enLanguageId.ToString());

			var apiResponseGetPhotoBooth = await PortalHttpClientHelper
				.GetAsync<ApiResponse<PhotoBoothApiResponse>>
				(ApiMethodName.Appointment.GetPhotoBooth + photoBoothId, AppSettings.PortalGatewayApiUrl, enApiRequestHeader)
				.ConfigureAwait(false);


			if (apiResponseGetPhotoBooth.Data.Status == (int)PhotoBoothStatus.Used)
			{

				var apiAddPhotoBoothRequest = new AddPhotoBoothApiRequest
				{
					BranchId = UserSession.BranchId.Value,
					ApplicationId = apiResponseGetPhotoBooth.Data.ApplicationId,
					Name = apiResponseGetPhotoBooth.Data.Name,
					Surname = apiResponseGetPhotoBooth.Data.Surname,
					PassportNumber = apiResponseGetPhotoBooth.Data.PassportNumber,
					Price = (decimal)apiResponseGetPhotoBooth.Data.Price,
					CurrencyId = (int)apiResponseGetPhotoBooth.Data.CurrencyId,
					Status = (int)PhotoBoothStatus.Expired,
					RequestedBy = UserSession.UserId,
				};

				var apiAddPhotoBoothResponse = await PortalHttpClientHelper
					.PostAsJsonAsync<ApiResponse<AddApiResponse>>
					(apiAddPhotoBoothRequest, ApiMethodName.Appointment.AddPhotoBooth, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				if (!await apiAddPhotoBoothResponse.Validate(out ResultModel resultAddPhotoBooth).ConfigureAwait(false))
					return Json(resultAddPhotoBooth);


				var apiResponseUpdatePhotoBoothApplicationStatus = await PortalHttpClientHelper
						   .GetAsync<ApiResponse<UpdateApiResponse>>
						   ($"{ApiMethodName.Appointment.UpdatePhotoBoothApplicationStatus + photoBoothId}/{(int)PhotoBoothStatus.BadShot}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
						   .ConfigureAwait(false);
			}

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		#endregion

		#region ServiceError

		[HttpPost]
		public async Task<IActionResult> ServiceErrorPhotoBooth(string encryptedId)
		{
			int photoBoothId = encryptedId.ToDecryptInt();
			var enLanguageId = (int)Language.English;
			var enApiRequestHeader = PortalGatewayApiDefaultRequestHeaders;
			enApiRequestHeader.Remove("languageId");
			enApiRequestHeader.Add("languageId", enLanguageId.ToString());

			var apiResponseGetPhotoBooth = await PortalHttpClientHelper
				.GetAsync<ApiResponse<PhotoBoothApiResponse>>
				(ApiMethodName.Appointment.GetPhotoBooth + photoBoothId, AppSettings.PortalGatewayApiUrl, enApiRequestHeader)
				.ConfigureAwait(false);


			if (apiResponseGetPhotoBooth.Data.Status == (int)PhotoBoothStatus.Ordered)
			{

				var apiAddPhotoBoothRequest = new AddPhotoBoothApiRequest
				{
					BranchId = UserSession.BranchId.Value,
					ApplicationId = apiResponseGetPhotoBooth.Data.ApplicationId,
					Name = apiResponseGetPhotoBooth.Data.Name,
					Surname = apiResponseGetPhotoBooth.Data.Surname,
					PassportNumber = apiResponseGetPhotoBooth.Data.PassportNumber,
					Price = (decimal)apiResponseGetPhotoBooth.Data.Price,
					CurrencyId = (int)apiResponseGetPhotoBooth.Data.CurrencyId,
					Status = (int)PhotoBoothStatus.Expired,
					RequestedBy = UserSession.UserId,
				};

				var apiAddPhotoBoothResponse = await PortalHttpClientHelper
					.PostAsJsonAsync<ApiResponse<AddApiResponse>>
					(apiAddPhotoBoothRequest, ApiMethodName.Appointment.AddPhotoBooth, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				if (!await apiAddPhotoBoothResponse.Validate(out ResultModel resultAddPhotoBooth).ConfigureAwait(false))
					return Json(resultAddPhotoBooth);


				var apiResponseUpdatePhotoBoothApplicationStatus = await PortalHttpClientHelper
						   .GetAsync<ApiResponse<UpdateApiResponse>>
						   ($"{ApiMethodName.Appointment.UpdatePhotoBoothApplicationStatus + photoBoothId}/{(int)PhotoBoothStatus.ServiceError}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
						   .ConfigureAwait(false);
			}

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		#endregion
		#region Get

		[ActionAttribute(IsMenuItem = true)]
		public async Task<IActionResult> PhotoBoothList()
		{
			var isPageAuthorized = false;

            var roleActions = await CacheHelper.GetRoleActionsAsync();

            isPageAuthorized = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
                .Any(p => p.RoleActions.Any(q => q.Action.Method == "ShowCompletedPhotobooths" && q.Action.IsActive));

            if (UserSession.BranchId == null)
			{
				return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
			}

			var filterViewModel = new FilterPhotoBoothViewModel
			{
				IsPhotoBoothReferenced = true,
				IsReferenced = true,
				IsPageAuthorized = isPageAuthorized,
				BranchId = UserSession.BranchId.Value
			};

			return View(filterViewModel);
		}

		[ActionAttribute(IsMenuItem = true)]
		public async Task<IActionResult> ReferencedList()
		{
			var isPageAuthorized = false;

            var roleActions = await CacheHelper.GetRoleActionsAsync();

            isPageAuthorized = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
                .Any(p => p.RoleActions.Any(q => q.Action.Method == "ShowCompletedPhotobooths" && q.Action.IsActive));

            if (UserSession.BranchId == null)
			{
				return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
			}

			var filterViewModel = new FilterPhotoBoothViewModel
			{
				IsReferenced = true,
				IsPageAuthorized = isPageAuthorized,
				BranchId = UserSession.BranchId.Value
			};

			return View(filterViewModel);
		}

		[ActionAttribute(IsMenuItem = true)]
		public async Task<IActionResult> NonReferencedList()
		{
			var isPageAuthorized = false;

            var roleActions = await CacheHelper.GetRoleActionsAsync();

            isPageAuthorized = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
                .Any(p => p.RoleActions.Any(q => q.Action.Method == "ShowCompletedPhotobooths" && q.Action.IsActive));

            if (UserSession.BranchId == null)
			{
				return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
			}

			var filterViewModel = new FilterPhotoBoothViewModel()
			{
				IsReferenced = false,
				IsPageAuthorized = isPageAuthorized,
				BranchId = UserSession.BranchId.Value
			};

			return View(filterViewModel);
		}

		public async Task<IActionResult> GetPaginatedPhotoBooths([DataSourceRequest] DataSourceRequest request, FilterPhotoBoothViewModel filterViewModel)
		{
			var paginationFilter = request.GetPaginationFilter(filterViewModel);
			var apiRequest = new PaginatedPhotoBoothApiRequest
			{
				BranchId = UserSession.BranchId.Value,
				FilterName = filterViewModel.FilterName,
				FilterSurname = filterViewModel.FilterSurname,
				FilterPassportNumber = filterViewModel.FilterPassportNumber,
				Status = filterViewModel.Status,
				ShowPastDays = filterViewModel.FilterShowPastDays,
				StartDate = filterViewModel.FilterStartDate,
				EndDate = filterViewModel.FilterEndDate,
				FilterBadShot = filterViewModel.FilterBadShot,
				FilterDeleted = filterViewModel.FilterDeleted,
				FilterExpired = filterViewModel.FilterExpired,
				FilterUsed = filterViewModel.FilterUsed,
				Pagination = new PaginationApiRequest
				{
					Page = paginationFilter.Page,
					PageSize = paginationFilter.PageSize,
					OrderBy = paginationFilter.OrderBy,
					SortDirection = paginationFilter.SortDirection
				}
			};

			var apiMethod = filterViewModel.IsReferenced ?
				(filterViewModel.IsPhotoBoothReferenced ? ApiMethodName.Appointment.GetPaginatedPhotoBooth : ApiMethodName.Appointment.GetPaginatedReferencedPhotoBooth) :
				ApiMethodName.Appointment.GetPaginatedNonReferencedPhotoBooth;

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedPhotoBoothsApiResponse>>>
				(apiRequest, apiMethod, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			var paginatedData = new List<PhotoBoothViewModel>();
			if (apiResponse.Data != null && apiResponse.Data.Items.Any())
			{
				var today = (int)apiResponse.Data.Items.First().LocalTimeOffset - apiResponse.Data.Items.First().LocalTimeOffset == 0 ? DateTime.Now.AddHours((int)apiResponse.Data.Items.First().LocalTimeOffset) :
					(int)apiResponse.Data.Items.First().LocalTimeOffset - apiResponse.Data.Items.First().LocalTimeOffset < 0 ? DateTime.Now.AddHours((int)apiResponse.Data.Items.First().LocalTimeOffset).AddMinutes(30) :
					DateTime.Now.AddHours((int)apiResponse.Data.Items.First().LocalTimeOffset).AddMinutes(-30);

				paginatedData = apiResponse.Data.Items.First().PhotoBoots
					.Select(p => new PhotoBoothViewModel
					{
						EncryptedId = p.Id.ToEncrypt(),
						EncryptedApplicationId = p.ApplicationId.ToEncrypt(),
						Name = p.Name,
						Surname = p.Surname,
						PassportNumber = p.PassportNumber,
						Price = p.Price,
						Currency = EnumHelper.GetEnumDescription(typeof(CurrencyType), p.CurrencyId.ToString()),
						Status = EnumHelper.GetEnumDescription(typeof(PhotoBoothStatus), p.Status.ToString()),
						IsNewStatus = p.Status != (int)PhotoBoothStatus.NotOrdered ? false : true,
						IsAvailable = p.ExpireDateTime < today ? false : (p.Status != (int)PhotoBoothStatus.Ordered ? false : true)
					}).ToList();
			}

			return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
		}

		public async Task<IActionResult> GetPhotoBooth(string encryptedId, bool isReferenced)
		{
			if (string.IsNullOrEmpty(encryptedId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				if (isReferenced)
				{
					return RedirectToAction("ReferencedList", "PhotoBooth", new { Area = "Appointment" });
				}
				else
				{
					return RedirectToAction("NonReferencedList", "PhotoBooth", new { Area = "Appointment" });
				}
			}

			int applicationId = encryptedId.ToDecryptInt();

			var enLanguageId = (int)Language.English;
			var enApiRequestHeader = PortalGatewayApiDefaultRequestHeaders;
			enApiRequestHeader.Remove("languageId");
			enApiRequestHeader.Add("languageId", enLanguageId.ToString());

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<PhotoBoothApiResponse>>
				(ApiMethodName.Appointment.GetPhotoBooth + applicationId, AppSettings.PortalGatewayApiUrl, enApiRequestHeader)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				if (isReferenced)
				{
					return RedirectToAction("ReferencedList", "PhotoBooth", new { Area = "Appointment" });
				}
				else
				{
					return RedirectToAction("NonReferencedList", "PhotoBooth", new { Area = "Appointment" });
				}
			}

			var viewModel = new PhotoBoothViewModel()
			{
				EncryptedId = encryptedId,
				Name = apiResponse.Data.Name,
				Surname = apiResponse.Data.Surname,
				PassportNumber = apiResponse.Data.PassportNumber,
				Price = apiResponse.Data.Price,
				Currency = EnumHelper.GetEnumDescription(typeof(CurrencyType), apiResponse.Data.CurrencyId.ToString()),
				ExpireDateTime = apiResponse.Data.ExpireDateTime
			};

			return View(viewModel);
		}

		public async Task<IActionResult> CreateBarcode(string encryptedId)
		{
			int photoBoothId = encryptedId.ToDecryptInt();
			string newPhotoBoothId = "";
			var enLanguageId = (int)Language.English;
			var enApiRequestHeader = PortalGatewayApiDefaultRequestHeaders;
			enApiRequestHeader.Remove("languageId");
			enApiRequestHeader.Add("languageId", enLanguageId.ToString());

			var apiResponseGetPhotoBooth = await PortalHttpClientHelper
				.GetAsync<ApiResponse<PhotoBoothApiResponse>>
				(ApiMethodName.Appointment.GetPhotoBooth + photoBoothId, AppSettings.PortalGatewayApiUrl, enApiRequestHeader)
				.ConfigureAwait(false);

			newPhotoBoothId = photoBoothId.ToString();

			if (apiResponseGetPhotoBooth.Data.Status == (int)PhotoBoothStatus.Expired)
			{

				var apiAddPhotoBoothRequest = new AddPhotoBoothApiRequest
				{
					BranchId = UserSession.BranchId.Value,
					ApplicationId = apiResponseGetPhotoBooth.Data.ApplicationId,
					Name = apiResponseGetPhotoBooth.Data.Name,
					Surname = apiResponseGetPhotoBooth.Data.Surname,
					PassportNumber = apiResponseGetPhotoBooth.Data.PassportNumber,
					Price = (decimal)apiResponseGetPhotoBooth.Data.Price,
					CurrencyId = (int)apiResponseGetPhotoBooth.Data.CurrencyId,
					Status = (int)PhotoBoothStatus.Expired,
					RequestedBy = UserSession.UserId,
				};

				var apiAddPhotoBoothResponse = await PortalHttpClientHelper
					.PostAsJsonAsync<ApiResponse<AddApiResponse>>
					(apiAddPhotoBoothRequest, ApiMethodName.Appointment.AddPhotoBooth, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				if (!await apiAddPhotoBoothResponse.Validate(out ResultModel resultAddPhotoBooth).ConfigureAwait(false))
					return Json(resultAddPhotoBooth);


				var apiResponseUpdatePhotoBoothApplicationStatus = await PortalHttpClientHelper
						   .GetAsync<ApiResponse<UpdateApiResponse>>
						   ($"{ApiMethodName.Appointment.UpdatePhotoBoothApplicationStatus + photoBoothId}/{(int)PhotoBoothStatus.Expired}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
						   .ConfigureAwait(false);

				newPhotoBoothId = apiAddPhotoBoothResponse.Data.Id.ToString();

			}

            return File(BarcodeClient.CreateBarcodeByteArray(Int32.Parse(newPhotoBoothId).ToString("9000000000000"), new SKColor(33, 37, 41), BarcodeClient.DefaultWidth, BarcodeClient.PhotoboothHeight), "image/png");
        }

        #endregion

        #region Report
        public async Task<FileContentResult> GetPhotoBoothReport(FilterPhotoBoothViewModel filterViewModel)
		{
			var reportName = EnumResources.ReportTypePhotobooth.ToTitleCase();
			var reportCreatedBy = UserSession.FullName;
			var reportDate = DateTime.UtcNow.Date;

			var apiRequest = new GenerateReportApiRequest
			{
				UserId = UserSession.UserId,
				ReportTypeId = 11
			};
			apiRequest.Request = new Report_PhotoboothRequest
			{
				BranchIds = new List<int>() { filterViewModel.BranchId },
				StartDate = filterViewModel.FilterStartDate != null ? filterViewModel.FilterStartDate.GetValueOrDefault() : DateTime.Now,
				EndDate = filterViewModel.FilterEndDate != null ? filterViewModel.FilterEndDate.GetValueOrDefault() : DateTime.Now
			};
			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<GenerateReportApiResponse>>
				(apiRequest, ApiMethodName.Report.Generate, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			Report_PhotoboothResponse response = new Report_PhotoboothResponse();
			response = JsonConvert.DeserializeObject<Report_PhotoboothResponse>(apiResponse.Data.Result.ToString());

			using (var workbook = new XLWorkbook())
			{
				foreach (var branchData in response.Branches)
				{
					//var branch = $"{rgx.Replace(branchData.BranchName, string.Empty)}"; //Note: removed for displaying special characters in turkish language
					var branch = branchData.BranchName;
					var sheetName = branch.Length > 30 ?
							branch.Substring(0, 30) : branch;

					var worksheet = workbook.Worksheets.Add(sheetName);

					var currentRow = 1;
					var headerRow = -1;

					worksheet.Cell(currentRow, 1).Value = reportName;
					var rangeTitle = worksheet.Range(worksheet.Cell(currentRow, 1), worksheet.Cell(currentRow, 5));
					rangeTitle.Merge().Style.Font.SetBold().Font.FontSize = 14;
					currentRow++;
					currentRow++;

					worksheet.Cell(currentRow, 1).Value = SiteResources.BranchName.ToTitleCase();
					worksheet.Cell(currentRow, 1).Style.Font.SetBold();
					currentRow++;
					worksheet.Cell(currentRow, 1).Value = branchData.BranchName;
					currentRow++;
					currentRow++;

					worksheet.Cell(currentRow, 1).Value = SiteResources.CreatedBy.ToTitleCase();
					worksheet.Cell(currentRow, 1).Style.Font.SetBold();
					currentRow++;
					worksheet.Cell(currentRow, 1).Value = reportCreatedBy;
					currentRow++;
					currentRow++;

					worksheet.Cell(currentRow, 1).Value = SiteResources.ReportDate.ToTitleCase();
					worksheet.Cell(currentRow, 1).Style.Font.SetBold();
					worksheet.Cell(currentRow, 3).Value = SiteResources.StartDate.ToTitleCase();
					worksheet.Cell(currentRow, 3).Style.Font.SetBold();
					worksheet.Cell(currentRow, 5).Value = SiteResources.EndDate.ToTitleCase();
					worksheet.Cell(currentRow, 5).Style.Font.SetBold();
					currentRow++;

					worksheet.Cell(currentRow, 1).Value = reportDate;
					worksheet.Cell(currentRow, 1).Style.DateFormat.Format = SiteResources.DatePickerFormatView;
					worksheet.Cell(currentRow, 3).Value = filterViewModel.FilterStartDate != null ? filterViewModel.FilterStartDate.GetValueOrDefault() : DateTime.Now;
					worksheet.Cell(currentRow, 3).Style.DateFormat.Format = SiteResources.DatePickerFormatView;
					worksheet.Cell(currentRow, 5).Value = filterViewModel.FilterEndDate != null ? filterViewModel.FilterEndDate.GetValueOrDefault() : DateTime.Now;
					worksheet.Cell(currentRow, 5).Style.DateFormat.Format = SiteResources.DatePickerFormatView;

					currentRow++;
					currentRow++;

					headerRow = currentRow;
					worksheet.Cell(currentRow, 1).Value = SiteResources.OrderNumber.ToTitleCase();
					worksheet.Cell(currentRow, 2).Value = SiteResources.BarcodeNumber.ToTitleCase();
					worksheet.Cell(currentRow, 3).Value = SiteResources.Name.ToTitleCase();
					worksheet.Cell(currentRow, 4).Value = SiteResources.Surname.ToTitleCase();
					worksheet.Cell(currentRow, 5).Value = SiteResources.Price.ToTitleCase();
					worksheet.Cell(currentRow, 6).Value = SiteResources.Currency.ToTitleCase();
					worksheet.Cell(currentRow, 7).Value = SiteResources.PhotoDate.ToTitleCase();
					worksheet.Cell(currentRow, 8).Value = SiteResources.WithReference.ToTitleCase() + " / " + SiteResources.WithoutReference.ToTitleCase();
					worksheet.Cell(currentRow, 9).Value = SiteResources.ExtraFee.ToTitleCase() + " ( PrimeTime/ MBS/ VIP/ Platinium ) ";
					worksheet.Cell(currentRow, 10).Value = SiteResources.ProcessedBy.ToTitleCase();
					worksheet.Cell(currentRow, 11).Value = SiteResources.CreatedBy.ToTitleCase();
					worksheet.Cell(currentRow, 12).Value = SiteResources.Status.ToTitleCase();

					currentRow++;

					for (int i = 0; i < branchData.Photobooths.Count(); i++)
					{
						var data = branchData.Photobooths.ElementAt(i);
						worksheet.Cell(currentRow, 1).Value = i + 1;
						worksheet.Cell(currentRow, 2).Value = "'" + data.BarcodeNumber;
						worksheet.Cell(currentRow, 2).DataType = XLDataType.Text;
						worksheet.Cell(currentRow, 3).Value = data.Name;
						worksheet.Cell(currentRow, 4).Value = data.Surname;
						worksheet.Cell(currentRow, 5).Value = data.Amount;
						worksheet.Cell(currentRow, 6).Value = EnumHelper.GetEnumDescription(typeof(CurrencyType), data.CurrencyId.ToString());
						worksheet.Cell(currentRow, 7).Value = data.ExpireDateTime.Date;
						worksheet.Cell(currentRow, 7).Style.DateFormat.Format = SiteResources.DatePickerFormatView;
						worksheet.Cell(currentRow, 8).Value = data.ApplicationId.HasValue ? SiteResources.WithReference : SiteResources.WithoutReference;
						worksheet.Cell(currentRow, 9).Value = data.ExtraFee;
						worksheet.Cell(currentRow, 10).Value = data.ProcessedBy;
						worksheet.Cell(currentRow, 11).Value = data.CreatedBy;
						worksheet.Cell(currentRow, 12).Value = EnumHelper.GetEnumDescription(typeof(PhotoBoothStatus), data.Status.ToString());

						currentRow++;
					}

					var rangeTable = worksheet.Range(worksheet.Cell(headerRow, 1), worksheet.Cell(currentRow - 1, 12));
					rangeTable.Style.Border.SetTopBorder(XLBorderStyleValues.Thin);
					rangeTable.Style.Border.SetBottomBorder(XLBorderStyleValues.Thin);
					rangeTable.Style.Border.SetLeftBorder(XLBorderStyleValues.Thin);
					rangeTable.Style.Border.SetRightBorder(XLBorderStyleValues.Thin);
					rangeTable.Style.Border.SetInsideBorder(XLBorderStyleValues.Thin);
					rangeTable.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);

					var rangeAlignment = worksheet.Range(worksheet.Cell(1, 1), worksheet.Cell(currentRow - 1, 12));
					rangeAlignment.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);

					for (int i = 1; i <= 12; i++)
					{
						worksheet.Column(i).AdjustToContents();
					}

					currentRow++;
				}
				using (var stream = new MemoryStream())
				{
					workbook.SaveAs(stream);
					var content = stream.ToArray();
					return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{reportName} - {reportDate.ToString("ddMMyyyy")}.xlsx");
				}
			}
		}

	}
	#endregion

}