﻿using FluentValidation;
using Gateway.Biometrics.Application.Appeal.DTO;
using Gateway.Biometrics.Application.Appeal.Validator;
using Gateway.Biometrics.Application.Chunk.DTO;
using Gateway.Biometrics.Resources;
using Gateway.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.Chunk.Validator
{

    public class SaveDataByChunksValidator : AbstractValidator<SaveDataByChunksRequest>
    {
        public SaveDataByChunksValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.FromOffline &&!item.RecordId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.RecordId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.FromOffline && item.UniqueId.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.UniqueId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.TotalPart.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.TotalPart)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Part.IsNumericAndGreaterOrEqualZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Part)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.ChunkData.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.ChunkData)));
            });
           
        }
    }

}
