﻿using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Management.Branch;
using Portal.Gateway.ApiModel.Requests.Management.BranchIcrNote;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.ApiModel.Responses.Management.BranchIcrNote;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.Branch;
using Portal.Gateway.UI.Areas.Management.ViewModels.General;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Gateway.Extensions;
using Portal.Gateway.Contracts.Entities.Dto.Management.Branch.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.Branch.Responses;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class BranchController : BaseController<BranchController>
    {
        public BranchController(
                IOptions<AppSettings> appSettings,
                ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {
        }

        #region Add

        public IActionResult PartialAddBranch()
        {
            var viewModel = new AddBranchViewModel();

            foreach (var item in EnumHelper.GetEnumAsDictionary(typeof(BranchTranslationLanguage)))
            {
                viewModel.BranchTranslations.Add(new AddBranchTranslationViewModel { LanguageId = item.Key });
            }
            foreach (var item in EnumHelper.GetEnumAsDictionary(typeof(BranchDataTranslationLanguage)))
            {
                viewModel.BranchDataTranslations.Add(new AddBranchDataTranslationViewModel { LanguageId = item.Key });
            }

            return PartialView("_AddBranch", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddBranch(AddBranchViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddBranchApiRequest
            {
                CountryId = viewModel.CountryId,
                Address = viewModel.Address,
                Email = viewModel.Email,
                Telephone = viewModel.Telephone,
                CityName = viewModel.CityName,
                Mission = viewModel.Mission,
                CorporateName = viewModel.CorporateName,
                InvoiceNumber = viewModel.InvoiceNumber,
                SapBranchId = viewModel.SapBranchId,
                TimeZoneOffset = viewModel.TimeZoneOffset,
                CheckRejectedStatus = viewModel.CheckRejectedStatus,
                CheckRejectedStatusPeriod = viewModel.CheckRejectedStatusPeriod,
                CheckUnrealDocumentStatus = viewModel.CheckUnrealDocumentStatus,
                CheckUnrealDocumentStatusPeriod = viewModel.CheckUnrealDocumentStatusPeriod,
                CheckRejectionWithCountryEntryBannedStatus = viewModel.CheckRejectionWithCountryEntryBannedStatus,
                CheckRejectionWithCountryEntryBannedStatusPeriod = viewModel.CheckRejectionWithCountryEntryBannedStatusPeriod,
                RejectionRefundDonePermissionNumber = viewModel.RejectionRefundDonePermissionNumber,
                IsValidAMS = viewModel.IsValidAMS,
                IsSlotTypesConnected = viewModel.IsSlotTypesConnected,
                ConnectedSlotTypeId = viewModel.ConnectedSlotTypeId,
                IsPassportScanRequired = viewModel.IsPassportScanRequired,
                EmailProviderId = viewModel.EmailProviderId,
                SmsProviderId = viewModel.SmsProviderId,
                QmsWalkinVipControl = viewModel.QmsWalkinVipControl,
                QueueMaticPlaylistId = viewModel.QueueMaticPlaylistId,
                SmsSender = viewModel.SmsSender,
                IsSendByPrefix = viewModel.IsSendByPrefix,
                MaxAppointmentDay = viewModel.MaxAppointmentDay,
                BasicGuidelineId = viewModel.BasicGuidelineId,
                InsuranceProviderId = viewModel.InsuranceProviderId,
                IsCargoIntegrationActive = viewModel.IsCargoIntegrationActive,
                ShowCityDropdown = viewModel.ShowCityDropdown,
                IsPrintAllIntegrationActive = viewModel.IsPrintAllIntegrationActive,
                IsPhotoBoothIntegrationActive = viewModel.IsPhotoBoothIntegrationActive,
                IsApplicationUpdateStatusCheckActive = viewModel.IsApplicationUpdateStatusCheckActive,
                IsPreApplicationConnectionActive = viewModel.IsPreApplicationConnectionActive,
                IsRejectionApprovalControl = viewModel.IsRejectionApprovalControl,
                CargoProviderId = viewModel.CargoProviderId,
                DisableContactInformationVerification = viewModel.DisableContactInformationVerification,
                ShowInB2c = viewModel.ShowInB2c,
                SendBasicGuidelineInAms = viewModel.SendBasicGuidelineInAms,
                IsDigitalSignatureIntegrationActive = viewModel.IsDigitalSignatureIntegrationActive,
                ShowPaymentMethods = viewModel.ShowPaymentMethods,
                QmsScreenTitle = viewModel.QmsScreenTitle,
                IsRelatedInsuranceForExempt = viewModel.IsRelatedInsuranceForExempt,
                ShowBranchInMobile = viewModel.ShowBranchInMobile,
                ShowMontageVisaType = viewModel.ShowMontageVisaType,
                IsB2CPaymentActive = viewModel.IsB2CPaymentActive,
                PaymentProviderTypeIds = viewModel.PaymentProviderTypeIds,
                BranchTranslations = viewModel.BranchTranslations.Select(p => new AddBranchTranslationApiRequest
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name
                }).ToList(),
                BranchDataTranslation = viewModel.BranchDataTranslations.Select(p => new AddBranchDataTranslationApiRequest
                {
                    Address = p.Address,
                    CorporateName = p.CorporateName,
                    InvoiceNumber = p.InvoiceNumber,
                    Mission = p.Mission,
                    CityName = p.CityName,
                    LanguageId = p.LanguageId
                }).ToList(),
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Management.AddBranch, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateBranch(string encryptedBranchId)
        {
            int id = encryptedBranchId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BranchApiResponse>>
                (ApiMethodName.Management.GetBranch + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdateBranchViewModel
            {
                Id = apiResponse.Data.Id,
                CountryId = apiResponse.Data.Country.Id,
                Address = apiResponse.Data.Address,
                Email = apiResponse.Data.Email,
                Telephone = apiResponse.Data.Telephone,
                CityName = apiResponse.Data.CityName,
                Mission = apiResponse.Data.Mission,
                IsActive = apiResponse.Data.IsActive,
                CorporateName = apiResponse.Data.CorporateName,
                InvoiceNumber = apiResponse.Data.InvoiceNumber,
                SapBranchId = apiResponse.Data.SapBranchId,
                TimeZoneOffset = apiResponse.Data.TimeZoneOffset,
                CheckRejectedStatus = apiResponse.Data.CheckRejectedStatus,
                CheckRejectedStatusPeriod = apiResponse.Data.CheckRejectedStatusPeriod,
                CheckUnrealDocumentStatus = apiResponse.Data.CheckUnrealDocumentStatus,
                CheckUnrealDocumentStatusPeriod = apiResponse.Data.CheckUnrealDocumentStatusPeriod,
                CheckRejectionWithCountryEntryBannedStatus = apiResponse.Data.CheckRejectionWithCountryEntryBannedStatus,
                CheckRejectionWithCountryEntryBannedStatusPeriod = apiResponse.Data.CheckRejectionWithCountryEntryBannedStatusPeriod,
                RejectionRefundDonePermissionNumber = apiResponse.Data.RejectionRefundDonePermissionNumber,
                IsValidAMS = apiResponse.Data.IsValidAMS,
                IsSlotTypesConnected = apiResponse.Data.IsSlotTypesConnected,
                ConnectedSlotTypeId = apiResponse.Data.ConnectedSlotTypeId,
                IsPassportScanRequired = apiResponse.Data.IsPassportScanRequired,
                EmailProviderId = apiResponse.Data.EmailProviderId,
                SmsProviderId = apiResponse.Data.SmsProviderId,
                QmsCompanyId = apiResponse.Data.QmsCompanyId,
                QmsWalkinVipControl = apiResponse.Data.QmsWalkinVipControl,
                QueueMaticPlaylistId = apiResponse.Data.QueueMaticPlaylistId,
                SmsSender = apiResponse.Data.SmsSender,
                IsSendByPrefix = apiResponse.Data.IsSendByPrefix,
                MaxAppointmentDay = apiResponse.Data.MaxAppointmentDay,
                BasicGuidelineId = apiResponse.Data.BasicGuidelineId,
                InsuranceProviderId = apiResponse.Data.InsuranceProviderId,
                IsCargoIntegrationActive = apiResponse.Data.IsCargoIntegrationActive,
                ShowCityDropdown = apiResponse.Data.ShowCityDropdown,
				IsPrintAllIntegrationActive = apiResponse.Data.IsPrintAllIntegrationActive,
                IsApplicationUpdateStatusCheckActive = apiResponse.Data.IsApplicationUpdateStatusCheckActive,
                IsPhotoBoothIntegrationActive = apiResponse.Data.IsPhotoBoothIntegrationActive,
                IsPrinterIntegrationActive = apiResponse.Data.IsPrinterIntegrationActive,
                IsPreApplicationConnectionActive= apiResponse.Data.IsPreApplicationConnectionActive,
                IsRejectionApprovalControl = apiResponse.Data.IsRejectionApprovalControl,
                IsVisaRejectionDocumentsControl = apiResponse.Data.IsVisaRejectionDocumentsControl,
                CargoProviderId = apiResponse.Data.CargoProviderId,
                IsUserSysdmin = apiResponse.Data.IsUserSysdmin,
                DisableContactInformationVerification = apiResponse.Data.DisableContactInformationVerification,
                ShowInB2c = apiResponse.Data.ShowInB2c,
                SendBasicGuidelineInAms = apiResponse.Data.SendBasicGuidelineInAms,
                IsDigitalSignatureIntegrationActive = apiResponse.Data.IsDigitalSignatureIntegrationActive,
                ShowPaymentMethods = apiResponse.Data.ShowPaymentMethods,
                QmsScreenTitle =apiResponse.Data.QmsScreenTitle,
                IsRelatedInsuranceForExempt = apiResponse.Data.IsRelatedInsuranceForExempt,
                ShowBranchInMobile = apiResponse.Data.ShowBranchInMobile,
                ShowMontageVisaType = apiResponse.Data.ShowMontageVisaType,
                IsB2CPaymentActive = apiResponse.Data.IsB2CPaymentActive,
                PaymentChannelTypeIds = apiResponse.Data.PaymentProviders.Select(s => s.ChannelId).ToList(),
                PaymentProviderTypeIds = apiResponse.Data.PaymentProviders.Select(s => s.Id).ToList(),
                BranchTranslations = apiResponse.Data.BranchTranslations.Select(p => new UpdateBranchTranslationViewModel
                {
                    Id = p.Id,
                    LanguageId = p.LanguageId,
                    Name = p.Name
                }).ToList(),
                BranchDataTranslations = apiResponse.Data.BranchDataTranslations.Select(p => new UpdateBranchDataTranslationViewModel
                {
                    Id = p.Id,
                    Address = p.Address,
                    CorporateName = p.CorporateName,
                    InvoiceNumber = p.InvoiceNumber,
                    Mission = p.Mission,
                    CityName = p.CityName,
                    LanguageId = p.LanguageId
                }).ToList()
            };

            return PartialView("_UpdateBranch", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateBranch(UpdateBranchViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateBranchApiRequest
            {
                Id = viewModel.Id,
                CountryId = viewModel.CountryId,
                Address = viewModel.Address,
                Email = viewModel.Email,
                Telephone = viewModel.Telephone,
                CityName = viewModel.CityName,
                Mission = viewModel.Mission,
                IsActive = viewModel.IsActive,
                CorporateName = viewModel.CorporateName,
                InvoiceNumber = viewModel.InvoiceNumber,
                SapBranchId = viewModel.SapBranchId,
                TimeZoneOffset = viewModel.TimeZoneOffset,
                CheckRejectedStatus = viewModel.CheckRejectedStatus,
                CheckRejectedStatusPeriod = viewModel.CheckRejectedStatusPeriod,
                CheckUnrealDocumentStatus = viewModel.CheckUnrealDocumentStatus,
                CheckUnrealDocumentStatusPeriod = viewModel.CheckUnrealDocumentStatusPeriod,
                CheckRejectionWithCountryEntryBannedStatus = viewModel.CheckRejectionWithCountryEntryBannedStatus,
                CheckRejectionWithCountryEntryBannedStatusPeriod = viewModel.CheckRejectionWithCountryEntryBannedStatusPeriod,
                RejectionRefundDonePermissionNumber = viewModel.RejectionRefundDonePermissionNumber,
                IsValidAMS = viewModel.IsValidAMS,
                IsSlotTypesConnected = viewModel.IsSlotTypesConnected,
                ConnectedSlotTypeId = viewModel.ConnectedSlotTypeId,
                IsPassportScanRequired = viewModel.IsPassportScanRequired,
                EmailProviderId = viewModel.EmailProviderId,
                SmsProviderId = viewModel.SmsProviderId,
                QmsCompanyId = viewModel.QmsCompanyId,
                QmsWalkinVipControl = viewModel.QmsWalkinVipControl,
                QueueMaticPlaylistId = viewModel.QueueMaticPlaylistId,
                IsSendByPrefix = viewModel.IsSendByPrefix,
                SmsSender = viewModel.SmsSender,
                MaxAppointmentDay = viewModel.MaxAppointmentDay,
                BasicGuidelineId = viewModel.BasicGuidelineId,
                InsuranceProviderId = viewModel.InsuranceProviderId,
                IsCargoIntegrationActive = viewModel.IsCargoIntegrationActive,
                ShowCityDropdown = viewModel.ShowCityDropdown,
				IsPrintAllIntegrationActive = viewModel.IsPrintAllIntegrationActive,
                IsPhotoBoothIntegrationActive = viewModel.IsPhotoBoothIntegrationActive,
                IsApplicationUpdateStatusCheckActive = viewModel.IsApplicationUpdateStatusCheckActive,
                IsPrinterIntegrationActive = viewModel.IsPrinterIntegrationActive,
                IsPreApplicationConnectionActive = viewModel.IsPreApplicationConnectionActive,
                IsRejectionApprovalControl = viewModel.IsRejectionApprovalControl,
                IsVisaRejectionDocumentsControl = viewModel.IsVisaRejectionDocumentsControl,
                CargoProviderId = viewModel.CargoProviderId,
                DisableContactInformationVerification= viewModel.DisableContactInformationVerification,
                ShowInB2c = viewModel.ShowInB2c,
                SendBasicGuidelineInAms = viewModel.SendBasicGuidelineInAms,
                IsDigitalSignatureIntegrationActive = viewModel.IsDigitalSignatureIntegrationActive,
                ShowPaymentMethods = viewModel.ShowPaymentMethods,
                QmsScreenTitle = viewModel.QmsScreenTitle,
                IsRelatedInsuranceForExempt = viewModel.IsRelatedInsuranceForExempt,
                ShowBranchInMobile = viewModel.ShowBranchInMobile,
                ShowMontageVisaType = viewModel.ShowMontageVisaType,
                IsB2CPaymentActive = viewModel.IsB2CPaymentActive,
                PaymentProviderTypeIds = viewModel.PaymentProviderTypeIds,
                BranchTranslations = viewModel.BranchTranslations.Select(p => new UpdateBranchTranslationApiRequest
                {
                    Id = p.Id,
                    LanguageId = p.LanguageId,
                    Name = p.Name
                }).ToList(),
                BranchDataTranslations = viewModel.BranchDataTranslations.Select(p => new UpdateBranchDataTranslationApiRequest
                {
                    Id = p.Id,
                    Address = p.Address,
                    CorporateName = p.CorporateName,
                    InvoiceNumber = p.InvoiceNumber,
                    Mission = p.Mission,
                    CityName = p.CityName,
                    LanguageId = p.LanguageId
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateBranch, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteBranch(string encryptedBranchId)
        {
            int id = encryptedBranchId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Management.DeleteBranch + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> PartialBranch(string encryptedBranchId)
        {
            int id = encryptedBranchId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BranchApiResponse>>
                (ApiMethodName.Management.GetBranch + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var channelTypes = EnumHelper.GetEnumAsDictionary(typeof(PaymentChannelType));

            var viewModel = new BranchViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Address = apiResponse.Data.Address,
                Email = apiResponse.Data.Email,
                Telephone = apiResponse.Data.Telephone,
                CityName = apiResponse.Data.CityName,
                Mission = apiResponse.Data.Mission,
                IsActive = apiResponse.Data.IsActive,
                CorporateName = apiResponse.Data.CorporateName,
                InvoiceNumber = apiResponse.Data.InvoiceNumber,
                SapBranchId = apiResponse.Data.SapBranchId,
                IsCargoIntegrationActive = apiResponse.Data.IsCargoIntegrationActive,
                ShowCityDropdown = apiResponse.Data.ShowCityDropdown,
				IsPrintAllIntegrationActive = apiResponse.Data.IsPrintAllIntegrationActive,
                IsPhotoBoothIntegrationActive = apiResponse.Data.IsPhotoBoothIntegrationActive,
                IsApplicationUpdateStatusCheckActive = apiResponse.Data.IsApplicationUpdateStatusCheckActive,
                IsPreApplicationConnectionActive = apiResponse.Data.IsPreApplicationConnectionActive,
                IsRejectionApprovalControl = apiResponse.Data.IsRejectionApprovalControl,
                CargoProviderId = apiResponse.Data.CargoProviderId,
                TimeZoneOffset = $"(GMT {apiResponse.Data.TimeZoneOffset}:00)".Replace(",30:00",":30"),
                CheckRejectedStatus = apiResponse.Data.CheckRejectedStatus,
                CheckRejectedStatusPeriod = apiResponse.Data.CheckRejectedStatusPeriod,
                CheckUnrealDocumentStatus = apiResponse.Data.CheckUnrealDocumentStatus,
                CheckUnrealDocumentStatusPeriod = apiResponse.Data.CheckUnrealDocumentStatusPeriod,
                CheckRejectionWithCountryEntryBannedStatus = apiResponse.Data.CheckRejectionWithCountryEntryBannedStatus,
                RejectionRefundDonePermissionNumber = apiResponse.Data.RejectionRefundDonePermissionNumber,
                IsValidAMS = apiResponse.Data.IsValidAMS,
                IsSlotTypesConnected = apiResponse.Data.IsSlotTypesConnected,
                ConnectedSlotTypeId = apiResponse.Data.ConnectedSlotTypeId,
                IsPassportScanRequired = apiResponse.Data.IsPassportScanRequired,
                EmailProviderId = apiResponse.Data.EmailProviderId,
                SmsProviderId = apiResponse.Data.SmsProviderId,
                QmsCompanyId = apiResponse.Data.QmsCompanyId,
                QmsWalkinVipControl = apiResponse.Data.QmsWalkinVipControl,
                QueueMaticPlaylistId = apiResponse.Data.QueueMaticPlaylistId,
                IsSendByPrefix = apiResponse.Data.IsSendByPrefix,
                SmsSender = apiResponse.Data.SmsSender,
                MaxAppointmentDay = apiResponse.Data.MaxAppointmentDay,
                BasicGuidelineId = apiResponse.Data.BasicGuidelineId,
                InsuranceProviderId = apiResponse.Data.InsuranceProviderId,
                DisableContactInformationVerification= apiResponse.Data.DisableContactInformationVerification,
                ShowInB2c = apiResponse.Data.ShowInB2c,
                SendBasicGuidelineInAms = apiResponse.Data.SendBasicGuidelineInAms,
                IsDigitalSignatureIntegrationActive = apiResponse.Data.IsDigitalSignatureIntegrationActive,
                ShowPaymentMethods = apiResponse.Data.ShowPaymentMethods,
                QmsScreenTitle = apiResponse.Data.QmsScreenTitle,
                IsRelatedInsuranceForExempt = apiResponse.Data.IsRelatedInsuranceForExempt,
                ShowBranchInMobile = apiResponse.Data.ShowBranchInMobile,
                ShowMontageVisaType = apiResponse.Data.ShowMontageVisaType,
                IsB2CPaymentActive = apiResponse.Data.IsB2CPaymentActive,
                PaymentChannelTypeIds = apiResponse.Data.PaymentProviders.Select(s => s.ChannelId).ToList(),
                PaymentProviderTypeIds = apiResponse.Data.PaymentProviders.Select(s =>s.Id).ToList(),
                PaymentChannelTypes = string.Join(", ", channelTypes.Where(s => apiResponse.Data.PaymentProviders.Select(r => r.ChannelId).Contains(s.Key)).Select(s => s.Value)),
                PaymentProviderTypes = string.Join(", ", apiResponse.Data.PaymentProviders.Select(s => s.Name)),
                Country = new CountryViewModel
                {
                    Id = apiResponse.Data.Country.Id,
                    Name = apiResponse.Data.Country.Name,
                },
                Name = apiResponse.Data.Name,
                BranchDataTranslations = apiResponse.Data.BranchDataTranslations.Select(r => new BranchDataTranslationDto
                {
                    Id = r.Id,
                    Address = r.Address,
                    CorporateName= r.CorporateName,
                    InvoiceNumber= r.InvoiceNumber,
                    Mission = r.Mission,
                    CityName = r.CityName,
                    LanguageId = r.LanguageId
                }).ToList()
            };

            return PartialView("_Branch", viewModel);
        }

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedBranches([DataSourceRequest] DataSourceRequest request, FilterBranchViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedBranchesApiRequest
            {
                CountryId = filterViewModel.FilterCountryId,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<BranchesApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedBranches, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<BranchViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().Branches
                    .Select(p => new BranchViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        Name = p.Name,
                        Address = p.Address,
                        Email = p.Email,
                        Telephone = p.Telephone,
                        CityName = p.CityName,
                        Mission = p.Mission,
                        IsActive = p.IsActive,
                        Country = new CountryViewModel
                        {
                            Id = p.Country.Id,
                            Name = p.Country.Name
                        }
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #region Branch Authorities

        [ActionAttribute(IsMenuItem = true)]
        public Task<IActionResult> BranchAuthorities()
        {
            return Task.FromResult<IActionResult>(View(new BranchAuthoritiesViewModel
            {
                SupervisorRoleList = new List<BranchAuthoritiesViewModel.AuthorityViewModel>()
                {
                    new()
                    {
                        RoleName = SiteResources.OperationManager,
                        IsSelected = false,
                        RoleId = 5
                    },
                    new()
                    {
                        RoleName = SiteResources.BranchManager,
                        IsSelected = false,
                        RoleId = 7
                    },
                    new()
                    {
                        RoleName = SiteResources.CountryManager,
                        IsSelected = false,
                        RoleId = 8
                    },
                }
            }));
        }

        public async Task<IActionResult> SearchBranchAuthorities(int countryId, int branchId)
        {
            var apiRequest = new GetBranchAuthoritiesApiRequest()
            {
                Id = branchId,
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<GetBranchAuthoritiesApiResponse>>
                (apiRequest, ApiMethodName.Management.GetBranchAuthorities, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            var roleList = apiResponse.Data.SupervisorRoleList.Any()
                ? apiResponse.Data.SupervisorRoleList.Select(s =>
                    new BranchAuthoritiesViewModel.AuthorityViewModel()
                    {
                        UserId = s.UserId,
                        IsSelected = s.IsSelected,
                        RoleName = s.Role.Name,
                        RoleId = s.Role.Id
                    }).ToList()
                : new List<BranchAuthoritiesViewModel.AuthorityViewModel>()
                {
                    new()
                    {
                        RoleName = SiteResources.OperationManager,
                        IsSelected = false,
                        RoleId = 5,
                    },
                    new()
                    {
                        RoleName = SiteResources.BranchManager,
                        IsSelected = false,
                        RoleId = 7
                    },
                    new()
                    {
                        RoleName = SiteResources.CountryManager,
                        IsSelected = false,
                        RoleId = 8
                    },
                };

            var viewModel = new BranchAuthoritiesViewModel
            {
                BranchId = branchId,
                CountryId = countryId,
                BranchName = apiResponse.Data.BranchName,
                CountryName = apiResponse.Data.CountryName,
                SupervisorRoleList = roleList
            };

            return PartialView("~/Areas/Management/Views/Branch/_AuthorityView.cshtml", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddBranchAuthorities(BranchAuthoritiesViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            if (!viewModel.BranchId.IsNumericAndGreaterThenZero() || !viewModel.CountryId.IsNumericAndGreaterThenZero())
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddOrUpdateBranchAuthoritiesApiRequest()
            {
               BranchId = viewModel.BranchId,
               SupervisorRoleList = viewModel.SupervisorRoleList.OrderBy(o => o.RoleId).Select(s => new AddOrUpdateBranchAuthoritiesApiRequest.AuthorityRequest()
               {
                   UserId = s.UserId,
                   RoleId = s.RoleId,
                   IsSelected = s.IsSelected,
               }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddOrUpdateBranchAuthoritiesApiResponse>>
                (apiRequest, ApiMethodName.Management.AddOrUpdateBranchAuthorities, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }


        #endregion


        #endregion

        #region IcrNote

        public async Task<IActionResult> PartialUpdateBranchIcr(string encryptedBranchId)
        {
            int branchId = encryptedBranchId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BranchIcrApiResponse>>
                (ApiMethodName.Management.GetBranchIcr + branchId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var branchName = string.Empty;
            var cacheItem = await CacheHelper.GetBranchesAsync();
            var branch = cacheItem.Branches.FirstOrDefault(p => p.Id == branchId);
            if (branch != null)
            {
                branchName = branch.BranchTranslations.Any(p => p.LanguageId == LanguageId) ?
                                        branch.BranchTranslations.First(p => p.LanguageId == LanguageId).Name :
                                        branch.BranchTranslations.First().Name;
            }

            var viewModel = new BranchIcrViewModel
            {
                EncryptedId = apiResponse.Data.Id.HasValue ? apiResponse.Data.Id.ToEncrypt() : string.Empty,
                EncryptedBranchId = apiResponse.Data.BranchId.ToEncrypt(),
                Note = apiResponse.Data.Note,
                IcrType = apiResponse.Data.Type,
                Name = apiResponse.Data.Name,
                BranchName = branchName,
                NameAr = apiResponse.Data.NameAr,
                NoteAr = apiResponse.Data.NoteAr,
                NameTm = apiResponse.Data.NameTm,
                NoteTm = apiResponse.Data.NoteTm,
                NameRu = apiResponse.Data.NameRu,
                NoteRu = apiResponse.Data.NoteRu,
                NameFr = apiResponse.Data.NameFr,
                NoteFr = apiResponse.Data.NoteFr,
            };

            return PartialView("_UpdateBranchIcrNote", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateBranchIcr(BranchIcrViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateBranchIcrApiRequest
            {
                BranchId = viewModel.EncryptedBranchId.ToDecryptInt(),
                Note = viewModel.Note,
                Type = viewModel.IcrType,
                Name = viewModel.Name,
                NameAr = viewModel.NameAr,
                NoteAr = viewModel.NoteAr,
                NameTm = viewModel.NameTm,
                NoteTm = viewModel.NoteTm,
                NameRu = viewModel.NameRu,
                NoteRu = viewModel.NoteRu,
                NameFr = viewModel.NameFr,
                NoteFr = viewModel.NoteFr
            };

            if (!string.IsNullOrEmpty(viewModel.EncryptedId))
                apiRequest.Id = viewModel.EncryptedId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateBranchIcr, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion
    }
}
