﻿using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Appointment.QualityCheck;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Appointment.QualityCheck;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Data.ViewModels.QualityCheck;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Data.Controllers
{
    [Area("Data")]
    public class QualityCheckController : BaseController<QualityCheckController>
    {
        public QualityCheckController(
            ICacheHelper cacheHelper,
            IOptions<AppSettings> appSettings)
            : base(appSettings, cacheHelper)
        {
        }

        #region Get

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedQualityChecks([DataSourceRequest] DataSourceRequest request,
            FilterQualityCheckViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedQualityCheckApiRequest
            {
                BranchId = filterViewModel.FilterBranchId,
                ApplicationNumber = filterViewModel.FilterApplicationNumber,
                ApplicantTypeId = filterViewModel.FilterApplicantTypeId,
                ApplicationTypeId = filterViewModel.FilterApplicationTypeId,
                Name = filterViewModel.FilterName,
                Surname = filterViewModel.FilterSurname,
                PassportNumber = filterViewModel.FilterPassportNumber,
                StatusId = filterViewModel.FilterStatusId,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedQualityCheckApiResponse>>>
                (apiRequest, ApiMethodName.Data.GetPaginatedQualityChecks, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<QualityCheckIViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().QualityCheck
                    .Select(p => new QualityCheckIViewModel
                    {
                        EncryptedQualityCheckId = p.QualityCheckId.ToEncrypt(),
                        EncryptedApplicationDataId = p.ApplicationDataId.ToEncrypt(),
                        ApplicaitionId = p.ApplicationId.ToApplicationNumber(),
                        ApplicationTypeId = p.ApplicationTypeId,
                        ApplicantTypeId = p.ApplicantTypeId,
                        PassportNumber = p.PassportNumber,
                        Name = p.Name,
                        Surname = p.Surname,
                        StatusId = p.StatusId,
                        LastUpdatedAt = p.LastUpdatedAt,
                        LastUpdatedBy = p.LastUpdatedBy
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        public async Task<IActionResult> PartialQualityCheck(string encryptedQualityCheckId)
        {
            int qualityCheckId = encryptedQualityCheckId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<QualityCheckApiResponse>>
                (ApiMethodName.Data.GetQualityCheck + qualityCheckId, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new QualityCheckUpdateViewModel
            {
                EncryptedQualityCheckId = apiResponse.Data.QualityCheckId.ToEncrypt(),
                EncryptedApplicationDataId = apiResponse.Data.ApplicationDataId.ToEncrypt(),
                EncryptedApplicationId = apiResponse.Data.ApplicationId.ToEncrypt(),
                ApplicationNumber = apiResponse.Data.ApplicationId.ToApplicationNumber(),
                DataNumber = apiResponse.Data.ApplicationDataId.ToApplicationNumber(),
                LastUpdateBy = apiResponse.Data.LastUpdatedBy,
                LastUpdatedAt = apiResponse.Data.LastUpdatedAt.DateTime,
                StatusId = apiResponse.Data.StatusId,
                MainCategories = EnumHelper.GetEnumAsDictionary(typeof(QualityCheckType)).Select(q =>
                    new QualityCheckUpdateViewModel.MainCategory()
                    {
                        Id = q.Key,
                        Name = q.Value,
                        Categories = ReflectionHelper.GetQualityCheckCategoryType((QualityCheckType)q.Key).Select(p =>
                            new QualityCheckUpdateViewModel.MainCategory.Category()
                            {
                                Id = p.ToInt(),
                                Name = p.ToDescription(),
                                IsChecked = apiResponse.Data.Categories.Any(q => q == p.ToInt())
                            }).ToArray()
                    }).ToArray()
            };

            return PartialView("_QualityCheck", viewModel);
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateQualityCheck(string encryptedQualityCheckId)
        {
            int qualityCheckId = encryptedQualityCheckId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<QualityCheckApiResponse>>
                (ApiMethodName.Data.GetQualityCheck + qualityCheckId, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new QualityCheckUpdateViewModel
            {
                EncryptedQualityCheckId = apiResponse.Data.QualityCheckId.ToEncrypt(),
                EncryptedApplicationDataId = apiResponse.Data.ApplicationDataId.ToEncrypt(),
                EncryptedApplicationId = apiResponse.Data.ApplicationId.ToEncrypt(),
                ApplicationNumber = apiResponse.Data.ApplicationId.ToApplicationNumber(),
                DataNumber = apiResponse.Data.ApplicationDataId.ToApplicationNumber(),
                LastUpdateBy = apiResponse.Data.LastUpdatedBy,
                LastUpdatedAt = apiResponse.Data.LastUpdatedAt.DateTime,
                StatusId = apiResponse.Data.StatusId,
                MainCategories = EnumHelper.GetEnumAsDictionary(typeof(QualityCheckType)).Select(q =>
                    new QualityCheckUpdateViewModel.MainCategory()
                    {
                        Id = q.Key,
                        Name = q.Value,
                        Categories = ReflectionHelper.GetQualityCheckCategoryType((QualityCheckType)q.Key).Select(p =>
                            new QualityCheckUpdateViewModel.MainCategory.Category
                            {
                                Id = p.ToInt(),
                                Name = p.ToDescription(),
                                IsChecked = apiResponse.Data.Categories.Any(q => q == p.ToInt())
                            }).ToArray()
                    }).ToArray()
            };

            return PartialView("_UpdateQualityCheck", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateQualityCheck(QualityCheckUpdateViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel
                    { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateQualityCheckApiRequest
            {
                QualityCheckId = viewModel.EncryptedQualityCheckId.ToDecryptInt(),
                ApplicationId = viewModel.EncryptedApplicationId.ToDecryptInt(),
                StatusId = viewModel.IsCompleted ? QualityCheckStatus.Sent.ToInt() : QualityCheckStatus.OnHold.ToInt(),
                UserId = UserSession.UserId,
                Categories = viewModel.MainCategories.SelectMany(q => q.Categories).Where(q => q.IsChecked)
                    .Select(q => q.Id).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Data.UpdateQualityCheck, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);
            
            return Json(new ResultModel
                { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpPut]
        public async Task<IActionResult> RejectQualityCheck(string encryptedQualityCheckId)
        {
            var apiRequest = new UpdateQualityCheckApiRequest
            {
                QualityCheckId = encryptedQualityCheckId.ToDecryptInt(),
                StatusId = QualityCheckStatus.Rejected.ToInt(),
                UserId = UserSession.UserId
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Data.UpdateQualityCheck, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel
                { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion
    }
}