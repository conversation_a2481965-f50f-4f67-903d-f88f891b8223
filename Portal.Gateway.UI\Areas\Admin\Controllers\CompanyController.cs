﻿using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Admin.Company;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Admin.Company;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Admin.ViewModels.Company;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class CompanyController : BaseController<CompanyController>
    {
        public CompanyController(
                IOptions<AppSettings> appSettings,
                ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        #region Get

        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedCompanies([DataSourceRequest] DataSourceRequest request, FilterCompanyViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedCompaniesApiRequest
            {
                CompanyName = filterViewModel.FilterCompanyName,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<CompaniesApiResponse>>>
                (apiRequest, ApiMethodName.Admin.GetPaginatedCompanies, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<CompanyViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().Companies
                    .Select(p => new CompanyViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        Name = p.Name,
                        CorporateId = p.CorporateId,
                        IsActive = p.IsActive,
                        ModuleCount = p.CompanyModules.Where(t => t.IsActive).Count()
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        public async Task<IActionResult> PartialCompany(string encryptedCompanyId)
        {
            int id = encryptedCompanyId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<CompanyApiResponse>>
                (ApiMethodName.Admin.GetCompany + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new DetailedCompanyViewModel
            {
                Name = apiResponse.Data.Name,
                CorporateId = apiResponse.Data.CorporateId,
                DbConnection = apiResponse.Data.DbConnection,
                IsActive = apiResponse.Data.IsActive,
                ModuleList = apiResponse.Data.CompanyModules.Select(p => new DetailedModuleViewModel()
                {
                    ModuleName = EnumHelper.GetEnumDescription(typeof(ModuleType), p.ModuleId.ToString()),
                    LicenseCount = p.LicenseCount,
                    DueDate = p.DueDate.ToString(SiteResources.DatePickerFormatView.ToString()),
                    IsActive = p.IsActive,
                    ReturnUrl = p.ReturnUrl
                }).ToList()
            };

            return PartialView("_Company", viewModel);
        }

        #endregion


        #region Add

        public IActionResult PartialAddCompany()
        {
            var viewModel = new AddCompanyViewModel();

            return PartialView("_AddCompany", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddCompany(AddCompanyViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddCompanyApiRequest
            {
                Name = viewModel.Name,
                CorporateId = viewModel.CorporateId,
                DbConnection = viewModel.DbConnection
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<CompanyApiResponse>>
                (apiRequest, ApiMethodName.Admin.AddCompany, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion


        #region Update

        public async Task<IActionResult> PartialUpdateCompany(string encryptedCompanyId)
        {
            int id = encryptedCompanyId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<CompanyApiResponse>>
                (ApiMethodName.Admin.GetCompany + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdateCompanyViewModel
            {
                Id = apiResponse.Data.Id,
                Name = apiResponse.Data.Name,
                CorporateId = apiResponse.Data.CorporateId,
                DbConnection = apiResponse.Data.DbConnection,
                IsActive = apiResponse.Data.IsActive,
            };

            return PartialView("_UpdateCompany", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateCompany(UpdateCompanyViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateCompanyApiRequest
            {
                Id = viewModel.Id,
                Name = viewModel.Name,
                CorporateId = viewModel.CorporateId,
                DbConnection = viewModel.DbConnection,
                IsActive = viewModel.IsActive,
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Admin.UpdateCompany, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        public async Task<IActionResult> PartialUpdateCompanyModules(string encryptedCompanyId)
        {
            int id = encryptedCompanyId.ToDecryptInt();

            Dictionary<int, string> moduleTypes = EnumHelper.GetEnumAsDictionary(typeof(ModuleType));

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<CompanyApiResponse>>
                (ApiMethodName.Admin.GetCompany + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdateCompanyModuleViewModel
            {
                CompanyId = apiResponse.Data.Id,
                CompanyName = apiResponse.Data.Name,
                ModuleList = moduleTypes.Select(m => new UpdateModuleViewModel()
                {
                    ModuleTypeId = m.Key,
                    ModuleName = m.Value,
                    DueDate = DateTimeOffset.UtcNow.AddYears(1).DateTime,
                }).ToList(),
            };

            if (apiResponse.Data.CompanyModules.Count() > 0)
            {
                foreach (var module in apiResponse.Data.CompanyModules)
                {
                    var moduleViewModel = viewModel.ModuleList.FirstOrDefault(p => p.ModuleTypeId == module.ModuleId);

                    moduleViewModel.ModuleId = module.Id;
                    moduleViewModel.LicenseCount = module.LicenseCount;
                    moduleViewModel.ReturnUrl = module.ReturnUrl;
                    moduleViewModel.IsActive = module.IsActive;
                    moduleViewModel.DueDate = module.DueDate.DateTime;
                }
            }

            return PartialView("_UpdateCompanyModules", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateCompanyModules(UpdateCompanyModuleViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateModuleToCompanyApiRequest
            {
                CompanyId = viewModel.CompanyId,
                UpdateModuleToCompanyListRequests = viewModel.ModuleList.Select(p => new UpdateModuleToCompanyItemApiRequest()
                {
                    Id = p.ModuleId,
                    ModuleId = p.ModuleTypeId,
                    DueDate = p.DueDate,
                    LicenseCount = p.LicenseCount,
                    IsActive = p.IsActive,
                    ReturnUrl = p.ReturnUrl
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Admin.UpdateModuleToCompany, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion


        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteCompany(string encryptedCompanyId)
        {
            int id = encryptedCompanyId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Admin.DeleteCompany + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

    }
}