﻿namespace Portal.Gateway.Entity.Entities.Portal
{
    public class PushNotificationTranslation : AuditableEntity
    {
        public PushNotificationTranslation()
        {
            IsActive = true;
            IsDeleted = false;
        }
        public int Id { get; set; }
        public int PushNotificationId { get; set; }
        public int LanguageId { get; set; }
        public string Subject { get; set; }
        public string Content { get; set; }
        public PushNotification PushNotification { get; set; }
    }
}
