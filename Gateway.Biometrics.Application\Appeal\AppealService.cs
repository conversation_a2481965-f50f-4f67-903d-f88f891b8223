﻿using Gateway.Biometrics.Application.Appeal.DTO;
using Gateway.Biometrics.Application.Appeal.Validator;
using Gateway.Biometrics.Persistence;
using Gateway.Biometrics.Resources;
using Gateway.ObjectStoring;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.IO.Compression;
using System.IO;
using System.Threading.Tasks;
using System.Net.Http;
using System.Buffers.Text;
using System;
using Microsoft.EntityFrameworkCore.ValueGeneration;
using Gateway.Biometrics.Core;
using Gateway.IO;
using Gateway.Biometrics.Application.Helpers;
using Gateway.Biometrics.Entity.Entities.Appeal;
using Gateway.Biometrics.Application.DemographicInformation.DTO;
using Gateway.Biometrics.Application.DemographicInformation.Validator;
using System.Linq;
using AutoMapper;
using System.Text;
using Gateway.Biometrics.Entity.ExternalDbEntities;
using Gateway.Biometrics.Application.Branch.DTO;
using Gateway.Biometrics.Application.Chunk.DTO;
using Gateway.Biometrics.Application.Chunk.Validator;
using Newtonsoft.Json;

namespace Gateway.Biometrics.Application.Appeal
{

    public class AppealService : IAppealService
    {
        private readonly IValidationService _validationService;
        private readonly BiometricsDbContext _dbContext;
        private readonly ExternalDbContext _externalDbContext;
        private readonly IFileStorage _fileStorage;
        private readonly IMapper _mapper;


        private string passKey = "09be2c29-6fa8-473d-8471-2e1a690c16d1-1522bd5d-7afe-4064-89d6-20c6f0a85f10";

        #region ctor

        public AppealService(IValidationService validationService,
            BiometricsDbContext dbContext,
            ExternalDbContext externalDbContext,
            IFileStorage fileStorage,
            IMapper mapper)
        {
            _validationService = validationService;
            _dbContext = dbContext;
            _externalDbContext = externalDbContext;
            _fileStorage = fileStorage;
            _mapper = mapper;
        }
        #endregion


        #region Public Methods

        public async Task<CreateAppealResult> CreateAppeal(CreateAppealRequest request)
        {
            var validationResult = _validationService.Validate(typeof(CreateAppealValidator), request);


            if (!validationResult.IsValid)
                return new CreateAppealResult
                {
                    Status = CreateAppealStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            #region Check db existing

            var cabinControl = await _dbContext.Cabin.AnyAsync(x => x.Id == request.AppealCabinId && x.IsDeleted == false);
            if (cabinControl == false)
            {
                return new CreateAppealResult
                {
                    Status = CreateAppealStatus.ResourceNotFound,
                    Message = ServiceResources.CABIN_NOT_FOUND
                };
            }

            var officeControl = await _dbContext.Office.AnyAsync(x => x.Id == request.AppealOfficeId && x.IsDeleted == false);
            if (officeControl == false)
            {
                return new CreateAppealResult
                {
                    Status = CreateAppealStatus.ResourceNotFound,
                    Message = ServiceResources.OFFICE_NOT_FOUND
                };
            }

            var countryControl = await _externalDbContext.Country.AnyAsync(x => x.Id == request.AppealCountryId);
            if (countryControl == false)
            {
                return new CreateAppealResult
                {
                    Status = CreateAppealStatus.ResourceNotFound,
                    Message = ServiceResources.COUNTRY_NOT_FOUND
                };
            }

            //var cityControl = await _externalDbContext.City.AnyAsync(x => x.Id == request.AppealCityId);
            //if (cityControl == false)
            //{
            //    return new CreateAppealResult
            //    {
            //        Status = CreateAppealStatus.ResourceNotFound,
            //        Message = ServiceResources.CITY_NOT_FOUND
            //    };
            //}
            #endregion

            var appeal = new Entity.Entities.Appeal.Appeal()
            {
                AppealCabinId = request.AppealCabinId,
                AppealCityId = request.AppealCityId,
                AppealCountryId = request.AppealCountryId,
                AppealOfficeId = request.AppealOfficeId,
                BirthDate = request.BirthDate,
                FatherName = request.FatherName,
                Gender = request.Gender,
                MaidenName = request.MaidenName,
                MotherName = request.MotherName,
                Name = request.Name,
                PassportNumber = request.PassportNumber,
                Status = request.Status,
                Surname = request.Surname,
                ReferenceNumber = request.ReferenceNumber,
                TakenDate = request.TakenDate,
                HostName = request.HostName,
            };

            await _dbContext.Appeal.AddAsync(appeal);

            await _dbContext.SaveChangesAsync();

            foreach (var detail in request.AppealDetails)
            {
                var appealDetail = new Entity.Entities.Appeal.AppealDetail()
                {
                    AppealId = appeal.Id,
                    Definition = detail.Definition ?? string.Empty,
                    InventoryId = detail.InventoryId,
                    Description = detail.Description
                };

                await _dbContext.AppealDetail.AddAsync(appealDetail);
            }

            await _dbContext.SaveChangesAsync();

            var result = await SaveToStorage(request.ReferenceNumber, GenerateStreamFromString(request.XmlData));

            return new CreateAppealResult
            {
                Status = CreateAppealStatus.Successful,
                Message = ServiceResources.SUCCESS,
                Id = appeal.Id
            };
        }

        public async Task<InsertAppealWithMetaDataResult> InsertAppealWithMetaData(InsertAppealWithMetaDataRequest request)
        {
            var validationResult = _validationService.Validate(typeof(InsertAppealWithMetaDataValidator), request);


            if (!validationResult.IsValid)
                return new InsertAppealWithMetaDataResult
                {
                    Status = InsertAppealWithMetaDataStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            #region Check db existing

            var cabinControl = await _dbContext.Cabin.AnyAsync(x => x.Id == request.AppealCabinId && x.IsDeleted == false);
            if (cabinControl == false)
            {
                return new InsertAppealWithMetaDataResult
                {
                    Status = InsertAppealWithMetaDataStatus.ResourceNotFound,
                    Message = ServiceResources.CABIN_NOT_FOUND
                };
            }

            var officeControl = await _dbContext.Office.AnyAsync(x => x.Id == request.AppealOfficeId && x.IsDeleted == false);
            if (officeControl == false)
            {
                return new InsertAppealWithMetaDataResult
                {
                    Status = InsertAppealWithMetaDataStatus.ResourceNotFound,
                    Message = ServiceResources.OFFICE_NOT_FOUND
                };
            }

            var countryControl = await _externalDbContext.Country.AnyAsync(x => x.Id == request.AppealCountryId);
            if (countryControl == false)
            {
                return new InsertAppealWithMetaDataResult
                {
                    Status = InsertAppealWithMetaDataStatus.ResourceNotFound,
                    Message = ServiceResources.COUNTRY_NOT_FOUND
                };
            }

            //var cityControl = await _externalDbContext.City.AnyAsync(x => x.Id == request.AppealCityId);
            //if (cityControl == false)
            //{
            //    return new CreateAppealResult
            //    {
            //        Status = CreateAppealStatus.ResourceNotFound,
            //        Message = ServiceResources.CITY_NOT_FOUND
            //    };
            //}
            #endregion

            var appeal = new Entity.Entities.Appeal.Appeal()
            {
                AppealCabinId = request.AppealCabinId,
                AppealCityId = request.AppealCityId,
                AppealCountryId = request.AppealCountryId,
                AppealOfficeId = request.AppealOfficeId,
                BirthDate = DateTime.SpecifyKind(request.BirthDate, DateTimeKind.Utc),
                FatherName = request.FatherName,
                Gender = request.Gender,
                MaidenName = request.MaidenName,
                MotherName = request.MotherName,
                Name = request.Name,
                PassportNumber = request.PassportNumber,
                Status = request.Status,
                Surname = request.Surname,
                ReferenceNumber = request.ReferenceNumber,
                HostName = request.HostName,
            };

            await _dbContext.Appeal.AddAsync(appeal);

            await _dbContext.SaveChangesAsync();

            foreach (var detail in request.AppealDetails)
            {
                var appealDetail = new Entity.Entities.Appeal.AppealDetail()
                {
                    AppealId = appeal.Id,
                    Definition = detail.Definition ?? string.Empty,
                    InventoryId = detail.InventoryId,
                    Description = detail.Description
                };

                await _dbContext.AppealDetail.AddAsync(appealDetail);
            }

            await _dbContext.SaveChangesAsync();

            #region Save MetaData

            AppealMetaData appealMetaData = new AppealMetaData();
            appealMetaData.AppealId = appeal.Id;

            await _dbContext.AppealMetaData.AddAsync(appealMetaData);
            await _dbContext.SaveChangesAsync();


            var saveMetaData = StringCipher.EncryptLong(request.MetaDataSerialized, passKey);
            //var decrypted = StringCipher.DecryptLong(saveMetaData, passKey);
            var result = await SaveToStorageMetaData(request.ReferenceNumber, appealMetaData.Id.ToString(), GenerateStreamFromString(saveMetaData));


            #endregion

            //var result = await SaveToStorage(request.ReferenceNumber, GenerateStreamFromString(request.XmlData));

            return new InsertAppealWithMetaDataResult
            {
                Status = InsertAppealWithMetaDataStatus.Successful,
                Message = ServiceResources.SUCCESS,
                Id = appeal.Id
            };
        }

        public async Task<InsertAppealWithMetaDataFromOfflineResult> InsertAppealWithMetaDataFromOffline(InsertAppealWithMetaDataFromOfflineRequest request)
        {
            var validationResult = _validationService.Validate(typeof(InsertAppealWithMetaDataFromOfflineValidator), request);


            if (!validationResult.IsValid)
                return new InsertAppealWithMetaDataFromOfflineResult
                {
                    Status = InsertAppealWithMetaDataFromOfflineStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            #region Check db existing

            var cabinControl = await _dbContext.Cabin.AnyAsync(x => x.Id == request.AppealCabinId && x.IsDeleted == false);
            if (cabinControl == false)
            {
                return new InsertAppealWithMetaDataFromOfflineResult
                {
                    Status = InsertAppealWithMetaDataFromOfflineStatus.ResourceNotFound,
                    Message = ServiceResources.CABIN_NOT_FOUND
                };
            }

            var officeControl = await _dbContext.Office.AnyAsync(x => x.Id == request.AppealOfficeId && x.IsDeleted == false);
            if (officeControl == false)
            {
                return new InsertAppealWithMetaDataFromOfflineResult
                {
                    Status = InsertAppealWithMetaDataFromOfflineStatus.ResourceNotFound,
                    Message = ServiceResources.OFFICE_NOT_FOUND
                };
            }

            var countryControl = await _externalDbContext.Country.AnyAsync(x => x.Id == request.AppealCountryId);
            if (countryControl == false)
            {
                return new InsertAppealWithMetaDataFromOfflineResult
                {
                    Status = InsertAppealWithMetaDataFromOfflineStatus.ResourceNotFound,
                    Message = ServiceResources.COUNTRY_NOT_FOUND
                };
            }

            #endregion

            var appeal = new Entity.Entities.Appeal.Appeal()
            {
                AppealCabinId = request.AppealCabinId,
                AppealCityId = request.AppealCityId,
                AppealCountryId = request.AppealCountryId,
                AppealOfficeId = request.AppealOfficeId,
                BirthDate = DateTime.SpecifyKind(request.BirthDate, DateTimeKind.Utc),
                FatherName = request.FatherName,
                Gender = request.Gender,
                MaidenName = request.MaidenName,
                MotherName = request.MotherName,
                Name = request.Name,
                PassportNumber = request.PassportNumber,
                Status = request.Status,
                Surname = request.Surname,
                ReferenceNumber = request.ReferenceNumber,
                HostName = request.HostName,
                FromOffline = request.FromOffline,
            };

            await _dbContext.Appeal.AddAsync(appeal);

            try
            {
                await _dbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                return new InsertAppealWithMetaDataFromOfflineResult
                {
                    Status = InsertAppealWithMetaDataFromOfflineStatus.InternalError,
                    Message = ex.Message,
                };
            }


            foreach (var detail in request.AppealDetails)
            {
                var appealDetail = new Entity.Entities.Appeal.AppealDetail()
                {
                    AppealId = appeal.Id,
                    Definition = detail.Definition ?? string.Empty,
                    InventoryId = detail.InventoryId,
                    Description = detail.Description
                };

                await _dbContext.AppealDetail.AddAsync(appealDetail);
            }

            await _dbContext.SaveChangesAsync();

            #region Save MetaData

            AppealMetaData appealMetaData = new AppealMetaData();
            appealMetaData.AppealId = appeal.Id;

            await _dbContext.AppealMetaData.AddAsync(appealMetaData);
            await _dbContext.SaveChangesAsync();


            var saveMetaData = StringCipher.EncryptLong(request.MetaDataSerialized, passKey);
            //var decrypted = StringCipher.DecryptLong(saveMetaData, passKey);
            var result = await SaveToStorageMetaData(request.ReferenceNumber, appealMetaData.Id.ToString(), GenerateStreamFromString(saveMetaData));


            #endregion

            //var result = await SaveToStorage(request.ReferenceNumber, GenerateStreamFromString(request.XmlData));

            return new InsertAppealWithMetaDataFromOfflineResult
            {
                Status = InsertAppealWithMetaDataFromOfflineStatus.Successful,
                Message = ServiceResources.SUCCESS,
                OfflineId = request.OfflineId,
            };
        }


        public async Task<InsertAppealWithMetaDataFastResult> InsertAppealWithMetaDataFast(InsertAppealWithMetaDataFastRequest request)
        {
            var validationResult = _validationService.Validate(typeof(InsertAppealWithMetaDataFastValidator), request);


            if (!validationResult.IsValid)
                return new InsertAppealWithMetaDataFastResult
                {
                    Status = InsertAppealWithMetaDataFastStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            #region Check db existing

            var cabinControl = await _dbContext.Cabin.AnyAsync(x => x.Id == request.AppealCabinId && x.IsDeleted == false);
            if (cabinControl == false)
            {
                return new InsertAppealWithMetaDataFastResult
                {
                    Status = InsertAppealWithMetaDataFastStatus.ResourceNotFound,
                    Message = ServiceResources.CABIN_NOT_FOUND
                };
            }

            var officeControl = await _dbContext.Office.AnyAsync(x => x.Id == request.AppealOfficeId && x.IsDeleted == false);
            if (officeControl == false)
            {
                return new InsertAppealWithMetaDataFastResult
                {
                    Status = InsertAppealWithMetaDataFastStatus.ResourceNotFound,
                    Message = ServiceResources.OFFICE_NOT_FOUND
                };
            }

            var countryControl = await _externalDbContext.Country.AnyAsync(x => x.Id == request.AppealCountryId);
            if (countryControl == false)
            {
                return new InsertAppealWithMetaDataFastResult
                {
                    Status = InsertAppealWithMetaDataFastStatus.ResourceNotFound,
                    Message = ServiceResources.COUNTRY_NOT_FOUND
                };
            }


            #endregion

            var appeal = new Entity.Entities.Appeal.Appeal()
            {
                AppealCabinId = request.AppealCabinId,
                AppealCityId = request.AppealCityId,
                AppealCountryId = request.AppealCountryId,
                AppealOfficeId = request.AppealOfficeId,
                BirthDate = DateTime.SpecifyKind(request.BirthDate, DateTimeKind.Utc),
                FatherName = request.FatherName,
                Gender = request.Gender,
                MaidenName = request.MaidenName,
                MotherName = request.MotherName,
                Name = request.Name,
                PassportNumber = request.PassportNumber,
                Status = request.Status,
                Surname = request.Surname,
                ReferenceNumber = request.ReferenceNumber,
                HostName = request.HostName,
            };

            await _dbContext.Appeal.AddAsync(appeal);

            await _dbContext.SaveChangesAsync();

            foreach (var detail in request.AppealDetails)
            {
                var appealDetail = new Entity.Entities.Appeal.AppealDetail()
                {
                    AppealId = appeal.Id,
                    Definition = detail.Definition ?? string.Empty,
                    InventoryId = detail.InventoryId,
                    Description = detail.Description
                };

                await _dbContext.AppealDetail.AddAsync(appealDetail);
            }

            await _dbContext.SaveChangesAsync();

            #region Save MetaData

            AppealMetaData appealMetaData = new AppealMetaData();
            appealMetaData.AppealId = appeal.Id;

            await _dbContext.AppealMetaData.AddAsync(appealMetaData);
            await _dbContext.SaveChangesAsync();

            #endregion



            //var result = await SaveToStorage(request.ReferenceNumber, GenerateStreamFromString(request.XmlData));

            return new InsertAppealWithMetaDataFastResult
            {
                Status = InsertAppealWithMetaDataFastStatus.Successful,
                Message = ServiceResources.SUCCESS,
                Id = appealMetaData.Id,
                AppealId = appeal.Id
            };
        }

        public async Task<SaveAppealMetaDataFullResult> SaveAppealMetaDataFull(SaveAppealMetaDataFullRequest request)
        {
            var validationResult = _validationService.Validate(typeof(SaveAppealMetaDataFullValidator), request);


            if (!validationResult.IsValid)
                return new SaveAppealMetaDataFullResult
                {
                    Status = SaveAppealMetaDataFullStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };


            #region Check db existing
            var appealMetaData = _dbContext.AppealMetaData.Where(n => n.Id == request.Id).FirstOrDefault();

            if (appealMetaData == null)
            {
                return new SaveAppealMetaDataFullResult
                {
                    Status = SaveAppealMetaDataFullStatus.ResourceNotFound,
                    Message = ServiceResources.APPEAL_METADATA_NOT_FOUND
                };
            }


            var appeal = _dbContext.Appeal.Where(n => n.Id == appealMetaData.AppealId).FirstOrDefault();
            if (appeal == null)
            {
                return new SaveAppealMetaDataFullResult
                {
                    Status = SaveAppealMetaDataFullStatus.ResourceNotFound,
                    Message = ServiceResources.APPEAL_NOT_FOUND
                };
            }

            #endregion            

            #region Save MetaData            

            var saveMetaData = StringCipher.EncryptLong(request.MetaDataSerialized, passKey);
            var result = await SaveToStorageMetaData(appeal.ReferenceNumber, appealMetaData.Id.ToString(), GenerateStreamFromString(saveMetaData));

            #endregion  


            return new SaveAppealMetaDataFullResult
            {
                Status = SaveAppealMetaDataFullStatus.Successful,
                Message = ServiceResources.SUCCESS,
                Id = appealMetaData.Id,
            };
        }

        public async Task<SaveDataByChunksResult> SaveAppealMetaDataByChunks(SaveDataByChunksRequest request)
        {
            var validationResult = _validationService.Validate(typeof(SaveDataByChunksValidator), request);


            if (!validationResult.IsValid)
                return new SaveDataByChunksResult
                {
                    Status = SaveDataByChunksStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };


            #region Check db existing
            var appealMetaData = _dbContext.AppealMetaData.Where(n => n.Id == request.RecordId).FirstOrDefault();

            if (appealMetaData == null)
            {
                return new SaveDataByChunksResult
                {
                    Status = SaveDataByChunksStatus.ResourceNotFound,
                    Message = ServiceResources.APPEAL_METADATA_NOT_FOUND
                };
            }


            var appeal = _dbContext.Appeal.Where(n => n.Id == appealMetaData.AppealId).FirstOrDefault();
            if (appeal == null)
            {
                return new SaveDataByChunksResult
                {
                    Status = SaveDataByChunksStatus.ResourceNotFound,
                    Message = ServiceResources.APPEAL_NOT_FOUND
                };
            }

            #endregion

            #region Save MetaData        

            string uniqueId = request.IsFirst ? DateTime.Now.ToString("yyyyMMddHHmmssfff") : request.UniqueId;
            string resultChunkData = request.IsFirst ? request.ChunkData : string.Empty;

            if (request.IsFirst)
            {
                if (!request.IsLast)
                {
                    await SaveToStorageMetaDataByChunks(appealMetaData.Id.ToString(), uniqueId, request.Part, GenerateStreamFromString(resultChunkData));
                }
            }
            else
            {
                //var availableChunkData = await LoadFromStorageMetaDataByChunk(appealMetaData.Id.ToString(), uniqueId);
                //resultChunkData = availableChunkData + resultChunkData;
                if (request.IsLast)
                {
                    var sb = new StringBuilder();
                    for (int part = 0; part < request.Part; part++)
                    {
                        var dataPart = await LoadFromStorageMetaDataByChunk(appealMetaData.Id.ToString(), uniqueId, part);
                        bool deleteResult = await DeleteFromStorageMetaDataByChunks(appealMetaData.Id.ToString(), uniqueId, part);
                        sb.Append(dataPart);
                    }
                    sb.Append(request.ChunkData);
                    resultChunkData = sb.ToString();


                }
                else
                {
                    await SaveToStorageMetaDataByChunks(appealMetaData.Id.ToString(), uniqueId, request.Part, GenerateStreamFromString(request.ChunkData));
                }
            }

            if (request.IsLast)
            {
                var saveMetaData = StringCipher.EncryptLong(resultChunkData, passKey);
                var saveResult = await SaveToStorageMetaData(appeal.ReferenceNumber, appealMetaData.Id.ToString(), GenerateStreamFromString(saveMetaData));
            }

            #endregion


            return new SaveDataByChunksResult
            {
                Status = SaveDataByChunksStatus.Successful,
                Message = ServiceResources.SUCCESS,
                Part = request.Part,
                FromOffline = request.FromOffline,
                UniqueId = uniqueId,
                RecordId = appealMetaData.Id,
                TotalPart = request.TotalPart
            };
        }

        public async Task<SaveDataByChunksResult> InsertAppealWithMetaDataByChunksFromOffline(SaveDataByChunksRequest requestChunk)
        {
            var validationResult = _validationService.Validate(typeof(SaveDataByChunksValidator), requestChunk);


            if (!validationResult.IsValid)
                return new SaveDataByChunksResult
                {
                    Status = SaveDataByChunksStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };


            #region Save MetaData        


            string resultChunkData = requestChunk.IsFirst ? requestChunk.ChunkData : string.Empty;

            if (requestChunk.IsFirst)
            {
                if (!requestChunk.IsLast)
                {
                    await SaveToStorageMetaDataByChunks(requestChunk.RecordId.ToString(), requestChunk.UniqueId, requestChunk.Part, GenerateStreamFromString(resultChunkData));
                }
            }
            else
            {
                if (requestChunk.IsLast)
                {
                    var sb = new StringBuilder();
                    for (int part = 0; part < requestChunk.Part; part++)
                    {
                        var dataPart = await LoadFromStorageMetaDataByChunk(requestChunk.RecordId.ToString(), requestChunk.UniqueId, part);
                        bool deleteResult = await DeleteFromStorageMetaDataByChunks(requestChunk.RecordId.ToString(), requestChunk.UniqueId, part);
                        sb.Append(dataPart);
                    }
                    sb.Append(requestChunk.ChunkData);
                    resultChunkData = sb.ToString();


                }
                else
                {
                    await SaveToStorageMetaDataByChunks(requestChunk.RecordId.ToString(), requestChunk.UniqueId, requestChunk.Part, GenerateStreamFromString(requestChunk.ChunkData));
                }
            }

            if (requestChunk.IsLast)
            {
                var request = JsonConvert.DeserializeObject<InsertAppealWithMetaDataFromOfflineRequest>(resultChunkData);
                request.OfflineId = requestChunk.UniqueId;

                validationResult = _validationService.Validate(typeof(InsertAppealWithMetaDataFromOfflineValidator), request);

                if (!validationResult.IsValid)
                    return new SaveDataByChunksResult
                    {
                        Status = SaveDataByChunksStatus.InvalidInput,
                        Message = ServiceResources.INVALID_INPUT_ERROR,
                        ValidationMessages = validationResult.ErrorMessages
                    };

                #region Check db existing

                var cabinControl = await _dbContext.Cabin.AnyAsync(x => x.Id == request.AppealCabinId && x.IsDeleted == false);
                if (cabinControl == false)
                {
                    return new SaveDataByChunksResult
                    {
                        Status = SaveDataByChunksStatus.ResourceNotFound,
                        Message = ServiceResources.CABIN_NOT_FOUND
                    };
                }

                var officeControl = await _dbContext.Office.AnyAsync(x => x.Id == request.AppealOfficeId && x.IsDeleted == false);
                if (officeControl == false)
                {
                    return new SaveDataByChunksResult
                    {
                        Status = SaveDataByChunksStatus.ResourceNotFound,
                        Message = ServiceResources.OFFICE_NOT_FOUND
                    };
                }

                var countryControl = await _externalDbContext.Country.AnyAsync(x => x.Id == request.AppealCountryId);
                if (countryControl == false)
                {
                    return new SaveDataByChunksResult
                    {
                        Status = SaveDataByChunksStatus.ResourceNotFound,
                        Message = ServiceResources.COUNTRY_NOT_FOUND
                    };
                }

                #endregion

                var appeal = new Entity.Entities.Appeal.Appeal()
                {
                    AppealCabinId = request.AppealCabinId,
                    AppealCityId = request.AppealCityId,
                    AppealCountryId = request.AppealCountryId,
                    AppealOfficeId = request.AppealOfficeId,
                    BirthDate = DateTime.SpecifyKind(request.BirthDate, DateTimeKind.Utc),
                    FatherName = request.FatherName,
                    Gender = request.Gender,
                    MaidenName = request.MaidenName,
                    MotherName = request.MotherName,
                    Name = request.Name,
                    PassportNumber = request.PassportNumber,
                    Status = request.Status,
                    Surname = request.Surname,
                    ReferenceNumber = request.ReferenceNumber,
                    HostName = request.HostName,
                    FromOffline = request.FromOffline,
                };

                await _dbContext.Appeal.AddAsync(appeal);

                try
                {
                    await _dbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    return new SaveDataByChunksResult
                    {
                        Status = SaveDataByChunksStatus.InternalError,
                        Message = ex.Message,
                    };
                }


                foreach (var detail in request.AppealDetails)
                {
                    var appealDetail = new Entity.Entities.Appeal.AppealDetail()
                    {
                        AppealId = appeal.Id,
                        Definition = detail.Definition ?? string.Empty,
                        InventoryId = detail.InventoryId,
                        Description = detail.Description
                    };

                    await _dbContext.AppealDetail.AddAsync(appealDetail);
                }

                await _dbContext.SaveChangesAsync();

                AppealMetaData appealMetaData = new AppealMetaData();
                appealMetaData.AppealId = appeal.Id;

                await _dbContext.AppealMetaData.AddAsync(appealMetaData);
                await _dbContext.SaveChangesAsync();

                var saveMetaData = StringCipher.EncryptLong(request.MetaDataSerialized, passKey);
                var saveResult = await SaveToStorageMetaData(request.ReferenceNumber, appealMetaData.Id.ToString(), GenerateStreamFromString(saveMetaData));
            }

            #endregion


            return new SaveDataByChunksResult
            {
                Status = SaveDataByChunksStatus.Successful,
                Message = ServiceResources.SUCCESS,
                Part = requestChunk.Part,
                FromOffline = requestChunk.FromOffline,
                UniqueId = requestChunk.UniqueId,
                RecordId = requestChunk.RecordId,
                TotalPart = requestChunk.TotalPart
            };
        }


        public async Task<InsertAppealMetaDataResult> InsertAppealMetaData(InsertAppealMetaDataRequest request)
        {
            var validationResult = _validationService.Validate(typeof(InsertAppealMetaDataValidator), request);


            if (!validationResult.IsValid)
                return new InsertAppealMetaDataResult
                {
                    Status = InsertAppealMetaDataStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            #region Check db existing

            var appeal = _dbContext.Appeal.Where(n => n.Id == request.AppealId).FirstOrDefault();
            if (appeal == null)
            {
                return new InsertAppealMetaDataResult
                {
                    Status = InsertAppealMetaDataStatus.ResourceNotFound,
                    Message = ServiceResources.APPEAL_NOT_FOUND
                };
            }

            #endregion

            var appealMetaData = new Entity.Entities.Appeal.AppealMetaData()
            {
                AppealId = request.AppealId,
                ParentId = request.ParentId,
            };

            await _dbContext.AppealMetaData.AddAsync(appealMetaData);

            await _dbContext.SaveChangesAsync();

            #region Save MetaData            

            var saveMetaData = StringCipher.EncryptLong(request.MetaDataSerialized, passKey);
            var result = await SaveToStorageMetaData(appeal.ReferenceNumber, appealMetaData.Id.ToString(), GenerateStreamFromString(saveMetaData));

            #endregion           

            return new InsertAppealMetaDataResult
            {
                Status = InsertAppealMetaDataStatus.Successful,
                Message = ServiceResources.SUCCESS,
                Id = appealMetaData.Id
            };
        }



        private async Task<List<AppealDto>> GenerateAppealsDto(List<Entity.Entities.Appeal.Appeal> appealsEntity)
        {
            var appealsDto = _mapper.Map<List<AppealDto>>(appealsEntity);

            var appealIds = appealsEntity.Select(n => n.Id).ToList();

            var appealMetaDatasEntity = _dbContext.AppealMetaData.Where(n => appealIds.Contains(n.AppealId)).ToList();

            var appealMetaDatasDtoAll = _mapper.Map<List<AppealMetaDataDto>>(appealMetaDatasEntity);
            var appealMetaDatasDto = appealMetaDatasDtoAll.Where(n => n.ParentId == null).ToList();
            foreach (var row in appealMetaDatasDto)
            {
                row.Children = appealMetaDatasDtoAll.Where(n => n.ParentId != null && n.ParentId.Value == row.Id).ToList();
            }

            #region Relative table values
            var countryIds = appealsDto.Select(n => n.AppealCountryId).ToList();
            var cityIds = appealsDto.Select(n => n.AppealCityId).ToList();
            var officeIds = appealsDto.Select(n => n.AppealOfficeId).ToList();
            var cabinIds = appealsDto.Select(n => n.AppealCabinId).ToList();

            var allCountries = _externalDbContext.Country.Where(n => countryIds.Contains(n.Id)).ToList();

            var queryBranches = await _externalDbContext.Branch.Where(n => !n.IsDeleted && cityIds.Contains(n.Id)).ToListAsync();

            var allCities = queryBranches.Select(c => new BranchDto
            {
                Id = c.Id,
                CountryId = c.CountryId,
                Name = c.CityName,
            }).OrderBy(c => c.Name).ToList();


            var allOffices = _dbContext.Office.Where(n => officeIds.Contains(n.Id)).ToList();
            var allCabins = _dbContext.Cabin.Where(n => cabinIds.Contains(n.Id)).ToList();

            #endregion

            foreach (var row in appealsDto)
            {
                row.MetaDatas = appealMetaDatasDto.Where(n => n.AppealId == row.Id).ToList();

                row.AppealCountryName = allCountries.FirstOrDefault(n => n.Id == row.AppealCountryId)?.Name;
                row.AppealCityName = allCities.FirstOrDefault(n => n.Id == row.AppealCityId)?.Name;
                row.AppealOfficeName = allOffices.FirstOrDefault(n => n.Id == row.AppealOfficeId)?.Name;
                row.AppealCabinName = allCabins.FirstOrDefault(n => n.Id == row.AppealCabinId)?.Name;
            }

            return appealsDto;
        }

        public async Task<GetAppealsResult> GetAppealsByXml(GetAppealsByXmlRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetAppealsByXmlValidator), request);
            if (!validationResult.IsValid)
                return new GetAppealsResult
                {
                    Status = GetAppealsStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var appealsEntity = _dbContext.Appeal.Where(n =>
                n.PassportNumber == request.PassportNumber &&
                n.Name == request.Name &&
                n.Surname == request.Surname &&
                n.FatherName == request.FatherName &&
                n.MotherName == request.MotherName &&
                n.MaidenName == request.MaidenName &&
                n.Gender == request.Gender &&
                n.BirthDate == request.BirthDate
            ).ToList();


            var appealsDto = await GenerateAppealsDto(appealsEntity);

            return new GetAppealsResult
            {
                Status = GetAppealsStatus.Successful,
                Message = ServiceResources.SUCCESS,
                Appeals = appealsDto
            };
        }

        public async Task<GetAppealsResult> GetAppealsByPassportAndCountry(GetAppealsByPassportAndCountryRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetAppealsByPassportAndCountryValidator), request);
            if (!validationResult.IsValid)
                return new GetAppealsResult
                {
                    Status = GetAppealsStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };


            var appealsEntity = _dbContext.Appeal.Where(n => n.PassportNumber == request.PassportNumber && n.AppealCountryId == request.CountryId).ToList();


            var appealsDto = await GenerateAppealsDto(appealsEntity);

            return new GetAppealsResult
            {
                Status = GetAppealsStatus.Successful,
                Message = ServiceResources.SUCCESS,
                Appeals = appealsDto
            };
        }

        public async Task<GetFullAppealMetaDataResult> GetFullAppealMetaDataById(GetFullAppealMetaDataRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetFullAppealMetaDataValidator), request);
            if (!validationResult.IsValid)
                return new GetFullAppealMetaDataResult
                {
                    Status = GetFullAppealMetaDataStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var appealMetaDataEntity = _dbContext.AppealMetaData.FirstOrDefault(n => n.Id == int.Parse(request.AppealMetaDataId));
            if (appealMetaDataEntity == null)
            {
                return new GetFullAppealMetaDataResult
                {
                    Status = GetFullAppealMetaDataStatus.ResourceNotFound,
                    Message = ServiceResources.RESOURCE_NOT_FOUND
                };
            }

            var appealEntity = _dbContext.Appeal.FirstOrDefault(n => n.Id == appealMetaDataEntity.AppealId);
            if (appealEntity == null)
            {
                return new GetFullAppealMetaDataResult
                {
                    Status = GetFullAppealMetaDataStatus.AppealNotFound,
                    Message = ServiceResources.RESOURCE_NOT_FOUND
                };
            }


            var appealMetaDataDto = _mapper.Map<AppealMetaDataDto>(appealMetaDataEntity);

            #region Load full metadata from minio
            /// TODO load from minio 
            var txtEncrypted = await LoadFromStorageMetaData(appealEntity.ReferenceNumber, appealMetaDataEntity.Id.ToString());
            appealMetaDataDto.MetaDataSerialized = StringCipher.DecryptLong(txtEncrypted, passKey);
            #endregion

            return new GetFullAppealMetaDataResult
            {
                Status = GetFullAppealMetaDataStatus.Successful,
                Message = ServiceResources.SUCCESS,
                AppealMetaData = appealMetaDataDto
            };
        }

        #endregion

        #region Private Methods

        public static MemoryStream GenerateStreamFromString(string s)
        {
            var stream = new MemoryStream();
            var writer = new StreamWriter(stream);
            writer.Write(s);
            writer.Flush();
            stream.Position = 0;
            return stream;

            //using (var stream = new MemoryStream())
            //{
            //    using (var writer = new StreamWriter(stream))
            //    {
            //        writer.Write(s);
            //        writer.Flush();
            //        stream.Position = 0;
            //        return stream;
            //    }
            //}


        }

        public async Task<bool> SaveToStorage(string applicationId, MemoryStream document)
        {
            var path = $"{applicationId}/{applicationId}.xml";

            //var result = true;
            var result = await _fileStorage.SaveFileAsync("biometric", path, document, MimeType.GetMimeType("xml"));

            return await Task.FromResult(result);
        }

        private string GetMinioChunkPath(string metaDataId, string uniqueId, int part)
        {
            return $"chunks/metadata/{metaDataId}_{uniqueId}_{part}.txt"; ;
        }

        public async Task<bool> SaveToStorageMetaDataByChunks(string metaDataId, string uniqueId, int part, MemoryStream document)
        {
            var path = GetMinioChunkPath(metaDataId, uniqueId, part);

            var result = await _fileStorage.SaveFileAsync("biometric", path, document, MimeType.GetMimeType("txt"));

            return await Task.FromResult(result);
        }

        public async Task<bool> DeleteFromStorageMetaDataByChunks(string metaDataId, string uniqueId, int part)
        {
            var path = GetMinioChunkPath(metaDataId, uniqueId, part);

            var result = await _fileStorage.DeleteFileAsync("biometric", path);

            return await Task.FromResult(result);
        }

        public async Task<string> LoadFromStorageMetaDataByChunk(string metaDataId, string uniqueId, int part)
        {
            var path = GetMinioChunkPath(metaDataId, uniqueId, part);


            string result = null;
            using (var stream = await _fileStorage.GetFileStreamAsync("biometric", path))
            {
                if (stream == null)
                {
                    throw new Exception("Chunk Metadata not found");
                }
                using (var reader = new StreamReader(stream))
                {
                    reader.BaseStream.Seek(0, SeekOrigin.Begin);
                    result = reader.ReadToEnd();
                }
            }

            return result;
        }

        public async Task<bool> SaveToStorageMetaData(string applicationId, string metaDataId, MemoryStream document)
        {
            var path = $"{applicationId}/metadata/{metaDataId}.txt";

            //var result = true;
            var result = await _fileStorage.SaveFileAsync("biometric", path, document, MimeType.GetMimeType("txt"));

            return await Task.FromResult(result);
        }

        public async Task<string> LoadFromStorageMetaData(string applicationId, string metaDataId)
        {
            var path = $"{applicationId}/metadata/{metaDataId}.txt";

            //var result = true;
            //var result = await _fileStorage.SaveFileAsync("biometric", path, document, MimeType.GetMimeType("txt"));

            //return await Task.FromResult(result);

            string result = null;
            using (var stream = await _fileStorage.GetFileStreamAsync("biometric", path))
            {
                if (stream == null)
                {
                    throw new Exception("Metadata not found");
                }
                using (var reader = new StreamReader(stream))
                {
                    //StringBuilder stringBuilder = new StringBuilder();
                    reader.BaseStream.Seek(0, SeekOrigin.Begin);
                    result = reader.ReadToEnd();
                }
            }

            return result;
        }

        #endregion
    }
}
