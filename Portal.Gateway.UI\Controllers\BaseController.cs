﻿using System;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Models;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Threading;
using Portal.Gateway.ApiModel.Responses.Appointment.Application;
using Portal.Gateway.Common.Utility.Helpers;
using Microsoft.AspNetCore.Mvc.Rendering;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.UI.Helpers;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Controllers
{
    public class BaseController<TController> : Controller
    {
        private static readonly Serilog.ILogger _logger = Serilog.Log.ForContext<TController>();

        protected AppSettings AppSettings { get; }

        protected readonly ICacheHelper CacheHelper;

        protected Dictionary<string, string> PortalGatewayApiDefaultRequestHeaders
        {
            get
            {
                var headers = new Dictionary<string, string>
                {
                    { "apiKey", AppSettings.PortalGatewayApiKey },
                    { "languageId", LanguageId.ToString() },
                    { "corporateId", UserSession?.CorporateId },
                    { "UserId", UserSession?.UserId.ToString() }
                };

                return headers;
            }
        }
        
        protected WebHeaderCollection QMSApiDefaultRequestHeaders
        {
            get
            {
                var headers = new Dictionary<string, string>
                {
                    {"Accept-Language", LanguageId == 1 ? "tr-TR" : "en"},
                    {"UserId", UserSession?.UserId.ToString()},
                    {"BranchId", UserSession?.BranchId.ToString()}
                };

                var webHeaderCollection = new WebHeaderCollection();

                foreach (var item in headers)
                {
                    webHeaderCollection.Add(item.Key, item.Value);
                }

                return webHeaderCollection;
            }
        }

        protected int LanguageId
        {
            get
            {
                var currentCulture = Thread.CurrentThread.CurrentCulture;
                int currentLanguageId = (int)Language.Turkish;

                switch (currentCulture.Name)
                {
                    case "tr-TR":
                        currentLanguageId = (int)Language.Turkish;
                        break;
                    case "en-US":
                        currentLanguageId = (int)Language.English;
                        break;
                    default:
                        break;
                }

                return currentLanguageId;
            }
        }

        protected AdminModel AdminSession => SessionExtensions.Get<AdminModel>(HttpContext.Session, SessionKeys.AdminSession);

        protected UserModel UserSession => SessionExtensions.Get<UserModel>(HttpContext.Session, SessionKeys.UserSession);

        protected void UpdateSessionLastPreApplicationId(int preApplicationId, bool isForSet)
        {
	        var userModel = UserSession;

	        userModel.LastTakenAppointmentId = isForSet ? preApplicationId : (int?)null;

	        try
	        {
		        SessionExtensions.Set(HttpContext.Session, SessionKeys.UserSession, userModel);
	        }
	        catch (Exception)
	        {
		        //ignore
	        }
        }

        protected void ClearModelStateErrors()
        {
            foreach (var modelValue in ModelState.Values)
            {
                modelValue.Errors.Clear();
            }
        }

        protected BaseController(IOptions<AppSettings> appSettings, ICacheHelper cacheHelper)
        {
            AppSettings = appSettings.Value;
            CacheHelper = cacheHelper;
        }

        protected virtual void OnException(ExceptionContext filterContext)
        {

        }

        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            var descriptor = (filterContext.ActionDescriptor as ControllerActionDescriptor);
            var areaName = descriptor.ControllerTypeInfo.GetCustomAttribute<AreaAttribute>()?.RouteValue;
            var controllerName = descriptor.ControllerName;
            var actionName = descriptor.ActionName;

            if (descriptor.ControllerTypeInfo.GetCustomAttributes(typeof(AllowAnonymousAttribute), true).Any() ||
                descriptor.MethodInfo.GetCustomAttributes(typeof(AllowAnonymousAttribute), true).Any())
                return;

            if (actionName == "Logout")
                return;

            if (areaName == "Admin")
            {
                if (AdminSession == null)
                {
                    filterContext.Result = RedirectToAction("Login", "Admin", new { Area = "" });
                    return;
                }
            }
            else
            {
                if (UserSession == null)
                {
                    filterContext.Result = RedirectToAction("Login", "User", new { Area = "" });
                    return;
                }

                if (UserSession.IsSysAdmin)
                {
                    //continues
                }
                else
                {
                    if (!UserSession.ModuleId.HasValue || !UserSession.BranchId.HasValue)
                    {
                        if (controllerName == "User" && (actionName == "SelectModuleBranch" || actionName == "SetModuleBranch"))
                            return;
                        else
                        {
                            filterContext.Result = RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
                            return;
                        }
                    }

                    if (UserSession.RoleIds.Count > 0)
                    {
                        var roleActions = CacheHelper.GetRoleActionsAsync().GetAwaiter().GetResult();

                        var availableActions = roleActions.RoleActionSites
                                                .Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
                                                .SelectMany(p => p.RoleActions)
                                                .ToList();

                        if (!availableActions.Any(p => p.Action != null &&
                                                   p.Action.Area == areaName &&
                                                   p.Action.Controller == controllerName &&
                                                   p.Action.Method == actionName &&
                                                   p.Action.IsActive
                                                ))
                        {
                            if (filterContext.HttpContext.Request.IsAjaxRequest())
                            {
                                filterContext.Result = new JsonResult(
                                    new ResultModel()
                                    {
                                        Message = SiteResources.NotSufficientPrivileges,
                                        ResultType = ResultType.Danger
                                    }
                                );
                            }
                            else
                            {
                                //TempData.Put("Notification", new ResultModel { Message = SiteResources.NotSufficientPrivileges, ResultType = ResultType.Danger });
                                _logger.Warning($"(OnActionExecuting) {SiteResources.NotSufficientPrivileges}... UserId: {UserSession.UserId} - UserEmail: {UserSession.Email} - AreaName: {areaName} - ControllerName: {controllerName} - ActionName: {actionName}");
                                filterContext.Result = Unauthorized();
                            }

                            return;
                        }

                    }
                }
            }

            if (areaName == "Admin")
            {
                ViewData["AdminActiveMenuAreaName"] = areaName;
                ViewData["AdminActiveMenuControllerName"] = controllerName;
                ViewData["AdminActiveMenuActionName"] = actionName;
            }
            else
            {
                ViewData["UserActiveMenuAreaName"] = areaName;
                ViewData["UserActiveMenuControllerName"] = controllerName;
                ViewData["UserActiveMenuActionName"] = actionName;
            }

            base.OnActionExecuting(filterContext);
        }

        #region VisaCategory

        protected async Task<List<BranchApplicationCountryVisaCategoriesApiResponse.VisaCategory>> GetCachedVisaCategories()
        {
            var categories = await CacheHelper.GetVisaCategoriesAsync();

            return categories;
        }

        protected string GetVisaCategoryNameFromId(int id, List<BranchApplicationCountryVisaCategoriesApiResponse.VisaCategory> visaCategoryDataList) 
        {
            var data = visaCategoryDataList.FirstOrDefault(s => s.Id == id) == null ? string.Empty :
                visaCategoryDataList.FirstOrDefault(s => s.Id == id).Translations.FirstOrDefault(t => t.LanguageId == LanguageId) == null ?
                visaCategoryDataList.FirstOrDefault(s => s.Id == id)?.Translations.FirstOrDefault().Name :
                                visaCategoryDataList.FirstOrDefault(s => s.Id == id).Translations.FirstOrDefault(t => t.LanguageId == LanguageId).Name;

            return data.ToTitleCase();
        }

        protected async Task<List<SelectListItem>> GetListedVisaCategories() 
        {
            var dataList = await GetCachedVisaCategories();

            var result = new Dictionary<int, string>();

            foreach (var item in dataList)
            {
                result.Add(item.Id, GetVisaCategoryNameFromId(item.Id, dataList));
            }

            var dataSet = result.Select(p => new SelectListItem()
            {
                Text = p.Value,
                Value = p.Key.ToString()
            }).OrderBy(q => q.Text).ToList();

            return dataSet;
        }

        #endregion
    }

    public static class HttpRequestExtensions
    {
        private const string RequestedWithHeader = "X-Requested-With";
        private const string XmlHttpRequest = "XMLHttpRequest";

        public static bool IsAjaxRequest(this Microsoft.AspNetCore.Http.HttpRequest request)
        {
            if (request == null)
            {
                throw new System.ArgumentNullException("request");
            }

            if (request.Headers != null)
            {
                return request.Headers[RequestedWithHeader] == XmlHttpRequest;
            }

            return false;
        }
    }
}