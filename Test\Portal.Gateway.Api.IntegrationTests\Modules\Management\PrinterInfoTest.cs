﻿using Portal.Gateway.ApiModel.Requests.Management.Branch;
using Portal.Gateway.ApiModel.Responses.PrinterAgent;

namespace Portal.Gateway.Api.IntegrationTests.Modules.Management;

public sealed class PrinterInfoTest : BaseIntegrationTest
{
    public PrinterInfoTest(SharedTestFixture fixture) : base(fixture) { }

    #region Add Printer Info Test

    [Fact]
    internal async Task AddPrinterInfo_WhenPrinterInfoIsValid_ShouldAddPrinterInfo()
    {
        // Arrange
        var request = GenerateAddUpdatePrinterInfoApiRequest(0);

        // Act
        var result = await HttpClient.PostAndGetResultWithoutApiResponseAsync<AddApiResponse>(ApiMethodName.PrinterAgent.CreatePrinterInfo, request);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().BePositive();

        var addedPrinterInfo = DbContext.PrinterInfo
            .AsNoTracking()
            .OrderByDescending(o => o.Id)
            .First();

        request.Should().BeEquivalentTo(addedPrinterInfo,
            options => options
                .Excluding(e => e.Id)
                .ExcludingMissingMembers());

        addedPrinterInfo.Id.Should().BeGreaterThan(0);
        addedPrinterInfo.IsActive.Should().BeTrue();
        addedPrinterInfo.IsDeleted.Should().BeFalse();
    }
    #endregion

    #region Update PrinterInfo Test
    [Fact]
    internal async Task UpdatePrinterInfo_WhenPrinterInfoIsValid_ShouldUpdatePrinterInfo()
    {
        // Arrange
        var existingPrinterInfo = CreateTestPrinterInfo();
        var request = GenerateAddUpdatePrinterInfoApiRequest(existingPrinterInfo.Id);

        // Use different values for update
        request.Name = Faker.Person.FirstName;

        // Act
        var result = await HttpClient.PutAndGetResultWithoutApiResponseAsync<UpdateApiResponse>(ApiMethodName.PrinterAgent.UpdatePrinterInfo, request);

        // Assert

        result.Should().NotBeNull();
        result.Result.Should().BeTrue();

        var updatedPrinterInfo = DbContext.PrinterInfo
            .AsNoTracking()
            .First(b => b.Id == existingPrinterInfo.Id);

        request.Should().BeEquivalentTo(updatedPrinterInfo,
            options => options.ExcludingMissingMembers());

        // Verify updated fields
        updatedPrinterInfo.UpdatedAt.Should().NotBeNull();
        updatedPrinterInfo.UpdatedBy.Should().NotBeNull();
    }

    [Fact]
    internal async Task UpdatePrinterInfo_WhenPrinterInfoNotFound_ShouldReturnNoFoundDataError()
    {
        // Arrange
        var request = GenerateAddUpdatePrinterInfoApiRequest(int.MaxValue);

        // Act
        var result = await HttpClient.PutAndGetResultWithoutApiResponseAsync<UpdateApiResponse>(ApiMethodName.PrinterAgent.UpdatePrinterInfo, request);

        // Assert
        result.Should().NotBeNull();
        result.Result.Should().BeFalse();
        result.Message.Should().Be(ApiErrorCodes.InternalServerError);
    }
    #endregion

    #region Private Methods
    private PrinterInfoApiRequest GenerateAddUpdatePrinterInfoApiRequest(int id)
    {
        var user = TestDataSeeder.GetRandomTestUser();

        return new PrinterInfoApiRequest
        {
            Id = id,
            PrinterTypeId = CreateTestPrinterType().Id,
            PrinterAgentId = CreateTestPrinterAgent().Id,
            Name = Faker.Person.FirstName,
            UserId = user.Id,
            CopyCount = Faker.Random.Int(1, 5),
            ICRTypeId = Faker.Random.Int(1, 20)
        };
    }

    private PrinterInfo CreateTestPrinterInfo()
    {
        var user = TestDataSeeder.GetRandomTestUser();

        var printerInfo = new Entity.Entities.Portal.PrinterInfo
        {
            PrinterTypeId = CreateTestPrinterType().Id,
            PrinterAgentId = CreateTestPrinterAgent().Id,
            Name = Faker.Person.FirstName,
            UpdatedBy = user.Id,
            UpdatedAt = DateTime.Now,
            CopyCount = Faker.Random.Int(1, 5),
            ICRTypeId = Faker.Random.Int(1, 20),
            IsActive = true,
            IsDeleted = false
        };

        DbContext.PrinterInfo.Add(printerInfo);
        DbContext.SaveChanges();
        return printerInfo;
    }

    private PrinterAgent CreateTestPrinterAgent()
    {
        var branch = CreateTestBranch();
        var user = TestDataSeeder.GetRandomTestUser();

        var printerAgent = new Entity.Entities.Portal.PrinterAgent
        {
            HostName = Faker.Person.FirstName,
            BranchId = branch.Id,
            UpdatedBy = user.Id,
            UpdatedAt = DateTime.Now,
            IsActive = true,
            IsDeleted = false
        };

        DbContext.PrinterAgent.Add(printerAgent);
        DbContext.SaveChanges();
        return printerAgent;
    }

    private PrinterType CreateTestPrinterType()
    {
        var branch = CreateTestBranch();
        var user = TestDataSeeder.GetRandomTestUser();

        var printertype = new Entity.Entities.Portal.PrinterType
        {
            TypeName = Faker.Person.FirstName,
            UpdatedBy = user.Id,
            UpdatedAt = DateTime.Now,
            IsActive = true,
            IsDeleted = false
        };

        DbContext.PrinterType.Add(printertype);
        DbContext.SaveChanges();
        return printertype;
    }

    private Branch CreateTestBranch()
    {
        var country = TestDataSeeder.GetRandomCountry();
        var provider = TestDataSeeder.GetRandomProvider();

        var branch = new Branch
        {
            CountryId = country.Id,
            InsuranceProviderId = provider.Id,
            Address = Faker.Address.StreetAddress(),
            Email = Faker.Internet.Email(),
            Telephone = Faker.Phone.PhoneNumber(),
            CityName = Faker.Address.City(),
            Mission = Faker.Company.CatchPhrase(),
            InvoiceNumber = Faker.Random.AlphaNumeric(4),
            CorporateName = Faker.Company.CompanyName(),
            BranchTranslations = new List<BranchTranslation>
            {
                new BranchTranslation
                {
                    LanguageId = Faker.Random.Int(1, 2),
                    Name = Faker.Company.CompanyName()
                }
            },
            BranchDataTranslation = new List<BranchDataTranslation>
            {
                new BranchDataTranslation
                {
                    LanguageId = Faker.Random.Int(1, 2),
                    Address = Faker.Address.StreetAddress(),
                    CorporateName = Faker.Company.CompanyName(),
                    InvoiceNumber = Faker.Random.AlphaNumeric(4),
                    Mission = Faker.Company.CatchPhrase(),
                    CityName = Faker.Address.City()
                }
            },
        };

        DbContext.Branch.Add(branch);
        DbContext.SaveChanges();
        return branch;
    }

    #endregion
}
