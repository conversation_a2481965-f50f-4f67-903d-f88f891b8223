﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Portal.Gateway.Entity.EFMigrationsHistory.PortalDb
{
    /// <inheritdoc />
    public partial class PushNotification_NewColumns : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Subject",
                table: "PushNotificationTranslation",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "LanguageId",
                table: "PushNotificationHistory",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "LanguageId",
                table: "CustomerUserDevice",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Subject",
                table: "PushNotificationTranslation");

            migrationBuilder.DropColumn(
                name: "LanguageId",
                table: "PushNotificationHistory");

            migrationBuilder.DropColumn(
                name: "LanguageId",
                table: "CustomerUserDevice");
        }
    }
}
