﻿using Gateway.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Management.Branch;
using Portal.Gateway.ApiModel.Requests.NotificationManagement;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.NotificationManagement.Notification;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.Branch;
using Portal.Gateway.UI.Areas.NotificationManagement.ViewModels.Notification;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.NotificationManagement.Controllers
{
    [Area("NotificationManagement")]
    public class NotificationController : BaseController<NotificationController>
    {
        public NotificationController(IOptions<AppSettings> appSettings, ICacheHelper cacheHelper) : base(appSettings, cacheHelper)
        {
        }

        #region Get

        [Action(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> GetPaginatedNotificationList([DataSourceRequest] DataSourceRequest request, NotificationFilterViewModel filterViewModel)
        {
            var paginatedData = new List<NotificationViewModel>();

            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var genericFilter = request.GetGenericFilter();

            var apiRequest = new GetPaginatedNotificationListApiRequest
            {
                StartDate = filterViewModel.FilterStartDate,
                EndDate = filterViewModel.FilterEndDate,
                NationalityId = filterViewModel.FilterNationalityId,
                LocationId = filterViewModel.FilterLocationId,
                StatusId = filterViewModel.FilterStatusId,
                GenericFilter = genericFilter,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<GetPaginatedNotificationListApiResponse>>>
                (apiRequest,ApiMethodName.NotificationManagement.GetPaginatedNotificationList, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            if (apiResponse.Data != null && apiResponse.Data.Items.Count != 0)
            {
                paginatedData = apiResponse.Data.Items.First().Notifications.Select(p => new NotificationViewModel()
                {
                    Status = EnumHelper.GetEnumDescription(typeof(NotificationStatusType), p.StatusId.ToString()),
                    Location = p.Location,
                    NotificationNumber = p.NotificationNumber,
                    StatusId = p.StatusId,
                    EncryptedId = p.EncryptedId,
                    Nationality = p.Nationality,
                    NotificationTitle = p.NotificationTitle,
                    SendTime = p.SendTime
                }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data?.TotalItemCount ?? 0 });
        }

        [HttpGet]
        public async Task<IActionResult> GetAllNotifications()
        {
            var dataList = new List<SelectListItem>();

            var apiRequest = new GetPaginatedNotificationListApiRequest
            {
                Pagination = new PaginationApiRequest
                {
                    Page = 1,
                    PageSize = int.MaxValue,
                    OrderBy = "Id",
                    SortDirection = ListSortDirection.Ascending
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<GetPaginatedNotificationListApiResponse>>>
                (apiRequest, ApiMethodName.NotificationManagement.GetPaginatedNotificationList, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            if (apiResponse.Data != null && apiResponse.Data.Items.Count != 0)
            {
                dataList = apiResponse.Data.Items.First().Notifications.Select(x => new SelectListItem
                {
                    Value = x.EncryptedId.ToString(),
                    Text = $"{x.NotificationTitle}"
                }).ToList();
            }

            return Json(dataList);
        }

        [HttpGet]
        public async Task<IActionResult> GetNotificationDetailForSend(string encryptedId)
        {
            var id = encryptedId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<GetNotificationDetailApiResponse>>
                    (ApiMethodName.NotificationManagement.GetNotificationDetail + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var traDictionary = EnumHelper.GetEnumAsDictionary(typeof(PushNotificationTranslationLanguage));

            var viewModel = new SendNotificationViewModel()
            {
                LocationId = apiResponse.Data.Notification.LocationId,
                IsSendNow = true,
                IsSendScheduled = false,
                NationalityId = apiResponse.Data.Notification.NationalityId,
                Translations = apiResponse.Data.Notification.Translations.Select(s => new NotificationTranslationViewModel
                {
                    LanguageId = s.LanguageId,
                    Id = s.Id,
                    IsActive = CheckIsActiveByLanguage(s.IsActive, s.LanguageId),
                    Subject = s.Subject,
                    Name = s.Name,
                    Language = traDictionary.First(r => r.Key == s.LanguageId).Value
                }).OrderTranslationsByLanguagePriority(
                t => t.LanguageId,
                (int)PushNotificationTranslationLanguage.English, (int)PushNotificationTranslationLanguage.Turkish)
            .ToList()
            };

            return Json(viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetNotificationDetail(string encryptedId)
        {
            var id = encryptedId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<GetNotificationDetailApiResponse>>
                    (ApiMethodName.NotificationManagement.GetNotificationDetail + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var traDictionary = EnumHelper.GetEnumAsDictionary(typeof(PushNotificationTranslationLanguage));

            var viewModel = new NotificationViewModel()
            {
                Status = EnumHelper.GetEnumDescription(typeof(NotificationStatusType), apiResponse.Data.Notification.StatusId.ToString()),
                Location = apiResponse.Data.Notification.Location,
                NotificationNumber = apiResponse.Data.Notification.NotificationNumber,
                EncryptedId = apiResponse.Data.Notification.EncryptedId,
                Nationality = apiResponse.Data.Notification.Nationality,
                NotificationTitle = apiResponse.Data.Notification.NotificationTitle,
                SendTime = apiResponse.Data.Notification.SendTime,
                CreatedAt = apiResponse.Data.Notification.CreatedAt,
                CreatedBy = apiResponse.Data.Notification.CreatedBy,
                SendAt = apiResponse.Data.Notification.SendAt,
                ScheduledAt = apiResponse.Data.Notification.ScheduledAt,
                Languages = apiResponse.Data.Notification.Languages,
                Translations = apiResponse.Data.Notification.Translations.Select(s => new NotificationTranslationViewModel
                {
                    Name = s.Name,
                    Subject = s.Subject,
                    Language = traDictionary.First(r => r.Key == s.LanguageId).Value
                }).OrderTranslationsByLanguagePriority(
                t => t.LanguageId,
                (int)PushNotificationTranslationLanguage.English, (int)PushNotificationTranslationLanguage.Turkish)
            };

            viewModel.Languages = string.Join(",", viewModel?.Translations?.Where(r => !r.Name.IsNullOrWhitespace()).Select(s => s.Language ?? string.Empty) ?? []);

            return PartialView("_NotificationDetail", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> PartialUpdateNotification(string encryptedId, bool isDuplicated)
        {
            var id = encryptedId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<GetNotificationDetailApiResponse>>
                    (ApiMethodName.NotificationManagement.GetNotificationDetail + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var traDictionary = EnumHelper.GetEnumAsDictionary(typeof(PushNotificationTranslationLanguage));

            var viewModel = new AddUpdateNotificationViewModel()
            {
                EncryptedId = apiResponse.Data.Notification.EncryptedId,
                NotificationTitle = apiResponse.Data.Notification.NotificationTitle,
                IsDuplicated = isDuplicated,
                Translations = apiResponse.Data.Notification.Translations.Select(s => new NotificationTranslationViewModel
                {
                    Id = s.Id,
                    Name = s.Name,
                    Language = traDictionary.First(r => r.Key == s.LanguageId).Value,
                    Subject = s.Subject,
                    LanguageId = s.LanguageId,
                    IsActive = CheckIsActiveByLanguage(s.IsActive, s.LanguageId),
                }).OrderTranslationsByLanguagePriority(
                t => t.LanguageId,
                (int)PushNotificationTranslationLanguage.English, (int)PushNotificationTranslationLanguage.Turkish)
            };

            return PartialView("_UpdateNotification", viewModel);
        }

        [HttpGet]
        public Task<IActionResult> PartialAddNotification()
        {
            var viewModel = new AddUpdateNotificationViewModel
            {
                Translations = EnumHelper.GetEnumAsDictionary(typeof(PushNotificationTranslationLanguage)).Select(s => new NotificationTranslationViewModel()
                {
                    LanguageId = s.Key,
                    Language = s.Value,
                    IsActive = CheckIsActiveByLanguage(false, s.Key)
                }).OrderTranslationsByLanguagePriority(
                t => t.LanguageId,
                (int)PushNotificationTranslationLanguage.English, (int)PushNotificationTranslationLanguage.Turkish)
            };

            return Task.FromResult<IActionResult>(PartialView("_AddNotification", viewModel));
        }

        [HttpGet]
        public Task<IActionResult> PartialSendNotification(string encryptedId)
        {
            var translations = CreateEmptyTranslationModel();

            var viewModel = new SendNotificationViewModel()
            {
                EncryptedId = encryptedId,
                Translations = translations
            };

            return Task.FromResult<IActionResult>(PartialView("_SendNotification", viewModel));
        }


        [HttpDelete]
        public async Task<IActionResult> DeleteNotification(string encryptedId)
        {
            int id = encryptedId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                    (ApiMethodName.NotificationManagement.DeleteNotification + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpPost]
        public async Task<IActionResult> AddNotification(AddUpdateNotificationViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var isEnglishSelected = CheckNotificationSelected(viewModel.Translations.FirstOrDefault(s => s.LanguageId == (int)PushNotificationTranslationLanguage.English));

            if (!isEnglishSelected)
                return Json(new ResultModel { Message = EnumResources.EnglishChoiceMustBeSelected, ResultType = ResultType.Danger });

            var apiRequest = new AddNotificationApiRequest
            {
                NotificationTitle = viewModel.NotificationTitle,
                Translations = viewModel.Translations.Select(s => new NotificationTranslationApiRequest()
                {
                    Id = s.Id,
                    Name = s.Name,
                    LanguageId = s.LanguageId,
                    Subject = s.Subject,
                    IsActive = s.IsActive
                }).OrderTranslationsByLanguagePriority(
                t => t.LanguageId,
                (int)PushNotificationTranslationLanguage.English, (int)PushNotificationTranslationLanguage.Turkish)
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.NotificationManagement.AddNotification, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpPut]
        public async Task<IActionResult> UpdateNotification(AddUpdateNotificationViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var isEnglishSelected = CheckNotificationSelected(viewModel.Translations.FirstOrDefault(s => s.LanguageId == (int)PushNotificationTranslationLanguage.English));

            if (!isEnglishSelected)
                return Json(new ResultModel { Message = EnumResources.EnglishChoiceMustBeSelected, ResultType = ResultType.Danger });

            var id = viewModel.EncryptedId.ToDecryptInt();

            var apiRequest = new UpdateNotificationApiRequest
            {
                Id = id,
                NotificationTitle = viewModel.NotificationTitle,
                Translations = viewModel.Translations.Select(s => new NotificationTranslationApiRequest()
                {
                    Id = s.Id,
                    Name = s.Name,
                    Subject = s.Subject,
                    LanguageId = s.LanguageId,
                    IsActive = s.IsActive
                }).OrderTranslationsByLanguagePriority(
                t => t.LanguageId,
                (int)PushNotificationTranslationLanguage.English, (int)PushNotificationTranslationLanguage.Turkish)
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                    (apiRequest, ApiMethodName.NotificationManagement.UpdateNotification, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpPost]
        public async Task<IActionResult> SendNotification(SendNotificationViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            if (!viewModel.IsSendScheduled && !viewModel.IsSendNow)
                return Json(new ResultModel { Message = EnumResources.InvalidSendType, ResultType = ResultType.Danger });

            var isEnglishSelected = CheckNotificationSelected(viewModel.Translations.FirstOrDefault(s => s.LanguageId == (int)PushNotificationTranslationLanguage.English));

            if (!isEnglishSelected)
                return Json(new ResultModel { Message = EnumResources.EnglishChoiceMustBeSelected, ResultType = ResultType.Danger });

            var id = viewModel.EncryptedId.ToDecryptInt();

            var apiRequest = new SendNotificationApiRequest
            {
                Id = id,
                IsSendNow = viewModel.IsSendNow,
                IsSendScheduled = viewModel.IsSendScheduled,
                LocationId = viewModel.LocationId,
                NationalityId = viewModel.NationalityId,
                ScheduleTime = viewModel.ScheduleTime,
                Translations = viewModel.Translations.Select(s => new NotificationTranslationApiRequest()
                {
                    Id = s.Id,
                    Name = s.Name,
                    LanguageId = s.LanguageId,
                    Subject = s.Subject,
                    IsActive = s.IsActive
                }).OrderTranslationsByLanguagePriority(
                t => t.LanguageId,
                (int)PushNotificationTranslationLanguage.English, (int)PushNotificationTranslationLanguage.Turkish)
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                    (apiRequest, ApiMethodName.NotificationManagement.SendNotification, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }


        #endregion

        private static List<NotificationTranslationViewModel> CreateEmptyTranslationModel()
        {
            return EnumHelper.GetEnumAsDictionary(typeof(PushNotificationTranslationLanguage)).Select(s =>
                new NotificationTranslationViewModel()
                {
                    LanguageId = s.Key,
                    Language = s.Value,
                    IsActive = true,
                    Id = 0,
                    Name = string.Empty
                }).OrderTranslationsByLanguagePriority(
                t => t.LanguageId,
                (int)PushNotificationTranslationLanguage.English, (int)PushNotificationTranslationLanguage.Turkish);
        }

        private static bool CheckNotificationSelected(NotificationTranslationViewModel model)
        {
            if(model == null) return false;

            if (!model.IsActive ||
                model.Name.IsNullOrWhitespace() ||
                model.Subject.IsNullOrWhitespace()) return false;

            return true;
        }

        private static bool CheckIsActiveByLanguage(bool isActive, int languageId)
        {
            if (languageId == (int)PushNotificationTranslationLanguage.English) return true;

            return isActive;
        }
    }
}
