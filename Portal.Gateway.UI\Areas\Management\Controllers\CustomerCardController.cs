﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.Contracts.Entities.Constants;
using Kendo.Mvc.UI;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Requests;
using System.Linq;
using System.Collections.Generic;
using Kendo.Mvc.Extensions;
using Portal.Gateway.UI.Validators;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.Common.Utility.Extensions;
using Microsoft.Extensions.Caching.Memory;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.ApiModel.Responses.Management.CustomerCard;
using Portal.Gateway.UI.Areas.Management.ViewModels.CustomerCard;
using Portal.Gateway.ApiModel.Requests.Management.CustomerCard;
using Portal.Gateway.Resources;
using Portal.Gateway.ApiModel.Requests.Management.CustomerCardNote;
using Portal.Gateway.ApiModel.Responses.Management.CustomerCardNote;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class CustomerCardController : BaseController<CustomerCardController>
    {
        public CustomerCardController(
                IOptions<AppSettings> appSettings,
                ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        #region Add

        public IActionResult PartialAddCustomerCard()
        {
            return PartialView("_AddCustomerCard");
        }

        [HttpPost]
        public async Task<IActionResult> AddCustomerCard(AddCustomerCardViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddCustomerCardApiRequest
            {
                Name = viewModel.Name,
                Surname = viewModel.Surname,
                Email = viewModel.Email,
                BirthDate = viewModel.BirthDate,
                GenderId = viewModel.GenderId,
                PhoneNumber = viewModel.PhoneNumber,
                NationalityId = viewModel.NationalityId,
                PassportExpireDate = viewModel.PassportExpireDate,
                PassportNumber = viewModel.PassportNumber
            };


            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Management.AddCustomerCard, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateCustomerCard(string encryptedCustomerCardId, string encryptedApplicationId)
        {
            var customerCardId = new int();
            var applicationId = new int();

            if (!string.IsNullOrEmpty(encryptedCustomerCardId))
                customerCardId = encryptedCustomerCardId.ToDecryptInt();
            else if (!string.IsNullOrEmpty(encryptedApplicationId))
                applicationId = encryptedApplicationId.ToDecryptInt();
            else
                return Content(EnumResources.MissingOrInvalidData);

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<CustomerCardApiResponse>>
                ($"{ApiMethodName.Management.GetCustomerCard + customerCardId}/{applicationId}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdateCustomerCardViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Name = apiResponse.Data.Name,
                Surname = apiResponse.Data.Surname,
                Email = apiResponse.Data.Email,
                BirthDate = apiResponse.Data.BirthDate,
                GenderId = apiResponse.Data.GenderId,
                PhoneNumber = apiResponse.Data.PhoneNumber,
                IsActive = apiResponse.Data.IsActive,
                NationalityId = apiResponse.Data.NationalityId,
                PassportExpireDate = apiResponse.Data.PassportExpireDate,
                PassportNumber = apiResponse.Data.PassportNumber
            };

            return PartialView("_UpdateCustomerCard", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateCustomerCard(UpdateCustomerCardViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateCustomerCardApiRequest
            {
                Id = viewModel.EncryptedId.ToDecryptInt(),
                Name = viewModel.Name,
                Surname = viewModel.Surname,
                Email = viewModel.Email,
                BirthDate = viewModel.BirthDate,
                GenderId = viewModel.GenderId,
                PhoneNumber = viewModel.PhoneNumber,
                IsActive = viewModel.IsActive,
                NationalityId = viewModel.NationalityId,
                PassportExpireDate = viewModel.PassportExpireDate,
                PassportNumber = viewModel.PassportNumber
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateCustomerCard, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteCustomerCard(string encryptedCustomerCardId)
        {
            int id = encryptedCustomerCardId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Management.DeleteCustomerCard + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> PartialCustomerCard(string encryptedCustomerCardId)
        {
            int id = encryptedCustomerCardId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<CustomerCardApiResponse>>
                ($"{ApiMethodName.Management.GetCustomerCard + id}/0", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var visaCategories = await GetCachedVisaCategories();

            var viewModel = new CustomerCardViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Name = apiResponse.Data.Name,
                Surname = apiResponse.Data.Surname,
                Email = apiResponse.Data.Email,
                PhoneNumber = apiResponse.Data.PhoneNumber,
                IsActive = apiResponse.Data.IsActive,
                BirthDate = apiResponse.Data.BirthDate,
                GenderId = apiResponse.Data.GenderId,
                NationalityId = apiResponse.Data.NationalityId,
                PassportExpireDate = apiResponse.Data.PassportExpireDate,
                PassportNumber = apiResponse.Data.PassportNumber,
                CustomerCardNotes = apiResponse.Data.Notes.Select(q => new CustomerCardViewModel.CustomerCardNote()
                {
                    EncryptedId = q.Id.ToEncrypt(),
                    Note = q.Note,
                    CreatedAt = q.CreatedAt.DateTime,
                    CreatedBy = q.CreatedBy
                }).ToList(),
                CustomerApplications = apiResponse.Data.CustomerApplications.Select(q => new CustomerCardViewModel.CustomerApplication()
                {
                    ApplicantTypeId = q.ApplicantTypeId,
                    ApplicationLastStatus = q.ApplicationLastStatus,
                    ApplicationTime = q.ApplicationTime.Date,
                    ApplicationTypeId = q.ApplicationTypeId,
                    Id = q.Id,
                    StatusId = q.StatusId
                }).ToList(),
                CustomerPreApplications = apiResponse.Data.CustomerPreApplications.Select(q => new CustomerCardViewModel.CustomerPreApplication()
                {
                    ApplicationTime = q.ApplicationTime.Date,
                    BranchName = q.BranchName,
                    Id = q.Id,
                    VisaCategoryId = q.VisaCategoryId,
                    VisaCategoryType = GetVisaCategoryNameFromId(q.VisaCategoryId, visaCategories)
                }).ToList()
            };

            return PartialView("_CustomerCard", viewModel);
        }

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedCustomerCards([DataSourceRequest] DataSourceRequest request, FilterCustomerCardViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedCustomerCardsApiRequest
            {
                PassportNo = filterViewModel.FilterPassportNo,
                Name = filterViewModel.FilterName,
                Surname = filterViewModel.FilterSurname,
                Email = filterViewModel.FilterEmail,
                PhoneNumber = filterViewModel.FilterPhoneNumber,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<CustomerCardsApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedCustomerCards, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<CustomerCardViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().CustomerCards
                    .Select(p => new CustomerCardViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        Name = p.Name,
                        Surname = p.Surname,
                        Email = p.Email,
                        PhoneNumber = p.PhoneNumber,
                        IsActive = p.IsActive,
                        PassportNumber = p.PassportNumber,
                        PassportExpireDate = p.PassportExpireDate,
                        NationalityId = p.NationalityId,
                        GenderId = p.GenderId,
                        BirthDate = p.BirthDate
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #endregion

        #region CustomerCardNote

        public IActionResult PartialAddCustomerCardNote(string encryptedCustomerCardId, string name, string surname)
        {
            var viewModel = new AddCustomerCardNoteViewModel()
            {
                EncryptedCustomerCardId = encryptedCustomerCardId,
                Name = name,
                Surname = surname
            };

            return PartialView("_AddCustomerCardNote", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddCustomerCardNote(AddCustomerCardNoteViewModel viewModel)
        {
            if (string.IsNullOrEmpty(viewModel.Note) || string.IsNullOrEmpty(viewModel.EncryptedCustomerCardId))
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddCustomerCardNoteApiRequest
            {
                CustomerCardId = viewModel.EncryptedCustomerCardId.ToDecryptInt(),
                Note = viewModel.Note,
                UserId = UserSession.UserId
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Management.AddCustomerCardNote, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteCustomerCardNote(string encryptedCustomerCardNoteId)
        {
            int id = encryptedCustomerCardNoteId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Management.DeleteCustomerCardNote + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        public async Task<IActionResult> PartialCustomerCardNotes(string encryptedCustomerCardId)
        {
            int customerCardId = encryptedCustomerCardId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<CustomerCardNotesApiResponse>>
                (ApiMethodName.Management.GetCustomerCardNotes + customerCardId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new CustomerCardNotesViewModel
            {
                EncryptedCustomerCardId = apiResponse.Data.CustomerCardId.ToEncrypt(),
                Name = apiResponse.Data.Name,
                Surname = apiResponse.Data.Surname,
                Email = apiResponse.Data.Email,
                PhoneNumber = apiResponse.Data.PhoneNumber,
                CustomerCardNotes = apiResponse.Data.CustomerCardNotes.Select(q => new CustomerCardNotesViewModel.CustomerCardNote()
                {
                    EncryptedId = q.Id.ToEncrypt(),
                    Note = q.Note,
                    CreatedAt = q.CreatedAt.DateTime,
                    CreatedBy = q.CreatedBy
                })
            };

            return PartialView("_CustomerCardNotes", viewModel);
        }

        #endregion
    }
}