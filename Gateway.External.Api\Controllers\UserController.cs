﻿using Gateway.External.Api.Factories.ResponseFactory;
using Gateway.External.Core.Context;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Gateway.External.Api.Models.User;
using Gateway.External.Application.User;
using Gateway.External.Application.User.Dto.Request;
using Microsoft.AspNetCore.Authorization;

namespace Gateway.External.Api.Controllers
{

    [Route("api")]
    [ApiController]
    public class UserController:Controller
    {
        private readonly IContext _context;
        private readonly IUserService _userService;

        #region ctor

        public UserController(IContext context, IUserService userService)
        {
            _context = context;
            _userService = userService;
        }

        #endregion

        #region Public Methods

        [Authorize]
        [HttpGet]
        [Route("users/logout")]
        public async Task<IActionResult> UserLogout([FromQuery]string? deviceId)
        {
            var serviceRequest = new UserLogoutRequest
            {
                DeviceId = deviceId,
                Context = _context
            };

            var result = await _userService.UserLogout(serviceRequest);

            return BaseResponseFactory.CreateResponse(result);
        }

        [AllowAnonymous]
        [HttpPost]
        [Route("users/register")]
        public async Task<IActionResult> UserRegister(UserRegisterRequestModel requestModel)
        {
            var serviceRequest = new UserRegisterRequest
            {
                Name = requestModel.Name,
                NationalityId = requestModel.NationalityId,
                Surname = requestModel.Surname,
                Email = requestModel.Email,
                ConfirmPassword = requestModel.ConfirmPassword,
                Password = requestModel.Password,
                Context = _context
            };

            var result = await _userService.UserRegister(serviceRequest);

            return BaseResponseFactory.CreateResponse(result, result.Id);
        }

        [Authorize]
        [HttpGet]
        [Route("users/profile")]
        public async Task<IActionResult> GetUserProfile()
        {
            var serviceRequest = new GetUserProfileRequest
            {
                Context = _context
            };

            var result = await _userService.GetUserProfile(serviceRequest);

            return BaseResponseFactory.CreateResponse(result, result.Data);
        }

        [Authorize]
        [HttpPut]
        [Route("users/profile")]
        public async Task<IActionResult> UpdateUserProfile(UpdateUserProfileRequestModel requestModel)
        {
            var serviceRequest = new UpdateUserProfileRequest
            {
                Name = requestModel.Name,
                NationalityId = requestModel.NationalityId,
                Surname = requestModel.Surname,
                Context = _context
            };

            var result = await _userService.UpdateUserProfile(serviceRequest);

            return BaseResponseFactory.CreateResponse(result, result.Id);
        }

        [Authorize]
        [HttpPost]
        [Route("users/device-entry")]
        public async Task<IActionResult> AddUpdateUserDeviceEntry(AddUpdateUserDeviceEntryRequestModel requestModel)
        {
            var serviceRequest = new AddUpdateUserDeviceEntryRequest
            {
                DeviceId = requestModel.DeviceId,
                Location = requestModel.Location,
                RegistryToken = requestModel.RegistryToken,
                DeviceLanguage = requestModel.DeviceLanguage,
                Context = _context
            };

            var result = await _userService.AddUpdateUserDeviceEntry(serviceRequest);

            return BaseResponseFactory.CreateResponse(result, result.Id);
        }

        [Authorize]
        [HttpPost]
        [Route("users/push-notifications")]
        public async Task<IActionResult> GetUserPushNotifications(GetUserPushNotificationsRequestModel requestModel)
        {
            var serviceRequest = new GetUserPushNotificationsRequest
            {
                Pagination = requestModel.Pagination,
                Context = _context
            };

            var result = await _userService.GetUserPushNotifications(serviceRequest);

            return BaseResponseFactory.CreateResponse(result, result.PushNotifications);
        }

        [Authorize]
        [HttpPut]
        [Route("users/update-settings")]
        public async Task<IActionResult> UpdateUserSettings(UpdateUserSettingsRequestModel requestModel)
        {
            var serviceRequest = new UpdateUserSettingsRequest
            {
                IsAllowedSendEmail = requestModel.IsAllowedSendEmail,
                IsAllowedSendSms = requestModel.IsAllowedSendSms,
                IsAllowedSendNotification = requestModel.IsAllowedSendNotification,
                Context = _context
            };

            var result = await _userService.UpdateUserSettings(serviceRequest);

            return BaseResponseFactory.CreateResponse(result, result.Id);
        }

        #endregion
    }
}
