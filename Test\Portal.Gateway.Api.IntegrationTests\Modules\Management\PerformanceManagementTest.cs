using Portal.Gateway.ApiModel.Requests.PerformanceManagement.PMS;
using Portal.Gateway.ApiModel.Responses.PerformanceManagement.PMS;

namespace Portal.Gateway.Api.IntegrationTests.Modules.Management;

public sealed class PerformanceManagementTest : BaseIntegrationTest
{
    public PerformanceManagementTest(SharedTestFixture fixture) : base(fixture) { }

    #region Add PMS Tests

    [Fact]
    internal async Task AddPMS_WhenPMSIsValid_ShouldAddPMS()
    {
        // Arrange
        var user = TestDataSeeder.GetRandomTestUser();
        var country = TestDataSeeder.GetRandomCountry();
        var branchApplicationCountry = TestDataSeeder.GetRandomBranchApplicationCountry();

        var request = GenerateAddPMSApiRequest(user.Id, country.Id, branchApplicationCountry.BranchId, null, null);

        // Act
        var result = await HttpClient.PostAndGetResultAsync<AddResponseDto>(
            ApiMethodName.PerformanceManagementSystem.AddPMS, request);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Id.Should().BePositive();

        var addedPMS = DbContext.PerformanceManagement
            .AsNoTracking()
            .First(p => p.Id == result.Data.Id);
        request.Should().BeEquivalentTo(addedPMS,
            options => options
                .Excluding(e => e.Id)
                .ExcludingMissingMembers());
    }

    [Fact]
    internal async Task AddPMS_WhenRequiredFieldsAreMissing_ShouldReturnValidationError()
    {
        // Arrange
        var request = GenerateAddPMSApiRequest();
        request.UserId = 0; // Invalid user ID
        request.CountryId = 0; // Invalid country ID
        request.BranchId = 0; // Invalid branch ID

        // Act
        var result = await HttpClient.PostAndGetResultAsync<AddResponseDto>(
            ApiMethodName.PerformanceManagementSystem.AddPMS, request);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Code.Should().Be(nameof(DbUpdateException));
        result.Data.Should().BeNull();
    }

    [Fact]
    internal async Task AddPMS_WhenNullOptionalFields_ShouldUseDefaultValues()
    {
        // Arrange
        var user = TestDataSeeder.GetRandomTestUser();
        var country = TestDataSeeder.GetRandomCountry();
        var branchApplicationCountry = TestDataSeeder.GetRandomBranchApplicationCountry();

        var request = GenerateAddPMSApiRequest();
        request.UserId = user.Id;
        request.CountryId = country.Id;
        request.BranchId = branchApplicationCountry.BranchId;
        request.DataGroupId = null;
        request.DataTypeId = null;
        request.Value = null;
        request.Explanation = null;

        // Act
        var result = await HttpClient.PostAndGetResultAsync<AddResponseDto>(
            ApiMethodName.PerformanceManagementSystem.AddPMS, request);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Id.Should().BePositive();

        var addedPMS = DbContext.PerformanceManagement
            .AsNoTracking()
            .OrderByDescending(o => o.Id)
            .First();

        addedPMS.DataGroupId.Should().Be(0); // Default value
        addedPMS.DataTypeId.Should().Be(0); // Default value
        addedPMS.Value.Should().Be(1); // Default value
        addedPMS.Explanation.Should().BeNull();
    }

    #endregion

    #region Update PMS Tests

    [Fact]
    internal async Task UpdatePMS_WhenPMSIsValid_ShouldUpdatePMS()
    {
        // Arrange
        var existingPMS = CreateTestPMS();
        var request = GenerateUpdatePMSApiRequest(existingPMS.Id);
        request.UserId = existingPMS.UserId;
        request.CountryId = existingPMS.CountryId;
        request.BranchId = existingPMS.BranchId;

        // Use different values for update
        request.Explanation = Faker.Lorem.Sentence();
        request.Value = Faker.Random.Int(1, 100);
        request.DataGroupId = (int)PMSDataGroup.Mistakes;
        request.DataTypeId = (int)PMSMistakesDataType.Signature;

        // Act
        var result = await HttpClient.PutAndGetResultAsync<ValidateResponseDto>(
            ApiMethodName.PerformanceManagementSystem.UpdatePMS, request);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Result.Should().BeTrue();

        var updatedPMS = DbContext.PerformanceManagement
            .AsNoTracking()
            .First(p => p.Id == existingPMS.Id);

        updatedPMS.Explanation.Should().Be(request.Explanation);
        updatedPMS.Value.Should().Be(request.Value ?? 0);
        updatedPMS.DataGroupId.Should().Be(request.DataGroupId ?? 0);
        updatedPMS.DataTypeId.Should().Be(request.DataTypeId ?? 0);
        updatedPMS.UpdatedAt.Should().NotBeNull();
        updatedPMS.UpdatedBy.Should().NotBeNull();
    }

    [Fact]
    internal async Task UpdatePMS_WhenPMSNotFound_ShouldReturnNoFoundDataError()
    {
        // Arrange
        var request = GenerateUpdatePMSApiRequest();
        request.Id = int.MaxValue;

        // Act
        var result = await HttpClient.PutAndGetResultAsync<ValidateResponseDto>(
            ApiMethodName.PerformanceManagementSystem.UpdatePMS, request);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Code.Should().Be(ApiErrorCodes.NoFoundData);
    }

    [Fact]
    internal async Task UpdatePMS_WhenRequiredFieldsAreMissing_ShouldReturnValidationError()
    {
        // Arrange
        var existingPMS = CreateTestPMS();
        var request = GenerateUpdatePMSApiRequest(existingPMS.Id);
        request.UserId = 0; // Invalid user ID
        request.CountryId = 0; // Invalid country ID

        // Act
        var result = await HttpClient.PutAndGetResultAsync<ValidateResponseDto>(
            ApiMethodName.PerformanceManagementSystem.UpdatePMS, request);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Code.Should().Be(nameof(DbUpdateException));
        result.Data.Should().BeNull();
    }

    #endregion

    #region Delete PMS Tests

    [Fact]
    internal async Task DeletePMS_WhenPMSExists_ShouldDeletePMS()
    {
        // Arrange
        var existingPMS = CreateTestPMS();

        // Act
        var result = await HttpClient.DeleteAndGetResultAsync<DeleteResponseDto>(
            ApiMethodName.PerformanceManagementSystem.DeletePMS + existingPMS.Id);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Result.Should().BeTrue();

        var deletedPMS = DbContext.PerformanceManagement
            .AsNoTracking()
            .First(p => p.Id == existingPMS.Id);

        deletedPMS.IsDeleted.Should().BeTrue();
        deletedPMS.DeletedAt.Should().NotBeNull();
    }

    [Fact]
    internal async Task DeletePMS_WhenPMSNotExists_ShouldReturnNoFoundDataError()
    {
        // Arrange
        var nonExistingPMSId = int.MaxValue;

        // Act
        var result = await HttpClient.DeleteAndGetResultAsync<DeleteResponseDto>(
            ApiMethodName.PerformanceManagementSystem.DeletePMS + nonExistingPMSId);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Code.Should().Be(ApiErrorCodes.NoFoundData);
    }

    #endregion

    #region Get PMS Tests

    [Fact]
    internal async Task GetPMS_WhenPMSExists_ShouldReturnPMS()
    {
        // Arrange
        var pms = CreateTestPMS();

        // Act
        var result = await HttpClient.GetAndDeserializeAsync<PMSApiResponse>(
            ApiMethodName.PerformanceManagementSystem.GetPMS + pms.Id);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Should().NotBeNull();

        result.Data.Should().BeEquivalentTo(pms,
            options => options
                .Excluding(o => new { o.Id, o.CreatedBy, o.CreatedAt })
                .ExcludingMissingMembers());
    }

    [Fact]
    internal async Task GetPMS_WhenPMSNotExists_ShouldReturnNotFoundError()
    {
        // Arrange
        var nonExistingPMSId = int.MaxValue;

        // Act
        var result = await HttpClient.GetAndDeserializeAsync<PMSApiResponse>(
            ApiMethodName.PerformanceManagementSystem.GetPMS + nonExistingPMSId);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Code.Should().Be(ApiErrorCodes.NoFoundData);
        result.Data.Should().BeNull();
    }

    #endregion

    #region Get Paginated PMS Tests

    [Fact]
    internal async Task GetPaginatedPMSList_WhenNoPMSExists_ShouldReturnEmptyPagination()
    {
        // Arrange
        var user = TestDataSeeder.GetRandomTestUser();
        var country = TestDataSeeder.GetRandomCountry();
        var branchApplicationCountry = TestDataSeeder.GetRandomBranchApplicationCountry();

        var request = new PaginatedPMSApiRequest
        {
            UserId = user.Id,
            User = $"{user.Name} {user.Surname}",
            CountryId = country.Id,
            Country = country.Name,
            BranchId = branchApplicationCountry.BranchId,
            Branch = "Test Branch",
            Date = DateTime.Today,
            Pagination = new PaginationApiRequest
            {
                Page = 1,
                PageSize = 5
            }
        };

        // Act
        // Clear existing PMS data
        DbContext.PerformanceManagement.RemoveRange(DbContext.PerformanceManagement);
        // Ensure no PMS exists for the specific test user/country/branch combination
        var existingPMS = DbContext.PerformanceManagement
            .Where(p => p.UserId == user.Id && p.CountryId == country.Id && p.BranchId == branchApplicationCountry.BranchId)
            .ToList();
        if (existingPMS.Any())
        {
            DbContext.PerformanceManagement.RemoveRange(existingPMS);
            DbContext.SaveChanges();
        }


        var result = await HttpClient.PostAndGetResultAsync<PaginationApiResponse<PaginatedPMSApiResponse>>(
            ApiMethodName.PerformanceManagementSystem.GetPaginatedPMSList, request);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Items.Should().NotBeNull();
        result.Data.Items.First().PerformanceManagemet.Should().BeEmpty();
        result.Data.TotalItemCount.Should().Be(0);
    }

    [Fact]
    internal async Task GetPaginatedPMSList_WhenPMSExists_ShouldReturnPaginatedPMS()
    {
        // Arrange
        var user = TestDataSeeder.GetRandomTestUser();
        var country = TestDataSeeder.GetRandomCountry();
        var branchApplicationCountry = TestDataSeeder.GetRandomBranchApplicationCountry();

        int pageSize = 2;
        var request = new Faker<PaginatedPMSApiRequest>()
            .RuleFor(r => r.UserId, f => user.Id)
            .RuleFor(r => r.User, f => $"{user.Name} {user.Surname}")
            .RuleFor(r => r.CountryId, f => country.Id)
            .RuleFor(r => r.Country, f => country.Name)
            .RuleFor(r => r.BranchId, f => branchApplicationCountry.BranchId)
            .RuleFor(r => r.Branch, f => f.Company.CompanyName()) // Or generate with Bogus
            .RuleFor(r => r.Date, f => DateTime.Today)
            .RuleFor(r => r.Pagination, f => new PaginationApiRequest { Page = 1, PageSize = pageSize })
            .Generate();

        // Act
        // Create test PMS records
        _ = Enumerable.Range(0, request.Pagination.PageSize)
            .Select(_ => CreateTestPMS(request.UserId, request.CountryId, request.BranchId, request.Date))
            .ToList();

        var result = await HttpClient.PostAndGetResultAsync<PaginationApiResponse<PaginatedPMSApiResponse>>(
            ApiMethodName.PerformanceManagementSystem.GetPaginatedPMSList, request);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Items.Should().NotBeEmpty();
        result.Data.Page.Should().Be(request.Pagination.Page);
        result.Data.TotalItemCount.Should().BeGreaterThanOrEqualTo(pageSize);
    }

    [Fact]
    internal async Task GetPaginatedPMSScoreCardList_WhenPMSExists_ShouldReturnPaginatedScoreCard()
    {
        // Arrange
        var user = TestDataSeeder.GetRandomTestUser();
        var country = TestDataSeeder.GetRandomCountry();
        var branchApplicationCountry = TestDataSeeder.GetRandomBranchApplicationCountry();

        var request = new PaginatedPMSApiRequest
        {
            UserId = user.Id,
            User = $"{user.Name} {user.Surname}",
            CountryId = country.Id,
            Country = country.Name,
            BranchId = branchApplicationCountry.BranchId,
            StartDate = DateTime.Today.AddDays(-7),
            EndDate = DateTime.Today,
            Pagination = new PaginationApiRequest
            {
                Page = 1,
                PageSize = 10
            }
        };

        // Act
        // Create test PMS records within date range
        CreateTestPMS(user.Id, country.Id, branchApplicationCountry.BranchId, DateTime.Today.AddDays(-3));
        CreateTestPMS(user.Id, country.Id, branchApplicationCountry.BranchId, DateTime.Today.AddDays(-1));

        var result = await HttpClient.PostAndGetResultAsync<PaginationApiResponse<PaginatedPMSApiResponse>>(
            ApiMethodName.PerformanceManagementSystem.GetPaginatedPMSScoreCardList, request);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Items.Should().NotBeEmpty();
        result.Data.Page.Should().Be(request.Pagination.Page);
    }

    [Fact]
    internal async Task GetPaginatedPMSList_WhenFilteringByDate_ShouldReturnCorrectResults()
    {
        // Arrange
        var user = TestDataSeeder.GetRandomTestUser();
        var country = TestDataSeeder.GetRandomCountry();
        var branchApplicationCountry = TestDataSeeder.GetRandomBranchApplicationCountry();

        var targetDate = DateTime.Today.AddDays(-1);
        var otherDate = DateTime.Today.AddDays(-5);

        // Create PMS records for different dates
        CreateTestPMS(user.Id, country.Id, branchApplicationCountry.BranchId, targetDate);
        CreateTestPMS(user.Id, country.Id, branchApplicationCountry.BranchId, otherDate);

        var request = new PaginatedPMSApiRequest
        {
            UserId = user.Id,
            User = $"{user.Name} {user.Surname}",
            CountryId = country.Id,
            Country = country.Name,
            BranchId = branchApplicationCountry.BranchId,
            Date = targetDate,
            Pagination = new PaginationApiRequest
            {
                Page = 1,
                PageSize = 10
            }
        };

        // Act
        var result = await HttpClient.PostAndGetResultAsync<PaginationApiResponse<PaginatedPMSApiResponse>>(
            ApiMethodName.PerformanceManagementSystem.GetPaginatedPMSList, request);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();

        // Should only return records for the target date
        if (result.Data.Items.Any() && result.Data.Items.First().PerformanceManagemet.Any())
        {
            result.Data.Items.First().PerformanceManagemet
                .Should().OnlyContain(pms => pms.Date.Date == targetDate.Date);
        }
    }

    [Fact]
    internal async Task GetPaginatedPMSScoreCardList_WhenDateRangeFilter_ShouldReturnCorrectResults()
    {
        // Arrange
        var user = TestDataSeeder.GetRandomTestUser();
        var country = TestDataSeeder.GetRandomCountry();
        var branchApplicationCountry = TestDataSeeder.GetRandomBranchApplicationCountry();

        var startDate = DateTime.Today.AddDays(-10);
        var endDate = DateTime.Today.AddDays(-5);
        var withinRangeDate = DateTime.Today.AddDays(-7);
        var outsideRangeDate = DateTime.Today.AddDays(-15);

        // Create PMS records within and outside the date range
        CreateTestPMS(user.Id, country.Id, branchApplicationCountry.BranchId, withinRangeDate);
        CreateTestPMS(user.Id, country.Id, branchApplicationCountry.BranchId, outsideRangeDate);

        var request = new PaginatedPMSApiRequest
        {
            UserId = user.Id,
            User = $"{user.Name} {user.Surname}",
            CountryId = country.Id,
            Country = country.Name,
            BranchId = branchApplicationCountry.BranchId,
            Branch = "Test Branch",
            StartDate = startDate,
            EndDate = endDate,
            Pagination = new PaginationApiRequest
            {
                Page = 1,
                PageSize = 10
            }
        };

        // Act
        var result = await HttpClient.PostAndGetResultAsync<PaginationApiResponse<PaginatedPMSApiResponse>>(
            ApiMethodName.PerformanceManagementSystem.GetPaginatedPMSScoreCardList, request);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();

        // Should only return records within the date range
        if (result.Data.Items.Any() && result.Data.Items.First().PerformanceManagemet.Any())
        {
            result.Data.Items.First().PerformanceManagemet
                .Should().OnlyContain(pms => pms.Date.Date >= startDate.Date && pms.Date.Date <= endDate.Date);
        }
    }

    #endregion

    #region Private Methods

    private PMSApiRequest GenerateAddPMSApiRequest(int? userId, int? countryId, int? branchId, int? dataGroupId, int? dataTypeId)
    {
        var request = GenerateAddPMSApiRequest();

        if (userId.HasValue)
            request.UserId = userId.Value;

        if (countryId.HasValue)
            request.CountryId = countryId.Value;

        if (branchId.HasValue)
            request.BranchId = branchId.Value;

        if (dataGroupId.HasValue)
            request.DataGroupId = dataGroupId.Value;

        if (dataTypeId.HasValue)
            request.DataTypeId = dataTypeId.Value;

        return request;
    }

    private static readonly Dictionary<PMSDataGroup, Type> PmsDataTypeMap = new()
    {
        { PMSDataGroup.Operation, typeof(PMSOperationDataType) },
        { PMSDataGroup.Vas, typeof(PMSVASDataType) },
        { PMSDataGroup.Mistakes, typeof(PMSMistakesDataType) },
        { PMSDataGroup.Warnings, typeof(PMSWarningsDataType) },
        { PMSDataGroup.ThanksComplaints, typeof(PMSThanksComplaintsDataType) },
        { PMSDataGroup.ManagerEvaluation, typeof(PMSManagerEvaluationDataType) }
    };

    private PMSApiRequest GenerateAddPMSApiRequest()
    {
        var selectedDataGroup = Faker.PickRandom<PMSDataGroup>();
        var dataTypeEnum = PmsDataTypeMap[selectedDataGroup];
        var selectedDataTypeId = Faker.PickRandom(Enum.GetValues(dataTypeEnum).Cast<int>());

        return new PMSApiRequest
        {
            UserId = Faker.Random.Int(1, 100),
            CountryId = Faker.Random.Int(1, 150),
            BranchId = Faker.Random.Int(1, 100),
            Date = DateTime.Today,
            DataGroupId = (int)selectedDataGroup,
            DataTypeId = selectedDataTypeId,
            Explanation = Faker.Lorem.Sentence(),
            Value = Faker.Random.Int(1, 10)
        };
    }

    private PMSApiRequest GenerateUpdatePMSApiRequest(int? pmsId = null)
    {
        var request = GenerateAddPMSApiRequest();

        if (pmsId.HasValue)
            request.Id = pmsId.Value;

        return request;
    }

    private PerformanceManagement CreateTestPMS(int? userId = null, int? countryId = null, int? branchId = null, DateTime? date = null)
    {
        var pms = new PerformanceManagement
        {
            UserId = userId ?? TestDataSeeder.GetRandomTestUser().Id,
            CountryId = countryId ?? TestDataSeeder.GetRandomCountry().Id,
            BranchId = branchId ?? TestDataSeeder.GetRandomBranchApplicationCountry().BranchId,
            Date = date ?? DateTime.Today,
            DataGroupId = (int)PMSDataGroup.Operation,
            DataTypeId = (int)PMSOperationDataType.ApplicationTaken,
            Explanation = Faker.Lorem.Sentence(),
            Value = Faker.Random.Int(1, 10),
            CreatedBy = userId ?? TestDataSeeder.GetRandomTestUser().Id
        };

        DbContext.PerformanceManagement.Add(pms);
        DbContext.SaveChanges();
        return pms;
    }

    #endregion
}
