﻿using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Management.Action;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Management.Action;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.Action;
using Portal.Gateway.UI.Areas.Management.ViewModels.General;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Enums;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class ActionController : BaseController<ActionController>
    {
        public ActionController(
               IOptions<AppSettings> appSettings,
               ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        #region Add

        public async Task<IActionResult> PartialAddAction(string encryptedActionId)
        {
            var viewModel = new AddActionViewModel()
            {
                ParentId = encryptedActionId?.ToDecryptInt() ?? (int?)null,
                IsPublic = true
            };

            foreach (var item in EnumHelper.GetEnumAsDictionary(typeof(Language)))
            {
                viewModel.ActionTranslations.Add(new AddActionTranslationViewModel { LanguageId = item.Key });
            }

            return PartialView("_AddAction", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddAction(AddActionViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddActionApiRequest
            {
                Area = viewModel.Area,
                Controller = viewModel.Controller,
                IsMenuItem = viewModel.IsMenuItem,
                IsPublic = viewModel.IsPublic,
                Method = viewModel.Method,
                Order = viewModel.Order,
                ParentId = viewModel.ParentId,
                ActionTranslations = viewModel.ActionTranslations.Select(p => new AddActionTranslationApiRequest
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                    Description = p.Description
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<EmptyApiResponse>>
                (apiRequest, ApiMethodName.Management.AddAction, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteAction(string encryptedActionId)
        {
            int id = encryptedActionId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Management.DeleteAction + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateAction(string encryptedActionId)
        {
            int id = encryptedActionId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ActionApiResponse>>
                (ApiMethodName.Management.GetAction + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdateActionViewModel
            {
                Id = apiResponse.Data.Id,
                ParentId = apiResponse.Data.ParentId,
                Order = apiResponse.Data.Order,
                Area = apiResponse.Data.Area,
                Controller = apiResponse.Data.Controller,
                IsMenuItem = apiResponse.Data.IsMenuItem,
                IsPublic = apiResponse.Data.IsPublic,
                Method = apiResponse.Data.Method,
                IsActive = apiResponse.Data.IsActive,
                ActionTranslations = apiResponse.Data.ActionTranslations.Select(p => new UpdateActionTranslationViewModel
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                    Description = p.Description
                }).ToList()
            };

            return PartialView("_UpdateAction", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateAction(UpdateActionViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateActionApiRequest
            {
                Id = viewModel.Id,
                ParentId = viewModel.ParentId,
                Order = viewModel.Order,
                Area = viewModel.Area,
                Controller = viewModel.Controller,
                IsMenuItem = viewModel.IsMenuItem,
                IsPublic = viewModel.IsPublic,
                Method = viewModel.Method,
                IsActive = viewModel.IsActive,
                ActionTranslations = viewModel.ActionTranslations.Select(p => new UpdateActionTranslationApiRequest
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                    Description = p.Description
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<EmptyApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateAction, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        [ActionAttribute(IsMenuItem = true)]
        public async Task<IActionResult> List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedActions([DataSourceRequest] DataSourceRequest request, FilterActionViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedActionsApiRequest
            {
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                },
                HidePublicActions = true
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<ActionsApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedActions, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<ActionViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().Actions
                    .Select(p => new ActionViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        Name = p.ActionTranslations.FirstOrDefault().Name,
                        Description = p.ActionTranslations.FirstOrDefault().Description,
                        Url = $"{p.Area}/{p.Controller}/{p.Method}",
                        IsActive = p.IsActive,
                        IsMenuItem = p.IsMenuItem
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #endregion
    }
}