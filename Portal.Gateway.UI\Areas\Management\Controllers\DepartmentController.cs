﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.Contracts.Entities.Constants;
using Kendo.Mvc.UI;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Requests;
using System.Linq;
using System.Collections.Generic;
using Portal.Gateway.UI.Areas.Management.ViewModels.General;
using Kendo.Mvc.Extensions;
using Portal.Gateway.UI.Validators;
using Portal.Gateway.UI.Enums;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.UI.Models;
using Portal.Gateway.Resources;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.UI.Areas.Management.ViewModels.Department;
using Portal.Gateway.ApiModel.Requests.Management.Department;
using Portal.Gateway.ApiModel.Responses.Management.Department;
using Microsoft.Extensions.Caching.Memory;
using Portal.Gateway.Contracts.Attributes;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class DepartmentController : BaseController<DepartmentController>
    {
        public DepartmentController(
               IOptions<AppSettings> appSettings,
               ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        #region Add

        public IActionResult PartialAddDepartment()
        {
            var viewModel = new AddDepartmentViewModel() { 
                NameTranslation = new List<AddDepartmentViewModel.NameTranslationViewModel>()
            };

            foreach (var item in EnumHelper.GetEnumAsDictionary(typeof(Language)))
            {
                viewModel.NameTranslation.Add(new AddDepartmentViewModel.NameTranslationViewModel { LanguageId = item.Key });
            }

            return PartialView("_AddDepartment", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddDepartment(AddDepartmentViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddDepartmentApiRequest
            {
                Status = viewModel.Status,
                NameTranslations = viewModel.NameTranslation.Select(p => new AddDepartmentApiRequest.DepartmentTranslationApiResponse
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<EmptyApiResponse>>
                (apiRequest, ApiMethodName.Management.AddDepartment, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateDepartment(string encryptedId)
        {
            int id = encryptedId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<DepartmentApiResponse>>
                (ApiMethodName.Management.GetDepartment + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdateDepartmentViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                IsActive = apiResponse.Data.IsActive,
                Status = apiResponse.Data.Status,
                NameTranslation = apiResponse.Data.NameTranslations.Select(p => new UpdateDepartmentViewModel.NameTranslationViewModel
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name
                }).ToList()
            };

            return PartialView("_UpdateDepartment", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateDepartment(UpdateDepartmentViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateDepartmentApiRequest
            {
                Id = viewModel.EncryptedId.ToDecryptInt(),
                IsActive = viewModel.IsActive,
                Status = viewModel.Status,
                NameTranslations = viewModel.NameTranslation.Select(p => new UpdateDepartmentApiRequest.DepartmentTranslationApiResponse
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<EmptyApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateDepartment, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteDepartment(string encryptedId)
        {
            int id = encryptedId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Management.DeleteDepartment + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> PartialDepartment(string encryptedId)
        {
            int id = encryptedId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<DepartmentApiResponse>>
                (ApiMethodName.Management.GetDepartment + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new DepartmentViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                IsActive = apiResponse.Data.IsActive,
                Status = EnumHelper.GetEnumDescription(typeof(DepartmentStatus), apiResponse.Data.Status.ToString()),
                NameTranslation = apiResponse.Data.NameTranslations.Select(q => new DepartmentViewModel.NameTranslationViewModel()
                {
                    LanguageId = q.LanguageId,
                    Name = q.Name
                }).ToList()
            };

            return PartialView("_Department", viewModel);
        }

        [ActionAttribute(IsMenuItem = true)]
        public async Task<IActionResult> List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedDepartments([DataSourceRequest] DataSourceRequest request, FilterDepartmentViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedDepartmentApiRequest
            {
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedDepartmentApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedDepartments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<DepartmentItemViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().Departments
                    .Select(p => new DepartmentItemViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        IsActive = p.IsActive,
                        Name = p.Name,
                        Status = EnumHelper.GetEnumDescription(typeof(DepartmentStatus), p.Status.ToString()),
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #endregion
    }
}