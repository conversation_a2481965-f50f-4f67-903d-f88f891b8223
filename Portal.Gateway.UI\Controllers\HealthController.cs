﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;

namespace Portal.Gateway.UI.Controllers
{
    public class HealthController : BaseController<HealthController>
    {
        public HealthController(IOptions<AppSettings> appSettings, ICacheHelper cacheHelper) : base(appSettings, cacheHelper)
        {
        }

        [AllowAnonymous]
        public ContentResult Check()
        {
            return Content("ok");
        }
    }
}
