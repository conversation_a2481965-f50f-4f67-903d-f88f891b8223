﻿@using Microsoft.AspNetCore.Builder
@using Microsoft.Extensions.Options
@using Microsoft.AspNetCore.Http
@using Portal.Gateway.UI.Constants
@inject IHttpContextAccessor HttpContextAccessor
@inject IOptions<RequestLocalizationOptions> RequestLocalizationOptions
@inject CurrentUserDataHelper CurrentUserDataHelper
@{
    var currentUser = Portal.Gateway.UI.Extensions.SessionExtensions.Get<UserModel>(HttpContextAccessor.HttpContext.Session, SessionKeys.UserSession);
    var userBranches = await CurrentUserDataHelper.BuildUserBranches(currentUser);
}

<style>
    .active-module {
        color: #fff !important;
        background-color: #663259 !important;
        border-color: #663259 !important;
    }

        .active-module .svg-icon svg g [fill] {
            fill: #fff !important;
        }
</style>

<div id="kt_quick_actions" class="offcanvas offcanvas-left p-10">
    <div class="offcanvas-header d-flex align-items-center justify-content-between pb-10">
        <h3 class="font-weight-bold m-0">
            @SiteResources.BranchManagement.ToTitleCase()
        </h3>
        <a href="#" class="btn btn-xs btn-icon btn-light btn-hover-primary" id="kt_quick_actions_close">
            <i class="ki ki-close icon-xs text-muted"></i>
        </a>
    </div>

    @{
        if (currentUser.BranchId == null)
        {
            <div class="alert alert-custom alert-white alert-shadow fade show gutter-b" role="alert">
                <div class="alert-icon">
                    <span class="svg-icon svg-icon-primary svg-icon-xl">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <rect x="0" y="0" width="24" height="24"></rect>
                                <path d="M7.07744993,12.3040451 C7.72444571,13.0716094 8.54044565,13.6920474 9.46808594,14.1079953 L5,23 L4.5,18 L7.07744993,12.3040451 Z M14.5865511,14.2597864 C15.5319561,13.9019016 16.375416,13.3366121 17.0614026,12.6194459 L19.5,18 L19,23 L14.5865511,14.2597864 Z M12,3.55271368e-14 C12.8284271,3.53749572e-14 13.5,0.671572875 13.5,1.5 L13.5,4 L10.5,4 L10.5,1.5 C10.5,0.671572875 11.1715729,3.56793164e-14 12,3.55271368e-14 Z" fill="#000000" opacity="0.3"></path>
                                <path d="M12,10 C13.1045695,10 14,9.1045695 14,8 C14,6.8954305 13.1045695,6 12,6 C10.8954305,6 10,6.8954305 10,8 C10,9.1045695 10.8954305,10 12,10 Z M12,13 C9.23857625,13 7,10.7614237 7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 C17,10.7614237 14.7614237,13 12,13 Z" fill="#000000" fill-rule="nonzero"></path>
                            </g>
                        </svg>
                    </span>
                </div>
                <div class="alert-text">@SiteResources.NeedToSelectBranchNotification</div>
            </div>
            <div class="row" style="justify-content: center;">
                <a href="/User/SelectModuleBranch" class="btn btn-outline-primary font-weight-bold">@SiteResources.GoToModules.ToTitleCase()</a>
            </div>
        }
    }

    <div class="offcanvas-content pr-5 mr-n5">
        <div class="row gutter-b">

            @if (currentUser != null)
            {
                if (userBranches.Any())
                {
                    var branchList = new Dictionary<int, string>();

                    foreach (var item in userBranches)
                    {
                        if (@item.BranchNames.Any())
                        {
                            var branchName = "";

                            if (item.BranchNames.Any(p => p.Key == Html.CurrentLanguageId().ToString().ToInt()))
                                branchName = item.BranchNames.First(p => p.Key == Html.CurrentLanguageId().ToString().ToInt()).Value;
                            else
                                branchName = item.BranchNames.First().Value;

                            branchList.Add(item.BranchId, branchName);
                        }
                    }

                    if (currentUser.BranchId.HasValue)
                    {
                        var currentBranchName = "";
                        branchList.TryGetValue(currentUser.BranchId.Value, out currentBranchName);

                        foreach (var item in branchList.OrderBy(o => o.Value))
                        {
                            <div class="col-6 mb-10">
                                <a href="/User/SetBranch?encryptedBranchId=@item.Key.ToEncrypt()" class="btn btn-block btn-light btn-hover-primary text-dark-50 text-center py-10 px-5 @(currentBranchName == item.Value ? "active-module" : "")">
                                    <span class="svg-icon svg-icon-3x svg-icon-primary m-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                <rect x="0" y="0" width="24" height="24" />
                                                <path d="M4.85714286,1 L11.7364114,1 C12.0910962,1 12.4343066,1.12568431 12.7051108,1.35473959 L17.4686994,5.3839416 C17.8056532,5.66894833 18,6.08787823 18,6.52920201 L18,19.0833333 C18,20.8738751 17.9795521,21 16.1428571,21 L4.85714286,21 C3.02044787,21 3,20.8738751 3,19.0833333 L3,2.91666667 C3,1.12612489 3.02044787,1 4.85714286,1 Z M8,12 C7.44771525,12 7,12.4477153 7,13 C7,13.5522847 7.44771525,14 8,14 L15,14 C15.5522847,14 16,13.5522847 16,13 C16,12.4477153 15.5522847,12 15,12 L8,12 Z M8,16 C7.44771525,16 7,16.4477153 7,17 C7,17.5522847 7.44771525,18 8,18 L11,18 C11.5522847,18 12,17.5522847 12,17 C12,16.4477153 11.5522847,16 11,16 L8,16 Z" fill="#000000" fill-rule="nonzero" opacity="0.3" />
                                                <path d="M6.85714286,3 L14.7364114,3 C15.0910962,3 15.4343066,3.12568431 15.7051108,3.35473959 L20.4686994,7.3839416 C20.8056532,7.66894833 21,8.08787823 21,8.52920201 L21,21.0833333 C21,22.8738751 20.9795521,23 19.1428571,23 L6.85714286,23 C5.02044787,23 5,22.8738751 5,21.0833333 L5,4.91666667 C5,3.12612489 5.02044787,3 6.85714286,3 Z M8,12 C7.44771525,12 7,12.4477153 7,13 C7,13.5522847 7.44771525,14 8,14 L15,14 C15.5522847,14 16,13.5522847 16,13 C16,12.4477153 15.5522847,12 15,12 L8,12 Z M8,16 C7.44771525,16 7,16.4477153 7,17 C7,17.5522847 7.44771525,18 8,18 L11,18 C11.5522847,18 12,17.5522847 12,17 C12,16.4477153 11.5522847,16 11,16 L8,16 Z" fill="#000000" fill-rule="nonzero" />
                                            </g>
                                        </svg>
                                    </span>
                                    <span class="d-block font-weight-bold font-size-h6 mt-2">@item.Value</span>
                                </a>
                            </div>
                        }
                    }
                }
            }
        </div>
    </div>
</div>
