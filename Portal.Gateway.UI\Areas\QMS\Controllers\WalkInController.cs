﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;

using Gateway.Http;
using Portal.Gateway.ApiModel.Requests.Appointment.PreApplication;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Appointment.PreApplication;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.QMS.Models.QMS.Responses;
using Portal.Gateway.UI.Areas.QMS.ViewModels.WalkIn;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Helpers;

namespace Portal.Gateway.UI.Areas.QMS.Controllers
{
    [Area("QMS")]
    public class WalkInController : BaseController<WalkInController>
    {
        public WalkInController(ICacheHelper cacheHelper, IOptions<AppSettings> appSettings) : base(appSettings, cacheHelper) { }

        public async Task<IActionResult> QmsWalkIn()
        {
            if (UserSession.BranchId == null)
            {
                return RedirectToAction("SelectModuleBranch", "User", new { Area = string.Empty });
            }

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BranchApiResponse>>
                    (ApiMethodName.Parameter.GetBranchApplicationCountryCallingCode + UserSession.BranchId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var viewModel = new QmsWalkInViewModel
            {
                Applicants = Enumerable.Range(0, 15).Select(p => new QmsWalkInViewModel.Applicant
                {
                    ApplicationEnabled = false
                }).ToList(),
                VisaCategoryId = VisaCategoryTypeEnum.Touristic.ToInt(),
                ApplicationTypeId = ApplicationType.Normal.ToInt(),
                NumberOfPerson = 1,
                SlotId = 0,
                CountryCallingCode = apiResponse.Data.CallingCode,
                QmsWalkinVipControl = apiResponse.Data.QmsWalkinVipControl,
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> SaveWalkInAppointment(QmsWalkInViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel
                {
                    Message = EnumResources.MissingOrInvalidData,
                    ResultType = ResultType.Danger
                });

            if (viewModel.IsLineChanging && viewModel.VasTypeId != (int)VasType.Vip)
                return Json(new ResultModel
                {
                    Message = EnumResources.MissingOrInvalidData,
                    ResultType = ResultType.Danger
                });

            var apiRequest = new AddUpdatePreApplicationApiRequest
            {
                ApplicantTypeId = viewModel.ApplicantTypeId,
                BranchApplicationCountryId = viewModel.BranchApplicationCountryId,
                VisaCategoryId = viewModel.VisaCategoryId,
                SlotId = viewModel.SlotId,
                VasTypeId = viewModel.VasTypeId,
                Date = DateTime.Now,
                ApplicationTypeId = viewModel.ApplicationTypeId,
                StatusId = ActivationStatusType.Active.ToInt(),
                Note = viewModel.Note,
                Description = viewModel.Description,
                IsCallCenterError = viewModel.IsCallCenterError,
                IsLineChanging = viewModel.IsLineChanging,
                IsFutureAppointment = viewModel.IsFutureAppointment,
                CCErrorDescription = viewModel.CCErrorDescription,
                CreatedBy = UserSession.UserId,
                CreatedAt = DateTime.Now,
                Applicants = viewModel.Applicants.Where(p => p.ApplicationEnabled).Select(p => new AddUpdatePreApplicationApiRequest.PreApplicationApplicants
                {
                    BirthDate = p.BirthDate.GetValueOrDefault(),
                    Email = string.IsNullOrEmpty(p.Email) ? string.Empty : p.Email,
                    GenderId = p.GenderId.GetValueOrDefault(),
                    Name = p.Name,
                    NationalityId = p.NationalityId.GetValueOrDefault(),
                    PassportExpireDate = p.PassportExpireDate.GetValueOrDefault(),
                    PassportNumber = p.PassportNumber,
                    PhoneNumber = string.IsNullOrEmpty(p.PhoneNumber) ? string.Empty : $"{viewModel.CountryCallingCode}{p.PhoneNumber}",
                    Surname = p.Surname
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Appointment.AddPreApplication, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            UpdateSessionLastPreApplicationId(apiResponse.Data.Id, true);

            return Json(new ResultModel
            {
                Data = apiResponse.Data,
                Message = $"{ResultMessage.OperationIsSuccessful.ToDescription()} - {SiteResources.AppointmentNumberIs}: {apiResponse.Data.Id}",
                ResultType = ResultType.Success
            });
        }

        [HttpGet]
        public async Task<IActionResult> CheckLineChanging(string encryptedPassportNumber)
        {
            var apiResponse = await RestHttpClient.Create().Get<GetAppointmentInformationResult>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.CheckLineChanging + encryptedPassportNumber, QMSApiDefaultRequestHeaders);

            return Json(new { Result = apiResponse?.Status == SiteResources.SUCCESS, Message = apiResponse?.Message });
        }

        [HttpGet]
        public async Task<IActionResult> CallCenterError(string passportNumber)
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<CheckCallCenterErrorOrFutureAppointmentApiResponse>>
                (ApiMethodName.Appointment.CallCenterError + passportNumber, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new { Result = apiResponse.Data.Result });
        }

        [HttpGet]
        public async Task<IActionResult> CheckFutureAppointment(string passportNumber)
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<CheckCallCenterErrorOrFutureAppointmentApiResponse>>
                (ApiMethodName.Appointment.CheckFutureAppointment + passportNumber + "/" + UserSession.BranchId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new { Result = apiResponse.Data.Result, AppointmentTime = apiResponse.Data.AppointmentTime.DateTime.ToString("dd/MM/yyyy HH:mm") });
        }

        public IActionResult WalkInCompleted(WalkInPreApplicationCompletedViewModel viewModel)
        {
            if (string.IsNullOrEmpty(viewModel.EncryptedApplicationId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

                return RedirectToAction("List", "PreApplication", new { Area = "Appointment" });
            }

            return View(viewModel);
        }
    }
}
