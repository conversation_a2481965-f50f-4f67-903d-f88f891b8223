﻿using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Management.Announcement;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Management.Announcement;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.Announcement;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class AnnouncementController : BaseController<AnnouncementController>
    {
        public AnnouncementController(
                IOptions<AppSettings> appSettings,
                ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        #region Add

        public IActionResult PartialAddAnnouncement()
        {
            var viewModel = new AddAnnouncementViewModel();
            return PartialView("_AddAnnouncement", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddAnnouncement(AddAnnouncementViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddAnnouncementApiRequest
            {
                BranchIds = viewModel.BranchIds,
                AnnouncementPersonIds = viewModel.AnnouncementPersonIds,
                DueDate = viewModel.DueDate,
                Subject = viewModel.Subject,
                Message = viewModel.Message,
                CreatedBy = UserSession.UserId
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Management.AddAnnouncement, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateAnnouncement(string encryptedAnnouncementId)
        {
            var apiRequest = new AnnouncementApiRequest
            {
                AnnouncementId = encryptedAnnouncementId.ToDecryptInt(),
                UserId = UserSession.UserId
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AnnouncementApiResponse>>
                (apiRequest, ApiMethodName.Management.GetAnnouncement, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdateAnnouncementViewModel
            {
                Id = apiResponse.Data.Id,
                BranchIds = apiResponse.Data.BranchIds,
                AnnouncementPersonIds = apiResponse.Data.AnnouncementPersonIds,
                DueDate = apiResponse.Data.DueDate,
                Subject = apiResponse.Data.Subject,
                Message = apiResponse.Data.Message
            };

            return PartialView("_UpdateAnnouncement", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateAnnouncement(UpdateAnnouncementViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateAnnouncementApiRequest
            {
                Id = viewModel.Id,
                BranchIds = viewModel.BranchIds,
                AnnouncementPersonIds = viewModel.AnnouncementPersonIds,
                DueDate = viewModel.DueDate,
                Subject = viewModel.Subject,
                Message = viewModel.Message
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateAnnouncement, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteAnnouncement(string encryptedAnnouncementId)
        {
            int id = encryptedAnnouncementId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Management.DeleteAnnouncement + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> PartialAnnouncement(string encryptedAnnouncementId)
        {
            var apiRequest = new AnnouncementApiRequest
            {
                AnnouncementId = encryptedAnnouncementId.ToDecryptInt(),
                UserId = UserSession.UserId
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AnnouncementApiResponse>>
                (apiRequest, ApiMethodName.Management.GetAnnouncement, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new AnnouncementViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                BranchIds = apiResponse.Data.BranchIds,
                BranchNames = string.Join(", ", apiResponse.Data.BranchNames.OrderBy(q => q)),
                AnnouncementPersonIds = apiResponse.Data.AnnouncementPersonIds,
                AnnouncementPersonNames = string.Join(", ", apiResponse.Data.AnnouncementPersonNames.OrderBy(q => q)),
                DueDate = apiResponse.Data.DueDate,
                Subject = apiResponse.Data.Subject,
                Message = apiResponse.Data.Message,
                CreatedAt = apiResponse.Data.CreatedAt,
                CreatedBy = apiResponse.Data.CreatedBy,
                CreatedByNameSurname = apiResponse.Data.CreatedByNameSurname,
                IsReadByUser = apiResponse.Data.IsReadByUser
            };

            return PartialView("_Announcement", viewModel);
        }

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult List()
        {
            if (UserSession.BranchId == null)
            {
                return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
            }

            var viewModel = new FilterAnnouncementViewModel()
            {
                FilterBranchId = UserSession.BranchId.Value
            };

            return View(viewModel);
        }

        public async Task<IActionResult> GetPaginatedAnnouncements([DataSourceRequest] DataSourceRequest request, FilterAnnouncementViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            int branchId = UserSession.BranchId.Value;
            if (filterViewModel.FilterBranchId.HasValue)
                branchId = filterViewModel.FilterBranchId.GetValueOrDefault();

            var apiRequest = new PaginatedAnnouncementsApiRequest
            {
                BranchId = branchId,
                UserId = UserSession.UserId,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<AnnouncementsApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedAnnouncements, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<AnnouncementViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().Announcements
                    .Select(p => new AnnouncementViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        BranchIds = p.BranchIds,
                        BranchNames = string.Join(", ", p.BranchNames.OrderBy(q => q)),
                        AnnouncementPersonIds = p.AnnouncementPersonIds,
                        AnnouncementPersonNames = string.Join(", ", p.AnnouncementPersonNames.OrderBy(q => q)),
                        DueDate = p.DueDate,
                        Subject = p.Subject,
                        Message = p.Message,
                        IsReadByUser = p.IsReadByUser
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #endregion

        #region History

        public IActionResult History(string encryptedAnnouncementId)
        {
            var viewModel = new FilterAnnouncementHistoryViewModel()
            {
                FilterAnnouncementId = encryptedAnnouncementId.ToDecryptInt()
            };

            return View(viewModel);
        }

        public async Task<IActionResult> GetPaginatedAnnouncementHistories([DataSourceRequest] DataSourceRequest request, FilterAnnouncementHistoryViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedAnnouncementHistoriesApiRequest
            {
                AnnouncementId = paginationFilter.FilterAnnouncementId,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<AnnouncementHistoriesApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedAnnouncementHistories, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<AnnouncementHistoryViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().AnnouncementHistories
                    .Select(p => new AnnouncementHistoryViewModel
                    {
                        Name = p.Name,
                        Surname = p.Surname,
                        CreatedAt = p.CreatedAt.Date
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #endregion
    }
}
