﻿using DocumentFormat.OpenXml.Wordprocessing;
using Gateway.Extensions;
using Gateway.Http;
using Gateway.Redis;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.QmsFolder;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.UI.Areas.Biometrics.Models.NeurotecLicense.RequestModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.NeurotecLicense.Results;
using Portal.Gateway.UI.Areas.Biometrics.Models.NeurotecLicense.ViewModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.NeurotecLicense;
using Portal.Gateway.UI.Models;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using System;
using System.Linq;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.UI.Areas.Biometrics.Models.ClientConfigurationInventory.Results;
using Portal.Gateway.UI.Constants;

namespace Portal.Gateway.UI.Areas.Biometrics.Controllers
{
    
    [Area("Biometrics")]
    public class NeurotecLicenseController : BaseController<NeurotecLicenseController>
    {
        public NeurotecLicenseController(
            IOptions<AppSettings> appSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper) { }

     
        #region Update

        public async Task<IActionResult> PartialUpdateNeurotecLicense(string encryptedNeurotecLicenseId)
        {
            if (encryptedNeurotecLicenseId.IsNullOrWhitespace())
            {
                return Content("Missing NeurotecLicense id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedNeurotecLicenseId.ToDecrypt());

            var apiResponse = await RestHttpClient.Create().Get<GetNeurotecLicenseResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetNeurotecLicense + resourceId, headers);

            var viewModel = new UpdateNeurotecLicenseViewModel
            {
                Id = apiResponse.Data.Id,
                HostName = apiResponse.Data.HostName,
                LicenseNumber = apiResponse.Data.LicenseNumber,
                LicenseTypeId = apiResponse.Data.LicenseTypeId,               
            };

            return PartialView("_UpdateNeurotecLicense", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateNeurotecLicense(UpdateNeurotecLicenseViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid) //buraya fluent val yazılıcak viewmodellere
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var apiRequest = new UpdateNeurotecLicenseRequestModel
            {
                HostName = viewModel.HostName,              
            };

            var apiResponse = await RestHttpClient.Create().Put<UpdateNeurotecLicenseResult>(
                AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.UpdateNeurotecLicense + viewModel.Id, headers,
                apiRequest);


            if (apiResponse.Data == null)
                return Json(new ResultModel { Message = apiResponse.Message+ "\r\n" + apiResponse.ValidationMessages.ToPlainText(), ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion
        

        #region Get

        public async Task<IActionResult> PartialDetailNeurotecLicense(string encryptedNeurotecLicenseId)
        {
            if (encryptedNeurotecLicenseId.IsNullOrWhitespace())
            {
                return Content("Missing NeurotecLicense id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedNeurotecLicenseId.ToDecrypt());

            var apiResponse = await RestHttpClient.Create().Get<GetNeurotecLicenseResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetNeurotecLicense + resourceId, headers);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var viewModel = new NeurotecLicenseViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                HostName = apiResponse.Data.HostName,
                LicenseNumber = apiResponse.Data.LicenseNumber,
                LicenseTypeId = apiResponse.Data.LicenseTypeId,                
            };

            return PartialView("_DetailNeurotecLicense", viewModel);
        }


        [Action(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedNeurotecLicenses([DataSourceRequest] DataSourceRequest request, FilterNeurotecLicenseViewModel filterViewModel)
        {
            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedNeurotecLicenseApiRequest
            {                
                Pagination = new QmsPaginationApiRequest
                {
                    PageNumber = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = "LicenseNumber",
                    Ascending = true
                }
            };

            var apiResponse = await RestHttpClient.Create().Post<GetPaginatedNeurotecLicensesResult>(
                AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetPaginatedNeurotecLicenses, headers, apiRequest);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var paginatedData = apiResponse.Data.Select(p =>
                new NeurotecLicenseViewModel
                {
                    EncryptedId = p.Id.ToEncrypt(),
                    HostName = p.HostName,
                    LicenseNumber = p.LicenseNumber,
                    LicenseTypeId = p.LicenseTypeId,                   
                   
                }).ToList();

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.TotalNumberOfRecords });
        }

        #endregion
             
    }

}
