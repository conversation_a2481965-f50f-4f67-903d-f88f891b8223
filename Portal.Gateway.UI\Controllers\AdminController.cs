﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.Admin.Login;
using Portal.Gateway.ApiModel.Responses.Admin.Login;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using Portal.Gateway.UI.ViewModels;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Controllers
{
    public class AdminController : BaseController<AdminController>
    {
        public AdminController(
                IOptions<AppSettings> appSettings,
                ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        [AllowAnonymous]
        public ActionResult Login()
        {
            var currentAdmin = SessionExtensions.Get<AdminModel>(HttpContext.Session, SessionKeys.AdminSession);

            if (currentAdmin != null)
                return Redirect("/Admin");

            return View();
        }

        [AllowAnonymous]
        [HttpPost]
        public async Task<IActionResult> LoginAsync(AdminLoginViewModel model)
        {
            if (model == null || !ModelState.IsValid)
            {
                ClearModelStateErrors();
                ModelState.AddModelError("InvalidModel", ResultMessage.MissingOrInvalidData.ToDescription());
                return View("Login", model);
            }

            var apiRequest = new AdminLoginApiRequest
            {
                Username = model.Username,
                Password = model.Password
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AdminLoginApiResponse>>
                (apiRequest, ApiMethodName.Admin.AdminLogin, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                ClearModelStateErrors();
                ModelState.AddModelError("ApiResponse", result.Message);
                return View("Login", model);
            }

            var adminModel = new AdminModel
            {
                AdminId = apiResponse.Data.AdminId,
                Username = apiResponse.Data.Username
            };

            SessionExtensions.Set(HttpContext.Session, SessionKeys.AdminSession, adminModel);
            return Redirect("/Admin");
        }

        [AllowAnonymous]
        public IActionResult Logout()
        {
            HttpContext.Session.Remove(SessionKeys.AdminSession);
            return RedirectToAction("Login", "Admin", new { Area = "" });
        }
    }
}