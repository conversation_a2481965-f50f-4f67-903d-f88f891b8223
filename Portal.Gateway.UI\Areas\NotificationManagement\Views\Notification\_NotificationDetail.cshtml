﻿@model Portal.Gateway.UI.Areas.NotificationManagement.ViewModels.Notification.NotificationViewModel

<div class="card card-custom card-stretch form">
    <div class="card-body">
        <div class="row">
            <div class="col-xl-12">
                <div class="form-group row mb-10">
                    <div class="col-lg-6">
                        <label class="font-weight-bolder">@SiteResources.NotificationNumber.ToTitleCase()</label>
                        <span class="d-block font-italic">@Html.DisplayFor(m => m.NotificationNumber)</span>
                    </div>
                    <div class="col-lg-6">
                        <label class="font-weight-bolder">@SiteResources.NotificationSubject.ToTitleCase()</label>
                        <span class="d-block font-italic">@Html.DisplayFor(m => m.NotificationTitle)</span>
                    </div>
                </div>
                <div class="form-group row mb-10">
                    <div class="col-lg-6 ">
                        <label class="font-weight-bolder">@SiteResources.Languages.ToTitleCase()</label>
                        <span class="d-block font-italic">@Html.DisplayFor(m =>m.Languages)</span>
                    </div>
                    <div class="col-lg-6 ">
                        <label class="font-weight-bolder">@SiteResources.Status.ToTitleCase()</label>
                        <span class="d-block font-italic">@Html.DisplayFor(m => m.Status)</span>
                    </div>
                </div>
                <div class="form-group row mb-10">
                    <div class="col-lg-6 ">
                        <label class="font-weight-bolder">@SiteResources.CreatedAt.ToTitleCase()</label>
                        <span class="d-block font-italic">@Html.DisplayFor(m =>m.CreatedAt)</span>
                    </div>
                    <div class="col-lg-6 ">
                        <label class="font-weight-bolder">@SiteResources.SendTime.ToTitleCase()</label>
                        <span class="d-block font-italic">@Html.DisplayFor(m => m.SendAt)</span>
                    </div>
                </div>
                <div class="form-group row mb-10">
                    <div class="col-lg-6 ">
                        <label class="font-weight-bolder">@SiteResources.ScheduledTime.ToTitleCase()</label>
                        <span class="d-block font-italic">@Html.DisplayFor(m => m.ScheduledAt)</span>
                    </div>
                    <div class="col-lg-6 ">
                        <label class="font-weight-bolder">@SiteResources.Location.ToTitleCase()</label>
                        <span class="d-block font-italic">@Html.DisplayFor(m => m.Location)</span>
                    </div>
                </div>
                <div class="form-group row mb-10">
                    <div class="col-lg-6 ">
                        <label class="font-weight-bolder">@SiteResources.Nationality.ToTitleCase()</label>
                        <span class="d-block font-italic">@Html.DisplayFor(m => m.Nationality)</span>
                    </div>
                    <div class="col-lg-6 ">
                        <label class="font-weight-bolder">@SiteResources.CreatedBy.ToTitleCase()</label>
                        <span class="d-block font-italic">@Html.DisplayFor(m => m.CreatedBy)</span>
                    </div>
                </div>
                <div class="form-group row mb-10">
                    <div class="col-lg-4 ">
                        <label class="font-weight-bolder">@SiteResources.Content.ToTitleCase()</label>
                    </div>
                </div>
                <hr/>
                @for (var index = 0; index < Model.Translations.Count; index++)
                {
                    <div class="form-group row mb-10">
                        <div class="col-lg-4">
                            <span class="d-block font-italic">@Html.DisplayFor(m => m.Translations[index].Language)</span>
                        </div>
                        <div class="col-lg-4">
                            <span class="d-block font-italic">@Html.DisplayFor(m => m.Translations[index].Subject)</span>
                        </div>
                        <div class="col-lg-4">
                            <span class="d-block font-italic">@Html.DisplayFor(m => m.Translations[index].Name)</span>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">@SiteResources.Close</button>
    </div>
</div>
