﻿-- Gateway portal veri tabanında çalıştırmak istediğiniz sql sorgularını lütfen sırasıyla aşağıya ekleyiniz. 
-- Sorgular yukarıdan aşağıya doğru çalışacağından ekleme yaptığınız sıralama çok önemlidir.
-- Düzenlemeleriniz bittikten sonra bu dosyayı check-in yapacağınız dosya kümesine eklediğinizden mutlaka emin olunuz aksi halde değişiklikler geçersiz olacaktır.
-- Otomatik oluşturulan bu dosya ismini Migration-add işleminden sonra \EFMigrationsHistory\PortalDb altında oluşan migration classına seed-readme.md de anlatıldığı gibi eklemeyi unutmayınız!

DROP FUNCTION IF EXISTS public.getvisarejectionlist(integer, integer, integer[], integer, integer[], integer, timestamp without time zone, timestamp without time zone, timestamp without time zone, text, text, text, integer, text, integer, integer, integer, text);

CREATE OR REPLACE FUNCTION public.getvisarejectionlist(
	p_applicationnumber integer,
	p_contryid integer,
	p_contryids integer[],
	p_branchid integer,
	p_branchids integer[],
	p_agencyid integer,
	p_startdate timestamp without time zone,
	p_enddate timestamp without time zone,
	p_applicationdate timestamp without time zone,
	p_passportnumber text,
	p_name text,
	p_surname text,
	p_nationalityid integer,
	p_phonenumber text,
	p_visatypeid integer,
	p_residingcountryid integer,
	p_verificationtypeid integer,
	p_environment text)
    RETURNS TABLE(id integer, passportnumber citext, applicationtime text, branchid integer, name citext, surname citext, rejecteddate timestamp with time zone, refunddate timestamp with time zone, gatewayservicefeename citext, gatewayserviceprice numeric, visafeename citext, visaprice numeric, claimno citext, insurancestartdate timestamp without time zone, insuranceenddate timestamp without time zone, contryid integer, agencyid integer, nationalityid integer, phonenumber text, visacategoryid integer, insurancenumber citext, filecheck boolean, residingcountryid integer, residingcountryname citext, verificationtypeid integer) 
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE PARALLEL UNSAFE
    ROWS 1000

AS $BODY$

declare
    sql text;
    rejectionEndTime timestamp := '2023-10-26';
    refuendIsDoneFileCheckStartDate timestamp := '2023-11-19';
	filtersql text;
    dateformat text;
begin
		filtersql := ' WHERE fileCheck is true';
    
    dateformat := 'MM/dd/yyyy';
   

	if p_startdate is not null then
		filtersql := filtersql || ' AND refundDate >=''' || to_char(p_startdate, dateformat) || '''';
	end if;

	if p_enddate is not null then
		filtersql := filtersql || ' AND refundDate <''' || to_char(p_enddate,dateformat) || '''';
	end if;

	if coalesce(p_applicationnumber,0) != 0 then
			filtersql := filtersql || ' AND Id=' || p_applicationnumber;
	end if;

	if coalesce(p_contryid, 0) != 0 then
			filtersql := filtersql || ' AND Contryid=' || p_contryid;
	end if;

	if coalesce(p_residingcountryid, 0) != 0 then
		filtersql := filtersql || ' AND residingcountryid=' || p_residingcountryid;
	end if;

	if coalesce(p_branchid, 0) != 0 then
			filtersql := filtersql || ' AND branchid=' || p_branchid;
	end if;

	if(array_length(p_branchids, 1) > 0) then
			filtersql := filtersql || ' AND (branchid IN ' || '(' || replace(replace(p_branchids::text,'{',''),'}','') || '))';
	end if;

	if(array_length(p_contryids, 1) > 0) then
			filtersql := filtersql || ' AND (residingcountryid IN ' || '(' || replace(replace(p_contryids::text,'{',''),'}','') || '))';
	end if;

	if coalesce(p_agencyid, 0) != 0 then
			filtersql := filtersql || ' AND agencyid=' || p_agencyid;
	end if;

	if p_applicationdate != null then
		filtersql := filtersql || ' AND applicationTime >= ''' || to_char(p_applicationdate,dateformat) || '''';
	end if;

	if coalesce(TRIM(p_passportnumber), '') != '' then
			filtersql := filtersql || ' AND passportNumber ILIKE ''%' || p_passportnumber || '%''' ;
	end if;

	if coalesce(TRIM(p_name), '') != '' then
			filtersql := filtersql || ' AND name ILIKE ''%' || p_name || '%''' ;
	end if;

	if coalesce(TRIM(p_surname), '') != '' then
			filtersql := filtersql || ' AND surname ILIKE ''%' || p_surname || '%''' ;
	end if;

	if coalesce(p_nationalityid, 0) != 0 then
			filtersql := filtersql || ' AND nationalityid=' || p_nationalityid;
	end if;

	if coalesce(TRIM(p_phonenumber), '') != '' then
			filtersql := filtersql || ' AND phonenumber ILIKE ''%' || p_phonenumber || '%''' ;
	end if;

	if coalesce(p_visatypeid, 0) != 0 then
			filtersql := filtersql || ' AND visacategoryid=' || p_visatypeid;
	end if;

	if p_verificationtypeid IS NOT NULL THEN
        if p_verificationtypeid != 0 THEN
            filtersql := filtersql || ' AND verificationtypeid = ' || p_verificationtypeid;
        else
            filtersql := filtersql || ' AND (verificationtypeid IS NULL)';
        end if;
    end if;

sql :=
		'with ResultSet(Id,
passportNumber,
applicationTime,
branchid,
name,
surname,
rejectedDate,
refundDate,
gatewayServiceFeeName,
gatewayServicePrice,
visaFeeName,
visaPrice,
claimNo,
insuranceStartDate,
insuranceEndDate,
contryid,
agencyid,
nationalityid,
phonenumber,
visacategoryid,
insuranceNumber,
fileCheck,
residingcountryid,
residingcountryname,
verificationtypeid
) as (
select
		a."Id",
		a."PassportNumber" ,
		to_char(a."ApplicationTime",''dd/MM/yyyy'') as applicationTime  ,
		b0."Id"  as branchId,
		a."Name" ,
		a."Surname" ,
		(
	select
		ash2."CreatedAt"
	from
		"ApplicationStatusHistory" ash2
	where
		ash2."ApplicationId" = a."Id"
		and
		 ash2."IsActive" = true
		and ash2."IsDeleted" = false
		and (ash2."ApplicationStatusId" = 16
			or ash2."ApplicationStatusId" = 26
			or ash2."ApplicationStatusId" = 33
			or ash2."ApplicationStatusId" = 24)
	order by
		"Id"
	limit 1) as RejectedDate,
	(
	select
		ash2."CreatedAt"
	from
		"ApplicationStatusHistory" ash2
	where
		ash2."ApplicationId" = a."Id"
		and
		 ash2."IsActive" = true
		and ash2."IsDeleted" = false
		and ash2."ApplicationStatusId" = 17
	order by
		"Id"
	limit 1) as RefundDate,
	(
	select
		e0."Name"
	from
		"ApplicationExtraFee" aef
	inner join "ExtraFee" as e on
			aef."ExtraFeeId" = e."Id"
	inner join "ExtraFeeTranslation" as e0 on
			e."Id" = e0."ExtraFeeId"
		and e0."LanguageId" = 1
	where
		aef."ApplicationId" = a."Id"
		and
		 aef."IsActive" = true
		and (aef."ExtraFeeId" = 1
			or aef."ExtraFeeId" = 67
			or aef."ExtraFeeId" = 80
			or aef."ExtraFeeId" = 74
			or aef."ExtraFeeId" = 133
		    or e."FlagId" = ''be39d059-11b9-56c5-9239-5a3ae6c9b860'')
	order by
		aef."Id"
	limit 1) as GatewayServiceFeeName,
	(
	select
		aef."Price"
	from
		"ApplicationExtraFee" aef
	inner join "ExtraFee" as e on
			aef."ExtraFeeId" = e."Id"
	inner join "ExtraFeeTranslation" as e0 on
			e."Id" = e0."ExtraFeeId"
		and e0."LanguageId" = 1
	where
		aef."ApplicationId" = a."Id"
		and
		 aef."IsActive" = true
		and (aef."ExtraFeeId" = 1
			or aef."ExtraFeeId" = 67
			or aef."ExtraFeeId" = 80
			or aef."ExtraFeeId" = 74
			or aef."ExtraFeeId" = 133
	        or e."FlagId" = ''be39d059-11b9-56c5-9239-5a3ae6c9b860'')
	order by
		aef."Id"
	limit 1) as GatewayServicePrice,
	(case
		when (
	select
		ash2."CreatedAt"
	from
		"ApplicationStatusHistory" ash2
	where
		ash2."ApplicationId" = a."Id"
		and
		 ash2."IsActive" = true
		and ash2."IsDeleted" = false
		and ash2."ApplicationStatusId" = 17
	order by
		"Id"
	limit 1) > ''' || to_char(rejectionEndTime, dateformat) || ''' then
	''''
		else
	(
		select
			e0."Name"
		from
			"ApplicationExtraFee" aef
		inner join "ExtraFee" as e on
			aef."ExtraFeeId" = e."Id"
		inner join "ExtraFeeTranslation" as e0 on
			e."Id" = e0."ExtraFeeId"
			and e0."LanguageId" = 1
		where
			aef."ApplicationId" = a."Id"
			and
		 aef."IsActive" = true
			and (aef."ExtraFeeId" = 2
				or aef."ExtraFeeId" = 3
				or aef."ExtraFeeId" = 4
				or aef."ExtraFeeId" = 5
				or aef."ExtraFeeId" = 6
				or aef."ExtraFeeId" = 14
				or aef."ExtraFeeId" = 15
				or aef."ExtraFeeId" = 16
				or aef."ExtraFeeId" = 17
				or aef."ExtraFeeId" = 29
				or aef."ExtraFeeId" = 31
				or aef."ExtraFeeId" = 34
				or aef."ExtraFeeId" = 35
				or aef."ExtraFeeId" = 36
				or aef."ExtraFeeId" = 37
				or aef."ExtraFeeId" = 38
				or aef."ExtraFeeId" = 76
				or aef."ExtraFeeId" = 77
				or aef."ExtraFeeId" = 118
				or aef."ExtraFeeId" = 119
				or aef."ExtraFeeId" = 120
				or aef."ExtraFeeId" = 121
				or aef."ExtraFeeId" = 132
				or aef."ExtraFeeId" = 157)
		order by
			aef."Id"
		limit 1)
	end )as VisaFeeName,
	(case
		when (
	select
		ash2."CreatedAt"
	from
		"ApplicationStatusHistory" ash2
	where
		ash2."ApplicationId" = a."Id"
		and
		 ash2."IsActive" = true
		and ash2."IsDeleted" = false
		and ash2."ApplicationStatusId" = 17
	order by
		"Id"
	limit 1) > ''' || to_char(rejectionEndTime, dateformat) || ''' then
	null
		else(
		select
			aef."Price"
		from
			"ApplicationExtraFee" aef
		inner join "ExtraFee" as e on
			aef."ExtraFeeId" = e."Id"
		inner join "ExtraFeeTranslation" as e0 on
			e."Id" = e0."ExtraFeeId"
			and e0."LanguageId" = 1
		where
			aef."ApplicationId" = a."Id"
			and
		 aef."IsActive" = true
			and (aef."ExtraFeeId" = 2
				or aef."ExtraFeeId" = 3
				or aef."ExtraFeeId" = 4
				or aef."ExtraFeeId" = 5
				or aef."ExtraFeeId" = 6
				or aef."ExtraFeeId" = 14
				or aef."ExtraFeeId" = 15
				or aef."ExtraFeeId" = 16
				or aef."ExtraFeeId" = 17
				or aef."ExtraFeeId" = 29
				or aef."ExtraFeeId" = 31
				or aef."ExtraFeeId" = 34
				or aef."ExtraFeeId" = 35
				or aef."ExtraFeeId" = 36
				or aef."ExtraFeeId" = 37
				or aef."ExtraFeeId" = 38
				or aef."ExtraFeeId" = 76
				or aef."ExtraFeeId" = 77
				or aef."ExtraFeeId" = 118
				or aef."ExtraFeeId" = 119
				or aef."ExtraFeeId" = 120
				or aef."ExtraFeeId" = 121
				or aef."ExtraFeeId" = 132
				or aef."ExtraFeeId" = 157)
		order by
			aef."Id"
		limit 1)
	end )as VisaPrice,
	(
	select
		l."ClaimNo"
	from
		"ClaimLossEntryLog" l
	where
		l."ApplicationId" = a."Id"
		and l."ClaimNo" != ''''
	order by
		l."Id"
	limit 1 ) as claimNo,
	a2."StartDate" as InsuranceStartDate,
	a2."EndDate"  as  InsuranceEndDate,
	b."CountryId" as Contryid,
	a."AgencyId",
	a."NationalityId",
	a."PhoneNumber1",
	ad."VisaCategoryId",
(
	select
		ai."Number"
	from
		"ApplicationInsurance" ai
	where
		ai."ApplicationId" = a."Id"
		and ai."RelatedIndividualInsuraneId" = 0
	order by
		"Id"
	limit 1) as insuranceNumber,
	(case
		when a."ApplicationTime" > ''' || to_char(refuendIsDoneFileCheckStartDate, dateformat) || ''' then
		(
			select
					count(af."Id") > 2
			from
					"ApplicationFile" af
			where
					af."ApplicationId" = a."Id"
				and af."FileTypeId" in (12, 13, 14)
				and af."IsActive" = true
				and af."IsDeleted" = false)
		else
		true
	end ) as fileCheck,
	b0."CountryId" as residingcountryid,
	bc."Name" as residingcountryname,
		(
	select
		arash."NotificationTypeId"
	from
		"ApplicationRejectionApprovalStatusHistory" arash
	where
		arash."ApplicationId" = a."Id"
	    and arash."IsApprovalCompleted" = true
	    and arash."IsCodeVerified" = true
	order by
		arash."CreatedAt" desc
	limit 1 ) as verificationtypeid
from
		"Application" as a
inner join "BranchApplicationCountry" as b on
		a."BranchApplicationCountryId" = b."Id"
inner join "Branch" as b0 on
		b."BranchId" = b0."Id"
inner join "Country" as bc on
		b0."CountryId" = bc."Id"
inner join "Country" as c on
		a."NationalityId" = c."Id"
inner join "ApplicationDocument" ad on
		a."Id" = ad."ApplicationId"
inner join "ApplicationStatus" as a1 on
		a."ApplicationStatusId" = a1."Id"
left join "ApplicationInsurance" as a2 on
		a."Id" = a2."ApplicationId"
		and a2."IsActive" =true
		and a2."RelatedIndividualInsuraneId" = 0
inner join "ApplicationStatusHistory" a3 on
		a."Id" = a3."ApplicationId"
	and a3."IsActive" = true
	and a3."ApplicationId" in (
	select
			ash."ApplicationId"
	from
			"ApplicationStatusHistory" ash
	where
			ash."ApplicationStatusId" in (16, 26, 33, 24)
		 )
where
		a."ApplicationStatusId" = 17
	and a."ApplicationTypeId" != 3
	and a."ApplicationTypeId" != 4
	and a."ApplicationTypeId" != 11
	and a."ApplicationTypeId" != 9
	and a."ApplicationTypeId" != 8
	and a."ApplicationTypeId" != 10
	and a."IsActive" = true
	and a."IsDeleted" = false
	and a."StatusId" != 2
	and b."IsActive" = true
	and b."IsDeleted" = false
	and a3."ApplicationStatusId" = 17)
 select
	ResultSet.*
from
	ResultSet  ' || filtersql || ' 	GROUP BY  Id,
passportNumber,
applicationTime,
branchid,
name,
surname,
rejectedDate,
refundDate,
gatewayServiceFeeName,
gatewayServicePrice,
visaFeeName,
visaPrice,
claimNo,
insuranceStartDate,
insuranceEndDate,
contryid,
agencyid,
nationalityid,
phonenumber,
visacategoryid,
insuranceNumber,
fileCheck,
residingcountryid,
residingcountryname,
verificationtypeid
order by Id asc';

return QUERY execute sql;
end
$BODY$;

ALTER FUNCTION public.getvisarejectionlist(integer, integer, integer[], integer, integer[], integer, timestamp without time zone, timestamp without time zone, timestamp without time zone, text, text, text, integer, text, integer, integer, integer, text)
    OWNER TO gw_portal_stg_app_usr;

GRANT EXECUTE ON FUNCTION public.getvisarejectionlist(integer, integer, integer[], integer, integer[], integer, timestamp without time zone, timestamp without time zone, timestamp without time zone, text, text, text, integer, text, integer, integer, integer, text) TO PUBLIC;

GRANT EXECUTE ON FUNCTION public.getvisarejectionlist(integer, integer, integer[], integer, integer[], integer, timestamp without time zone, timestamp without time zone, timestamp without time zone, text, text, text, integer, text, integer, integer, integer, text) TO gw_portal_stg_app_usr;

GRANT EXECUTE ON FUNCTION public.getvisarejectionlist(integer, integer, integer[], integer, integer[], integer, timestamp without time zone, timestamp without time zone, timestamp without time zone, text, text, text, integer, text, integer, integer, integer, text) TO gw_stg_app_usr;