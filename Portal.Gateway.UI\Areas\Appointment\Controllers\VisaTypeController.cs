﻿using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Appointment.VisaType;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Appointment.VisaType;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.VisaType;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Appointment.Controllers
{
    [Area("Appointment")]
    public class VisaTypeController : BaseController<VisaTypeController>
    {
        public VisaTypeController(
                IOptions<AppSettings> appSettings,
                ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        #region Add

        public IActionResult PartialAddVisaType()
        {
            var viewModel = new AddVisaTypeViewModel
            {
                VisaTypeName = ""
            };

            foreach (var item in EnumHelper.GetEnumAsDictionary(typeof(ICRLanguage)))
            {
                viewModel.VisaTypeTranslations.Add(new AddVisaTypeTranslationViewModel { LanguageId = item.Key });
            }

            return PartialView("_AddVisaType", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddVisaType(AddVisaTypeViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddVisaTypeApiRequest
            {

                VisaTypeName = viewModel.VisaTypeName,
                VisaTypeTranslations = viewModel.VisaTypeTranslations.Select(p => new AddVisaTypeTranslationApiRequest
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Appointment.AddVisaType, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateVisaType(string encryptedVisaTypeId)
        {
            int id = encryptedVisaTypeId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<VisaTypeApiResponse>>
                (ApiMethodName.Appointment.GetVisaType + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdateVisaTypeViewModel
            {
                Id = apiResponse.Data.Id,
                VisaTypeName = apiResponse.Data.VisaTypeName,
                IsActive = apiResponse.Data.IsActive,
                VisaTypeTranslations = apiResponse.Data.VisaTypeTranslations.Select(p => new UpdateVisaTypeTranslationViewModel
                {
                    Id = p.Id,
                    LanguageId = p.LanguageId,
                    Name = p.Name
                }).ToList()
            };

            return PartialView("_UpdateVisaType", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateVisaType(UpdateVisaTypeViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateVisaTypeApiRequest
            {
                Id = viewModel.Id,
                VisaTypeName = viewModel.VisaTypeName,
                IsActive = viewModel.IsActive,
                VisaTypeTranslations = viewModel.VisaTypeTranslations.Select(p => new UpdateVisaTypeTranslationApiRequest
                {
                    Id = p.Id,
                    LanguageId = p.LanguageId,
                    Name = p.Name
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<EmptyApiResponse>>
                (apiRequest, ApiMethodName.Appointment.UpdateVisaType, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteVisaType(string encryptedVisaTypeId)
        {
            int id = encryptedVisaTypeId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Appointment.DeleteVisaType + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> PartialVisaType(string encryptedVisaTypeId)
        {
            int id = encryptedVisaTypeId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<VisaTypeApiResponse>>
                (ApiMethodName.Appointment.GetVisaType + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new VisaTypeViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                VisaTypeName = apiResponse.Data.VisaTypeName,
                IsActive = apiResponse.Data.IsActive,
                VisaTypeTranslations = apiResponse.Data.VisaTypeTranslations.Select(p => new AddVisaTypeTranslationViewModel()
                {
                    Name = p.Name,
                    LanguageId = p.LanguageId
                }).ToList()
            };

            return PartialView("_VisaType", viewModel);
        }

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedVisaTypes([DataSourceRequest] DataSourceRequest request, FilterVisaTypeViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedVisaTypesApiRequest
            {
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<VisaTypesApiResponse>>>
                (apiRequest, ApiMethodName.Appointment.GetPaginatedVisaTypes, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<VisaTypeItemViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().VisaTypes
                    .Select(p => new VisaTypeItemViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        VisaTypeName = p.VisaTypeName,
                        IsActive = p.IsActive
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #endregion
    }
}
