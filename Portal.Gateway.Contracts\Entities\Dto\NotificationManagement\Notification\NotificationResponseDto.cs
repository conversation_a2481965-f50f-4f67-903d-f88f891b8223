﻿using Portal.Gateway.ApiModel.Responses.NotificationManagement.Notification;
using System.Collections.Generic;
using System;

namespace Portal.Gateway.Contracts.Entities.Dto.NotificationManagement.Notification
{
    public class NotificationResponseDto
    {
        public NotificationResponseDto()
        {
            Translations = new List<NotificationTranslationResponseDto>();
        }
        public string EncryptedId { get; set; }
        public int Id { get; set; }
        public string NotificationNumber { get; set; }
        public string NotificationTitle { get; set; }
        public int StatusId { get; set; }
        public byte JobStatus { get; set; }
        public string Location { get; set; }
        public int? LocationId { get; set; }
        public string Nationality { get; set; }
        public int? NationalityId { get; set; }
        public string SendTime { get; set; }
        public string CreatedBy { get; set; }
        public string Languages { get; set; }
        public int? JobRetryCount { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? NextAttemptDateTime { get; set; }
        public DateTime? ScheduledAt { get; set; }
        public DateTime? SendAt { get; set; }
        public List<NotificationTranslationResponseDto> Translations { get; set; }
    }

    public class NotificationTranslationResponseDto
    {
        public int Id { get; set; }
        public string Language { get; set; }
        public int LanguageId { get; set; }
        public string Subject { get; set; }
        public string Name { get; set; }
        public bool IsActive { get; set; }
    }
}
