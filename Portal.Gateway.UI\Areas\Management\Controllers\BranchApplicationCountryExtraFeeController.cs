﻿using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.Management.BranchApplicationCountryExtraFee;
using Portal.Gateway.ApiModel.Requests.Management.BranchApplicationCountryExtraFeeCommission;
using Portal.Gateway.ApiModel.Requests.Management.BranchApplicationCountryExtraFeeCost;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Management.BranchApplicationCountry;
using Portal.Gateway.ApiModel.Responses.Management.BranchApplicationCountryCompanyExtraFee;
using Portal.Gateway.ApiModel.Responses.Management.BranchApplicationCountryExtraFee;
using Portal.Gateway.ApiModel.Responses.Management.BranchApplicationCountryExtraFeeCommission;
using Portal.Gateway.ApiModel.Responses.Management.BranchApplicationCountryExtraFeeCost;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.BranchApplicationCountry;
using Portal.Gateway.UI.Areas.Management.ViewModels.BranchApplicationCountryExtraFee;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Portal.Gateway.UI.Helpers;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
	[Area("Management")]
	public class BranchApplicationCountryExtraFeeController : BaseController<BranchApplicationCountryExtraFeeController>
	{
		public BranchApplicationCountryExtraFeeController(
			IOptions<AppSettings> appSettings,
			ICacheHelper cacheHelper)
			: base(appSettings, cacheHelper) { }

		#region Add

		public async Task<IActionResult> PartialAddBranchApplicationCountryExtraFee(string encryptedBranchApplicationCountryId)
		{
			var branchApplicationCountryId = encryptedBranchApplicationCountryId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<BranchApplicationCountryApiResponse>>
				(ApiMethodName.Management.GetBranchApplicationCountry + branchApplicationCountryId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Content(result.Message);

			var viewModel = new AddBranchApplicationCountryExtraFeeViewModel()
			{
				BranchName = apiResponse.Data.BranchName,
				CountryName = apiResponse.Data.CountryName,
				BranchApplicationCountryId = apiResponse.Data.Id,
				CurrencyList = EnumHelper.GetEnumAsDictionary(typeof(CurrencyType)).Select(p => new SelectListItem()
				{
					Text = p.Value,
					Value = p.Key.ToString()
				}).ToList()
			};

			viewModel.CurrencyList.Insert(0, new SelectListItem() { Text = SiteResources.Select, Value = "" });

			return PartialView("_AddBranchApplicationCountryExtraFee", viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> AddBranchApplicationCountryExtraFee(AddBranchApplicationCountryExtraFeeViewModel viewModel)
		{
			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var apiRequest = new AddBranchApplicationCountryExtraFeeApiRequest
			{
				BranchApplicationCountryId = viewModel.BranchApplicationCountryId,
				ExtraFeeId = viewModel.ExtraFeeId,
				IsAutoChecked = viewModel.IsAutoChecked,
				Tax = viewModel.Tax,
				TaxRatio = viewModel.TaxRatio,
				ServiceTax = viewModel.ServiceTax,
				BasePrice = viewModel.BasePrice,
				CurrencyId = viewModel.CurrencyId,
				SapExtraFeeId = viewModel.SapExtraFeeId,
				ShowInICR = viewModel.ShowInICR,
				ShowInSummary = viewModel.ShowInSummary,
				IsShowInReport = viewModel.IsShowInReport,
				IsGroupInIcr = viewModel.IsGroupInIcr,
				IsShowInRejectionList = viewModel.IsShowInRejectionList,
				IsShowInAllApplicationsReport = viewModel.IsShowInAllApplicationsReport,
                IsShowInApplicationAfterRejection = viewModel.IsShowInApplicationAfterRejection,
				IsShowInB2C = viewModel.IsShowInB2C
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<AddApiResponse>>
				(apiRequest, ApiMethodName.Management.AddBranchApplicationCountryExtraFee, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		#endregion

		#region Update

		public async Task<IActionResult> PartialUpdateBranchApplicationCountryExtraFee(string encryptedId)
		{
			int id = encryptedId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<BranchApplicationCountryExtraFeeApiResponse>>
				(ApiMethodName.Management.GetBranchApplicationCountryExtraFee + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Content(result.Message);

			var viewModel = new UpdateBranchApplicationCountryExtraFeeViewModel
			{
				EncryptedId = apiResponse.Data.Id.ToEncrypt(),
				BranchName = apiResponse.Data.BranchName,
				CountryName = apiResponse.Data.CountryName,
				BranchApplicationCountryId = apiResponse.Data.BranchApplicationCountryId,
				ExtraFeeId = apiResponse.Data.ExtraFeeId,
				IsAutoChecked = apiResponse.Data.IsAutoChecked,
				Tax = apiResponse.Data.Tax,
				TaxRatio = apiResponse.Data.TaxRatio,
				ServiceTax = apiResponse.Data.ServiceTax,
				BasePrice = apiResponse.Data.BasePrice,
				CurrencyId = apiResponse.Data.CurrencyId,
				SapExtraFeeId = apiResponse.Data.SapExtraFeeId,
				IsActive = apiResponse.Data.IsActive,
				ShowInICR = apiResponse.Data.ShowInICR,
				ShowInSummary = apiResponse.Data.ShowInSummary,
				IsShowInReport = apiResponse.Data.IsShowInReport,
				IsGroupInIcr = apiResponse.Data.IsGroupInIcr,
				IsShowInRejectionList = apiResponse.Data.IsShowInRejectionList,
				IsShowInAllApplicationsReport = apiResponse.Data.IsShowInAllApplicationsReport,
                IsShowInApplicationAfterRejection = apiResponse.Data.IsShowInApplicationAfterRejection,
                IsShowInB2C = apiResponse.Data.IsShowInB2C,
                CurrencyList = EnumHelper.GetEnumAsDictionary(typeof(CurrencyType)).Select(p => new SelectListItem()
				{
					Text = p.Value,
					Value = p.Key.ToString()
				}).ToList(),
            };

			return PartialView("_UpdateBranchApplicationCountryExtraFee", viewModel);
		}

		[HttpPut]
		public async Task<IActionResult> UpdateBranchApplicationCountryExtraFee(UpdateBranchApplicationCountryExtraFeeViewModel viewModel)
		{
			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var apiRequest = new UpdateBranchApplicationCountryExtraFeeApiRequest
			{
				Id = viewModel.EncryptedId.ToDecryptInt(),
				BranchApplicationCountryId = viewModel.BranchApplicationCountryId,
				ExtraFeeId = viewModel.ExtraFeeId,
				IsAutoChecked = viewModel.IsAutoChecked,
				Tax = viewModel.Tax,
				TaxRatio = viewModel.TaxRatio,
				ServiceTax = viewModel.ServiceTax,
				BasePrice = viewModel.BasePrice,
				CurrencyId = viewModel.CurrencyId,
				SapExtraFeeId = viewModel.SapExtraFeeId,
				IsActive = viewModel.IsActive,
				ShowInICR = viewModel.ShowInICR,
				ShowInSummary = viewModel.ShowInSummary,
				IsShowInReport = viewModel.IsShowInReport,
				IsGroupInIcr = viewModel.IsGroupInIcr,
				IsShowInRejectionList = viewModel.IsShowInRejectionList,
				IsShowInAllApplicationsReport = viewModel.IsShowInAllApplicationsReport,
                IsShowInApplicationAfterRejection = viewModel.IsShowInApplicationAfterRejection,
                IsShowInB2C = viewModel.IsShowInB2C
            };

			var apiResponse = await PortalHttpClientHelper
				.PutAsJsonAsync<ApiResponse<EmptyApiResponse>>
				(apiRequest, ApiMethodName.Management.UpdateBranchApplicationCountryExtraFee, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		#endregion

		#region Delete

		[HttpDelete]
		public async Task<IActionResult> DeleteBranchApplicationCountryExtraFee(string encryptedId)
		{
			var id = encryptedId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.DeleteAsync<ApiResponse<DeleteApiResponse>>
				(ApiMethodName.Management.DeleteBranchApplicationCountryExtraFee + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		#endregion

		#region Get

		public async Task<IActionResult> List(string encryptedBranchApplicationCountryId)
		{
			if (string.IsNullOrEmpty(encryptedBranchApplicationCountryId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "BranchApplicationCountry", new { Area = "Management" });
			}

			int branchApplicationCountryId = encryptedBranchApplicationCountryId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<BranchApplicationCountryApiResponse>>
				(ApiMethodName.Management.GetBranchApplicationCountry + branchApplicationCountryId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
			{
				TempData.Put("Notification", result);
				return RedirectToAction("List", "BranchApplicationCountry", new { Area = "Management" });
			}

			var branchApplicationCountryViewModel = new BranchApplicationCountryViewModel
			{
				EncryptedId = apiResponse.Data.Id.ToEncrypt(),
				BranchName = apiResponse.Data.BranchName,
				CountryName = apiResponse.Data.CountryName
			};

			ViewData["BranchApplicationCountryExtraFee_List"] = branchApplicationCountryViewModel;
			return View();
		}

		public async Task<IActionResult> GetBranchApplicationCountryExtraFees([DataSourceRequest] DataSourceRequest request, FilterBranchApplicationCountryExtraFeeViewModel filterViewModel)
		{
			var apiRequest = new BranchApplicationCountryExtraFeesApiRequest
			{
				BranchApplicationCountryId = filterViewModel.EncryptedBranchApplicationCountryId.ToDecryptInt()
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<BranchApplicationCountryExtraFeesApiResponse>>
				(apiRequest, ApiMethodName.Management.GetBranchApplicationCountryExtraFees, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			var data = new List<BranchApplicationCountryExtraFeeViewModel>();

			if (apiResponse.Data != null && apiResponse.Data.BranchApplicationCountryExtraFees.Any())
			{
				data = apiResponse.Data.BranchApplicationCountryExtraFees
					.Select(p => new BranchApplicationCountryExtraFeeViewModel
					{
						EncryptedId = p.Id.ToEncrypt(),
						ExtraFeeName = p.ExtraFeeName,
						IsAutoChecked = p.IsAutoChecked,
						IsActive = p.IsActive,
						Price = p.Price,
						Tax = p.Tax,
						TaxRatio = p.TaxRatio,
						ServiceTax = p.ServiceTax,
						BasePrice = p.BasePrice,
						ShowInICR = p.ShowInICR,
						ShowInSummary = p.ShowInSummary,
						IsShowInAllApplicationsReport = p.IsShowInAllApplicationsReport,
						IsShowInApplicationAfterRejection = p.IsShowInApplicationAfterRejection,
						IsShowInB2C = p.IsShowInB2C,
                        Currency = EnumHelper.GetEnumDescription(typeof(CurrencyType), p.CurrencyId.ToString())
					}).ToList();
			}

			return Json(data.ToDataSourceResult(request));
		}

		#endregion

		#region AddUpdateCommission

		public async Task<IActionResult> PartialAddUpdateBranchApplicationCountryExtraFeeCommission(string encryptedBranchApplicationExtraFeeId)
		{
			int branchApplicationExtraFeeId = encryptedBranchApplicationExtraFeeId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
			   .GetAsync<ApiResponse<BranchApplicationCountryExtraFeeCommissionApiResponse>>
			   (ApiMethodName.Management.GetBranchApplicationCountryExtraFeeCommission + branchApplicationExtraFeeId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
			   .ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Content(result.Message);

			var viewModel = new AddUpdateBranchApplicationCountryExtraFeeCommissionViewModel();
			viewModel.BranchApplicationCountryExtraFeeId = branchApplicationExtraFeeId;
			viewModel.BranchApplicationCountryExtraFeeName = apiResponse.Data.BranchApplicationCountryExtraFeeName;

			if (apiResponse.Data.IsExisting)
			{
				viewModel.CurrencyId = apiResponse.Data.CurrencyId;
				viewModel.Price = apiResponse.Data.Price;
				viewModel.Tax = apiResponse.Data.Tax;
				viewModel.TaxRatio = apiResponse.Data.TaxRatio;
				viewModel.CommissionTypeId = apiResponse.Data.CommissionTypeId;
			}

			return PartialView("_AddUpdateBranchApplicationCountryExtraFeeCommission", viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> AddUpdateBranchApplicationCountryExtraFeeCommission(AddUpdateBranchApplicationCountryExtraFeeCommissionViewModel viewModel)
		{
			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var apiRequest = new AddUpdateBranchApplicationCountryExtraFeeCommissionApiRequest
			{
				BranchApplicationCountryExtraFeeId = viewModel.BranchApplicationCountryExtraFeeId,
				Price = viewModel.Price,
				Tax = viewModel.Tax,
				TaxRatio = viewModel.TaxRatio,
				CurrencyId = viewModel.CurrencyId,
				CommissionTypeId = viewModel.CommissionTypeId
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<AddApiResponse>>
				(apiRequest, ApiMethodName.Management.AddUpdateBranchApplicationCountryExtraFeeCommission, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		#endregion

		#region AddUpdateCost

		public async Task<IActionResult> PartialAddUpdateBranchApplicationCountryExtraFeeCost(string encryptedBranchApplicationExtraFeeId)
		{
			int branchApplicationExtraFeeId = encryptedBranchApplicationExtraFeeId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
			   .GetAsync<ApiResponse<BranchApplicationCountryExtraFeeCostApiResponse>>
			   (ApiMethodName.Management.GetBranchApplicationCountryExtraFeeCost + branchApplicationExtraFeeId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
			   .ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Content(result.Message);

			var viewModel = new AddUpdateBranchApplicationCountryExtraFeeCostViewModel();
			viewModel.BranchApplicationCountryExtraFeeId = branchApplicationExtraFeeId;
			viewModel.BranchApplicationCountryExtraFeeName = apiResponse.Data.BranchApplicationCountryExtraFeeName;

			if (apiResponse.Data.IsExisting)
			{
				viewModel.CurrencyId = apiResponse.Data.CurrencyId;
				viewModel.Price = apiResponse.Data.Price;
				viewModel.Tax = apiResponse.Data.Tax;
				viewModel.TaxRatio = apiResponse.Data.TaxRatio;
			}

			return PartialView("_AddUpdateBranchApplicationCountryExtraFeeCost", viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> AddUpdateBranchApplicationCountryExtraFeeCost(AddUpdateBranchApplicationCountryExtraFeeCostViewModel viewModel)
		{
			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var apiRequest = new AddUpdateBranchApplicationCountryExtraFeeCostApiRequest
			{
				BranchApplicationCountryExtraFeeId = viewModel.BranchApplicationCountryExtraFeeId,
				Price = viewModel.Price,
				Tax = viewModel.Tax,
				TaxRatio = viewModel.TaxRatio,
				CurrencyId = viewModel.CurrencyId
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<AddApiResponse>>
				(apiRequest, ApiMethodName.Management.AddUpdateBranchApplicationCountryExtraFeeCost, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		#endregion

		#region AddUpdateCompanyExtraFee

		public async Task<IActionResult> PartialAddUpdateBranchApplicationCountryCompanyExtraFee(string encryptedBranchApplicationExtraFeeId)
		{
			int branchApplicationExtraFeeId = encryptedBranchApplicationExtraFeeId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
			   .GetAsync<ApiResponse<GetBranchApplicationCountryCompanyExtraFeeApiResponse>>
			   (ApiMethodName.Management.GetBranchApplicationCountryCompanyExtraFee + branchApplicationExtraFeeId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
			   .ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Content(result.Message);

			var viewModel = new AddUpdateBranchApplicationCountryCompanyExtraFeeViewModel();
			viewModel.BranchApplicationCountryExtraFeeId = branchApplicationExtraFeeId;
			viewModel.BranchApplicationCountryExtraFeeName = apiResponse.Data.BranchApplicationCountryExtraFee;

            var reportyTypes = GetEnumSelectList<CompanyExtraFeeReportType>();

            viewModel.ReportTypes = reportyTypes;

            viewModel.ReportTypeIds = apiResponse.Data.BranchApplicationCountryCompanyExtraFee?.ReportTypeIds ??
                                      new List<int>();
            
            viewModel.ReportTypes2 = reportyTypes;

            viewModel.ReportTypeIds2 = apiResponse.Data.BranchApplicationCountryCompanyExtraFee?.ReportTypeIds2 ??
                                      new List<int>();

            var providerCompanies = GetEnumSelectList<CompanyExtraFeeInsuranceProviderCompany>();

			viewModel.ProviderCompanies = providerCompanies;
			
			viewModel.ProviderCompanies2 = providerCompanies;

            if (apiResponse.Data.BranchApplicationCountryCompanyExtraFee != null)
			{
				viewModel.CurrencyId = apiResponse.Data.BranchApplicationCountryCompanyExtraFee.CurrencyId;
				viewModel.CompanyPrice = apiResponse.Data.BranchApplicationCountryCompanyExtraFee.CompanyPrice;
				viewModel.ProviderCompanyId =
					apiResponse.Data.BranchApplicationCountryCompanyExtraFee.ProviderCompanyId;
				viewModel.ShowInReport = apiResponse.Data.BranchApplicationCountryCompanyExtraFee.ShowInReport;
				
				viewModel.CurrencyId2 = apiResponse.Data.BranchApplicationCountryCompanyExtraFee.CurrencyId2;
				viewModel.CompanyPrice2 = apiResponse.Data.BranchApplicationCountryCompanyExtraFee.CompanyPrice2;
				viewModel.ProviderCompanyId2 =
					apiResponse.Data.BranchApplicationCountryCompanyExtraFee.ProviderCompanyId2;
				viewModel.ShowInReport2 = apiResponse.Data.BranchApplicationCountryCompanyExtraFee.ShowInReport2;
            }

			return PartialView("_AddUpdateBranchApplicationCountryCompanyExtraFee", viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> AddUpdateBranchApplicationCountryCompanyExtraFee(AddUpdateBranchApplicationCountryCompanyExtraFeeViewModel viewModel)
		{
			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var apiRequest = new AddUpdateBranchApplicationCountryCompanyExtraFeeViewModel
			{
				BranchApplicationCountryExtraFeeId = viewModel.BranchApplicationCountryExtraFeeId,
				CompanyPrice = viewModel.CompanyPrice,
				CurrencyId = viewModel.CurrencyId,
				ProviderCompanyId = viewModel.ProviderCompanyId,
				ReportTypeIds =  viewModel.ReportTypeIds,
				ShowInReport = viewModel.ShowInReport,
				CompanyPrice2 = viewModel.CompanyPrice2,
				CurrencyId2 = viewModel.CurrencyId2,
				ProviderCompanyId2 = viewModel.ProviderCompanyId2,
				ReportTypeIds2 =  viewModel.ReportTypeIds2,
				ShowInReport2 = viewModel.ShowInReport2,
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<AddUpdateBranchApplicationCountryCompanyExtraFeeApiResponse>>
				(apiRequest, ApiMethodName.Management.AddOrUpdateBranchApplicationCountryCompanyExtraFees, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

        #region PrivateMethods
        private IEnumerable<SelectListItem> GetEnumSelectList<TEnum>() where TEnum : Enum
        {
            return EnumHelper.GetEnumAsDictionary(typeof(TEnum))
                .Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() })
                .OrderBy(o => o.Text);
        }

        #endregion

        #endregion
    }
}
