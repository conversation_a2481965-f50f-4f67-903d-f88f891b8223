﻿using DocumentFormat.OpenXml.Wordprocessing;
using Gateway.Extensions;
using Gateway.Http;
using Gateway.Redis;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.QmsFolder;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.UI.Areas.Biometrics.Models.Office.RequestModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.Office.Results;
using Portal.Gateway.UI.Areas.Biometrics.Models.Office.ViewModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.Office;
using Portal.Gateway.UI.Models;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using System;
using System.Linq;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Constants;

namespace Portal.Gateway.UI.Areas.Biometrics.Controllers
{
    
    [Area("Biometrics")]
    public class OfficeController : BaseController<OfficeController>
    {
        public OfficeController(
            IOptions<AppSettings> appSettings, 
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper) { }

        #region Add

        public IActionResult PartialAddOffice()
        {
            var viewModel = new AddOfficeViewModel();

            return PartialView("_AddOffice", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddOffice(AddOfficeViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var request = new AddOfficeRequestModel
            {
                Name = viewModel.Name,
                OfficeCode = viewModel.OfficeCode,
                CountryId = viewModel.CountryId,
                Status = 0,
                Address = viewModel.Address,
                BranchId = viewModel.BranchId,
                Phone = viewModel.Phone,
                Email = viewModel.Email
            };

            var apiResponse = await RestHttpClient.Create().Post<AddOfficeResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.AddOffice, headers, request);

            if (apiResponse.Data == null)
                return Json(new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateOffice(string encryptedOfficeId)
        {
            if (encryptedOfficeId.IsNullOrWhitespace())
            {
                return Content("Missing Office id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedOfficeId.ToDecrypt());

            var apiResponse = await RestHttpClient.Create().Get<GetOfficeResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetOffice + resourceId, headers);

            var viewModel = new UpdateOfficeViewModel
            {
                Id = apiResponse.Data.Id,
                Name = apiResponse.Data.Name,
                OfficeCode = apiResponse.Data.OfficeCode,
                CountryId = apiResponse.Data.CountryId,
                Status = 0,
                Address = apiResponse.Data.Address,
                BranchId = apiResponse.Data.BranchId,
                Phone = apiResponse.Data.Phone,
                Email = apiResponse.Data.Email
            };

            return PartialView("_UpdateOffice", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateOffice(UpdateOfficeViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid) //buraya fluent val yazılıcak viewmodellere
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var apiRequest = new UpdateOfficeRequestModel
            {
                Name = viewModel.Name,
                OfficeCode = viewModel.OfficeCode,
                CountryId = viewModel.CountryId,
                Status = 0,
                Address = viewModel.Address,
                BranchId = viewModel.BranchId,
                Phone = viewModel.Phone,
                Email = viewModel.Email,
                
            };

            var apiResponse = await RestHttpClient.Create().Put<UpdateOfficeResult>(
                AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.UpdateOffice + viewModel.Id, headers,
                apiRequest);


            if (apiResponse.Data == null)
                return Json(new ResultModel { Message = apiResponse.ValidationMessages.ToPlainText(), ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion
        

        #region Get

        public async Task<IActionResult> PartialDetailOffice(string encryptedOfficeId)
        {
            if (encryptedOfficeId.IsNullOrWhitespace())
            {
                return Content("Missing Office id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedOfficeId.ToDecrypt());

            var apiResponse = await RestHttpClient.Create().Get<GetOfficeResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetOffice + resourceId, headers);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var viewModel = new OfficeViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Name = apiResponse.Data.Name,
                CountryId = apiResponse.Data.CountryId,
                Address = apiResponse.Data.Address,
                BranchId = apiResponse.Data.BranchId,
                Status = apiResponse.Data.Status,
                OfficeCode = apiResponse.Data.OfficeCode,
                Phone = apiResponse.Data.Phone
            };

            return PartialView("_DetailOffice", viewModel);
        }


        [Action(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedOffices([DataSourceRequest] DataSourceRequest request, FilterOfficeViewModel filterViewModel)
        {
            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedOfficeApiRequest
            {
                FilterCountryId = filterViewModel.FilterCountryId,
                Pagination = new QmsPaginationApiRequest
                {
                    PageNumber = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = "Name",
                    Ascending = true
                }
            };

            var apiResponse = await RestHttpClient.Create().Post<GetPaginatedOfficesResult>(
                AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetPaginatedOffices, headers, apiRequest);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var paginatedData = apiResponse.Data.Select(p =>
                new OfficeViewModel
                {
                    EncryptedId = p.Id.ToEncrypt(),
                    Name = p.Name,
                    Status = p.Status,
                    Address = p.Address,
                    BranchId = p.BranchId,
                    CountryId = p.CountryId,
                    OfficeCode = p.OfficeCode,
                    Phone = p.Phone,
                    
                }).ToList();

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.TotalNumberOfRecords });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteOffice(string encryptedOfficeId)
        {
            if (encryptedOfficeId.IsNullOrWhitespace())
            {
                return Content("Missing Office id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedOfficeId.ToDecrypt());

            var apiResponse = await RestHttpClient.Create().Delete<DeleteOfficeResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.DeleteOffice + resourceId, headers);

            if (apiResponse.Data == null)
                return Json(apiResponse.Message);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

    }

}
