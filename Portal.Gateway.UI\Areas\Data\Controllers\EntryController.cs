﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.Appointment.Data;
using Portal.Gateway.ApiModel.Requests.Appointment.QualityCheck;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Appointment.Application;
using Portal.Gateway.ApiModel.Responses.Appointment.Data;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Data.ViewModels.Entry;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Data.Controllers
{
    [Area("Data")]
    public class EntryController : BaseController<EntryController>
    {
        private readonly IWebHostEnvironment _environment;

        public EntryController(
            IWebHostEnvironment environment,
            ICacheHelper cacheHelper,
            IOptions<AppSettings> appSettings)
            : base(appSettings, cacheHelper)
        {
            _environment = environment;
        }

        #region Information

        public async Task<IActionResult> Information(string encryptedApplicationId)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            int id = encryptedApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>
                (ApiMethodName.Appointment.GetApplication + id, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            var apiResponseData = await PortalHttpClientHelper
                .GetAsync<ApiResponse<DataApiResponse>>
                (ApiMethodName.Data.GetData + id, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            var viewModel = new AddUpdateInformationViewModel
            {
                NationalityId = apiResponseData.Data?.NationalityId ?? (apiResponse.Data?.NationalityId ?? 0),
                PassportNumber = apiResponseData.Data?.PassportNumber ?? apiResponse.Data?.PassportNumber,
                EncryptedApplicationId = encryptedApplicationId,
                EncryptedId = apiResponseData.Data?.Id.ToEncrypt(),
                TravelDocumentTypeId = apiResponseData.Data?.TravelDocumentTypeId ?? 0,
                AppliedCountryId = apiResponseData.Data?.AppliedCountryId ?? 0,
                AppliedMissionId = apiResponseData.Data?.AppliedMissionId ?? 0,
                TravelTypeId = apiResponseData.Data?.TravelTypeId ?? 0,
                VisaEntryTypeId = apiResponseData.Data?.VisaEntryTypeId ?? 0,
                VisaTypeId = apiResponseData.Data?.VisaTypeId ?? 0,
                ContactStatusId = apiResponseData.Data?.Contact?.DataStatusId,
                InformationStatusId = apiResponseData.Data?.DataStatusId,
                DemographicStatusId = apiResponseData.Data?.Demographic?.DataStatusId,
                TravelDetailStatusId = apiResponseData.Data?.TravelDetail?.DataStatusId,
                TravelStatusId = apiResponseData.Data?.Travel?.DataStatusId,
                HealthInstitutionId = apiResponseData.Data?.HealthInstitutionId,
                IsReadOnly = apiResponseData.Data != null && apiResponseData.Data.QualityCheckId.HasValue &&
                             apiResponseData.Data.QualityCheckStatusId != QualityCheckStatus.Rejected.ToInt()
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> Information(AddUpdateInformationViewModel request)
        {
            var apiRequest = new AddUpdateDataInformationApiRequest
            {
                ApplicationId = request.EncryptedApplicationId.ToDecryptInt(),
                AppliedCountryId = request.AppliedCountryId,
                AppliedMissionId = request.AppliedMissionId,
                CreatedBy = UserSession.UserId,
                Id = !string.IsNullOrEmpty(request.EncryptedId) ? request.EncryptedId.ToDecryptInt() : (int?)null,
                HealthInstitutionId = request.HealthInstitutionId,
                NationalityId = request.NationalityId,
                PassportNumber = request.PassportNumber,
                VisaTypeId = request.VisaTypeId,
                TravelDocumentTypeId = request.TravelDocumentTypeId,
                TravelTypeId = request.TravelTypeId,
                VisaEntryTypeId = request.VisaEntryTypeId
            };

            var apiResponse = new ApiResponse<AddApiResponse>();

            if (apiRequest.Id.HasValue)
            {
                apiResponse = await PortalHttpClientHelper
                    .PutAsJsonAsync<ApiResponse<AddApiResponse>>
                    (apiRequest, ApiMethodName.Data.UpdateDataInformation, AppSettings.PortalGatewayApiUrl,
                        PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);
            }
            else
            {
                apiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                    (apiRequest, ApiMethodName.Data.AddDataInformation, AppSettings.PortalGatewayApiUrl,
                        PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);
            }

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel
            {
                Data = apiResponse.Data, Message = ResultMessage.OperationIsSuccessful.ToDescription(),
                ResultType = ResultType.Success
            });
        }

        #endregion

        #region Travel

        public async Task<IActionResult> Travel(string encryptedApplicationId)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var id = encryptedApplicationId.ToDecryptInt();

            var apiResponseApplication = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>
                (ApiMethodName.Appointment.GetApplication + id, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<DataApiResponse>>
                (ApiMethodName.Data.GetData + id, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var viewModel = new AddUpdateTravelViewModel
            {
                EncryptedApplicationId = encryptedApplicationId,
                EncryptedApplicationDataId = apiResponse.Data?.Id.ToEncrypt(),
                EncryptedId = apiResponse.Data?.Travel?.Id.ToEncrypt(),
                IsHoldTravelDocumentExpireDate = apiResponse.Data?.Travel?.IsHoldTravelDocumentExpireDate ?? false,
                IssuingAuthority = apiResponse.Data?.Travel?.IssuingAuthority,
                IssuingStateId = apiResponse.Data?.Travel?.IssuingStateId ?? 0,
                PassportExpiryDate = apiResponse.Data?.Travel?.PassportExpiryDate.DateTime ?? DateTime.Now,
                PassportIssueDate = apiResponse.Data?.Travel?.PassportIssueDate.DateTime ?? DateTime.Now,
                PassportNumber = apiResponseApplication.Data?.PassportNumber,
                TravelDocumentTypeId = 1, // TODO
                ContactStatusId = apiResponse.Data?.Contact?.DataStatusId,
                InformationStatusId = apiResponse.Data?.DataStatusId,
                DemographicStatusId = apiResponse.Data?.Demographic?.DataStatusId,
                TravelDetailStatusId = apiResponse.Data?.TravelDetail?.DataStatusId,
                TravelStatusId = apiResponse.Data?.Travel?.DataStatusId,
                IsReadOnly = apiResponse.Data != null && apiResponse.Data.QualityCheckId.HasValue &&
                             apiResponse.Data.QualityCheckStatusId != QualityCheckStatus.Rejected.ToInt(),
                TravelHistories = EnumHelper.GetEnumAsDictionary(typeof(DataTravelHistoryQuestionType)).Select(
                    (p, index) => new AddUpdateTravelViewModel.TravelHistory()
                    {
                        QuestionId = p.Key,
                        Question = p.Value,
                        AppliedDocument = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)?.AppliedDocument,
                        ArrivalDate = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)?.ArrivalDate,
                        CrimeDate = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)?.CrimeDate,
                        DepartureDate = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)?.DepartureDate,
                        DeportationDate = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)?.DeportationDate,
                        DeportationInstitutionId = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)
                            ?.DeportationInstitutionId,
                        DeportationReasonId = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)
                            ?.DeportationReasonId,
                        DetailOfCrime = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)?.DetailOfCrime,
                        DocumentReceived = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)?.DocumentReceived,
                        IssuingAuthority = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)?.IssuingAuthority,
                        PointOfEntry = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)?.PointOfEntry,
                        ReasonOfOverStay = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)?.ReasonOfOverStay,
                        RefusingAuthority = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)
                            ?.RefusingAuthority,
                        RejectedDate = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)?.RejectedDate,
                        RejectedReasonId = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)?.RejectedReasonId,
                        RevocationDate = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)?.RevocationDate,
                        RevocationReason = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)?.RevocationReason,
                        RevokingAuthority = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)
                            ?.RevokingAuthority,
                        TravelHistoryAnswerId = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)
                            ?.TravelHistoryAnswerId ?? 0,
                        TypeOfCrime = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)?.TypeOfCrime,
                        TypeOfDocument = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)?.TypeOfDocument,
                        TypeOfVisa = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)?.TypeOfVisa,
                        ValidUntil = apiResponse.Data?.Travel?.TravelHistories.ElementAt(index)?.ValidUntil
                    }).ToList()
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> Travel(AddUpdateTravelViewModel request)
        {
            var apiRequest = new AddUpdateDataTravelApiRequest
            {
                ApplicationDataId = request.EncryptedApplicationDataId.ToDecryptInt(),
                CreatedBy = UserSession.UserId,
                Id = request.EncryptedId.ToDecryptInt(),
                IsHoldTravelDocumentExpireDate = request.IsHoldTravelDocumentExpireDate,
                IssuingAuthority = request.IssuingAuthority,
                IssuingState = request.IssuingStateId,
                PassportExpiryDate = request.PassportExpiryDate,
                PassportIssueDate = request.PassportIssueDate,
                TravelHistories = request.TravelHistories.OrderBy(p => p.QuestionId).Select(p =>
                    new AddUpdateDataTravelApiRequest.DataTravelHistory()
                    {
                        AppliedDocument = p.AppliedDocument,
                        ArrivalDate = p.ArrivalDate,
                        CrimeDate = p.CrimeDate,
                        DepartureDate = p.DepartureDate,
                        DeportationDate = p.DeportationDate,
                        DeportationInstitutionId = p.DeportationInstitutionId,
                        DeportationReasonId = p.DeportationReasonId,
                        DetailOfCrime = p.DetailOfCrime,
                        DocumentReceived = p.DocumentReceived,
                        IssuingAuthority = p.IssuingAuthority,
                        PointOfEntry = p.PointOfEntry,
                        ReasonOfOverStay = p.ReasonOfOverStay,
                        RefusingAuthority = p.RefusingAuthority,
                        RejectedDate = p.RejectedDate,
                        RejectedReasonId = p.RejectedReasonId,
                        RevocationDate = p.RevocationDate,
                        RevocationReason = p.RevocationReason,
                        RevokingAuthority = p.RevokingAuthority,
                        TravelHistoryAnswerId = p.TravelHistoryAnswerId,
                        TypeOfCrime = p.TypeOfCrime,
                        TypeOfDocument = p.TypeOfDocument,
                        TypeOfVisa = p.TypeOfVisa,
                        ValidUntil = p.ValidUntil
                    }).ToList()
            };

            var apiResponse = new ApiResponse<AddApiResponse>();

            if (apiRequest.Id.HasValue && apiRequest.Id > 0)
            {
                apiResponse = await PortalHttpClientHelper
                    .PutAsJsonAsync<ApiResponse<AddApiResponse>>
                    (apiRequest, ApiMethodName.Data.UpdateDataTravel, AppSettings.PortalGatewayApiUrl,
                        PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);
            }
            else
            {
                apiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                    (apiRequest, ApiMethodName.Data.AddDataTravel, AppSettings.PortalGatewayApiUrl,
                        PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);
            }

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel
            {
                Data = apiResponse.Data, Message = ResultMessage.OperationIsSuccessful.ToDescription(),
                ResultType = ResultType.Success
            });
        }

        #endregion

        #region TravelDetail

        public async Task<IActionResult> TravelDetail(string encryptedApplicationId)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            int id = encryptedApplicationId.ToDecryptInt();

            var apiResponseApplication = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>
                (ApiMethodName.Appointment.GetApplication + id, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponseApplication.Validate(out ResultModel resultApplication).ConfigureAwait(false))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<DataApiResponse>>
                (ApiMethodName.Data.GetData + id, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var viewModel = new AddUpdateTravelDetailViewModel
            {
                EncryptedApplicationId = encryptedApplicationId,
                EncryptedApplicationDataId = apiResponse.Data?.Id.ToEncrypt(),
                EncryptedId = apiResponse.Data?.TravelDetail?.Id.ToEncrypt(),
                Address = apiResponse.Data?.TravelDetail?.Address,
                ArrivalDate = apiResponse.Data?.TravelDetail?.ArrivalDate ??
                              apiResponseApplication.Data.Document.EntryDate,
                CityId = apiResponse.Data?.TravelDetail?.CityId ?? 0,
                DurationStayInDays = apiResponse.Data?.TravelDetail?.DurationStayInDays ?? 1,
                ExpensesCoverByTypeId = apiResponse.Data?.TravelDetail?.ExpensesCoverByTypeId ?? 0,
                HostAddress = apiResponse.Data?.TravelDetail?.HostAddress,
                HostEmail = apiResponse.Data?.TravelDetail?.HostEmail,
                HostNameSurname = apiResponse.Data?.TravelDetail?.HostNameSurname,
                HostPhoneNumber = apiResponse.Data?.TravelDetail?.HostPhoneNumber,
                InsuranceEndDate = apiResponse.Data?.TravelDetail?.InsuranceEndDate,
                IsHealthInsuranceExisting = apiResponse.Data?.TravelDetail?.IsHealthInsuranceExisting ?? false,
                MeansOfTransportId = apiResponse.Data?.TravelDetail?.MeansOfTransportId ?? 0,
                Note = apiResponse.Data?.TravelDetail?.Note,
                PhoneNumber = apiResponse.Data?.TravelDetail?.PhoneNumber,
                PostalCode = apiResponse.Data?.TravelDetail?.PostalCode ?? 0,
                ContactStatusId = apiResponse.Data?.Contact?.DataStatusId,
                InformationStatusId = apiResponse.Data?.DataStatusId,
                DemographicStatusId = apiResponse.Data?.Demographic?.DataStatusId,
                TravelDetailStatusId = apiResponse.Data?.TravelDetail?.DataStatusId,
                TravelStatusId = apiResponse.Data?.Travel?.DataStatusId,
                IsReadOnly = apiResponse.Data != null && apiResponse.Data.QualityCheckId.HasValue &&
                             apiResponse.Data.QualityCheckStatusId != QualityCheckStatus.Rejected.ToInt(),
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> TravelDetail(AddUpdateTravelDetailViewModel request)
        {
            var apiRequest = new AddUpdateDataTravelDetailApiRequest
            {
                Address = request.Address,
                ApplicationDataId = request.EncryptedApplicationDataId.ToDecryptInt(),
                ArrivalDate = request.ArrivalDate,
                CityId = request.CityId,
                CreatedBy = UserSession.UserId,
                DurationStayInDays = request.DurationStayInDays,
                ExpensesCoverByTypeId = request.ExpensesCoverByTypeId,
                HostAddress = request.HostAddress,
                HostEmail = request.HostEmail,
                HostNameSurname = request.HostNameSurname,
                HostPhoneNumber = request.HostPhoneNumber,
                Id = !string.IsNullOrEmpty(request.EncryptedId) ? request.EncryptedId.ToDecryptInt() : (int?)null,
                InsuranceEndDate = request.InsuranceEndDate,
                IsHealthInsuranceExisting = request.IsHealthInsuranceExisting,
                MeansOfTransportId = request.MeansOfTransportId,
                PhoneNumber = request.PhoneNumber,
                PostalCode = request.PostalCode
            };

            var apiResponse = new ApiResponse<AddApiResponse>();

            if (apiRequest.Id.HasValue && apiRequest.Id > 0)
            {
                apiResponse = await PortalHttpClientHelper
                    .PutAsJsonAsync<ApiResponse<AddApiResponse>>
                    (apiRequest, ApiMethodName.Data.UpdateDataTravelDetail, AppSettings.PortalGatewayApiUrl,
                        PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);
            }
            else
            {
                apiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                    (apiRequest, ApiMethodName.Data.AddDataTravelDetail, AppSettings.PortalGatewayApiUrl,
                        PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);
            }

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel
            {
                Data = apiResponse.Data, Message = ResultMessage.OperationIsSuccessful.ToDescription(),
                ResultType = ResultType.Success
            });
        }

        #endregion

        #region Demographic

        public async Task<IActionResult> Demographic(string encryptedApplicationId)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            int id = encryptedApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>
                (ApiMethodName.Appointment.GetApplication + id, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var apiResponseData = await PortalHttpClientHelper
                .GetAsync<ApiResponse<DataApiResponse>>
                (ApiMethodName.Data.GetData + id, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponseData.Validate(out ResultModel resultData).ConfigureAwait(false))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var viewModel = new AddUpdateDemographicViewModel
            {
                BirthCountryId = apiResponseData.Data?.Demographic?.BirthCountryId ?? 0,
                BirthDate = apiResponseData.Data?.Demographic?.BirthDate ?? apiResponse.Data.BirthDate,
                BirthPlace = apiResponseData.Data?.Demographic?.BirthPlace,
                DataOccupationId = apiResponseData.Data?.Demographic?.DataOccupationId ?? 0,
                EncryptedApplicationId = encryptedApplicationId,
                EncryptedApplicationDataId = apiResponseData.Data?.Id.ToEncrypt(),
                EncryptedId = apiResponseData.Data?.Demographic?.Id.ToEncrypt(),
                FatherName = apiResponseData.Data?.Demographic?.FatherName ?? apiResponse.Data.FatherName,
                GenderId = apiResponseData.Data?.Demographic?.GenderId ?? apiResponse.Data.GenderId,
                IsSpouseTurkishCitizen = apiResponseData.Data?.Demographic?.IsSpouseTurkishCitizen ?? false,
                MaritalStatusId =
                    apiResponseData.Data?.Demographic?.MaritalStatusId ?? apiResponse.Data.MaritalStatusId,
                MotherName = apiResponseData.Data?.Demographic?.MotherName ?? apiResponse.Data.MotherName,
                Name = apiResponseData.Data?.Demographic?.Name ?? apiResponse.Data.Name,
                Surname = apiResponseData.Data?.Demographic?.Surname ?? apiResponse.Data.Surname,
                NationalIdNumber = apiResponseData.Data?.Demographic?.NationalIdNumber,
                NationalityId = apiResponseData.Data?.Demographic?.NationalityId ??
                                (apiResponse.Data?.NationalityId ?? 0),
                OtherSurname = apiResponseData.Data?.Demographic?.OtherSurname,
                ContactStatusId = apiResponseData.Data?.Contact?.DataStatusId,
                InformationStatusId = apiResponseData.Data?.DataStatusId,
                DemographicStatusId = apiResponseData.Data?.Demographic?.DataStatusId,
                SpouseNationalIdNumber = apiResponseData.Data?.Demographic?.SpouseNationalIdNumber,
                TravelDetailStatusId = apiResponseData.Data?.TravelDetail?.DataStatusId,
                TravelStatusId = apiResponseData.Data?.Travel?.DataStatusId,
                IsReadOnly = apiResponseData.Data != null && apiResponseData.Data.QualityCheckId.HasValue &&
                             apiResponseData.Data.QualityCheckStatusId != QualityCheckStatus.Rejected.ToInt()
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> Demographic(AddUpdateDemographicViewModel request)
        {
            var apiRequest = new AddUpdateDataDemographicApiRequest
            {
                ApplicationDataId = request.EncryptedApplicationDataId.ToDecryptInt(),
                BirthCountryId = request.BirthCountryId,
                BirthDate = request.BirthDate,
                BirthPlace = request.BirthPlace,
                CreatedBy = UserSession.UserId,
                CreatedDate = DateTime.UtcNow,
                DataOccupationId = request.DataOccupationId,
                FatherName = request.FatherName,
                GenderId = request.GenderId,
                Id = request.EncryptedId.ToDecryptInt(),
                IsSpouseTurkishCitizen = request.IsSpouseTurkishCitizen,
                MaritalStatusId = request.MaritalStatusId,
                MotherName = request.MotherName,
                Name = request.Name,
                NationalIdNumber = request.NationalIdNumber,
                NationalityId = request.NationalityId,
                OtherSurname = request.OtherSurname,
                Surname = request.Surname,
                SpouseNationalIdNumber = request.SpouseNationalIdNumber
            };

            var apiResponse = new ApiResponse<AddApiResponse>();

            if (apiRequest.Id.HasValue && apiRequest.Id > 0)
            {
                apiResponse = await PortalHttpClientHelper
                    .PutAsJsonAsync<ApiResponse<AddApiResponse>>
                    (apiRequest, ApiMethodName.Data.UpdateDataDemographic, AppSettings.PortalGatewayApiUrl,
                        PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);
            }
            else
            {
                apiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                    (apiRequest, ApiMethodName.Data.AddDataDemographic, AppSettings.PortalGatewayApiUrl,
                        PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);
            }

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel
            {
                Data = apiResponse.Data, Message = ResultMessage.OperationIsSuccessful.ToDescription(),
                ResultType = ResultType.Success
            });
        }

        #endregion

        #region Contact

        public async Task<IActionResult> Contact(string encryptedApplicationId)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            int id = encryptedApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>
                (ApiMethodName.Appointment.GetApplication + id, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var apiResponseData = await PortalHttpClientHelper
                .GetAsync<ApiResponse<DataApiResponse>>
                (ApiMethodName.Data.GetData + id, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponseData.Validate(out ResultModel resultData).ConfigureAwait(false))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var viewModel = new AddUpdateContactViewModel
            {
                Address = apiResponseData.Data?.Contact?.Address ?? apiResponse.Data.Address,
                City = apiResponseData.Data?.Contact?.City,
                CountryId = apiResponseData.Data?.Contact?.CountryId ?? 0,
                EncryptedApplicationDataId = apiResponseData.Data?.Id.ToEncrypt(),
                EncryptedApplicationId = encryptedApplicationId,
                EncryptedId = apiResponseData.Data?.Contact?.Id.ToEncrypt(),
                Email = apiResponseData.Data?.Contact?.Email ?? apiResponse.Data.Email,
                InstitutionAddress = apiResponseData.Data?.Contact?.InstitutionAddress,
                InstitutionCity = apiResponseData.Data?.Contact?.InstitutionCity,
                InstitutionCountryId = apiResponseData.Data?.Contact?.InstitutionCountryId,
                InstitutionEmail = apiResponseData.Data?.Contact?.InstitutionEmail,
                InstitutionName = apiResponseData.Data?.Contact?.InstitutionName,
                InstitutionPhoneNumber1 = apiResponseData.Data?.Contact?.InstitutionPhoneNumber1,
                InstitutionPhoneNumber2 = apiResponseData.Data?.Contact?.InstitutionPhoneNumber2,
                InstitutionPostalCode = apiResponseData.Data?.Contact?.InstitutionPostalCode,
                PhoneNumber1 = apiResponseData.Data?.Contact?.PhoneNumber1 ?? apiResponse.Data.PhoneNumber1,
                PhoneNumber2 = apiResponseData.Data?.Contact?.PhoneNumber2 ?? apiResponse.Data.PhoneNumber2,
                PostalCode = apiResponseData.Data?.Contact?.PostalCode ?? 0,
                ContactStatusId = apiResponseData.Data?.Contact?.DataStatusId,
                InformationStatusId = apiResponseData.Data?.DataStatusId,
                DemographicStatusId = apiResponseData.Data?.Demographic?.DataStatusId,
                TravelDetailStatusId = apiResponseData.Data?.TravelDetail?.DataStatusId,
                TravelStatusId = apiResponseData.Data?.Travel?.DataStatusId,
                IsReadOnly = apiResponseData.Data != null && apiResponseData.Data.QualityCheckId.HasValue &&
                             apiResponseData.Data.QualityCheckStatusId != QualityCheckStatus.Rejected.ToInt()
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> Contact(AddUpdateContactViewModel request)
        {
            var apiRequest = new AddUpdateDataContactApiRequest
            {
                Address = request.Address,
                ApplicationDataId = request.EncryptedApplicationDataId.ToDecryptInt(),
                City = request.City,
                CountryId = request.CountryId,
                CreatedBy = UserSession.UserId,
                Email = request.Email,
                Id = request.EncryptedId.ToDecryptInt(),
                InstitutionAddress = request.InstitutionAddress,
                InstitutionCity = request.InstitutionCity,
                InstitutionCountryId = request.InstitutionCountryId,
                InstitutionEmail = request.InstitutionEmail,
                InstitutionName = request.InstitutionName,
                InstitutionPhoneNumber1 = request.InstitutionPhoneNumber1,
                InstitutionPhoneNumber2 = request.InstitutionPhoneNumber2,
                InstitutionPostalCode = request.InstitutionPostalCode,
                PhoneNumber1 = request.PhoneNumber1,
                PhoneNumber2 = request.PhoneNumber2,
                PostalCode = request.PostalCode
            };

            var apiResponse = new ApiResponse<AddApiResponse>();

            if (apiRequest.Id.HasValue && apiRequest.Id > 0)
            {
                apiResponse = await PortalHttpClientHelper
                    .PutAsJsonAsync<ApiResponse<AddApiResponse>>
                    (apiRequest, ApiMethodName.Data.UpdateDataContact, AppSettings.PortalGatewayApiUrl,
                        PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);
            }
            else
            {
                apiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                    (apiRequest, ApiMethodName.Data.AddDataContact, AppSettings.PortalGatewayApiUrl,
                        PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);
            }

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel
            {
                Data = apiResponse.Data, Message = ResultMessage.OperationIsSuccessful.ToDescription(),
                ResultType = ResultType.Success
            });
        }

        #endregion

        [HttpGet]
        public async Task<IActionResult> AddQualityCheck(string encryptedApplicationId)
        {
            var apiResponseData = await PortalHttpClientHelper
                .GetAsync<ApiResponse<DataApiResponse>>
                (ApiMethodName.Data.GetData + encryptedApplicationId.ToDecryptInt(),
                    AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponseData.Validate(out ResultModel resultData).ConfigureAwait(false))
            {
                return Json(new ResultModel
                {
                    Data = apiResponseData, Message = ResultMessage.MissingOrInvalidData.ToDescription(),
                    ResultType = ResultType.Success
                });
            }

            if (apiResponseData.Data?.Travel == null || apiResponseData.Data.TravelDetail == null ||
                apiResponseData.Data.Demographic == null || apiResponseData.Data.Contact == null)
            {
                return Json(new ResultModel
                {
                    Data = null, Message = ResultMessage.MissingOrInvalidData.ToDescription(),
                    ResultType = ResultType.Success
                });
            }

            if (apiResponseData.Data.QualityCheckId.HasValue) // reactivate rejected QC
            {
                var updateApiRequest = new UpdateQualityCheckApiRequest
                {
                    QualityCheckId = apiResponseData.Data.QualityCheckId.GetValueOrDefault(),
                    StatusId = QualityCheckStatus.Pending.ToInt(),
                    UserId = UserSession.UserId
                };

                var updateApiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                    (updateApiRequest, ApiMethodName.Data.UpdateQualityCheck, AppSettings.PortalGatewayApiUrl,
                        PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                return Json(new ResultModel
                {
                    Data = updateApiResponse.Data, Message = ResultMessage.OperationIsSuccessful.ToDescription(),
                    ResultType = ResultType.Success
                });
            }

            var apiRequest = new AddQualityCheckApiRequest
            {
                ApplicationId = apiResponseData.Data.ApplicationId,
                ApplicationDataId = apiResponseData.Data.Id,
                UserId = UserSession.UserId
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Data.AddQualityCheck, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel
            {
                Data = apiResponse.Data, Message = ResultMessage.OperationIsSuccessful.ToDescription(),
                ResultType = ResultType.Success
            });
        }
    }
}