﻿@{
    ViewBag.Title = SiteResources.SelectModuleBranch.ToTitleCase();
    Layout = "~/Views/Shared/_LayoutLogin.cshtml";
}

@using Microsoft.AspNetCore.Http
@inject IHttpContextAccessor HttpContextAccessor
@inject CurrentUserDataHelper CurrentUserDataHelper
@{
    var currentUser = Portal.Gateway.UI.Extensions.SessionExtensions.Get<UserModel>(HttpContextAccessor.HttpContext?.Session, SessionKeys.UserSession);
    var userBranches = await CurrentUserDataHelper.BuildUserBranches(currentUser);
}

<div class="login login-4 login-signin-on d-flex flex-row-fluid">
    <div class="d-flex flex-center flex-row-fluid bgi-size-cover bgi-position-top bgi-no-repeat">
        <div class="login-form text-center p-7 position-relative" style="width: 400px;">
            <div class="d-flex flex-center mb-15">
                <a href="#">
                    <img src="~/images/logo.png" class="max-h-75px" alt="" />
                </a>
            </div>
            <div class="login-signin">
                <div class="mb-20">
                    <div class="mb-10">
                        <span class="font-weight-bold">@SiteResources.Hello @currentUser.FullName</span>
                        <a class="text-muted font-weight-bold" href="@Url.Action("Logout", "User", new { Area = "" })">(@SiteResources.Logout)</a>
                    </div>
                    <div class="mb-3">
                        <h3>@SiteResources.SelectModuleBranch.ToTitleCase()</h3>
                    </div>
                    @if (currentUser.UserModules.Any())
                    {
                        <div class="d-block mb-3 dropdown dropdown-inline">
                            <a href="#" class="btn btn-sm btn-light-success font-weight-bold dropdown-toggle w-50" data-toggle="dropdown" aria-expanded="false">
                                <i class="la la-puzzle-piece"></i>@SiteResources.SelectModule.ToTitleCase()
                            </a>
                            <div class="dropdown-menu dropdown-menu-xl">
                                <ul class="navi navi-hover">
                                    @foreach (var item in currentUser.UserModules)
                                    {
                                        <li class="navi-item">
                                            <a href='javascript:void(0);' onclick="selectUserModule('@item.ModuleId', '@((int)ModuleType.Appointment)', '@item.ReturnUrl');" class="navi-link">
                                                <span class="navi-icon"><i class="la la-puzzle-piece text-success"></i></span>
                                                <span class="navi-text">@item.ModuleName</span>
                                            </a>
                                        </li>
                                    }
                                </ul>
                            </div>
                        </div>

                        @if (userBranches.Any())
                        {
                            var branchList = new Dictionary<int, string>();

                            foreach (var item in userBranches)
                            {
                                if (@item.BranchNames.Any())
                                {
                                    var branchName = "";

                                    if (item.BranchNames.Any(p => p.Key == Html.CurrentLanguageId().ToString().ToInt()))
                                        branchName = item.BranchNames.First(p => p.Key == Html.CurrentLanguageId().ToString().ToInt()).Value;
                                    else
                                        branchName = item.BranchNames.First().Value;

                                    branchList.Add(item.BranchId, branchName);
                                }
                            }

                            <div class="d-block w-100 mb-3 dropdown dropdown-inline disabled">
                                <a id="dropdownUserBranch" href="#" class="btn btn-sm btn-light-success font-weight-bold dropdown-toggle w-50 disabled" data-toggle="dropdown" aria-expanded="false">
                                    <i class="la la-map-marker"></i>@SiteResources.SelectBranch.ToTitleCase()
                                </a>
                                <div class="dropdown-menu dropdown-menu-xl">
                                    <ul class="navi navi-hover">
                                        @foreach (var item in branchList.OrderBy(o => o.Value))
                                        {
                                            <li class="navi-item">
                                                <a href="/User/SetModuleBranch?encryptedModuleId=@(((int)ModuleType.Appointment).ToEncrypt())&encryptedBranchId=@item.Key.ToEncrypt()" class="navi-link">
                                                    <span class="navi-icon"><i class="la la-map-marker text-success"></i></span>
                                                    <span class="navi-text">@item.Value</span>
                                                </a>
                                            </li>
                                        }
                                    </ul>
                                </div>
                            </div>
                        }
                        else
                        {
                            <div class="d-block mb-3">
                                <span class="text-danger">@SiteResources.BranchNotFound</span>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="d-block mb-3">
                            <span class="text-danger">@SiteResources.ModuleNotFound</span>
                        </div>
                    }
                </div>
            </div>
            <partial name="_LanguageText" />
        </div>
    </div>
</div>


@section Scripts {
    <script src="~/js/User/user.js"></script>
}
