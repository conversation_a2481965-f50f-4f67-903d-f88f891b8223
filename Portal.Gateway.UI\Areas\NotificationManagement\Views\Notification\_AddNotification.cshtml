﻿@model Portal.Gateway.UI.Areas.NotificationManagement.ViewModels.Notification.AddUpdateNotificationViewModel

<form id="formAddNotification" class="card card-custom card-stretch form">
    <div class="card-body">
        <div class="row">
            <div class="col-xl-12">
                <div class="form-group row">
                    <div class="col-lg-9 mb-5">
                        <label class="font-weight-bold">
                            @SiteResources.NotificationSubject.ToTitleCase()
                        </label>
                        @Html.TextBoxFor(m => m.NotificationTitle, null, new { @class = "form-control" })
                        @Html.ValidationMessageFor(m => m.NotificationTitle)
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-lg-2">
                        @(Html.Kendo().CheckBox().Name("allCheckBox").Label(SiteResources.Active.ToTitleCase()))
                    </div>
                    <div class="col-lg-3">
                        <label class="font-weight-bold">@SiteResources.Title.ToTitleCase()</label>
                    </div>
                    <div class="col-lg-2">
                        <label class="font-weight-bold">@SiteResources.Languages.ToTitleCase()</label>
                    </div>
                    <div class="col-lg-5">
                        <label class="font-weight-bold">@SiteResources.Notification.ToTitleCase()</label>
                    </div>
                </div>
                <hr/>
                @for (var i = 0; i < @Model.Translations.Count; i++)
                {
                    <div class="form-group row">
                        @Html.HiddenFor(m => m.Translations[i].Id)
                        @Html.HiddenFor(m => m.Translations[i].LanguageId)

                        <div class="col-lg-2 mb-5">
                            <div>
                                <div class="active_checkbox">
                                @(Html.Kendo().CheckBoxFor(m => m.Translations[i].IsActive)
                                      .Checked(Model.Translations[i].IsActive))
                                  </div>
                            </div>
                        </div>
                        <div class="col-lg-3 mb-5">
                            <div>
                                <div>
                                    @Html.TextBoxFor(m => m.Translations[i].Subject, null, new { @class = "form-control" })
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-2 mb-5">
                            <div>
                                <div>
                                    <label class="font-weight-bold">
                                    @Model.Translations[i].Language.ToTitleCase()
                                        @if (Model.Translations[i].LanguageId == (int)PushNotificationTranslationLanguage.English)
                                        {
                                            <span style="color: red;">*</span>
                                        }
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-5 mb-5">
                            <div>
                                <div>
                                    @Html.TextBoxFor(m => m.Translations[i].Name, null, new { @class = "form-control" })
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">@SiteResources.Close</button>
        <button type="submit" class="btn btn-primary font-weight-bold">@SiteResources.Save</button>
    </div>
</form>

<script src="~/js/NotificationManagement/notification.js"></script>
<script src="~/js/site.js"></script>