﻿using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Config;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Portal.Gateway.ApiModel.Requests.Management.Tablet;
using Portal.Gateway.ApiModel.Responses.Management.Tablet;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.UI.Areas.Management.ViewModels.Tablet;
using Portal.Gateway.UI.Validators;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Constants;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class TabletController : BaseController<TabletController>
    {
        public TabletController(IOptions<AppSettings> appSettings, ICacheHelper cacheHelper) : base(appSettings, cacheHelper) { }

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedTablets([DataSourceRequest] DataSourceRequest request, FilterTabletViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new GetPaginatedTabletsApiRequest
            {
                FilterName = filterViewModel.FilterName,
                FilterBranchId = filterViewModel.FilterBranchId,
                FilterVersion = filterViewModel.FilterVersion,
                FilterDeviceId = filterViewModel.FilterDeviceId,
                FilterLastHeartbeatTime = filterViewModel.FilterLastHeartbeatTime,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedTabletsApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedTablets, AppSettings.PrintData.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            var paginatedData = new List<TabletViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                var cacheItem = await CacheHelper.GetBranchesAsync();

                paginatedData = apiResponse.Data.Items.FirstOrDefault()?.Tablets
                    .Select(p => new TabletViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        Name = p.Name,
                        DeviceId = p.DeviceId,
                        Version = p.Version,
                        BranchId = p.BranchId,
                        BranchName = cacheItem?.Branches?.FirstOrDefault(b => b.Id == p.BranchId)?.BranchTranslations?.FirstOrDefault(q => q.LanguageId == LanguageId)?.Name
                    }).ToList();
            }
           
            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data?.TotalItemCount ?? 0 });
        }

        public async Task<IActionResult> PartialUpdateTablet(string encryptedTabletId)
        {
            int id = encryptedTabletId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<TabletApiResponse>>
                    (ApiMethodName.Management.GetTablet + id, AppSettings.PrintData.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var cacheItem = await CacheHelper.GetBranchesAsync();

            var viewModel = new TabletViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Name = apiResponse.Data.Name,
                DeviceId = apiResponse.Data.DeviceId,
                Version = apiResponse.Data.Version,
                BranchId = apiResponse.Data.BranchId,
                BranchName = cacheItem?.Branches?.FirstOrDefault(b => b.Id == apiResponse.Data.BranchId)?.BranchTranslations?.FirstOrDefault(p => p.LanguageId == LanguageId)?.Name
            };

            return PartialView("_UpdateTablet", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateTablet(TabletViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateTabletApiRequest
            {
                Id = viewModel.EncryptedId.ToDecryptInt(),
                Name = viewModel.Name,
                BranchId = viewModel.BranchId
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                    (apiRequest, ApiMethodName.Management.UpdateTablet, AppSettings.PrintData.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteTablet(string encryptedTabletId)
        {
            int id = encryptedTabletId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                    (ApiMethodName.Management.DeleteTablet + id, AppSettings.PrintData.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }
    }
}
