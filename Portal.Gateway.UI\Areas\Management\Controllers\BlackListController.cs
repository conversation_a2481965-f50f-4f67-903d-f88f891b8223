﻿using Gateway.Extensions;
using Gateway.Http;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.QmsFolder;
using Portal.Gateway.ApiModel.Requests.Management.BlackList;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.BlackList;
using Portal.Gateway.UI.Areas.Management.ViewModels.BlackList.RequestModels;
using Portal.Gateway.UI.Areas.Management.ViewModels.BlackList.Results;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using System.Linq;
using System.Threading.Tasks;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.UI.Constants;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class BlackListController : BaseController<BlackListController>
    {
        public BlackListController(
            IOptions<AppSettings> appSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {
        }

        #region Get

        [Action(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedBlackLists([DataSourceRequest] DataSourceRequest request, FilterBlackListViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedBlackListApiRequest
            {
                FilterPassportNumber = filterViewModel.FilterPassportNumber,
                FilterName = filterViewModel.FilterName,
                FilterSurname = filterViewModel.FilterSurname,
                FilterNationalityId = filterViewModel.FilterNationalityId,
                
                Pagination = new QmsPaginationApiRequest
                {
                    PageNumber = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = "Name",
                    Ascending = true
                }
            };

            var apiResponse = await RestHttpClient.Create().Post<GetPaginatedBlackListsResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.GetPaginatedBlackLists, QMSApiDefaultRequestHeaders, apiRequest);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var paginatedData = apiResponse.Data.Select(p =>
                new BlackListViewModel
                {
                    EncryptedId = p.Id.ToEncrypt(),
                    Id = p.Id,
                    PassportNumber = p.PassportNumber,
                    Name = p.Name,
                    Surname = p.Surname,
                    Nationality = p.Nationality,
                    NationalityId= p.NationalityId,  
                }).ToList();

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.TotalNumberOfRecords });
        }

        public async Task<IActionResult> GetBlackList(string EncryptedId)
        {
            var resourceId = EncryptedId.ToDecryptInt();

            var apiResponse = await RestHttpClient.Create().Get<GetBlackListResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.GetBlackList + resourceId, QMSApiDefaultRequestHeaders);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var viewModel = new BlackListViewModel
            {
                PassportNumber = apiResponse.Data.PassportNumber,
                Name = apiResponse.Data.Name,
                Surname = apiResponse.Data.Surname,
                Nationality = apiResponse.Data.Nationality,
                Note = apiResponse.Data.Note
            };

            return PartialView("_BlackList", viewModel);
        }

        public async Task<IActionResult> PartialUpdateBlackList(string EncryptedId)
        {
            var resourceId = EncryptedId.ToDecryptInt();

            var apiResponse = await RestHttpClient.Create().Get<GetBlackListResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.GetBlackList + resourceId, QMSApiDefaultRequestHeaders);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var viewModel = new AddUpdateBlackListViewModel
            {
                EncryptedId = EncryptedId,
                PassportNumber = apiResponse.Data.PassportNumber,
                Name = apiResponse.Data.Name,
                Surname = apiResponse.Data.Surname,
                NationalityId = apiResponse.Data.NationalityId,
                Note = apiResponse.Data.Note
            };

            return PartialView("_UpdateBlackList", viewModel);
        }

        #endregion

        #region Add

        public IActionResult PartialAddBlackList()
        {
            var viewModel = new AddUpdateBlackListViewModel();

            return PartialView("_AddBlackList", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddBlackList(AddUpdateBlackListViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var request = new AddBlackListRequestModel
            {
                PassportNumber = viewModel.PassportNumber,
                Name = viewModel.Name,
                Surname = viewModel.Surname,
                NationalityId= viewModel.NationalityId,
                Note = viewModel.Note
            };

            var apiResponse = await RestHttpClient.Create().Post<AddBlackListResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.AddBlackList, QMSApiDefaultRequestHeaders, request);
                
            if (apiResponse.Data == null)
                return Json(new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        [HttpPut]
        public async Task<IActionResult> UpdateBlackList(AddUpdateBlackListViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddBlackListRequestModel
            {
                Id = viewModel.EncryptedId.ToDecryptInt(),
                PassportNumber = viewModel.PassportNumber,
                Name = viewModel.Name,
                Surname = viewModel.Surname,
                NationalityId = viewModel.NationalityId,
                Note = viewModel.Note
            };

            var apiResponse = await RestHttpClient.Create().Put<UpdateBlackListResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.UpdateBlackList, QMSApiDefaultRequestHeaders, apiRequest);


            if (apiResponse.Data == null)
                return Json(new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteBlackList(string EncryptedId)
        {
            if (EncryptedId.IsNullOrWhitespace())
            {
                return Content("Missing BlackList id");
            }

            var resourceId = EncryptedId.ToDecryptInt();

            var apiResponse = await RestHttpClient.Create().Delete<DeleteBlackListResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.DeleteBlackList + resourceId, QMSApiDefaultRequestHeaders);

            if (apiResponse.Data == null)
                return Json(apiResponse.Message);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion
    }
}
