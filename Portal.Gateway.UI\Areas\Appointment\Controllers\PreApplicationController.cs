﻿using BarcodeStandard;
using ClosedXML.Excel;
using Gateway.Extensions;
using iTextSharp.text;
using iTextSharp.text.pdf;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Agency.Agency;
using Portal.Gateway.ApiModel.Requests.Appointment.Application;
using Portal.Gateway.ApiModel.Requests.Appointment.PreApplication;
using Portal.Gateway.ApiModel.Requests.DocumentManagement;
using Portal.Gateway.ApiModel.Requests.Management.CustomerCard;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Agency.Agency;
using Portal.Gateway.ApiModel.Responses.Appointment.Application;
using Portal.Gateway.ApiModel.Responses.Appointment.PreApplication;
using Portal.Gateway.ApiModel.Responses.DocumentManagement;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.ApiModel.Responses.Management.BranchApplicationCountryFile;
using Portal.Gateway.ApiModel.Responses.Management.CustomerCard;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.PreApplication.Responses;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.PreApplication;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Drawing.Text;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Portal.Gateway.ApiModel.Responses.Management.RoleAction;
using System.Threading;
using SkiaSharp;
using System.Reflection;
using Gateway.Barcode;

namespace Portal.Gateway.UI.Areas.Appointment.Controllers
{
    [Area("Appointment")]
    public class PreApplicationController : BaseController<PreApplicationController>
    {
        private readonly IWebHostEnvironment _webHostEnvironment;

        public PreApplicationController(
            IOptions<AppSettings> appSettings,
            IWebHostEnvironment webHostEnvironment,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper) {
            _webHostEnvironment = webHostEnvironment;
        }

        #region Add-Update

        public async Task<IActionResult> New()
        {
            var apiResponseAgency = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedAgencyApiResponse>>>
                (
                new PaginatedAgencyApiRequest
                {
                    Pagination = new PaginationApiRequest
                    {
                        Page = 1,
                        PageSize = 100,
                        OrderBy = string.Empty,
                        SortDirection = ListSortDirection.Ascending
                    }

                }, ApiMethodName.Agency.GetPaginatedAgencies, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            var viewModel = new AddUpdatePreApplicationViewModel()
            {
                Applicants = Enumerable.Range(0, 15).Select(p => new AddUpdatePreApplicationViewModel.AddUpdatePreApplicationApplicants()
                {
                    SaveApplicantAsCustomer = false,
                    ApplicationEnabled = false
                }).ToList(),
                NumberOfPerson = 1,
                CountryId = 180,
                Date = DateTime.Today.AddDays(1),
                VisaCategoryId = VisaCategoryTypeEnum.Touristic.ToInt(),
                ApplicationTypeId = ApplicationType.Normal.ToInt(),
                StatusId = ActivationStatusType.Active.ToInt(),
                SlotTypeSelectList = EnumHelper.GetEnumAsDictionary(typeof(Contracts.Entities.Enums.SlotType)).Select(p => new SelectListItem()
                {
                    Text = p.Value.ToTitleCase(),
                    Value = p.Key.ToString()
                }).ToList(),
                AgencySelectList = apiResponseAgency.Data.Items?.SelectMany(p => p.Agencies).Where(q => q.ActivationStatusId == ActivationStatusType.Active.ToInt()).Select(p => new SelectListItem()
                {
                    Text = p.Name,
                    Value = p.Id.ToString()
                }).ToList()
            };

            viewModel.AgencySelectList.Insert(0, new SelectListItem() { Text = SiteResources.Select, Value = "" });

            return View("AddUpdate", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddPreApplication(AddUpdatePreApplicationViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            if(viewModel.Applicants.Any(r => r.ApplicationEnabled && r.PassportNumber.IsNullOrWhitespace()))
				return Json(new ResultModel { Message = EnumResources.MissingPassportNumber, ResultType = ResultType.Danger });

			var apiRequest = new AddUpdatePreApplicationApiRequest()
            {
                ApplicantTypeId = viewModel.ApplicantTypeId,
                BranchApplicationCountryId = viewModel.BranchApplicationCountryId,
                VisaCategoryId = viewModel.VisaCategoryId,
                SlotId = viewModel.SlotId,
                VasTypeId = viewModel.VasTypeId == 0 ? null : viewModel.VasTypeId,
                ApplicationTypeId = viewModel.ApplicationTypeId,
                SlotTypeId = viewModel.SlotTypeId,
                Note = viewModel.Note,
                StatusId = viewModel.StatusId,
                AgencyId = viewModel.IsAgency ? viewModel.AgencyId : null,
                CreatedBy = UserSession.UserId,
                Date = viewModel.Date,
                CreatedAt = DateTime.Now,
                Applicants = viewModel.Applicants.Where(p => p.ApplicationEnabled).Select(p => new AddUpdatePreApplicationApiRequest.PreApplicationApplicants
                {
                    BirthDate = p.BirthDate.GetValueOrDefault(),
                    Email = p.Email,
                    GenderId = p.GenderId.GetValueOrDefault(),
                    Name = p.Name.Trim(),
                    NationalityId = p.NationalityId.GetValueOrDefault(),
                    PassportExpireDate = p.PassportExpireDate.GetValueOrDefault(),
                    PassportNumber = p.PassportNumber.Trim(),
                    PhoneNumber = $"{viewModel.CountryCallingCode}{p.PhoneNumber}",
                    Surname = p.Surname.Trim(),
                    SaveApplicantAsCustomer = p.SaveApplicantAsCustomer,
                    RelationShipId = p.PassportNumber == viewModel.Applicants.First().PassportNumber ? 99 : p.RelationShipId,
                    InsuranceTypeIds = p.InsuranceTypeIds == null ? string.Empty : string.Join(",", from i in p.InsuranceTypeIds select i.ToString()),
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Appointment.AddPreApplication, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

			UpdateSessionLastPreApplicationId(apiResponse.Data.Id, true);

			var apiResponse2 = await PortalHttpClientHelper
                .GetAsync<ApiResponse<PreApplicationConfirmationPdfMailApiResponse>>
                ($"{ApiMethodName.Appointment.AddUpdatePreApplicationConfirmationPdf + apiRequest.SlotId}/0/{LanguageId}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);
    
            string webSite = "https://gatewayinternational.com.tr";
            if (apiResponse2.Data.BranchId == 43 || apiResponse2.Data.BranchId == 20 || apiResponse2.Data.BranchId == 24 || apiResponse2.Data.BranchId == 25 || apiResponse2.Data.BranchId == 39 || apiResponse2.Data.BranchId == 38 || apiResponse2.Data.BranchId == 22 || apiResponse2.Data.BranchId == 44 || apiResponse2.Data.BranchId == 21 || apiResponse2.Data.BranchId == 23 || apiResponse2.Data.BranchId == 45 || apiResponse2.Data.BranchId == 26) //Algeria
                webSite = "https://gatewayinternational.com.tr/en/algeria";
            else if (apiResponse2.Data.BranchId == 35 || apiResponse2.Data.BranchId == 16 || apiResponse2.Data.BranchId == 48 || apiResponse2.Data.BranchId == 17 || apiResponse2.Data.BranchId == 18 || apiResponse2.Data.BranchId == 53 || apiResponse2.Data.BranchId == 46 || apiResponse2.Data.BranchId == 19 || apiResponse2.Data.BranchId == 49 || apiResponse2.Data.BranchId == 50 || apiResponse2.Data.BranchId == 34 || apiResponse2.Data.BranchId == 15 || apiResponse2.Data.BranchId == 14 || apiResponse2.Data.BranchId == 52 || apiResponse2.Data.BranchId == 36 || apiResponse2.Data.BranchId == 51) //India
                webSite = "https://gatewayinternational.com.tr/en/india";
            else if (apiResponse2.Data.BranchId == 33 || apiResponse2.Data.BranchId == 8 || apiResponse2.Data.BranchId == 5 || apiResponse2.Data.BranchId == 3 || apiResponse2.Data.BranchId == 42 || apiResponse2.Data.BranchId == 9 || apiResponse2.Data.BranchId == 7 || apiResponse2.Data.BranchId == 11 || apiResponse2.Data.BranchId == 41 || apiResponse2.Data.BranchId == 40 || apiResponse2.Data.BranchId == 4 || apiResponse2.Data.BranchId == 13 || apiResponse2.Data.BranchId == 6 || apiResponse2.Data.BranchId == 10) //Iraq
                webSite = "https://gatewayinternational.com.tr/en/iraq";
            else if (apiResponse2.Data.BranchId == 1) //Kuwait
                webSite = "https://gatewayinternational.com.tr/en/kuwait";
            else if (apiResponse2.Data.BranchId == 12 || apiResponse2.Data.BranchId == 2) //Libya
                webSite = "https://gatewayinternational.com.tr/en/libya";
            else if (apiResponse2.Data.BranchId == 27) //Nepal
                webSite = "https://gatewayinternational.com.tr/en/nepal";
            else if (apiResponse2.Data.BranchId == 30 || apiResponse2.Data.BranchId == 29 || apiResponse2.Data.BranchId == 28) //Saudi Arabia
                webSite = "https://gatewayinternational.com.tr/en/saudi-arabia";
            else if (apiResponse2.Data.BranchId == 32 || apiResponse2.Data.BranchId == 31 || apiResponse2.Data.BranchId == 47) //UAE
                webSite = "https://gatewayinternational.com.tr/en/uae";
            else if (apiResponse2.Data.BranchId == 37) //Askabat
                webSite = "https://gatewayinternational.com.tr/en/turkmenistan";

            string text_english = "Dear Applicant,\nYour appointment has been confirmed for the date and time reflecting against your name and appointment number. Please be available at the following address not more that 15 minutes before the appointed time.\n\nTurkey Visa Application Center - " + apiResponse2.Data.BranchApplicationCountryName + "\n\n" + apiResponse2.Data.BranchAdress + "\n\nFor more information, please visit our website:\n\n" + webSite + "\n\n\nYou can reach us by e-mail and call center number at the below mentioned details.\n\ne-mail: " + apiResponse2.Data.BranchEmail + "\n\nCall Center: " + apiResponse2.Data.BranchCallCenterTelephone + "\n ";
            string text_french = "Cher candidat,\nVotre rendez-vous a été confirmé pour l'heure et la date correspondant à votre nom et numéro de rendez-vous. Veuillez-vous présenter à l'adresse mentionnée ci-dessous 15 minutes avant l'heure de votre rendez-vous.\n\nLe centre de demande de visa pour la Turkiye - " + apiResponse2.Data.BranchApplicationCountryName + "\n\n" + apiResponse2.Data.BranchAdress + "\n\nPour plus d'information, veuillez visiter notre site web:\n\n" + webSite + "\n\n\nVous pouvez nous joindre par e-mails ou par nos numéros du centre d'appels suivants.\n\nCourrier Electronique: " + apiResponse2.Data.BranchEmail + "\n\nCentre d'appel: " + apiResponse2.Data.BranchCallCenterTelephone + "\n ";
            string text_arabic = "عزيزنا المراجع الكريم،" + "\n" + "لقد تمت الموافقة على تحديد الموعد في الساعة والتاريخ المتقابلين لمعلومات الاسم ورقم الموعد الخاصة بك. نرجو تواجدك في العنوان المبين أدناه قبل 15 دقيقة من الموعد." + "\n\n" + "مركز تقديم التأشيرات التركية" + " - " + apiResponse2.Data.BranchApplicationCountryName + "\n\n" + apiResponse2.Data.BranchAdressArabic + "\n\n" + "لمزيداً من المعلومات يرجى زيارة موقعنا الالكتروني" + "\n\n" + webSite + "\n\n\n" + "يمكنك التواصل معنا عبر عنوان البريد الالكتروني ورقم خدمة العملاء المبينين أدناه." + "\n\n" + "البريد الإلكتروني" + apiResponse2.Data.BranchEmail + "\n\n" + "هاتف" + apiResponse2.Data.BranchCallCenterTelephone + "\n ";
            string text_english_Askabat = "Your appointment has been confirmed for the date and time reflecting against your name and appointment number. Please be available at the following address not more that 15 minutes before the appointed time.\n\nTurkey Visa Application Center - " + apiResponse2.Data.BranchApplicationCountryName + "\n\n" + apiResponse2.Data.BranchAdress + "\n\nFor more information, please visit our website:\n\n" + webSite + "\n ";
            string text_turkoman_Askabat = "Siziň bellenmegiňiz, adyňyzy we salgy belgiňizi görkezýän sene we wagt üçin tassyklandy. Bellenen wagtdan 15 minut öň aşakdaky salgyda elýeterli bolmagyňyzy haýyş edýäris.\n\nTürkiye wiza anketasy - " + apiResponse2.Data.BranchApplicationCountryName + "\n\n" + apiResponse2.Data.BranchAdress + "\n\nMaglumat üçin, web sahypamyza girmegiňizi haýyş edýäris:\n\n" + webSite + "\n "; ;
            string text_russian_Askabat = "Ваша встреча была подтверждена на дату и время, отражающие ваше имя и регистрационный номер. Пожалуйста, будьте доступны по следующему адресу не более чем за 15 минут до назначенного времени.\n\nВизовый Центр Турции - " + apiResponse2.Data.BranchApplicationCountryName + "\n\n" + apiResponse2.Data.BranchAdress + "\n\nДля получения информации посетите наш веб-:\n\n" + webSite + "\n "; ;

            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
            iTextSharp.text.Document document = new iTextSharp.text.Document(PageSize.A4, 20, 20, 20, 20);
            var filePath_georgia = Path.Combine(_webHostEnvironment.WebRootPath, "documents", "Fonts", "Georgia.ttf");
            var filePathArial = Path.Combine(_webHostEnvironment.WebRootPath, "documents", "Fonts", "arial.ttf");
            BaseFont georgia = BaseFont.CreateFont(filePath_georgia, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            iTextSharp.text.Font fontGeneral = new iTextSharp.text.Font(georgia, 11, iTextSharp.text.Font.NORMAL, iTextSharp.text.BaseColor.BLACK);
            iTextSharp.text.Font fontHeader = new iTextSharp.text.Font(georgia, 12, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.BLACK);

            MemoryStream memoryStream = new MemoryStream();
            PdfWriter writer = PdfWriter.GetInstance(document, memoryStream);
            document.AddTitle("Appointment Confirmation");            
            document.Open();

            PdfPTable table = new PdfPTable(2);
            table.DefaultCell.BorderColor = iTextSharp.text.BaseColor.BLACK;
            
            PdfPCell cell = new PdfPCell(new Phrase(DateTime.Now.Date.ToString("dd/MM/yyyy"), fontGeneral));
            cell.Colspan = 2;
            cell.HorizontalAlignment = PdfPCell.ALIGN_RIGHT;
            cell.VerticalAlignment = PdfPCell.ALIGN_MIDDLE;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Appointment Details", fontHeader));
            cell.Colspan = 2;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            cell.VerticalAlignment = PdfPCell.ALIGN_MIDDLE;
            cell.BackgroundColor = iTextSharp.text.BaseColor.LIGHT_GRAY;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Applicant Name", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            string name = string.Join(", ", apiRequest.Applicants.Select(applicant => $"{applicant.Name} {applicant.Surname}"));
            cell = new PdfPCell(new Phrase(name, fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Passport Number", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            string passportNo = "";
            for (int i = 0; i < apiRequest.Applicants.Count; i++)
            {
                if (i != apiRequest.Applicants.Count - 1)
                    passportNo += apiRequest.Applicants[i].PassportNumber + ",";
                else
                    passportNo += apiRequest.Applicants[i].PassportNumber;
            }
            cell = new PdfPCell(new Phrase(passportNo, fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Birth Date", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            string birthOfDate = "";
            for (int i = 0; i < apiRequest.Applicants.Count; i++)
            {
                if (i != apiRequest.Applicants.Count - 1) { 
                    if(apiRequest.Applicants[i].BirthDate == DateTime.MinValue || viewModel.Applicants[i].BirthDate == null)
                        birthOfDate += "N/A" + ",";
                    else
                        birthOfDate += apiRequest.Applicants[i].BirthDate.Value.Date.ToString("dd/MM/yyyy") + ",";
                }
                else {
                    if (apiRequest.Applicants[i].BirthDate == DateTime.MinValue || viewModel.Applicants[i].BirthDate == null)
                        birthOfDate += "N/A";
                    else
                        birthOfDate += apiRequest.Applicants[i].BirthDate.Value.Date.ToString("dd/MM/yyyy");
                }
            }
            cell = new PdfPCell(new Phrase(birthOfDate, fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Visa Category", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            cell = await ChangePageLanguageToEnglish(apiRequest.VisaCategoryId, fontGeneral);
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Appointment Date and Appointment Time", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            cell = new PdfPCell(new Phrase(apiRequest.Date.Date.ToString("dd/MM/yyyy") + " " + apiResponse2.Data.SlotTime, fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Appointment Number", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            var barcode = CreateBarcode(apiResponse.Data.Id);
            iTextSharp.text.Image png = iTextSharp.text.Image.GetInstance(barcode);
            png.ScalePercent(50f);
            cell = new PdfPCell(png);
            cell.Colspan = 1;
            cell.FixedHeight = 70f;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            cell.VerticalAlignment = PdfPCell.ALIGN_MIDDLE;
            table.AddCell(cell);

            if(apiResponse2.Data.BranchId != 37) 
            { 
                cell = new PdfPCell(new Phrase(text_english, fontGeneral));
                cell.Colspan = 2;
                cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                table.AddCell(cell);
                cell = new PdfPCell(new Phrase(text_french, fontGeneral));
                cell.Colspan = 2;
                cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                table.AddCell(cell);

                var arabicFont = BaseFont.CreateFont(filePathArial, BaseFont.IDENTITY_H, true);
                var el = new Chunk();
                var f2 = new iTextSharp.text.Font(arabicFont, el.Font.Size, el.Font.Style, el.Font.Color);
                el.Font = f2;
                cell = new PdfPCell(new Phrase(11, text_arabic, el.Font));
                cell.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                cell.Colspan = 2;
                table.AddCell(cell);
            }
            else
            {
                cell = new PdfPCell(new Phrase(text_english_Askabat, fontGeneral));
                cell.Colspan = 2;
                cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                table.AddCell(cell);
                cell = new PdfPCell(new Phrase(text_turkoman_Askabat, fontGeneral));
                cell.Colspan = 2;
                cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                table.AddCell(cell);
                cell = new PdfPCell(new Phrase(text_russian_Askabat, fontGeneral));
                cell.Colspan = 2;
                cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                table.AddCell(cell);
            }

            document.Add(table);
            document.AddAuthor("Appointment Confirmation");
            document.Close();
            writer.Close();
            byte[] pdfFile = memoryStream.ToArray();
            
            memoryStream.Close();

            var apiRequest3 = new AddUpdatePreApplicationApiRequest()
            {
                pdfFile = pdfFile,    
                LanguageId = LanguageId,
                ApplicationNo = apiResponse.Data.Id,
                BranchApplicationCountryName = apiResponse2.Data.BranchApplicationCountryName,
                ApplicationDate = apiResponse2.Data.slotDateAndTime,
                SlotId = viewModel.SlotId,
                CountryId = apiResponse2.Data.CountryId,
                BranchId = apiResponse2.Data.BranchId,
                BranchCountryId = apiResponse2.Data.BranchCountryId,
                BranchNameEn = apiResponse2.Data.BranchNameEn,
                Note = apiResponse2.Data.Note,
                Applicants = viewModel.Applicants.Where(p => p.ApplicationEnabled).Select(p => new AddUpdatePreApplicationApiRequest.PreApplicationApplicants()
                {
                    Email = p.Email,
                    PhoneNumber = $"{viewModel.CountryCallingCode}{p.PhoneNumber}",
                }).ToList(),
                BranchEmailProviderId = apiResponse2.Data.BranchEmailProviderId,
                BranchSmsProviderId = apiResponse2.Data.BranchSmsProviderId,
                BranchSmsIsSendByPrefix = apiResponse2.Data.BranchSmsIsSendByPrefix,
                BranchSmsSender = apiResponse2.Data.BranchSmsSender,
                IsUpdated = false
            };

            var apiResponse3 = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest3, ApiMethodName.Appointment.AddPreApplicationConfirmationPdfMailBefore, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            return Json(new ResultModel { Data = apiResponse.Data, Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        public async Task<IActionResult> Update(string encryptedPreApplicationId)
        {
            int id = encryptedPreApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<PreApplicationApiResponse>>
                (ApiMethodName.Appointment.GetPreApplication + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var countries = await CacheHelper.GetCountriesAsync();
                
            var apiResponseAgency = await PortalHttpClientHelper
               .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedAgencyApiResponse>>>
               (
               new PaginatedAgencyApiRequest
               {
                   Pagination = new PaginationApiRequest
                   {
                       Page = 1,
                       PageSize = 100,
                       OrderBy = string.Empty,
                       SortDirection = ListSortDirection.Ascending
                   }

               }, ApiMethodName.Agency.GetPaginatedAgencies, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
               .ConfigureAwait(false);

            var hasAuthorizedRoleForB2CUpdateAppointment = false;

            var roleActions = await CacheHelper.GetRoleActionsAsync();

            hasAuthorizedRoleForB2CUpdateAppointment = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
                .Any(p => p.RoleActions.Any(q => q.Action.Method == "B2CUpdateAppointment" && q.Action.IsActive));

            var viewModel = new AddUpdatePreApplicationViewModel()
            {
                EncryptedPreApplicationId = apiResponse.Data.PreApplicationId.ToEncrypt(),
                CountryId = apiResponse.Data.CountryId,
                CountryName = countries?.Countries.FirstOrDefault(q => q.Id == apiResponse.Data.CountryId).Name,
                BranchApplicationCountryId = apiResponse.Data.BranchApplicationCountryId,
                BranchName = apiResponse.Data.BranchName,
                CountryCallingCode = apiResponse.Data.CountryCallingCode,
                VisaCategoryId = apiResponse.Data.VisaCategoryId,
                ApplicantTypeId = apiResponse.Data.ApplicantTypeId,
                SlotId = apiResponse.Data.SlotId,
                SelectedSlotId = apiResponse.Data.SlotId,
                NumberOfPerson = apiResponse.Data.Applicants.Count(),
                Date = apiResponse.Data.AppointmentTime.Date,
                IsB2CAppointment = apiResponse.Data.ChannelTypeId == (int)ChannelType.WebSite || apiResponse.Data.ChannelTypeId == (int)ChannelType.Mobile,
                VasTypeId = apiResponse.Data.VasTypeId,
                ApplicationTypeId = apiResponse.Data.ApplicationTypeId,
                SlotTypeId = apiResponse.Data.SlotTypeId,
                AgencyId = apiResponse.Data.AgencyId,
                IsAgency = apiResponse.Data.AgencyId.HasValue,
                Note = apiResponse.Data.Note,
                ChannelTypeId = apiResponse.Data.ChannelTypeId,
                StatusId = apiResponse.Data.StatusId,
                SlotTypeSelectList = EnumHelper.GetEnumAsDictionary(typeof(Contracts.Entities.Enums.SlotType)).Select(p => new SelectListItem()
                {
                    Text = p.Value.ToTitleCase(),
                    Value = p.Key.ToString()
                }).ToList(),
                AgencySelectList = apiResponseAgency.Data.Items?.SelectMany(p => p.Agencies).Where(q => q.ActivationStatusId == ActivationStatusType.Active.ToInt()).Select(p => new SelectListItem()
                {
                    Text = p.Name,
                    Value = p.Id.ToString()
                }).ToList(),
                Applicants = Enumerable.Range(0, 15).Select(p => new AddUpdatePreApplicationViewModel.AddUpdatePreApplicationApplicants()
                {
                    SaveApplicantAsCustomer = false,
                    ApplicationEnabled = false,
                    BirthDate = DateTime.Today.AddYears(-20),
                    PassportExpireDate = DateTime.Today.AddMonths(6)
                }).ToList(),
            };

            var applicants = apiResponse.Data.Applicants.ToList();

            foreach (var item in applicants.Select((value, index) => new { index, value }))
            {
                var currentApplicant = new AddUpdatePreApplicationViewModel.AddUpdatePreApplicationApplicants()
                {
                    EncryptedPreApplicationApplicantId = applicants[item.index].ApplicantId.ToEncrypt(),
                    SaveApplicantAsCustomer = applicants[item.index].IsExistingCustomer,
                    ApplicationEnabled = true,
                    BirthDate = applicants[item.index].BirthDate.GetValueOrDefault(),
                    Name = applicants[item.index].Name,
                    Surname = applicants[item.index].Surname,
                    PassportNumber = applicants[item.index].PassportNumber,
                    PassportExpireDate = applicants[item.index].PassportExpireDate.GetValueOrDefault(),
                    NationalityId = applicants[item.index].NationalityId.GetValueOrDefault(),
                    GenderId = applicants[item.index].GenderId.GetValueOrDefault(),
                    Email = applicants[item.index].Email,
                    SlotId = applicants[item.index].SlotId.GetValueOrDefault(),
                    RelationShipId = applicants[item.index].RelationShipId.GetValueOrDefault(),
                    PhoneNumber = applicants[item.index].PhoneNumber.StartsWith(viewModel.CountryCallingCode) ? applicants[item.index].PhoneNumber.Substring(viewModel.CountryCallingCode.Length) : applicants[item.index].PhoneNumber,
                    InsuranceTypeIds = (applicants[item.index].InsuranceTypeIds == null || applicants[item.index].InsuranceTypeIds == string.Empty) ? new List<int>() { 0 } : applicants[item.index].InsuranceTypeIds.Split(',').Select(Int32.Parse).ToList(),
                };

                viewModel.Applicants[item.index] = currentApplicant;
            }

            return View("AddUpdate", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> UpdatePreApplication(AddUpdatePreApplicationViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            int id = viewModel.EncryptedPreApplicationId.ToDecryptInt();

            var apiResponse2 = await PortalHttpClientHelper
                .GetAsync<ApiResponse<PreApplicationConfirmationPdfMailApiResponse>>
                ($"{ApiMethodName.Appointment.AddUpdatePreApplicationConfirmationPdf + viewModel.SlotId}/{id}/{LanguageId}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (viewModel.IsB2CAppointment is false && (viewModel.ChannelTypeId == (int)ChannelType.WebSite || viewModel.ChannelTypeId == (int)ChannelType.Mobile) &&  apiResponse2.Data.OldSlotId != viewModel.SlotId)
            {
                return Json(new ResultModel { Message = EnumResources.B2CAppointmentCantAllowedBeforeChangeSlot, ResultType = ResultType.Danger });
            }

             List<int> applicants = new List<int>();
            for(int i=0; i<viewModel.Applicants.Count; i++)
            {
                if (viewModel.Applicants[i].Name != null) { 
                    applicants.Add(i);
                }
            }
            string webSite = "https://gatewayinternational.com.tr";
            if (apiResponse2.Data.BranchId == 43 || apiResponse2.Data.BranchId == 20 || apiResponse2.Data.BranchId == 24 || apiResponse2.Data.BranchId == 25 || apiResponse2.Data.BranchId == 39 || apiResponse2.Data.BranchId == 38 || apiResponse2.Data.BranchId == 22 || apiResponse2.Data.BranchId == 44 || apiResponse2.Data.BranchId == 21 || apiResponse2.Data.BranchId == 23 || apiResponse2.Data.BranchId == 45 || apiResponse2.Data.BranchId == 26) //Algeria
                webSite = "https://gatewayinternational.com.tr/en/algeria";
            else if (apiResponse2.Data.BranchId == 35 || apiResponse2.Data.BranchId == 16 || apiResponse2.Data.BranchId == 48 || apiResponse2.Data.BranchId == 17 || apiResponse2.Data.BranchId == 18 || apiResponse2.Data.BranchId == 53 || apiResponse2.Data.BranchId == 46 || apiResponse2.Data.BranchId == 19 || apiResponse2.Data.BranchId == 49 || apiResponse2.Data.BranchId == 50 || apiResponse2.Data.BranchId == 34 || apiResponse2.Data.BranchId == 15 || apiResponse2.Data.BranchId == 14 || apiResponse2.Data.BranchId == 52 || apiResponse2.Data.BranchId == 36 || apiResponse2.Data.BranchId == 51) //India
                webSite = "https://gatewayinternational.com.tr/en/india";
            else if (apiResponse2.Data.BranchId == 33 || apiResponse2.Data.BranchId == 8 || apiResponse2.Data.BranchId == 5 || apiResponse2.Data.BranchId == 3 || apiResponse2.Data.BranchId == 42 || apiResponse2.Data.BranchId == 9 || apiResponse2.Data.BranchId == 7 || apiResponse2.Data.BranchId == 11 || apiResponse2.Data.BranchId == 41 || apiResponse2.Data.BranchId == 40 || apiResponse2.Data.BranchId == 4 || apiResponse2.Data.BranchId == 13 || apiResponse2.Data.BranchId == 6 || apiResponse2.Data.BranchId == 10) //Iraq
                webSite = "https://gatewayinternational.com.tr/en/iraq";
            else if (apiResponse2.Data.BranchId == 1) //Kuwait
                webSite = "https://gatewayinternational.com.tr/en/kuwait";
            else if (apiResponse2.Data.BranchId == 12 || apiResponse2.Data.BranchId == 2) //Libya
                webSite = "https://gatewayinternational.com.tr/en/libya";
            else if (apiResponse2.Data.BranchId == 27) //Nepal
                webSite = "https://gatewayinternational.com.tr/en/nepal";
            else if (apiResponse2.Data.BranchId == 30 || apiResponse2.Data.BranchId == 29 || apiResponse2.Data.BranchId == 28) //Saudi Arabia
                webSite = "https://gatewayinternational.com.tr/en/saudi-arabia";
            else if (apiResponse2.Data.BranchId == 32 || apiResponse2.Data.BranchId == 31 || apiResponse2.Data.BranchId == 47) //UAE
                webSite = "https://gatewayinternational.com.tr/en/uae";
            else if (apiResponse2.Data.BranchId == 37) //Askabat
                webSite = "https://gatewayinternational.com.tr/en/turkmenistan";

            string text_english = "Dear Applicant,\nYour appointment has been confirmed for the date and time reflecting against your name and appointment number. Please be available at the following address not more that 15 minutes before the appointed time.\n\nTurkey Visa Application Center - " + apiResponse2.Data.BranchApplicationCountryName + "\n\n" + apiResponse2.Data.BranchAdress + "\n\nFor more information, please visit our website:\n\n" + webSite + "\n\n\nYou can reach us by e-mail and call center number at the below mentioned details.\n\ne-mail: " + apiResponse2.Data.BranchEmail + "\n\nCall Center: " + apiResponse2.Data.BranchCallCenterTelephone + "\n ";
            string text_french = "Cher candidat,\nVotre rendez-vous a été confirmé pour l'heure et la date correspondant à votre nom et numéro de rendez-vous. Veuillez-vous présenter à l'adresse mentionnée ci-dessous 15 minutes avant l'heure de votre rendez-vous.\n\nLe centre de demande de visa pour la Turkiye - " + apiResponse2.Data.BranchApplicationCountryName + "\n\n" + apiResponse2.Data.BranchAdress + "\n\nPour plus d'information, veuillez visiter notre site web:\n\n" + webSite + "\n\n\nVous pouvez nous joindre par e-mails ou par nos numéros du centre d'appels suivants.\n\nCourrier Electronique: " + apiResponse2.Data.BranchEmail + "\n\nCentre d'appel: " + apiResponse2.Data.BranchCallCenterTelephone + "\n ";
            string text_arabic = "عزيزنا المراجع الكريم،" + "\n" + "لقد تمت الموافقة على تحديد الموعد في الساعة والتاريخ المتقابلين لمعلومات الاسم ورقم الموعد الخاصة بك. نرجو تواجدك في العنوان المبين أدناه قبل 15 دقيقة من الموعد." + "\n\n" + "مركز تقديم التأشيرات التركية" + " - " + apiResponse2.Data.BranchApplicationCountryName + "\n\n" + apiResponse2.Data.BranchAdressArabic + "\n\n" + "لمزيداً من المعلومات يرجى زيارة موقعنا الالكتروني" + "\n\n" + webSite + "\n\n\n" + "يمكنك التواصل معنا عبر عنوان البريد الالكتروني ورقم خدمة العملاء المبينين أدناه." + "\n\n" + "البريد الإلكتروني" + apiResponse2.Data.BranchEmail + "\n\n" + "هاتف" + apiResponse2.Data.BranchCallCenterTelephone + "\n ";
            string text_english_Askabat = "Your appointment has been confirmed for the date and time reflecting against your name and appointment number. Please be available at the following address not more that 15 minutes before the appointed time.\n\nTurkey Visa Application Center - " + apiResponse2.Data.BranchApplicationCountryName + "\n\n" + apiResponse2.Data.BranchAdress + "\n\nFor more information, please visit our website:\n\n" + webSite + "\n ";
            string text_turkoman_Askabat = "Siziň bellenmegiňiz, adyňyzy we salgy belgiňizi görkezýän sene we wagt üçin tassyklandy. Bellenen wagtdan 15 minut öň aşakdaky salgyda elýeterli bolmagyňyzy haýyş edýäris.\n\nTürkiye wiza anketasy - " + apiResponse2.Data.BranchApplicationCountryName + "\n\n" + apiResponse2.Data.BranchAdress + "\n\nMaglumat üçin, web sahypamyza girmegiňizi haýyş edýäris:\n\n" + webSite + "\n "; ;
            string text_russian_Askabat = "Ваша встреча была подтверждена на дату и время, отражающие ваше имя и регистрационный номер. Пожалуйста, будьте доступны по следующему адресу не более чем за 15 минут до назначенного времени.\n\nВизовый Центр Турции - " + apiResponse2.Data.BranchApplicationCountryName + "\n\n" + apiResponse2.Data.BranchAdress + "\n\nДля получения информации посетите наш веб-:\n\n" + webSite + "\n "; ;

            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
            iTextSharp.text.Document document = new iTextSharp.text.Document(PageSize.A4, 20, 20, 20, 20);
            var filePath_georgia = Path.Combine(_webHostEnvironment.WebRootPath, "documents", "Fonts", "Georgia.ttf");
            var filePathArial = Path.Combine(_webHostEnvironment.WebRootPath, "documents", "Fonts", "arial.ttf");
            BaseFont georgia = BaseFont.CreateFont(filePath_georgia, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            iTextSharp.text.Font fontGeneral = new iTextSharp.text.Font(georgia, 11, iTextSharp.text.Font.NORMAL, iTextSharp.text.BaseColor.BLACK);
            iTextSharp.text.Font fontHeader = new iTextSharp.text.Font(georgia, 12, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.BLACK);

            MemoryStream memoryStream = new MemoryStream();
            PdfWriter writer = PdfWriter.GetInstance(document, memoryStream);
            document.AddTitle("Appointment Confirmation");
            document.Open();

            PdfPTable table = new PdfPTable(2);
            table.DefaultCell.BorderColor = iTextSharp.text.BaseColor.BLACK;

            PdfPCell cell = new PdfPCell(new Phrase(DateTime.Now.Date.ToString("dd/MM/yyyy"), fontGeneral));
            cell.Colspan = 2;
            cell.HorizontalAlignment = PdfPCell.ALIGN_RIGHT;
            cell.VerticalAlignment = PdfPCell.ALIGN_MIDDLE;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Appointment Details", fontHeader));
            cell.Colspan = 2;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            cell.VerticalAlignment = PdfPCell.ALIGN_MIDDLE;
            cell.BackgroundColor = iTextSharp.text.BaseColor.LIGHT_GRAY;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Applicant Name", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            string name = string.Join(", ", applicants.Select(i => $"{viewModel.Applicants[applicants[i]].Name} {viewModel.Applicants[applicants[i]].Surname}"));
            cell = new PdfPCell(new Phrase(name, fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Passport Number", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            string passportNo = "";
            for (int i = 0; i < applicants.Count; i++)
            {
                if (i != applicants.Count - 1)
                    passportNo += viewModel.Applicants[applicants[i]].PassportNumber + ",";
                else
                    passportNo += viewModel.Applicants[applicants[i]].PassportNumber;
            }                
            cell = new PdfPCell(new Phrase(passportNo, fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Birth Date", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            string birthOfDate = "";
            for (int i = 0; i < applicants.Count; i++)
            {
                if (i != applicants.Count - 1) { 
                    if(viewModel.Applicants[applicants[i]].BirthDate == DateTime.MinValue || viewModel.Applicants[applicants[i]].BirthDate == null)
                        birthOfDate += "N/A" + ",";
                    else
                        birthOfDate += viewModel.Applicants[applicants[i]].BirthDate.Value.Date.ToString("dd/MM/yyyy") + ",";
                }
                else{
                    if (viewModel.Applicants[applicants[i]].BirthDate == DateTime.MinValue || viewModel.Applicants[applicants[i]].BirthDate == null)
                        birthOfDate += "N/A";
                    else
                        birthOfDate += viewModel.Applicants[applicants[i]].BirthDate.Value.Date.ToString("dd/MM/yyyy");
                }
            }
            cell = new PdfPCell(new Phrase(birthOfDate, fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
         
            cell = new PdfPCell(new Phrase("Visa Category", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            cell = await ChangePageLanguageToEnglish(viewModel.VisaCategoryId, fontGeneral);
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Appointment Date and Appointment Time", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            cell = new PdfPCell(new Phrase(apiResponse2.Data.slotDateAndTime.Date.ToString("dd/MM/yyyy") + " " + apiResponse2.Data.SlotTime, fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Appointment Number", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            var barcode = CreateBarcode(viewModel.EncryptedPreApplicationId.ToDecryptInt());
            iTextSharp.text.Image png = iTextSharp.text.Image.GetInstance(barcode);
            png.ScalePercent(50f);
            cell = new PdfPCell(png);
            cell.Colspan = 1;
            cell.FixedHeight = 70f;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            cell.VerticalAlignment = PdfPCell.ALIGN_MIDDLE;
            table.AddCell(cell);

            if (apiResponse2.Data.BranchId != 37)
            {
                cell = new PdfPCell(new Phrase(text_english, fontGeneral));
                cell.Colspan = 2;
                cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                table.AddCell(cell);
                cell = new PdfPCell(new Phrase(text_french, fontGeneral));
                cell.Colspan = 2;
                cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                table.AddCell(cell);

                var arabicFont = BaseFont.CreateFont(filePathArial, BaseFont.IDENTITY_H, true);
                var el = new Chunk();
                var f2 = new iTextSharp.text.Font(arabicFont, el.Font.Size, el.Font.Style, el.Font.Color);
                el.Font = f2;
                cell = new PdfPCell(new Phrase(11, text_arabic, el.Font));
                cell.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                cell.Colspan = 2;
                table.AddCell(cell);
            }
            else
            {
                cell = new PdfPCell(new Phrase(text_english_Askabat, fontGeneral));
                cell.Colspan = 2;
                cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                table.AddCell(cell);
                cell = new PdfPCell(new Phrase(text_turkoman_Askabat, fontGeneral));
                cell.Colspan = 2;
                cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                table.AddCell(cell);
                cell = new PdfPCell(new Phrase(text_russian_Askabat, fontGeneral));
                cell.Colspan = 2;
                cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                table.AddCell(cell);
            }

            document.Add(table);
            document.AddAuthor("Appointment Confirmation");
            document.Close();
            writer.Close();
            byte[] pdfFile = memoryStream.ToArray();

            memoryStream.Close();

            var apiRequest = new AddUpdatePreApplicationApiRequest()
            {
                PreApplicationId = viewModel.EncryptedPreApplicationId.ToDecryptInt(),
                ApplicantTypeId = viewModel.ApplicantTypeId,
                BranchApplicationCountryId = viewModel.BranchApplicationCountryId,
                VisaCategoryId = viewModel.VisaCategoryId,
                SlotId = viewModel.SlotId,
                VasTypeId = viewModel.VasTypeId,
                ApplicationTypeId = viewModel.ApplicationTypeId,
                SlotTypeId = viewModel.SlotTypeId,
                AgencyId = viewModel.IsAgency ? viewModel.AgencyId : null,
                Note = viewModel.Note,
                StatusId = viewModel.StatusId,
                UpdatedBy = UserSession.UserId,
                UpdatedAt = DateTime.Now,
                pdfFile = pdfFile,
                IsUpdated = true,
                Applicants = viewModel.Applicants.Where(p => p.ApplicationEnabled).Select(p => new AddUpdatePreApplicationApiRequest.PreApplicationApplicants()
                {
                    ApplicantId = !string.IsNullOrEmpty(p.EncryptedPreApplicationApplicantId) ? p.EncryptedPreApplicationApplicantId.ToDecryptInt() : new int(),
                    BirthDate = p.BirthDate.GetValueOrDefault(),
                    Email = p.Email,
                    GenderId = p.GenderId.GetValueOrDefault(),
                    Name = p.Name.Trim(),
                    NationalityId = p.NationalityId.GetValueOrDefault(),
                    PassportExpireDate = p.PassportExpireDate.GetValueOrDefault(),
                    PassportNumber = p.PassportNumber.Trim(),
                    PhoneNumber = $"{viewModel.CountryCallingCode}{p.PhoneNumber}",
                    Surname = p.Surname.Trim(),
                    SlotId = p.SlotId ?? 0,
                    RelationShipId = p.PassportNumber == viewModel.Applicants.First().PassportNumber ? 99 : p.RelationShipId,
                    InsuranceTypeIds = p.InsuranceTypeIds == null ? string.Empty : string.Join(",", from i in p.InsuranceTypeIds select i.ToString()),
                    SaveApplicantAsCustomer = p.SaveApplicantAsCustomer,
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UpdatePreApplicationApiResponse>>
                (apiRequest, ApiMethodName.Appointment.UpdatePreApplication, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Data = apiResponse.Data, Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        private byte[] CreateBarcode(int encryptedId)
        {
            return BarcodeClient.CreateBarcodeByteArray(encryptedId.ToString("0000000000000"), new SKColor(33, 37, 41), BarcodeClient.AMSWidth, BarcodeClient.AMSHeight);
        }

        [HttpGet]
        public async Task<IActionResult> ActivatePreApplication(string encryptedPreApplicationId)
        {
            int id = encryptedPreApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<UpdateApiResponse>>
                ($"{ApiMethodName.Appointment.ActivatePreApplication + id}/{UserSession.UserId}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        private async Task<PdfPCell> ChangePageLanguageToEnglish(int visaCategoryId, iTextSharp.text.Font font)
        {
            var originalCulture = Thread.CurrentThread.CurrentUICulture;
            Thread.CurrentThread.CurrentUICulture = new CultureInfo("en-US");
            try
            {
                var visaCategoryDataList = await GetCachedVisaCategories();
                var description = GetVisaCategoryNameFromId(visaCategoryId, visaCategoryDataList);
                return new PdfPCell(new Phrase(description, font));
            }
            finally
            {
                Thread.CurrentThread.CurrentUICulture = originalCulture;
            }
        }

        #endregion

        #region CustomerCard

        public IActionResult PartialSearchCustomerCard()
        {
            return PartialView("_SearchCustomerCard");
        }

        [HttpGet]
        public async Task<IActionResult> SearchCustomerCard(string passportNumber)
        {
            var apiRequest = new PaginatedCustomerCardsApiRequest
            {
                PassportNo = passportNumber,
                Name = string.Empty,
                Surname = string.Empty,
                Email = string.Empty,
                Pagination = new PaginationApiRequest
                {
                    Page = 1,
                    PageSize = 500,
                    OrderBy = "1",
                    SortDirection = System.ComponentModel.ListSortDirection.Ascending
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<CustomerCardsApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedCustomerCards, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            return Json(apiResponse.Data.Items.FirstOrDefault().CustomerCards.Select(x => new SelectListItem { Value = x.Id.ToEncrypt(), Text = $"{x.Name} {x.Surname}" }));
        }

        public async Task<IActionResult> GetCustomerCard(string encryptedCustomerCardId)
        {
            int id = encryptedCustomerCardId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<CustomerCardApiResponse>>
                ($"{ApiMethodName.Management.GetCustomerCardWithoutApplication + id}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            return Json(apiResponse);
        }

        [HttpGet]
        public async Task<IActionResult> GetPreApplicationNotExistPassportNumber(string passportNumber)
        {
            bool notExist = true;
            string[] pass = passportNumber.Split(",");
            foreach (var passNumber in pass)
            {
                if (passNumber != string.Empty)
                {
                    var apiResponse = await PortalHttpClientHelper
                    .GetAsync<ApiResponse<UpdateApiResponse>>
                    ($"{ApiMethodName.Appointment.GetPreApplicationNotExistPassportNumber + passNumber}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                    if (!apiResponse.Data.Result)
                    {
                        notExist = false;
                        break;
                    }

                }
            }
            return Json(notExist);
        }


        #endregion

        #region Get

        public async Task<IActionResult> PartialPreApplication(string encryptedPreApplicationId)
        {
            int id = encryptedPreApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<PreApplicationApiResponse>>
                (ApiMethodName.Appointment.GetPreApplication + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var countries = await CacheHelper.GetCountriesAsync();

            var visaCategories = await GetCachedVisaCategories();

            var viewModel = new PreApplicationViewModel
            {
                EncryptedPreApplicationId = apiResponse.Data.PreApplicationId.ToEncrypt(),
                BranchName = apiResponse.Data.BranchName,
                VisaCategoryId = apiResponse.Data.VisaCategoryId,
                VisaCategory = GetVisaCategoryNameFromId(apiResponse.Data.VisaCategoryId, visaCategories),
                ApplicantTypeId = apiResponse.Data.ApplicantTypeId,
                AppointmentTime = apiResponse.Data.AppointmentTime,
                ApplicationTypeId = apiResponse.Data.ApplicationTypeId,
                VasTypeId = apiResponse.Data.VasTypeId,
                Note = apiResponse.Data.Note,
                AgencyName = apiResponse.Data.AgencyName,
                StatusId = apiResponse.Data.StatusId,
                CreatedByNameSurname = apiResponse.Data.CreatedByNameSurname,
                UpdatedByNameSurname = apiResponse.Data.UpdatedByNameSurname,
                CreatedAt = apiResponse.Data.CreatedAt,
                UpdatedAt = apiResponse.Data.UpdatedAt,
                PreApplicationSlotId = apiResponse.Data.PreApplicationSlotId,
                ChannelTypeId = apiResponse.Data.ChannelTypeId,
                PaymentChannel = apiResponse.Data.PaymentChannel,
                PaymentStatus = apiResponse.Data.TotalPrice != null ? apiResponse.Data.PaymentStatus : string.Empty,
                TotalPrice = apiResponse.Data.TotalPrice,   
                Applicants = apiResponse.Data.Applicants.Select(p => new PreApplicationViewModel.ApplicantVieModel()
                {
                    EncryptedApplicantId = p.ApplicantId.ToEncrypt(),
                    Name = p.Name,
                    Surname = p.Surname,
                    PassportNumber = p.PassportNumber,
                    PassportExpireDate = p.PassportExpireDate.GetValueOrDefault(),
                    BirthDate = p.BirthDate.GetValueOrDefault(),
                    GenderId = p.GenderId.GetValueOrDefault(),
                    Nationality = countries?.Countries.FirstOrDefault(q => q.Id == p.NationalityId) == null ? "N/A" : countries?.Countries.FirstOrDefault(q => q.Id == p.NationalityId).Name,
                    Email = p.Email,
                    PhoneNumber = p.PhoneNumber,
                    SlotId = p.SlotId ?? 0,
                    RelationShipId = p.RelationShipId,
                    InsuranceType = (p.InsuranceTypeIds == null || p.InsuranceTypeIds == string.Empty) ? "-" : string.Join(",", from i in p.InsuranceTypeIds.Split(',').Select(Int32.Parse).ToList() select EnumHelper.GetEnumDescription(typeof(PreApplicationInsuranceType), i.ToString())),
                    AppointmentTime = p.AppointmentTime,
                    Documents = p.Documents.Select(q => new PreApplicationViewModel.ApplicantVieModel.Document()
                    {
                        EncryptedDocumentId = q.DocumentId.ToEncrypt(),
                        EncryptedFileId = q.FileId.ToEncrypt(),
                        FileTypeId = q.FileTypeId,
                        DisplayFileName = q.DisplayFileName,
                        DisplayUniqueFileName = q.DisplayUniqueFileName
                    }).ToList()
                }).ToList()
            };

            return PartialView("_PreApplication", viewModel);
        }

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedPreApplications([DataSourceRequest] DataSourceRequest request, FilterPreApplicationViewModel filterViewModel)
        {
            var hasAuthorizedRoleForB2CUpdateAppointment = false;
            var hasAuthorizedRoleForB2CDeleteAppointment = false;
            var hasAuthorizedRoleForAppointmentListShowEmptyOnInitial = false;

            var roleActions = await CacheHelper.GetRoleActionsAsync();

            hasAuthorizedRoleForB2CUpdateAppointment = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
                .Any(p => p.RoleActions.Any(q => q.Action.Method == "B2CUpdateAppointment" && q.Action.IsActive));

            hasAuthorizedRoleForB2CDeleteAppointment = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
                .Any(p => p.RoleActions.Any(q => q.Action.Method == "B2CDeleteAppointment" && q.Action.IsActive));

            hasAuthorizedRoleForAppointmentListShowEmptyOnInitial = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
                .Any(p => p.RoleActions.Any(q => q.Action.Method == "IsShowEmptyOnInitialAppointmentList" && q.Action.IsActive));

            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedPreApplicationApiRequest
            {
                BranchIds = UserSession.BranchIds,
                BranchId = filterViewModel.FilterBranchId,
                ApplicantTypeId = filterViewModel.FilterApplicantTypeId,
                Name = filterViewModel.FilterName,
                Surname = filterViewModel.FilterSurname,
                PassportNumber = filterViewModel.FilterPassportNumber,
                PhoneNumber = filterViewModel.FilterPhoneNumber,
                Email = filterViewModel.FilterEmail,
                IsDeleted = filterViewModel.FilterIsDeleted,
                IsUpdated = filterViewModel.FilterUpdated,
                IsWalkinAppointment = filterViewModel.FilterWalkin,
                StartDate = filterViewModel.FilterStartDate,
                EndDate = filterViewModel.FilterEndDate,
                StatusId = filterViewModel.FilterStatusId,
                VasTypeIds = filterViewModel.FilterVasTypeIds,
                AgencyId = filterViewModel.FilterAgencyId,
                MyCreations = filterViewModel.FilterMyCreations,
                CreatedToday = filterViewModel.FilterCreatedToday,
                UserId = UserSession.UserId,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            if (hasAuthorizedRoleForAppointmentListShowEmptyOnInitial)
            {
                var propListToAllow = new string[] { "PassportNumber", "Name", "Surname", "Email","PhoneNumber" };

                var allowCheck = HasValidProperties(apiRequest, propListToAllow);

                if(!allowCheck)
                    return Json(new DataSourceResult { Data = new List<PreApplicationApplicantViewModel>() , Total = 0 });
            }

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedPreApplicationApiResponse>>>
                (apiRequest, ApiMethodName.Appointment.GetPaginatedPreApplications, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(new DataSourceResult());

            var paginatedData = new List<PreApplicationApplicantViewModel>();

            var visaCategories = await GetCachedVisaCategories();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().PreApplications
                    .Select(p => new PreApplicationApplicantViewModel
                    {
                        EncryptedPreApplicationId = p.PreApplicationId.ToEncrypt(),
                        EncryptedPreApplicationApplicantId = p.PreApplicationApplicantId.ToEncrypt(),
                        ApplicationTypeId = p.ApplicationTypeId,
                        ApplicantTypeId = p.ApplicantTypeId,
                        PassportNumber = p.PassportNumber,
                        Name = p.Name,
                        Surname = p.Surname,
                        VisaCategoryId = p.VisaCategoryId,
                        VisaCategory = GetVisaCategoryNameFromId(p.VisaCategoryId, visaCategories),
                        PreApplicationTime = p.Slot.DateTime == new DateTime(2021,01,01) ? "" : p.Slot.DateTime.ToString("dd/MM/yyyy HH:mm"),
                        ShowGridOperations = filterViewModel.FilterIsDeleted == false,
                        SlotTypeId = p.SlotTypeId,
                        VasTypeId = p.VasTypeId,
                        IsExistNote = p.IsExistNote,
                        StatusId = p.StatusId,
                        AgencyId = p.AgencyId,
                        AgencyName = p.AgencyName,
                        PhoneNumber = p.PhoneNumber,
                        BranchName = p.BranchName,
                        ChannelType = p.ChannelType,
                        AuthorizedForDeleteB2CAppointment = hasAuthorizedRoleForB2CDeleteAppointment,
                        AuthorizedForUpdateB2CAppointment = hasAuthorizedRoleForB2CUpdateAppointment,
                        IsUpdateEnabled = p.GeneratedTokenId != null,
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        private static bool HasValidProperties(PaginatedPreApplicationApiRequest request, string[] propListToAllow)
        {
            if (request == null)
                return false;

            var type = request.GetType();

            foreach (var propName in propListToAllow)
            {
                var property = type.GetProperty(propName);
                if (property == null) continue;
                var value = property.GetValue(request);
                if (value != null && !string.IsNullOrEmpty(value.ToString()))
                {
                    return true;
                }
            }

            return false;
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeletePreApplication(string encryptedPreApplicationId, string reason)
        {
            int id = encryptedPreApplicationId.ToDecryptInt();

            var apiRequest = new DeletePreApplicationApiRequest
            {
                Id = id,
                UserId = UserSession.UserId,
                Reason = reason
            };

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (apiRequest,$"{ApiMethodName.Appointment.DeletePreApplication}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpDelete]
        public async Task<IActionResult> DeletePreApplicationApplicant(string EncryptedPreApplicationApplicantId, string reason)
        {
            int id = EncryptedPreApplicationApplicantId.ToDecryptInt();

            var apiRequest = new DeletePreApplicationApplicantApiRequest
            {
                Id = id,
                UserId = UserSession.UserId,
                Reason = reason
            };

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (apiRequest, $"{ApiMethodName.Appointment.DeletePreApplicationApplicant}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region BranchApplicationCountryFiles

        public async Task<IActionResult> PartialApplicationFiles(int branchApplicationCountryId)
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BranchApplicationCountryFileWithNoteApiResponse>>
                (ApiMethodName.Management.GetAllBranchApplicationCountryFiles + branchApplicationCountryId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var viewModel = new BranchApplicationCountryFileWithNoteViewModel()
            {
                Note = apiResponse.Data.Note,
                BranchApplicationCountryFiles = apiResponse.Data.BranchApplicationCountryFiles.Select(p => new BranchApplicationCountryFileWithNoteViewModel.BranchApplicationCountryFile()
                {
                    EncryptedId = p.Id.ToEncrypt(),
                    EncryptedBranchApplicationCountryFileId = p.BranchApplicationCountryId.ToEncrypt(),
                    FileTypeId = p.FileTypeId,
                    EncryptedDocumentId = p.DocumentId.ToEncrypt()
                }).ToList()
            };

            if (apiResponse.Data.BranchApplicationCountryFiles.Select(p => p.DocumentId).Any())
            {
                var documentsApiRequest = new DocumentsApiRequest
                {
                    Ids = viewModel.BranchApplicationCountryFiles.Select(p => p.EncryptedDocumentId.ToDecryptInt()).ToList(),
                    IsFileContentIncluded = false
                };

                var getDocumentsApiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<DocumentsApiResponse>>
                    (documentsApiRequest, ApiMethodName.DocumentManagement.GetDocuments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                if (!await getDocumentsApiResponse.Validate(out ResultModel getDocumentsResult).ConfigureAwait(false))
                    return Json(getDocumentsResult);

                foreach (var item in viewModel.BranchApplicationCountryFiles)
                {
                    var document = getDocumentsApiResponse.Data.Documents.FirstOrDefault(p => p.Id == item.EncryptedDocumentId.ToDecryptInt());

                    if (document != null)
                    {
                        item.OriginalFileName = document.OriginalFileName;
                        item.UniqueFileName = document.UniqueFileName;
                        item.FileExtension = document.FileExtension;
                    }
                }
            }

            return PartialView("_RequiredDocuments", viewModel);
        }

        #endregion

        #region Report

        [HttpPost]
        public async Task<FileContentResult> GetPreApplicationReport(FilterPreApplicationViewModel filterViewModel)
        {
            var reportName = filterViewModel.FilterIsDeleted 
                ? SiteResources.CancelledReport.ToTitleCase() 
                : filterViewModel.FilterUpdated ? SiteResources.UpdatedReport.ToTitleCase() : SiteResources.AppointmentReport.ToTitleCase();

            var apiRequest = new GetPreApplicationReportApiRequest
            {
                BranchIds = UserSession.BranchIds,
                BranchId = filterViewModel.FilterBranchId,
                ApplicantTypeId = filterViewModel.FilterApplicantTypeId,
                VasTypeIds = filterViewModel.FilterVasTypeIds,
                AgencyId = filterViewModel.FilterAgencyId,
                Name = filterViewModel.FilterName,
                Surname = filterViewModel.FilterSurname,
                PassportNumber = filterViewModel.FilterPassportNumber,
                PhoneNumber = filterViewModel.FilterPhoneNumber,
                Email = filterViewModel.FilterEmail,
                IsDeleted = filterViewModel.FilterIsDeleted,
                IsUpdated = filterViewModel.FilterUpdated,
                MyCreations = filterViewModel.FilterMyCreations,
                CreatedToday = filterViewModel.FilterCreatedToday,
                StartDate = filterViewModel.FilterStartDate,
                EndDate = filterViewModel.FilterEndDate,
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PreApplicationReportApiResponse>>
                (apiRequest, ApiMethodName.Appointment.GetPreApplicationReport, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            using (var workbook = new XLWorkbook())
            {
                var worksheet = workbook.Worksheets.Add(reportName);
                var currentRow = 1;
                int orderNumber = 0;
                worksheet.Cell(currentRow, 1).Value = SiteResources.OrderNumber.ToTitleCase();
                worksheet.Cell(currentRow, 2).Value = SiteResources.CountryName.ToTitleCase();
                worksheet.Cell(currentRow, 3).Value = SiteResources.BranchName.ToTitleCase();
                worksheet.Cell(currentRow, 4).Value = SiteResources.ApplicationNumber.ToTitleCase();
                worksheet.Cell(currentRow, 5).Value = SiteResources.AppointmentNumber.ToTitleCase();
                worksheet.Cell(currentRow, 6).Value = SiteResources.PassportNumber.ToTitleCase();
                worksheet.Cell(currentRow, 7).Value = SiteResources.NameSurname.ToTitleCase();
                worksheet.Cell(currentRow, 8).Value = SiteResources.Age.ToTitleCase();
                worksheet.Cell(currentRow, 9).Value = SiteResources.PhoneNumber.ToTitleCase();
                worksheet.Cell(currentRow, 10).Value = SiteResources.Email.ToTitleCase();
                worksheet.Cell(currentRow, 11).Value = SiteResources.ApplicationType.ToTitleCase();
                worksheet.Cell(currentRow, 12).Value = SiteResources.ApplicantType.ToTitleCase();
                worksheet.Cell(currentRow, 13).Value = SiteResources.VisaCategory.ToTitleCase();
                worksheet.Cell(currentRow, 14).Value = SiteResources.VasType.ToTitleCase();
                worksheet.Cell(currentRow, 15).Value = SiteResources.InsuranceType.ToTitleCase();
                worksheet.Cell(currentRow, 16).Value = SiteResources.AppointmentDate.ToTitleCase();
                worksheet.Cell(currentRow, 17).Value = SiteResources.AppointmentHour.ToTitleCase();
                if (!filterViewModel.FilterUpdated)
                {
                    worksheet.Cell(currentRow, 18).Value = SiteResources.Note.ToTitleCase();
                    worksheet.Cell(currentRow, 19).Value = SiteResources.CreatedBy.ToTitleCase();
                }
                if (filterViewModel.FilterUpdated)
                {
                    worksheet.Cell(currentRow, 18).Value = SiteResources.LastUpdatedAt.ToTitleCase();
                    worksheet.Cell(currentRow, 19).Value = SiteResources.UpdatedInformation.ToTitleCase();
                    worksheet.Cell(currentRow, 20).Value = SiteResources.CreatedBy.ToTitleCase();
                    worksheet.Cell(currentRow, 21).Value = SiteResources.UpdateBy.ToTitleCase();

                    var rangeTableHeader = worksheet.Range(worksheet.Cell(currentRow, 1), worksheet.Cell(currentRow, 23));
                    rangeTableHeader.Style.Font.SetBold();
                }
                else
                {
                    if (filterViewModel.FilterIsDeleted)
                    {
                        worksheet.Cell(currentRow, 20).Value = SiteResources.DeleteReason.ToTitleCase();
                        var rangeTableHeader = worksheet.Range(worksheet.Cell(currentRow, 1), worksheet.Cell(currentRow, 20));
                        rangeTableHeader.Style.Font.SetBold();
                    }
                    else
                    {
                        var rangeTableHeader = worksheet.Range(worksheet.Cell(currentRow, 1), worksheet.Cell(currentRow, 19));
                        rangeTableHeader.Style.Font.SetBold();
                    }
                }
                currentRow++;
                DateTime? lastUpdateDate = DateTime.MinValue;
                int previousPreApplicationApplicantId = 0;

                var visaCategories = await GetCachedVisaCategories();

                for (int i = 0; i < apiResponse.Data.Appointments.Count(); i++)
                {
                    StringBuilder bld = new StringBuilder();
                    string insuranceTypeIds = "";
                    var data = apiResponse.Data.Appointments.ElementAt(i);
                    if (previousPreApplicationApplicantId != data.PreApplicationApplicantId)
                    {
                        previousPreApplicationApplicantId = data.PreApplicationApplicantId;
                        orderNumber += 1;
                        worksheet.Cell(currentRow, 1).Value = orderNumber;
                        worksheet.Cell(currentRow, 2).Value = data.CountryName;
                        worksheet.Cell(currentRow, 3).Value = data.BranchName;
                        worksheet.Cell(currentRow, 4).Value = "'" + data.PreApplicationApplicantId.ToApplicationNumber();
                        worksheet.Cell(currentRow, 4).DataType = XLDataType.Text;
                        worksheet.Cell(currentRow, 5).Value = "'" + data.PreApplicationId.ToApplicationNumber();
                        worksheet.Cell(currentRow, 5).DataType = XLDataType.Text;
                        worksheet.Cell(currentRow, 6).Value = "'" + data.PassportNumber;
                        worksheet.Cell(currentRow, 6).DataType = XLDataType.Text;
                        worksheet.Cell(currentRow, 7).Value = $"{data.Name} {data.Surname}";
                        worksheet.Cell(currentRow, 8).Value = data.Age == -1 ? null : data.Age;
                        worksheet.Cell(currentRow, 9).Value = "'" + data.PhoneNumber;
                        worksheet.Cell(currentRow, 9).DataType = XLDataType.Text;
                        worksheet.Cell(currentRow, 10).Value = data.Email;
                        worksheet.Cell(currentRow, 11).Value = ((ApplicationType)data.ApplicationTypeId).ToDescription();
                        worksheet.Cell(currentRow, 12).Value = ((ApplicantType)data.ApplicantTypeId).ToDescription();
                        worksheet.Cell(currentRow, 13).Value = GetVisaCategoryNameFromId(data.VisaCategoryId, visaCategories);
                        worksheet.Cell(currentRow, 14).Value = ((VasType)data.VasTypeId).ToDescription();
                        if (data.InsuranceTypeIds.Count > 0)
                        {
                            for (int j = 0; j < data.InsuranceTypeIds.Count; j++)
                            {
                                if (j != data.InsuranceTypeIds.Count - 1)
                                    bld.AppendLine(EnumHelper.GetEnumDescription(typeof(PreApplicationInsuranceType), data.InsuranceTypeIds[j].ToString()));
                                else
                                    bld.Append(EnumHelper.GetEnumDescription(typeof(PreApplicationInsuranceType), data.InsuranceTypeIds[j].ToString()));
                            }
                            insuranceTypeIds = bld.ToString();
                        }
                        else
                        {
                            insuranceTypeIds = "-";
                        }
                        worksheet.Cell(currentRow, 15).Value = insuranceTypeIds;
                        worksheet.Cell(currentRow, 16).Value = data.AppointmentDate.DateTime;
                        worksheet.Cell(currentRow, 16).Style.DateFormat.Format = SiteResources.DatePickerFormatView;
                        worksheet.Cell(currentRow, 17).Value = data.AppointmentDate.TimeOfDay;
                        worksheet.Cell(currentRow, 17).Style.DateFormat.Format = "HH:mm";
                        if (!filterViewModel.FilterUpdated)
                        {
                            worksheet.Cell(currentRow, 18).Value = data.Note;
                            worksheet.Cell(currentRow, 19).Value = data.CreatedByNameAndSurname;

                            if (filterViewModel.FilterIsDeleted)
                            {
                                worksheet.Cell(currentRow, 20).Value = data.DeleteReason;
                            }
                            currentRow++;
                        }
                        lastUpdateDate = DateTime.MinValue;
                    }
                    if (filterViewModel.FilterUpdated && worksheet.Cell(currentRow - 1, 18).Value.ToString() != worksheet.Cell(currentRow, 18).Value.ToString())
                    {
                        if (data.LastUpdatedPreApplicationHistoryDate != null)
                        {
                            worksheet.Cell(currentRow, 18).Value = data.LastUpdatedPreApplicationHistoryDate;
                            lastUpdateDate = data.LastUpdatedPreApplicationHistoryDate;
                            if (data.LastUpdatedPreApplicationHistoryDate > lastUpdateDate)
                            {
                                worksheet.Cell(currentRow, 18).Value = data.LastUpdatedPreApplicationHistoryDate;
                                lastUpdateDate = data.LastUpdatedPreApplicationHistoryDate;
                            }

                        }
                        if (data.LastUpdatedPreApplicationApplicantHistoryDate != null)
                        {
                            if (lastUpdateDate == DateTime.MinValue || data.LastUpdatedPreApplicationApplicantHistoryDate > lastUpdateDate)
                            {
                                worksheet.Cell(currentRow, 18).Value = data.LastUpdatedPreApplicationApplicantHistoryDate;
                                lastUpdateDate = data.LastUpdatedPreApplicationApplicantHistoryDate;
                            }
                            else
                            {
                                worksheet.Cell(currentRow, 18).Value = lastUpdateDate;
                            }
                        }
                        if (data.UpdatedInformationPreApplicationHistoryPropertyName != null)
                        {
                            worksheet.Cell(currentRow, 19).Value = GetPreApplicationHistoryPropertyDetail(data.UpdatedInformationPreApplicationHistoryPropertyName) + " = " + GetPreApplicationHistoryValueDetail(data.UpdatedInformationPreApplicationHistoryPropertyName, data.UpdatedInformationPreApplicationHistoryPreviousValue, visaCategories)
                            + " => " + GetPreApplicationHistoryValueDetail(data.UpdatedInformationPreApplicationHistoryPropertyName, data.UpdatedInformationPreApplicationHistoryCurrentValue, visaCategories);
                            worksheet.Cell(currentRow, 20).Value = data.CreatedByNameAndSurname;
                            if (data.UpdatedInformationPreApplicationHistoryUpdateBy != null)
                            {
                                worksheet.Cell(currentRow, 21).Value = data.UpdatedInformationPreApplicationHistoryUpdateBy;
                            }
                        }

                        if (data.UpdatedInformationPreApplicationApplicantHistoryPropertyName != null)
                        {
                            worksheet.Cell(currentRow, 19).Value = GetPreApplicationHistoryPropertyDetail(data.UpdatedInformationPreApplicationApplicantHistoryPropertyName) + " = " + GetPreApplicationHistoryValueDetail(data.UpdatedInformationPreApplicationApplicantHistoryPropertyName, data.UpdatedInformationPreApplicationApplicantHistoryPreviousValue, visaCategories)
                            + " => " + GetPreApplicationHistoryValueDetail(data.UpdatedInformationPreApplicationApplicantHistoryPropertyName, data.UpdatedInformationPreApplicationApplicantHistoryCurrentValue, visaCategories);
                            worksheet.Cell(currentRow, 20).Value = data.CreatedByNameAndSurname;
                            if (data.UpdatedInformationPreApplicationApplicantHistoryUpdateBy != null)
                            {
                                worksheet.Cell(currentRow, 21).Value = data.UpdatedInformationPreApplicationApplicantHistoryUpdateBy;
                            }
                        }
                        currentRow++;
                    }
                }
                var range = 20;
                if (filterViewModel.FilterUpdated)
                    range = 21;
                var rangeTable = worksheet.Range(worksheet.Cell(1, 1), worksheet.Cell(currentRow - 1, range));
                rangeTable.Style.Border.SetTopBorder(XLBorderStyleValues.Thin);
                rangeTable.Style.Border.SetBottomBorder(XLBorderStyleValues.Thin);
                rangeTable.Style.Border.SetLeftBorder(XLBorderStyleValues.Thin);
                rangeTable.Style.Border.SetRightBorder(XLBorderStyleValues.Thin);
                rangeTable.Style.Border.SetInsideBorder(XLBorderStyleValues.Thin);
                rangeTable.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                var rangeAlignment = worksheet.Range(worksheet.Cell(1, 1), worksheet.Cell(currentRow - 1, range));
                rangeAlignment.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);

                for (int i = 1; i <= range; i++)
                {
                    worksheet.Column(i).AdjustToContents();
                }

                worksheet.Column(8).Width = 5;
                worksheet.Column(16).Width = 16;
                if (filterViewModel.FilterUpdated)
                    worksheet.Column(19).Width = 40;

                using (var stream = new MemoryStream())
                {
                    workbook.SaveAs(stream);
                    var content = stream.ToArray();
                    return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{reportName} - {DateTime.UtcNow.ToString("ddMMyyyy")}.xlsx");
                }
            }
        }

        private string GetPreApplicationHistoryPropertyDetail(string propertyName)
        {
            var result = propertyName;

            switch (propertyName)
            {
                case "VisaCategoryId":
                    result = SiteResources.VisaCategory;
                    break;
                case "ApplicantTypeId":
                    result = SiteResources.ApplicantType;
                    break;
                case "SlotId":
                    result = SiteResources.Slot;
                    break;
                case "ApplicationTypeId":
                    result = SiteResources.ApplicationType;
                    break;
                case "AgencyId":
                    result = SiteResources.Agency;
                    break;
                case "SlotTypeId":
                    result = SiteResources.SlotType;
                    break;
                case "VasTypeId":
                    result = SiteResources.VasType;
                    break;
                case "Note":
                    result = SiteResources.Notes;
                    break;
                case "StatusId":
                    result = SiteResources.Status;
                    break;
                //PreApplicationApplicant
                case "PassportNumber":
                    result = SiteResources.PassportNumber;
                    break;
                case "PassportExpireDate":
                    result = SiteResources.PassportExpireDate;
                    break;
                case "Name":
                    result = SiteResources.Name;
                    break;
                case "Surname":
                    result = SiteResources.Surname;
                    break;
                case "BirthDate":
                    result = SiteResources.BirthDate;
                    break;
                case "GenderId":
                    result = SiteResources.Gender;
                    break;
                case "NationalityId":
                    result = SiteResources.Nationality;
                    break;
                case "Email":
                    result = SiteResources.Email;
                    break;
                case "PhoneNumber":
                    result = SiteResources.PhoneNumber;
                    break;
                case "PreApplicationId":
                    result = SiteResources.PreApplication;
                    break;
                case "CustomerId":
                    result = SiteResources.Customer;
                    break;
                //PreApplicationApplicantFile
                case "PreApplicationApplicantId":
                    result = SiteResources.PreApplicationApplicant;
                    break;
                case "FileTypeId":
                    result = SiteResources.FileType;
                    break;
                case "DocumentId":
                    result = SiteResources.Document;
                    break;
            }
            return result;
        }

        private string GetPreApplicationHistoryValueDetail(string propertyName, string value, List<BranchApplicationCountryVisaCategoriesApiResponse.VisaCategory> visaCategories)
        {
            var result = value;
            switch (propertyName)
            {
                case "VisaCategoryId":
                    result = GetVisaCategoryNameFromId(value.ToInt(), visaCategories);
                    break;
                case "ApplicantTypeId":
                    result = EnumHelper.GetEnumDescription(typeof(ApplicantType), value);
                    break;

                case "ApplicationTypeId":
                    result = EnumHelper.GetEnumDescription(typeof(ApplicationType), value);
                    break;
                case "SlotTypeId":
                    result = EnumHelper.GetEnumDescription(typeof(SlotType), value);
                    break;
                case "VasTypeId":
                    result = EnumHelper.GetEnumDescription(typeof(VasType), value);
                    break;
                case "StatusId":
                    result = EnumHelper.GetEnumDescription(typeof(ApplicationStatus), value);
                    break;
                //PreApplicationApplicant
                case "GenderId":
                    result = EnumHelper.GetEnumDescription(typeof(Gender), value);
                    break;
                default:
                    break;
            }
            if (result == "")
                result = "-";
            return result;
        }

        [HttpPost]
        public async Task<FileContentResult> GetProcedureControlReport(FilterPreApplicationViewModel filterViewModel)
        {
            var reportName = filterViewModel.FilterIsDeleted ? SiteResources.CancelledReport.ToTitleCase() : SiteResources.ProcedureControlReport.ToTitleCase();
            var apiRequest = new GetProcedureControlReportApiRequest
            {
                BranchIds = UserSession.BranchIds,
                BranchId = filterViewModel.FilterBranchId,
                ApplicantTypeId = filterViewModel.FilterApplicantTypeId,
                VasTypeIds = filterViewModel.FilterVasTypeIds,
                AgencyId = filterViewModel.FilterAgencyId,
                Name = filterViewModel.FilterName,
                Surname = filterViewModel.FilterSurname,
                PassportNumber = filterViewModel.FilterPassportNumber,
                PhoneNumber = filterViewModel.FilterPhoneNumber,
                Email = filterViewModel.FilterEmail,
                IsDeleted = filterViewModel.FilterIsDeleted,
                StartDate = filterViewModel.FilterStartDate,
                EndDate = filterViewModel.FilterEndDate,
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<ProcedureControlReportApiResponse>>
                (apiRequest, ApiMethodName.Appointment.GetProcedureControlReport, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            using (var workbook = new XLWorkbook())
            {
                for (int i = 0; i < apiResponse.Data.Appointments.Count() + 1; i++)
                {
                    var sheetName = i == 0 ? SiteResources.BySamePassportNumber.ToTitleCase() : SiteResources.BySamePhoneNumber.ToTitleCase();
                    var worksheet = workbook.Worksheets.Add(sheetName);
                    var currentRow = 1;

                    worksheet.Cell(currentRow, 1).Value = SiteResources.OrderNumber.ToTitleCase();
                    worksheet.Cell(currentRow, 2).Value = SiteResources.CountryName.ToTitleCase();
                    worksheet.Cell(currentRow, 3).Value = SiteResources.BranchName.ToTitleCase();
                    worksheet.Cell(currentRow, 4).Value = SiteResources.ApplicationNumber.ToTitleCase();
                    worksheet.Cell(currentRow, 5).Value = SiteResources.AppointmentNumber.ToTitleCase();
                    worksheet.Cell(currentRow, 6).Value = SiteResources.PassportNumber.ToTitleCase();
                    worksheet.Cell(currentRow, 7).Value = SiteResources.NameSurname.ToTitleCase();
                    worksheet.Cell(currentRow, 8).Value = SiteResources.Age.ToTitleCase();
                    worksheet.Cell(currentRow, 9).Value = SiteResources.PhoneNumber.ToTitleCase();
                    worksheet.Cell(currentRow, 10).Value = SiteResources.Email.ToTitleCase();
                    worksheet.Cell(currentRow, 11).Value = SiteResources.ApplicationType.ToTitleCase();
                    worksheet.Cell(currentRow, 12).Value = SiteResources.ApplicantType.ToTitleCase();
                    worksheet.Cell(currentRow, 13).Value = SiteResources.VisaCategory.ToTitleCase();
                    worksheet.Cell(currentRow, 14).Value = SiteResources.VasType.ToTitleCase();
                    worksheet.Cell(currentRow, 15).Value = SiteResources.CreatedBy.ToTitleCase();
                    worksheet.Cell(currentRow, 16).Value = SiteResources.UpdatedBy.ToTitleCase();
                    worksheet.Cell(currentRow, 17).Value = SiteResources.AppointmentDate.ToTitleCase();
                    worksheet.Cell(currentRow, 18).Value = SiteResources.AppointmentHour.ToTitleCase();
                    worksheet.Cell(currentRow, 19).Value = SiteResources.Note.ToTitleCase();
                    var rangeTableHeader = worksheet.Range(worksheet.Cell(currentRow, 1), worksheet.Cell(currentRow, 18));
                    rangeTableHeader.Style.Font.SetBold();
                    currentRow++;

                    var visaCategories = await GetCachedVisaCategories();
                    if (sheetName == SiteResources.BySamePassportNumber.ToTitleCase())
                    {
                        for (int k = 0; k < apiResponse.Data.Appointments.First().AppointmentsGroupedByPassport.Count(); k++)
                        {
                            var data = apiResponse.Data.Appointments.First().AppointmentsGroupedByPassport.ElementAt(k);
                            worksheet.Cell(currentRow, 1).Value = k + 1;
                            worksheet.Cell(currentRow, 2).Value = data.CountryName;
                            worksheet.Cell(currentRow, 3).Value = data.BranchName;
                            worksheet.Cell(currentRow, 4).Value = "'" + data.PreApplicationApplicantId.ToApplicationNumber();
                            worksheet.Cell(currentRow, 4).DataType = XLDataType.Text;
                            worksheet.Cell(currentRow, 5).Value = "'" + data.PreApplicationId.ToApplicationNumber();
                            worksheet.Cell(currentRow, 5).DataType = XLDataType.Text;
                            worksheet.Cell(currentRow, 6).Value = data.PassportNumber;
                            worksheet.Cell(currentRow, 7).Value = $"{data.Name} {data.Surname}";
                            worksheet.Cell(currentRow, 8).Value = data.Age == -1 ? null : data.Age;
                            worksheet.Cell(currentRow, 9).Value = "'" + data.PhoneNumber;
                            worksheet.Cell(currentRow, 9).DataType = XLDataType.Text;
                            worksheet.Cell(currentRow, 10).Value = data.Email;
                            worksheet.Cell(currentRow, 11).Value = ((ApplicationType)data.ApplicationTypeId).ToDescription();
                            worksheet.Cell(currentRow, 12).Value = ((ApplicantType)data.ApplicantTypeId).ToDescription();
                            worksheet.Cell(currentRow, 13).Value = GetVisaCategoryNameFromId(data.VisaCategoryId, visaCategories);
                            worksheet.Cell(currentRow, 14).Value = ((VasType)data.VasTypeId).ToDescription();
                            worksheet.Cell(currentRow, 15).Value = data.CreatedByNameAndSurname;
                            worksheet.Cell(currentRow, 16).Value = data.UpdatedByNameAndSurname;
                            worksheet.Cell(currentRow, 17).Value = data.AppointmentDate.DateTime;
                            worksheet.Cell(currentRow, 17).Style.DateFormat.Format = SiteResources.DatePickerFormatView;
                            worksheet.Cell(currentRow, 18).Value = data.AppointmentDate.TimeOfDay;
                            worksheet.Cell(currentRow, 18).Style.DateFormat.Format = "HH:mm";
                            worksheet.Cell(currentRow, 19).Value = data.Note;

                            currentRow++;
                        }


                        var rangeTable = worksheet.Range(worksheet.Cell(1, 1), worksheet.Cell(currentRow - 1, 19));
                        rangeTable.Style.Border.SetTopBorder(XLBorderStyleValues.Thin);
                        rangeTable.Style.Border.SetBottomBorder(XLBorderStyleValues.Thin);
                        rangeTable.Style.Border.SetLeftBorder(XLBorderStyleValues.Thin);
                        rangeTable.Style.Border.SetRightBorder(XLBorderStyleValues.Thin);
                        rangeTable.Style.Border.SetInsideBorder(XLBorderStyleValues.Thin);
                        rangeTable.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                        var rangeAlignment = worksheet.Range(worksheet.Cell(1, 1), worksheet.Cell(currentRow - 1, 19));
                        rangeAlignment.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);

                        for (int j = 1; j <= 19; j++)
                        {
                            worksheet.Column(j).AdjustToContents();
                        }

                        worksheet.Column(8).Width = 5;
                        worksheet.Column(15).Width = 16;
                    }
                    else
                    {
                        for (int s = 0; s < apiResponse.Data.Appointments.First().AppointmentsGroupedByPhone.Count(); s++)
                        {
                            var data = apiResponse.Data.Appointments.First().AppointmentsGroupedByPhone.ElementAt(s);
                            worksheet.Cell(currentRow, 1).Value = s + 1;
                            worksheet.Cell(currentRow, 2).Value = data.CountryName;
                            worksheet.Cell(currentRow, 3).Value = data.BranchName;
                            worksheet.Cell(currentRow, 4).Value = "'" + data.PreApplicationApplicantId.ToApplicationNumber();
                            worksheet.Cell(currentRow, 4).DataType = XLDataType.Text;
                            worksheet.Cell(currentRow, 5).Value = "'" + data.PreApplicationId.ToApplicationNumber();
                            worksheet.Cell(currentRow, 5).DataType = XLDataType.Text;
                            worksheet.Cell(currentRow, 6).Value = data.PassportNumber;
                            worksheet.Cell(currentRow, 7).Value = $"{data.Name} {data.Surname}";
                            worksheet.Cell(currentRow, 8).Value = data.Age == -1 ? null : data.Age;
                            worksheet.Cell(currentRow, 9).Value = "'" + data.PhoneNumber;
                            worksheet.Cell(currentRow, 9).DataType = XLDataType.Text;
                            worksheet.Cell(currentRow, 10).Value = data.Email;
                            worksheet.Cell(currentRow, 11).Value = ((ApplicationType)data.ApplicationTypeId).ToDescription();
                            worksheet.Cell(currentRow, 12).Value = ((ApplicantType)data.ApplicantTypeId).ToDescription();
                            worksheet.Cell(currentRow, 13).Value = GetVisaCategoryNameFromId(data.VisaCategoryId, visaCategories);
                            worksheet.Cell(currentRow, 14).Value = ((VasType)data.VasTypeId).ToDescription();
                            worksheet.Cell(currentRow, 15).Value = data.CreatedByNameAndSurname;
                            worksheet.Cell(currentRow, 16).Value = data.UpdatedByNameAndSurname;
                            worksheet.Cell(currentRow, 17).Value = data.AppointmentDate.DateTime;
                            worksheet.Cell(currentRow, 17).Style.DateFormat.Format = SiteResources.DatePickerFormatView;
                            worksheet.Cell(currentRow, 18).Value = data.AppointmentDate.TimeOfDay;
                            worksheet.Cell(currentRow, 18).Style.DateFormat.Format = "HH:mm";
                            worksheet.Cell(currentRow, 19).Value = data.Note;

                            currentRow++;
                        }

                        var rangeTable = worksheet.Range(worksheet.Cell(1, 1), worksheet.Cell(currentRow - 1, 19));
                        rangeTable.Style.Border.SetTopBorder(XLBorderStyleValues.Thin);
                        rangeTable.Style.Border.SetBottomBorder(XLBorderStyleValues.Thin);
                        rangeTable.Style.Border.SetLeftBorder(XLBorderStyleValues.Thin);
                        rangeTable.Style.Border.SetRightBorder(XLBorderStyleValues.Thin);
                        rangeTable.Style.Border.SetInsideBorder(XLBorderStyleValues.Thin);
                        rangeTable.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                        var rangeAlignment = worksheet.Range(worksheet.Cell(1, 1), worksheet.Cell(currentRow - 1, 19));
                        rangeAlignment.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);

                        for (int r = 1; r <= 19; r++)
                        {
                            worksheet.Column(r).AdjustToContents();
                        }

                        worksheet.Column(8).Width = 5;
                        worksheet.Column(15).Width = 16;
                    }
                }
                using (var stream = new MemoryStream())
                {
                    workbook.SaveAs(stream);
                    var content = stream.ToArray();
                    return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{reportName} - {DateTime.UtcNow.ToString("ddMMyyyy")}.xlsx");
                }
            }
        }

        #endregion

        public async Task<IActionResult> DownloadFile(string encryptedDocumentId)
        {
            var documentsApiRequest = new DocumentsApiRequest
            {
                Ids = new List<int> { encryptedDocumentId.ToDecryptInt() },
                IsFileContentIncluded = true
            };

            var getDocumentsApiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<DocumentsApiResponse>>
                (documentsApiRequest, ApiMethodName.DocumentManagement.GetDocuments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await getDocumentsApiResponse.Validate(out ResultModel getDocumentsResult).ConfigureAwait(false))
                return Json(null);

            var document = getDocumentsApiResponse.Data.Documents.FirstOrDefault();

            if (document == null || document.FileContent == null)
                return Json(null);

            return File(document.FileContent, "application/octet-stream", document.UniqueFileNameWithExtension);
        }

        public async Task<IActionResult> IsPreApplicationAvailableByPassportNumber(string passportNumber, string branchId, string slotDate)
        {
            DateTime appointmentDate;
            if (!DateTime.TryParseExact(slotDate, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out appointmentDate))
                return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurred });

            var cacheItemBranchApplicationStatus = await CacheHelper.GetBranchApplicationStatusAsync();
            var cacheItemBranch = await CacheHelper.GetBranchesAsync();
            var branchApplicationStatuses = cacheItemBranchApplicationStatus.BranchApplicationStatuses.Where(p => p.BranchId == Convert.ToInt32(branchId)).ToList();

            if (branchApplicationStatuses == null || !branchApplicationStatuses.Any())
                return Json(new { Result = false, ErrorMessage = $"{SiteResources.ErrorOccurredInApplicationStatusCheck} ({SiteResources.BranchApplicationStatusItemNotFound})" });

            string[] pass = passportNumber.Split(",");
            foreach (var passNumber in pass)
            {
                if (passNumber != string.Empty)
                {
                    var apiRequest = new PaginatedApplicationsApiRequest
                    {
                        PassportNumber = passNumber,
                        IncludeApplicationStatusHistories = true
                    };

                    var apiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<PaginationApiResponse<ApplicationsApiResponse>>>
                    (apiRequest, ApiMethodName.Appointment.GetPaginatedApplicationsForRejectionControl, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                    if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                        return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurred });

                    if (apiResponse.Data == null || !apiResponse.Data.Items.Any())
                        return Json(new { Result = true });

                    var applicationId = 0;

                    var branch = cacheItemBranch.Branches.FirstOrDefault(p => p.Id == Convert.ToInt32(branchId));

                    if (branch == null)
                        return Json(new { Result = false, ErrorMessage = $"{SiteResources.ErrorOccurredInApplicationStatusCheck} ({SiteResources.BranchNotFound})" });

                    if (branch.CheckRejectedStatus)
                    {
                        var checkRejectedStatusStartDate = DateTime.Today.AddDays(branch.CheckRejectedStatusPeriod * -1);
                        var checkRejectedStatusEndDate = DateTime.Today;

                        var rejectionApplicationStatusIds = branch.Country.Id == 80 ?
                            new int[] { (int)ApplicationStatusType.Rejection } : 
                            new int[] { (int)ApplicationStatusType.Rejection, (int)ApplicationStatusType.IstizanRejection, (int)ApplicationStatusType.RejectionWithCountryEntryBanned };


                        var rejectedAppointment = apiResponse.Data.Items.First().Applications.FirstOrDefault(p => (applicationId != 0 ? p.Id != applicationId : true) &&
                            p.PassportNumber == passNumber &&
                            p.StatusId != (int)ApplicationStatus.Cancelled &&
                            p.StatusHistories != null &&
                            p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
                            p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
                            p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
                            p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
                            p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
                            p.StatusHistories.Any(p2 =>
                                rejectionApplicationStatusIds.Contains(p2.ApplicationStatusId) &&
                                p2.StatusDate.Date >= checkRejectedStatusStartDate &&
                                p2.StatusDate.Date <= checkRejectedStatusEndDate));

                        if (rejectedAppointment != null)
                        {
                            var rejectedStatusEndDate = rejectedAppointment.StatusHistories.First(p2 =>
                                    rejectionApplicationStatusIds.Contains(p2.ApplicationStatusId) &&
                                    p2.StatusDate.Date >= checkRejectedStatusStartDate &&
                                    p2.StatusDate.Date <= checkRejectedStatusEndDate).StatusDate.Date
                                .AddDays(branch.CheckRejectedStatusPeriod - 1);
                            return rejectedStatusEndDate > appointmentDate
                                ? Json(new
                                {
                                    Result = false,
                                    ErrorMessage =
                                        $"{SiteResources.CannotApplyWithThisPassportNumber} {passNumber} ({SiteResources.ExistingRejectedApplicationStatus} - {SiteResources.CheckRejectedStatusPeriod}: {branch.CheckRejectedStatusPeriod} {SiteResources.Day})"
                                })
                                : Json(new { Result = true });
                        }
                    }

                    if (branch.CheckUnrealDocumentStatus)
                    {
                        var checkUnrealDocumentStatusStartDate = DateTime.Today.AddDays(branch.CheckUnrealDocumentStatusPeriod * -1);
                        var checkUnrealDocumentStatusEndDate = DateTime.Today;
                        const int unrealDocumentApplicationStatusId = (int)ApplicationStatusType.UnrealDocument;

                        var rejectedAppointment = apiResponse.Data.Items.First().Applications.FirstOrDefault(p => (applicationId != 0 ? p.Id != applicationId : true) &&
                            p.PassportNumber == passNumber &&
                            p.StatusId != (int)ApplicationStatus.Cancelled &&
                            p.StatusHistories != null &&
                            p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
                            p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
                            p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
                            p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
                            p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
                            p.StatusHistories.Any(p2 =>
                                p2.ApplicationStatusId == unrealDocumentApplicationStatusId &&
                                p2.StatusDate.Date >= checkUnrealDocumentStatusStartDate &&
                                p2.StatusDate.Date <= checkUnrealDocumentStatusEndDate));

                        if (rejectedAppointment != null)
                        {
                            var unrealDocumentStatusEndDate = rejectedAppointment.StatusHistories.First(p2 =>
                                    p2.ApplicationStatusId == unrealDocumentApplicationStatusId &&
                                    p2.StatusDate.Date >= checkUnrealDocumentStatusStartDate &&
                                    p2.StatusDate.Date <= checkUnrealDocumentStatusEndDate).StatusDate.Date
                                .AddDays(branch.CheckUnrealDocumentStatusPeriod - 1);
                            return unrealDocumentStatusEndDate > appointmentDate
                                ? Json(new
                                {
                                    Result = false,
                                    ErrorMessage =
                                        $"{SiteResources.CannotApplyWithThisPassportNumber} {passNumber} ({SiteResources.ExistingUnrealDocumentApplicationStatus} - {SiteResources.CheckUnrealDocumentStatusPeriod}: {branch.CheckUnrealDocumentStatusPeriod} {SiteResources.Day})"
                                })
                                : Json(new { Result = true });
                        }
                    }

                    if (branch.Country.Id == 80 && branch.CheckRejectionWithCountryEntryBannedStatus)
                    {
                        var checkRejectionWithCountryEntryBannedStatusStartDate = DateTime.Today.AddDays(branch.CheckRejectionWithCountryEntryBannedStatusPeriod * -1);
                            var checkRejectionWithCountryEntryBannedStatusEndDate = DateTime.Today;
                            const int rejectionWithCountryEntryBannedStatusId = (int)ApplicationStatusType.RejectionWithCountryEntryBanned;

                            var rejectedAppointment = apiResponse.Data.Items.First().Applications.FirstOrDefault(p => (applicationId != 0 ? p.Id != applicationId : true) &&
                                p.PassportNumber == passNumber &&
                                p.StatusId != (int)ApplicationStatus.Cancelled &&
                                p.StatusHistories != null &&
                                p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
                                p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
                                p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
                                p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
                                p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
                                p.StatusHistories.Any(p2 =>
                                    p2.ApplicationStatusId == rejectionWithCountryEntryBannedStatusId &&
                                    p2.StatusDate.Date >= checkRejectionWithCountryEntryBannedStatusStartDate &&
                                    p2.StatusDate.Date <= checkRejectionWithCountryEntryBannedStatusEndDate));

                            if (rejectedAppointment != null)
                            {
                                var rejectionWithCountryEntryBannedStatusEndDate = rejectedAppointment.StatusHistories.First(p2 =>
                                        p2.ApplicationStatusId == rejectionWithCountryEntryBannedStatusId &&
                                        p2.StatusDate.Date >= checkRejectionWithCountryEntryBannedStatusStartDate &&
                                        p2.StatusDate.Date <= checkRejectionWithCountryEntryBannedStatusEndDate).StatusDate.Date
                                    .AddDays(branch.CheckRejectionWithCountryEntryBannedStatusPeriod - 1);
                                return rejectionWithCountryEntryBannedStatusEndDate > appointmentDate
                                    ? Json(new
                                    {
                                        Result = false,
                                        ErrorMessage =
                                            $"{SiteResources.CannotApplyWithThisPassportNumber} {passNumber} ({SiteResources.ExistingRejectionWithCountryEntryBannedStatus} - {SiteResources.CheckRejectionWithCountryEntryBannedStatusPeriod}: {branch.CheckRejectionWithCountryEntryBannedStatusPeriod} {SiteResources.Day})"
                                    })
                                    : Json(new { Result = true });
                            }
                    }
                }
            }

            return Json(new { Result = true });
        }

        public async Task<IActionResult> IsPreApplicationAvailableByPersonalInformation(int branchId, string name, string surname, string birthDate, int nationalityId, string AppointmentDate, string encryptedApplicationId = null)
        {
            var apiRequest = new PaginatedApplicationsApiRequest
            {
                NationalityId = nationalityId,
                Name = name,
                Surname = surname,
                BirthDate = DateTime.TryParse(birthDate, new CultureInfo(SiteResources.CultureTr), DateTimeStyles.None, out var temp) ? temp : null,
                IncludeApplicationStatusHistories = true
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<ApplicationsApiResponse>>>
                (apiRequest, ApiMethodName.Appointment.GetPaginatedApplicationsForRejectionControl, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurred });

            if (apiResponse.Data == null || !apiResponse.Data.Items.Any())
                return Json(new { Result = true });

            int applicationId = 0;

            var cacheItemBranch = await CacheHelper.GetBranchesAsync();
            var branch = cacheItemBranch.Branches.FirstOrDefault(p => p.Id == branchId);

            if (branch == null)
                return Json(new { Result = false, ErrorMessage = $"{SiteResources.ErrorOccurredInApplicationStatusCheck} ({SiteResources.BranchNotFound})" });

            var appointmentDate = DateTime.ParseExact(AppointmentDate, "dd/MM/yyyy", CultureInfo.InvariantCulture);

            if (branch.CheckRejectedStatus)
            {
                var rejectionApplicationStatusIds = branch.Country.Id == 80 ?
                    new int[] { (int)ApplicationStatusType.Rejection } :
                    new int[] { (int)ApplicationStatusType.Rejection, (int)ApplicationStatusType.IstizanRejection, (int)ApplicationStatusType.RejectionWithCountryEntryBanned };

                var rejectedAppointment = apiResponse.Data.Items.First().Applications.FirstOrDefault(p =>
                    (applicationId != 0 ? p.Id != applicationId : true) &&
                    p.StatusId != (int)ApplicationStatus.Cancelled &&
                    p.StatusHistories != null &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
                    p.StatusHistories.Any(p2 => rejectionApplicationStatusIds.Contains(p2.ApplicationStatusId)));

                if (rejectedAppointment != null)
                {
                    var rejectedStatusEndDate = rejectedAppointment.StatusHistories.First(p2 =>
                            rejectionApplicationStatusIds.Contains(p2.ApplicationStatusId)).StatusDate.Date
                        .AddDays(branch.CheckRejectedStatusPeriod - 1);
                    return appointmentDate.Date < rejectedStatusEndDate ? Json(new { Result = false, ErrorMessage = $"{SiteResources.PersonIsNotAllowedToApply} {name} {surname} ({SiteResources.PreviouslyRejected})" }) : Json(new { Result = true });
                }
            }

            if (branch.CheckUnrealDocumentStatus)
            {
                const int unrealDocumentApplicationStatusId = (int)ApplicationStatusType.UnrealDocument;

                var rejectedAppointment = apiResponse.Data.Items.First().Applications.FirstOrDefault(p =>
                    (applicationId != 0 ? p.Id != applicationId : true) &&
                    p.StatusId != (int)ApplicationStatus.Cancelled &&
                    p.StatusHistories != null &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
                    p.StatusHistories.Any(p2 => p2.ApplicationStatusId == unrealDocumentApplicationStatusId));

                if (rejectedAppointment != null)
                {
                    var unrealDocumentStatusEndDate = rejectedAppointment.StatusHistories.First(p =>
                        p.ApplicationStatusId == unrealDocumentApplicationStatusId).StatusDate.Date
                        .AddDays(branch.CheckUnrealDocumentStatusPeriod - 1);
                    return appointmentDate.Date < unrealDocumentStatusEndDate ? Json(new { Result = false, ErrorMessage = $"{SiteResources.PersonIsNotAllowedToApply} {name} {surname} ({SiteResources.PreviouslyHasUnrealDocument})" }) : Json(new { Result = true });
                }
            }

            if (branch.Country.Id == 80 && branch.CheckRejectionWithCountryEntryBannedStatus)
            {
                const int rejectionWithCountryEntryBannedStatusId = (int)ApplicationStatusType.RejectionWithCountryEntryBanned;

                var rejectedAppointment = apiResponse.Data.Items.First().Applications.FirstOrDefault(p =>
                    (applicationId != 0 ? p.Id != applicationId : true) &&
                    p.StatusId != (int)ApplicationStatus.Cancelled &&
                    p.StatusHistories != null &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
                    p.StatusHistories.Any(p2 => p2.ApplicationStatusId == rejectionWithCountryEntryBannedStatusId));

                if (rejectedAppointment != null)
                {
                    var rejectionWithCountryEntryBannedStatusEndDate = rejectedAppointment.StatusHistories.First(p =>
                            p.ApplicationStatusId == rejectionWithCountryEntryBannedStatusId).StatusDate.Date
                        .AddDays(branch.CheckRejectionWithCountryEntryBannedStatusPeriod - 1);
                    return appointmentDate.Date < rejectionWithCountryEntryBannedStatusEndDate ? Json(new { Result = false, ErrorMessage = $"{SiteResources.PersonIsNotAllowedToApply} {name} {surname} ({SiteResources.PreviouslyHasRejectionWithCountryEntryBannedStatus})" }) : Json(new { Result = true });
                }
            }

            return Json(new { Result = true });
        }
    }
}
