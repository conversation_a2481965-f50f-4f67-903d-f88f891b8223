﻿using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Management.Inventory;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Management.Inventory;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.Inventory;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class InventoryController : BaseController<InventoryController>
    {
        public InventoryController(
                IOptions<AppSettings> appSettings,
                ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        { }

        #region Add

        public IActionResult PartialAddInventory()
        {
            return PartialView("_AddInventory");
        }

        [HttpPost]
        public async Task<IActionResult> AddInventory(AddInventoryViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddInventoryApiRequest
            {
                InventoryTypeId = viewModel.InventoryTypeId,
                Name = viewModel.Name,
                Brand = viewModel.Brand,
                Model = viewModel.Model,
                SerialNumber = viewModel.SerialNumber,
                SDKVersion = viewModel.SDKVersion,
                SDKAddress = viewModel.SDKAddress,
                StatusId = viewModel.StatusId,
                UpdatedAt = viewModel.UpdatedAt,
                Definition = viewModel.Definition
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Management.AddInventory, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateInventory(string encryptedInventoryId)
        {
            int id = encryptedInventoryId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<InventoryApiResponse>>
                (ApiMethodName.Management.GetInventory + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new InventoryViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                InventoryTypeId = apiResponse.Data.InventoryTypeId,
                Name = apiResponse.Data.Name,
                Brand = apiResponse.Data.Brand,
                Model = apiResponse.Data.Model,
                SerialNumber = apiResponse.Data.SerialNumber,
                SDKAddress = apiResponse.Data.SDKAddress,
                SDKVersion = apiResponse.Data.SDKVersion,
                StatusId = apiResponse.Data.StatusId,
                UpdatedAt = apiResponse.Data.UpdatedAt.Date,
                Definition = apiResponse.Data.Definition,
                IsActive = apiResponse.Data.IsActive
            };

            return PartialView("_UpdateInventory", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateInventory(InventoryViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateInventoryApiRequest
            {
                Id = viewModel.EncryptedId.ToDecryptInt(),
                InventoryTypeId = viewModel.InventoryTypeId,
                Name = viewModel.Name,
                Brand = viewModel.Brand,
                Model = viewModel.Model,
                SerialNumber = viewModel.SerialNumber,
                SDKVersion = viewModel.SDKVersion,
                SDKAddress = viewModel.SDKAddress,
                StatusId = viewModel.StatusId,
                UpdatedAt = viewModel.UpdatedAt,
                Definition = viewModel.Definition,
                IsActive = viewModel.IsActive
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateInventory, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteInventory(string encryptedInventoryId)
        {
            int id = encryptedInventoryId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Management.DeleteInventory + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> PartialInventory(string encryptedInventoryId)
        {
            int id = encryptedInventoryId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<InventoryApiResponse>>
                (ApiMethodName.Management.GetInventory + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new InventoryViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                InventoryTypeId = apiResponse.Data.InventoryTypeId,
                Name = apiResponse.Data.Name,
                Brand = apiResponse.Data.Brand,
                Model = apiResponse.Data.Model,
                SerialNumber = apiResponse.Data.SerialNumber,
                SDKAddress = apiResponse.Data.SDKAddress,
                SDKVersion = apiResponse.Data.SDKVersion,
                StatusId = apiResponse.Data.StatusId,
                UpdatedAt = apiResponse.Data.UpdatedAt.Date,
                Definition = apiResponse.Data.Definition,
                IsActive = apiResponse.Data.IsActive
            };

            return PartialView("_Inventory", viewModel);
        }

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult InventoryList()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedInventories([DataSourceRequest] DataSourceRequest request, FilterInventoryViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new GetPaginatedInventoriesApiRequest
            {
                Name = filterViewModel.FilterName,
                Model = filterViewModel.FilterModel,
                SerialNumber = filterViewModel.FilterSerialNumber,
                Brand = filterViewModel.FilterBrand,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedInventoriesApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedInventories, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<InventoryViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().Inventories
                    .Select(p => new InventoryViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        InventoryTypeId = p.InventoryTypeId,
                        Name = p.Name,
                        Brand = p.Brand,
                        Model = p.Model,
                        SerialNumber = p.SerialNumber,
                        SDKAddress = p.SDKAddress,
                        SDKVersion = p.SDKVersion,
                        StatusId = p.StatusId,
                        UpdatedAt = p.UpdatedAt.Date,
                        Definition = p.Definition,
                        IsActive = p.IsActive
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #endregion

    }
}
