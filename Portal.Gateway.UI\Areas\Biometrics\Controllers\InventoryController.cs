﻿using Gateway.Extensions;
using Gateway.Http;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.QmsFolder;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Biometrics.Models.Inventory.RequestModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.Inventory.Results;
using Portal.Gateway.UI.Areas.Biometrics.Models.Inventory.ViewModels;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using System;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Biometrics.Controllers
{

    [Area("Biometrics")]
    public class InventoryController : BaseController<InventoryController>
    {
        public InventoryController(
            IOptions<AppSettings> appSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper) { }

        #region Add

        public IActionResult PartialAddInventory()
        {
            var viewModel = new AddInventoryViewModel();

            return PartialView("_AddInventory", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddInventory(AddInventoryViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var request = new AddInventoryRequestModel
            {
                Status = viewModel.Status,
                Description = viewModel.Description,
                SerialNumber = viewModel.SerialNumber,
                InventoryDefinitionId = viewModel.InventoryDefinitionId,
                IpCameras = viewModel.IpCameras,
            };

            var apiResponse = await RestHttpClient.Create().Post<AddInventoryResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.AddInventory, headers, request);

            if (apiResponse.Data == null)
                return Json(new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateInventory(string encryptedInventoryId)
        {
            if (encryptedInventoryId.IsNullOrWhitespace())
            {
                return Content("Missing Inventory id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedInventoryId.ToDecrypt());

            var apiResponse = await RestHttpClient.Create().Get<GetInventoryResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetInventory + resourceId, headers);

            var viewModel = new UpdateInventoryViewModel
            {
                Id = apiResponse.Data.Id,
                Status = apiResponse.Data.Status,
                SerialNumber = apiResponse.Data.SerialNumber,
                Description = apiResponse.Data.Description,
                InventoryDefinitionId = apiResponse.Data.InventoryDefinitionId,
                InventoryDefinition = apiResponse.Data.InventoryDefinition,
                IpCameras = apiResponse.Data.IpCameras
            };

            return PartialView("_UpdateInventory", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateInventory(UpdateInventoryViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid) //buraya fluent val yazılıcak viewmodellere
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var apiRequest = new UpdateInventoryRequestModel
            {
                Status = viewModel.Status,
                SerialNumber = viewModel.SerialNumber,
                Description = viewModel.Description,
                InventoryDefinitionId = viewModel.InventoryDefinitionId,
                InventoryDefinition = viewModel.InventoryDefinition,
                IpCameras = viewModel.IpCameras,
            };

            var apiResponse = await RestHttpClient.Create().Put<UpdateInventoryResult>(
                AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.UpdateInventory + viewModel.Id, headers,
                apiRequest);


            if (apiResponse.Data == null)
                return Json(new ResultModel { Message = apiResponse.ValidationMessages.ToPlainText(), ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion


        #region Get

        public async Task<IActionResult> PartialDetailInventory(string encryptedInventoryId)
        {
            if (encryptedInventoryId.IsNullOrWhitespace())
            {
                return Content("Missing Inventory id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedInventoryId.ToDecrypt());

            var apiResponse = await RestHttpClient.Create().Get<GetInventoryResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetInventory + resourceId, headers);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var viewModel = new InventoryViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Status = apiResponse.Data.Status,
                SerialNumber = apiResponse.Data.SerialNumber,
                Description = apiResponse.Data.Description,
                InventoryDefinitionId = apiResponse.Data.InventoryDefinitionId,
                InventoryDefinition = apiResponse.Data.InventoryDefinition,
                IpCameras = apiResponse.Data.IpCameras,
            };

            return PartialView("_DetailInventory", viewModel);
        }


        [Action(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedInventories([DataSourceRequest] DataSourceRequest request, FilterInventoryViewModel filterViewModel)
        {
            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedInventoryApiRequest
            {
                FilterInventoryTypeId = filterViewModel.FilterInventoryTypeId,
                Pagination = new QmsPaginationApiRequest
                {
                    PageNumber = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = "Id",
                    Ascending = true
                }
            };

            var apiResponse = await RestHttpClient.Create().Post<GetPaginatedInventoriesResult>(
                AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetPaginatedInventories, headers, apiRequest);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var paginatedData = apiResponse.Data.Select(p =>
                new InventoryViewModel
                {
                    EncryptedId = p.Id.ToEncrypt(),
                    Status = p.Status,
                    Description = p.Description,
                    SerialNumber = p.SerialNumber,
                    InventoryDefinitionId = p.InventoryDefinitionId,
                    InventoryDefinition = p.InventoryDefinition

                }).ToList();

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.TotalNumberOfRecords });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteInventory(string encryptedInventoryId)
        {
            if (encryptedInventoryId.IsNullOrWhitespace())
            {
                return Content("Missing Inventory id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedInventoryId.ToDecrypt());

            var apiResponse = await RestHttpClient.Create().Delete<DeleteInventoryResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.DeleteInventory + resourceId, headers);

            if (apiResponse.Data == null)
                return Json(apiResponse.Message);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

    }

}
