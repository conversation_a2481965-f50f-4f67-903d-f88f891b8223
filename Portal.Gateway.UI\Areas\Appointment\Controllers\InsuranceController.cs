﻿using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.Insurance;
using Portal.Gateway.ApiModel.Responses.Appointment.Application;
using Portal.Gateway.ApiModel.Responses.Insurance;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.Insurance;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Linq;
using System.Threading.Tasks;
using iTextSharp.text.pdf;
using Microsoft.AspNetCore.Hosting;
using System.IO;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.DocumentManagement;
using Portal.Gateway.ApiModel.Requests.Insurance.EmaaHasarServisExcel;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.DocumentManagement;
using Portal.Gateway.ApiModel.Responses.Insurance;
using Portal.Gateway.Contracts.Attributes;
using System.Collections.Generic;
using Portal.Gateway.ApiModel.Requests.Appointment.Application;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.EmaaHasarServisExcel;
using static Portal.Gateway.ApiModel.Requests.Insurance.ClaimEntryPolicyApiRequest;
using Portal.Gateway.UI.Helpers;

namespace Portal.Gateway.UI.Areas.Appointment.Controllers
{
    [Area("Appointment")]
    public class InsuranceController : BaseController<InsuranceController>
    {
        private readonly IWebHostEnvironment _webHostEnvironment;

        public InsuranceController(
            ICacheHelper cacheHelper,
            IOptions<AppSettings> appSettings,
            IWebHostEnvironment webHostEnvironment)
            : base(appSettings, cacheHelper)
        {
            _webHostEnvironment = webHostEnvironment;
        }

        public async Task<IActionResult> PartialCreateInsurance(string encryptedApplicationId)
        {
            int id = encryptedApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>

                (ApiMethodName.Appointment.GetApplicationInsuranceInformation + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            var viewModel = new CreateInsuranceViewModel
            {
                Id = id,
                ApplicationId = apiResponse.Data.Id,
                ApplicationNumber = apiResponse.Data.Id.ToApplicationNumber(),
                EntryDate = apiResponse.Data.ApplicationTime.DateTime,
                BranchCountryIso3 = apiResponse.Data.BranchCountryIso3,
                VisaCategoryId = apiResponse.Data.Document.VisaCategoryId,
                SelectedInsurance = new CreateInsuranceViewModel.Insurance(),
                ShowMontageVisaType = apiResponse.Data.ShowMontageVisaType
            };


            if (apiResponse.Data.ExtraFees != null &&
                apiResponse.Data.ExtraFees.Any(p => p.Category == (int)ExtraFeeCategoryType.Insurance))
            {
                var policyName = apiResponse.Data.ExtraFees
                        .First(p => p.Category == (int)ExtraFeeCategoryType.Insurance).ExtraFeeName;

                var policyDays = apiResponse.Data.ExtraFees
                        .First(p => p.Category == (int)ExtraFeeCategoryType.Insurance).PolicyPeriod;

                if (policyDays == null) // null olması durumunda isimden bul
                {
                    policyDays = 0;

                    if (!string.IsNullOrEmpty(policyName))
                    {
                        policyDays = GetPolicyDaysFromPolicyName(policyName);
                    }
                }

                viewModel.SelectedInsurance.PolicyName = policyName;
                viewModel.SelectedInsurance.PolicyDays = policyDays.Value;
                viewModel.ExitDate = viewModel.EntryDate.AddDays(policyDays.Value);
            }

            return PartialView("_CreateInsurance", viewModel);
        }

        public async Task<IActionResult> PartialUpdateInsurance(string encryptedApplicationId)
        {
            int id = encryptedApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>
                (ApiMethodName.Appointment.GetApplicationInsuranceInformation + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            var viewModel = new CreateInsuranceViewModel
            {
                Id = id,
                ApplicationId = apiResponse.Data.Id,
                ApplicationNumber = apiResponse.Data.Id.ToApplicationNumber(),
                EntryDate = DateTime.Now,
                BranchCountryIso3 = apiResponse.Data.BranchCountryIso3,
                SelectedInsurance = new CreateInsuranceViewModel.Insurance()
            };


            if (apiResponse.Data.ExtraFees != null &&
                apiResponse.Data.ExtraFees.Any(p => p.Category == (int)ExtraFeeCategoryType.Insurance))
            {
                var policyName = apiResponse.Data.ExtraFees
                        .First(p => p.Category == (int)ExtraFeeCategoryType.Insurance).ExtraFeeName;

                var policyDays = apiResponse.Data.ExtraFees
                        .First(p => p.Category == (int)ExtraFeeCategoryType.Insurance).PolicyPeriod;

                if(policyDays == null) // null olması durumunda isimden bul
                {
                    policyDays = 0;

                    if (!string.IsNullOrEmpty(policyName))
                    {
                        policyDays = GetPolicyDaysFromPolicyName(policyName);
                    }
                }

                viewModel.SelectedInsurance.PolicyName = policyName;
                viewModel.SelectedInsurance.PolicyDays = policyDays.Value;
                viewModel.ExitDate = viewModel.EntryDate.AddDays(policyDays.Value);
            }

            return PartialView("_UpdateInsurance", viewModel);
        }

        public async Task<IActionResult> PartialTsCreateInsurance(string encryptedApplicationId)
        {
            int id = encryptedApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>
                (ApiMethodName.Appointment.GetApplicationInsuranceInformation + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            var viewModel = new CreateInsuranceViewModel
            {
                Id = id,
                ApplicationId = apiResponse.Data.Id,
                ApplicationNumber = apiResponse.Data.Id.ToApplicationNumber(),
                EntryDate = apiResponse.Data.ApplicationTime.DateTime,
                BranchCountryIso3 = apiResponse.Data.BranchCountryIso3,
                VisaCategoryId = apiResponse.Data.Document.VisaCategoryId,
                SelectedInsurance = new CreateInsuranceViewModel.Insurance()
            };


            if (apiResponse.Data.ExtraFees != null &&
                apiResponse.Data.ExtraFees.Any(p => p.Category == (int)ExtraFeeCategoryType.TS))
            {
                var tsPolicyName = apiResponse.Data.ExtraFees.First(p => p.Category == (int)ExtraFeeCategoryType.TS)
                    .ExtraFeeName;
                var tsPolicyDays = apiResponse.Data.ExtraFees.First(p => p.Category == (int)ExtraFeeCategoryType.TS)
                    .PolicyPeriod ?? 0;

                if (tsPolicyDays == 0)
                {
                    var numberInPolicyNameString = string.Join("", tsPolicyName.ToCharArray().Where(char.IsDigit));
                    int.TryParse(numberInPolicyNameString, out int numberInPolicyName);

                    if (tsPolicyName.Contains("Gün", StringComparison.OrdinalIgnoreCase) ||
                        tsPolicyName.Contains("Day", StringComparison.OrdinalIgnoreCase))
                    {
                        switch (numberInPolicyName)
                        {
                            case 090: //0-90
                                tsPolicyDays = 90;
                                break;
                            case 91180: //91-180
                                tsPolicyDays = 180;
                                break;
                            case 181270: //181-270
                                tsPolicyDays = 270;
                                break;
                            case 271: //271
                                tsPolicyDays = 360;
                                break;
                            default:
                                break;
                        }
                    }
                }

                viewModel.SelectedInsurance.PolicyName = tsPolicyName;
                viewModel.SelectedInsurance.PolicyDays = tsPolicyDays;
                viewModel.ExitDate = viewModel.EntryDate.AddDays(tsPolicyDays);
            }

            return PartialView("_TsCreateInsurance", viewModel);
        }

        public async Task<IActionResult> UpdateWorkPermitPolicy(string encryptedApplicationId)
        {
            int id = encryptedApplicationId.ToDecryptInt();

            DateTime EntryDate = DateTime.Now;
            DateTime ExitDate = DateTime.Now;

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>

                (ApiMethodName.Appointment.GetApplicationInsuranceInformation + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            if (apiResponse.Data.ExtraFees != null &&
                apiResponse.Data.ExtraFees.Any(p => p.Category == (int)ExtraFeeCategoryType.Insurance))
            {
                var policyName = apiResponse.Data.ExtraFees
                    .First(p => p.Category == (int)ExtraFeeCategoryType.Insurance).ExtraFeeName;

                var policyDays = apiResponse.Data.ExtraFees
                    .First(p => p.Category == (int)ExtraFeeCategoryType.Insurance).PolicyPeriod;

                if(policyDays == null) // null olması durumunda isimden bul
                {
                    policyDays = 0;

                    if (!string.IsNullOrEmpty(policyName))
                    {
                        policyDays = GetPolicyDaysFromPolicyName(policyName);
                    }
                }

                ExitDate = EntryDate.AddDays(policyDays.Value);
            }

            for (int i = 0; i < apiResponse.Data.Insurance.Number.Count; i++)
            {
                var cancelApiRequest = new CancelPolicyApiRequest
                {
                    PolicyNo = apiResponse.Data.Insurance.Number[i],
                    AppointmentId = apiResponse.Data.Id,
                    UserId = UserSession.UserId,
                    ProviderId = ProviderType.Emaa.ToInt()
                };

                var apiResponseOfCancelPolicy = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<CancelPolicyApiResponse>>
                    (cancelApiRequest, ApiMethodName.Insurance.CancelPolicy, AppSettings.PortalGatewayApiUrl,
                        PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                var updateOldInsurancePassive = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                    (null,
                        $"{ApiMethodName.Appointment.UpdateOldInsurancePassive + apiResponse.Data.Id}/{apiResponse.Data.Insurance.Number[i]}",
                        AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                if (!await apiResponseOfCancelPolicy.Validate(out ResultModel resultOfCancelPolicy)
                        .ConfigureAwait(false))
                    return Json(resultOfCancelPolicy);
            }

            string visaCategoryQuestionsValue = "1";

            if (apiResponse.Data.Document.VisaCategoryId == (int)VisaCategoryTypeEnum.TouristicResidencePermit)
                visaCategoryQuestionsValue = "4";
            else if (apiResponse.Data.Document.VisaCategoryId == (int)VisaCategoryTypeEnum.VisitSpecialResidancePermit)
                visaCategoryQuestionsValue = "5";

            var apiRequest = new CreatePolicyApiRequest
            {
                UserId = UserSession.UserId,
                OldCancelledInsuranceId = apiResponse.Data.Insurance.Id,
                ApplicationId = apiResponse.Data.Id,
                StartDate = EntryDate,
                EndDate = ExitDate,
                BranchId = apiResponse.Data.BranchId,
                VisaCategoryQuestionsValue = visaCategoryQuestionsValue,
                InsuredInfo = new CreatePolicyApiRequest.InsuredInformation()
                {
                    BirthDate = apiResponse.Data.BirthDate,
                    CountryCode = apiResponse.Data.NationalityIso2,
                    FirstName = apiResponse.Data.Name,
                    SurName = apiResponse.Data.Surname,
                    PassportNo = apiResponse.Data.PassportNumber,
                    Gender = apiResponse.Data.GenderId,
                    GsmPhone = apiResponse.Data.GsmPhone.Substring(0, 3) + "-" +
                               apiResponse.Data.GsmPhone.Substring(3, 3) + "-" + apiResponse.Data.GsmPhone.Substring(6),
                    EmailAdress = apiResponse.Data.EmailAdress
                },
                ApplicationExtraFeeId = apiResponse.Data.ExtraFees
                    .Where(q => q.Category == (int)ExtraFeeCategoryType.Insurance).Select(q => q.ApplicationExtraFeeId)
                    .FirstOrDefault()
            };

            var apiResponseOfCreatePolicy = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<CreatePolicyApiResponse>>
                (apiRequest, ApiMethodName.Insurance.CreatePolicy, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponseOfCreatePolicy.Validate(out ResultModel resultOfCreatePolicy).ConfigureAwait(false))
                return Json(resultOfCreatePolicy);

            return Json(new ResultModel
            {
                Message = ResultMessage.OperationIsSuccessful.ToDescription(),
                ResultType = ResultType.Success
            });
        }

        [HttpPost]
        public async Task<IActionResult> CreateInsurance(CreateInsuranceViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel
                { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiResponseOfGetApplication = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>
                (ApiMethodName.Appointment.GetApplicationInsuranceInformation + viewModel.Id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponseOfGetApplication.Validate(out ResultModel resultOfGetApplication)
                    .ConfigureAwait(false))
                return Json(resultOfGetApplication);

            if (apiResponseOfGetApplication.Data.GsmPhone.Count() < 7)
                return Json(new ResultModel
                { Message = EnumResources.PhoneNumberLenghtError, ResultType = ResultType.Danger });

            string visaCategoryQuestionsValue = "1";

            if (apiResponseOfGetApplication.Data.Document.VisaCategoryId == (int)VisaCategoryTypeEnum.TouristicResidencePermit)
                visaCategoryQuestionsValue = "4";
            else if(apiResponseOfGetApplication.Data.Document.VisaCategoryId == (int)VisaCategoryTypeEnum.VisitSpecialResidancePermit)
                visaCategoryQuestionsValue = "5";

            var apiRequest = new CreatePolicyApiRequest
            {
                UserId = UserSession.UserId,
                ApplicationId = viewModel.Id,
                StartDate = viewModel.EntryDate,
                EndDate = viewModel.ExitDate,
                BranchId = apiResponseOfGetApplication.Data.BranchId,
                VisaCategoryQuestionsValue = visaCategoryQuestionsValue,
                InsuredInfo = new CreatePolicyApiRequest.InsuredInformation()
                {
                    BirthDate = apiResponseOfGetApplication.Data.BirthDate,
                    CountryCode = apiResponseOfGetApplication.Data.NationalityIso2,
                    FirstName = apiResponseOfGetApplication.Data.Name,
                    SurName = apiResponseOfGetApplication.Data.Surname,
                    PassportNo = apiResponseOfGetApplication.Data.PassportNumber,
                    Gender = apiResponseOfGetApplication.Data.GenderId,
                    GsmPhone = apiResponseOfGetApplication.Data.GsmPhone.Substring(0, 3) + "-" +
                               apiResponseOfGetApplication.Data.GsmPhone.Substring(3, 3) + "-" +
                               apiResponseOfGetApplication.Data.GsmPhone.Substring(6),
                    EmailAdress = apiResponseOfGetApplication.Data.EmailAdress
                },
                ApplicationExtraFeeId = apiResponseOfGetApplication.Data.ExtraFees
                    .Where(q => q.Category == (int)ExtraFeeCategoryType.Insurance).Select(q => q.ApplicationExtraFeeId)
                    .FirstOrDefault()
            };

            var apiResponseOfCreatePolicy = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<CreatePolicyApiResponse>>
                (apiRequest, ApiMethodName.Insurance.CreatePolicy, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponseOfCreatePolicy.Validate(out ResultModel resultOfCreatePolicy).ConfigureAwait(false))
                return Json(resultOfCreatePolicy);

            return Json(new ResultModel
            { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpPost]
        public async Task<IActionResult> CancelInsurance(CreateInsuranceViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel
                { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiResponseOfGetApplication = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>

                (ApiMethodName.Appointment.GetApplicationInsuranceInformation + viewModel.Id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponseOfGetApplication.Validate(out ResultModel resultOfGetApplication)
                    .ConfigureAwait(false))
                return Json(resultOfGetApplication);

            for (int i = 0; i < apiResponseOfGetApplication.Data.Insurance.Number.Count; i++)
            {
                if (apiResponseOfGetApplication.Data.Insurance.RelatedIndividualInsuraneId[i] == 0)
                {
                    var apiRequest = new CancelPolicyApiRequest
                    {
                        PolicyNo = apiResponseOfGetApplication.Data.Insurance.Number[i],
                        AppointmentId = viewModel.Id,
                        UserId = UserSession.UserId,
                        ProviderId = ProviderType.Emaa.ToInt()
                    };

                    var apiResponseOfCancelPolicy = await PortalHttpClientHelper
                        .PostAsJsonAsync<ApiResponse<CancelPolicyApiResponse>>
                        (apiRequest, ApiMethodName.Insurance.CancelPolicy, AppSettings.PortalGatewayApiUrl,
                            PortalGatewayApiDefaultRequestHeaders)
                        .ConfigureAwait(false);

                    var updateOldInsurancePassive = await PortalHttpClientHelper
                        .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                        (null,
                            $"{ApiMethodName.Appointment.UpdateOldInsurancePassive + viewModel.Id}/{apiResponseOfGetApplication.Data.Insurance.Number[i]}",
                            AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                        .ConfigureAwait(false);

                    if (!await apiResponseOfCancelPolicy.Validate(out ResultModel resultOfCancelPolicy)
                            .ConfigureAwait(false))
                        return Json(resultOfCancelPolicy);
                }
            }

            string visaCategoryQuestionsValue = "1";

            if (apiResponseOfGetApplication.Data.Document.VisaCategoryId == (int)VisaCategoryTypeEnum.TouristicResidencePermit)
                visaCategoryQuestionsValue = "4";
            else if (apiResponseOfGetApplication.Data.Document.VisaCategoryId == (int)VisaCategoryTypeEnum.VisitSpecialResidancePermit)
                visaCategoryQuestionsValue = "5";


            var apiRequestCreate = new CreatePolicyApiRequest
            {
                UserId = UserSession.UserId,
                OldCancelledInsuranceId = apiResponseOfGetApplication.Data.Insurance.Id,
                ApplicationId = viewModel.Id,
                StartDate = viewModel.EntryDate,
                EndDate = viewModel.ExitDate,
                BranchId = apiResponseOfGetApplication.Data.BranchId,
                VisaCategoryQuestionsValue = visaCategoryQuestionsValue,
                InsuredInfo = new CreatePolicyApiRequest.InsuredInformation()
                {
                    BirthDate = apiResponseOfGetApplication.Data.BirthDate,
                    CountryCode = apiResponseOfGetApplication.Data.NationalityIso2,
                    FirstName = apiResponseOfGetApplication.Data.Name,
                    SurName = apiResponseOfGetApplication.Data.Surname,
                    PassportNo = apiResponseOfGetApplication.Data.PassportNumber,
                    Gender = apiResponseOfGetApplication.Data.GenderId,
                    GsmPhone = apiResponseOfGetApplication.Data.GsmPhone.Substring(0, 3) + "-" +
                               apiResponseOfGetApplication.Data.GsmPhone.Substring(3, 3) + "-" +
                               apiResponseOfGetApplication.Data.GsmPhone.Substring(6),
                    EmailAdress = apiResponseOfGetApplication.Data.EmailAdress
                },
                ApplicationExtraFeeId = apiResponseOfGetApplication.Data.ExtraFees
                    .Where(q => q.Category == (int)ExtraFeeCategoryType.Insurance)
                    .Select(q => q.ApplicationExtraFeeId).FirstOrDefault()
            };

            var apiResponseOfCreatePolicy = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<CreatePolicyApiResponse>>
                (apiRequestCreate, ApiMethodName.Insurance.CreatePolicy, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponseOfCreatePolicy.Validate(out ResultModel resultOfCreatePolicy)
                    .ConfigureAwait(false))
                return Json(resultOfCreatePolicy);


            return Json(new ResultModel
            { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }


        [HttpPost]
        public async Task<IActionResult> TsCreateInsurance(CreateInsuranceViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel
                { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiResponseOfGetApplication = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>

                (ApiMethodName.Appointment.GetApplicationInsuranceInformation + viewModel.Id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponseOfGetApplication.Validate(out ResultModel resultOfGetApplication)
                    .ConfigureAwait(false))
                return Json(resultOfGetApplication);

            if (apiResponseOfGetApplication.Data.GsmPhone.Count() < 7)
                return Json(new ResultModel
                { Message = EnumResources.PhoneNumberLenghtError, ResultType = ResultType.Danger });

            string visaCategoryQuestionsValue = "1";

            if (apiResponseOfGetApplication.Data.Document.VisaCategoryId == (int)VisaCategoryTypeEnum.TouristicResidencePermit)
                visaCategoryQuestionsValue = "4";
            else if (apiResponseOfGetApplication.Data.Document.VisaCategoryId == (int)VisaCategoryTypeEnum.VisitSpecialResidancePermit)
                visaCategoryQuestionsValue = "5";

            var apiRequest = new CreatePolicyApiRequest
            {
                UserId = UserSession.UserId,
                ApplicationId = viewModel.Id,
                StartDate = viewModel.EntryDate,
                EndDate = viewModel.ExitDate,
                BranchId = apiResponseOfGetApplication.Data.BranchId,
                VisaCategoryQuestionsValue = visaCategoryQuestionsValue,
                ProviderId = ProviderType.EmaaTs.ToInt(), // Emaa Trafik Sigortası
                Masterr = new CreatePolicyApiRequest.Master()
                {
                    PlateNo = apiResponseOfGetApplication.Data.Document.PlateNo
                },
                InsuredInfo = new CreatePolicyApiRequest.InsuredInformation()
                {
                    BirthDate = apiResponseOfGetApplication.Data.BirthDate,
                    CountryCode = apiResponseOfGetApplication.Data.NationalityIso2,
                    FirstName = apiResponseOfGetApplication.Data.Name,
                    SurName = apiResponseOfGetApplication.Data.Surname,
                    PassportNo = apiResponseOfGetApplication.Data.PassportNumber,
                    Gender = apiResponseOfGetApplication.Data.GenderId,
                    GsmPhone = apiResponseOfGetApplication.Data.GsmPhone.Substring(0, 3) + "-" +
                               apiResponseOfGetApplication.Data.GsmPhone.Substring(3, 3) + "-" +
                               apiResponseOfGetApplication.Data.GsmPhone.Substring(6),
                    EmailAdress = apiResponseOfGetApplication.Data.EmailAdress
                },
                Questionss = new CreatePolicyApiRequest.Questions()
                {
                    Questionn = new CreatePolicyApiRequest.Questions.Question()
                    {
                        QuestionCode1 = "10005",
                        Answer1 = apiResponseOfGetApplication.Data.Document.ModelYear.ToString(),
                        QuestionCode2 = "10272",
                        Answer2 = apiResponseOfGetApplication.Data.Document.BrandCode,
                        QuestionCode3 = "10006",
                        Answer3 = apiResponseOfGetApplication.Data.Document.BrandText,
                        QuestionCode4 = "10007",
                        Answer4 = apiResponseOfGetApplication.Data.Document.ModelText,
                        QuestionCode5 = "10011",
                        Answer5 = apiResponseOfGetApplication.Data.Document.ChassisNumber
                    }
                },
                ApplicationExtraFeeId = apiResponseOfGetApplication.Data.ExtraFees
                    .Where(q => q.Category == (int)ExtraFeeCategoryType.TS).Select(q => q.ApplicationExtraFeeId)
                    .FirstOrDefault()
            };

            var apiResponseOfCreatePolicy = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<CreatePolicyApiResponse>>
                (apiRequest, ApiMethodName.Insurance.CreatePolicy, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponseOfCreatePolicy.Validate(out ResultModel resultOfCreatePolicy).ConfigureAwait(false))
                return Json(resultOfCreatePolicy);

            return Json(new ResultModel
            { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpGet]
        public async Task<IActionResult> CertificateInsurance(string encryptedApplicationId, int languageId,
            bool isNotificationEnabled)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var id = encryptedApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>

                (ApiMethodName.Appointment.GetApplicationInsuranceInformation + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var filePath_OutPdf = "";
            var filePath_backGroundTurkishPdf = "";
            var filePath_backGroundEnglishPdf = "";
            var filePath_backGroundArabicPdf = "";



			
			
            filePath_backGroundTurkishPdf = Path.Combine(_webHostEnvironment.WebRootPath,
												"documents", "InsurancePolicy", "Tazminat_Talep_Bilgilendirme_Formu.pdf");
			filePath_backGroundEnglishPdf = Path.Combine(_webHostEnvironment.WebRootPath,
													"documents", "InsurancePolicy", "Tazminat_Talep_Bilgilendirme_Formu_English.pdf");
			filePath_backGroundArabicPdf = Path.Combine(_webHostEnvironment.WebRootPath,
												   "documents", "InsurancePolicy" ,"Tazminat_Talep_Bilgilendirme_Formu_Arabic.pdf");
			

            filePath_OutPdf = languageId switch
            {
                10 => filePath_backGroundTurkishPdf,
                11 => filePath_backGroundEnglishPdf,
                12 => filePath_backGroundArabicPdf,
                20 => filePath_backGroundTurkishPdf,
                21 => filePath_backGroundEnglishPdf,
                22 => filePath_backGroundArabicPdf,
                _ => filePath_OutPdf
            };

            var mergeStream = new MemoryStream();
            var finalStream = new MemoryStream();
            PdfCopyFields copy = new PdfCopyFields(finalStream);



            for (var i = 0; i < apiResponse.Data.Insurance.Number.Count; i++)
            {
                if (apiResponse.Data.Insurance.RelatedIndividualInsuraneId[i] == 0)
                {
                    int firstLanguageId = languageId;

                    if (i != apiResponse.Data.Insurance.Number.Count - 1)
                    {
                        if (languageId == 20)
                            firstLanguageId = 10;
                        else if (languageId == 21)
                            firstLanguageId = 11;
                        else if (languageId == 22)
                            firstLanguageId = 12;
                    }

                    var apiRequest = new CertificatePolicyApiRequest()
                    {
                        PolicyNumber = Convert.ToInt64(apiResponse.Data.Insurance.Number[i]),
                        LanguageId = firstLanguageId,
                        ApplicationId = id,
                        IsNotificationEnabled = isNotificationEnabled,
                        UserId = UserSession.UserId,
                    };

                    var apiResponseOfCertificateInsurance = await PortalHttpClientHelper
                        .PostAsJsonAsync<ApiResponse<CertificatePolicyApiResponse>>
                        (apiRequest, ApiMethodName.Insurance.CertificatePolicy, AppSettings.PortalGatewayApiUrl,
                            PortalGatewayApiDefaultRequestHeaders)
                        .ConfigureAwait(false);

                    if (!await apiResponseOfCertificateInsurance.Validate(out ResultModel resultOfCertificateInsurance)
                            .ConfigureAwait(false))
                    {
                        TempData.Put("Notification",
                            new ResultModel
                            { Message = resultOfCertificateInsurance.Message, ResultType = ResultType.Danger });
                        return RedirectToAction("List", "Application", new { Area = "Appointment" });
                    }

                    var certificateStream = new MemoryStream();

                    var byteInfo = Convert.FromBase64String(apiResponseOfCertificateInsurance.Data.Certificate);

                    if (apiResponseOfCertificateInsurance.Data.ProviderId == ProviderType.Emaa.ToInt())
                    {
                        var decodedString = System.Text.Encoding.UTF8.GetString(byteInfo);

                        byteInfo = Convert.FromBase64String(decodedString);
                    }

                    certificateStream.Write(byteInfo, 0, byteInfo.Length);
                    certificateStream.Position = 0;
                    copy.AddDocument(new PdfReader(certificateStream));
                    certificateStream.Dispose();
                }
            }

            if (filePath_OutPdf != "")
            {
                var pdfStream = new MemoryStream();
                pdfStream = new MemoryStream(System.IO.File.ReadAllBytes(filePath_OutPdf));
                pdfStream.Position = 0;
                copy.AddDocument(new PdfReader(pdfStream));
                pdfStream.Dispose();
            }
            copy.Close();
            mergeStream.Write(finalStream.GetBuffer(), 0, finalStream.ToArray().Length);
            mergeStream.Position = 0;

            var notificationApiRequest = new SendInsuranceNotificationApiRequest
            {
                PolicyAttachment = new List<byte[]> { mergeStream.ToArray() },
                LanguageId = languageId,
                ApplicationNo = id,
                IsNotificationEnabled = isNotificationEnabled,
                EmailList = new List<string> { apiResponse.Data.Email },
                BranchEmailProviderId = apiResponse.Data.BranchEmailProviderId,
                BranchSmsProviderId = apiResponse.Data.BranchSmsProviderId,
                UserId = UserSession.UserId
            };

            await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<CertificatePolicyApiResponse>>
                (notificationApiRequest, ApiMethodName.Appointment.ApplicationInsuranceNotification,
                    AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            return File(mergeStream, "application/pdf");
        }

        [HttpGet]
        public async Task<IActionResult> TsCertificateInsurance(string encryptedApplicationId, int languageId,
            bool isNotificationEnabled)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var id = encryptedApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>

                (ApiMethodName.Appointment.GetApplicationInsuranceInformation + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var apiRequest = new CertificatePolicyApiRequest()
            {
                PolicyNumber = Convert.ToInt64(apiResponse.Data.Insurance.Number[0]),
                LanguageId = languageId,
                ApplicationId = id,
                IsNotificationEnabled = isNotificationEnabled,
                ProviderId = ProviderType.EmaaTs.ToInt(), // Emaa Sigorta Sertifika
                UserId = UserSession.UserId,
            };

            var apiResponseOfCertificateInsurance = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<CertificatePolicyApiResponse>>
                (apiRequest, ApiMethodName.Insurance.CertificatePolicy, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponseOfCertificateInsurance.Validate(out ResultModel resultOfCertificateInsurance)
                    .ConfigureAwait(false))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = resultOfCertificateInsurance.Message, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var certificateStream = new System.IO.MemoryStream();

            byte[] byteInfo = Convert.FromBase64String(apiResponseOfCertificateInsurance.Data.Certificate);

            if (apiResponseOfCertificateInsurance.Data.ProviderId == ProviderType.EmaaTs.ToInt())
            {
                string decodedString = System.Text.Encoding.UTF8.GetString(byteInfo);

                byteInfo = Convert.FromBase64String(decodedString);
            }

            certificateStream.Write(byteInfo, 0, byteInfo.Length);

            certificateStream.Position = 0;


            var notificationApiRequest = new SendInsuranceNotificationApiRequest
            {
                PolicyAttachment = new List<byte[]> { certificateStream.ToArray() },
                LanguageId = languageId,
                ApplicationNo = id,
                IsNotificationEnabled = isNotificationEnabled,
                EmailList = new List<string> { apiResponse.Data.Email },
                UserId = UserSession.UserId
            };

            await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<CertificatePolicyApiResponse>>
                (notificationApiRequest, ApiMethodName.Appointment.ApplicationInsuranceNotification,
                    AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            return new FileStreamResult(certificateStream, "application/pdf");
        }

        public async Task<IActionResult> ClaimLossSend(string encryptedApplicationId, string gatewayServicePriceText,
            string visaPriceText, string gatewayServiceFeeCurrency, string claimNoText)
        {
            if (claimNoText == "null")
            {
                int id = encryptedApplicationId.ToDecryptInt();

                Decimal? gatewayServicePrice = Convert.ToDecimal(gatewayServicePriceText != null
                    ? gatewayServicePriceText.Replace('.', ',')
                    : "0");
                Decimal? visaPrice = Convert.ToDecimal(visaPriceText != "null" ? visaPriceText.Replace('.', '.') : "0");
                Decimal? claimAmountDecimal = new decimal(0);
                String claimAmount = "";

                if (gatewayServiceFeeCurrency != "USD")
                {
                    var getSapRateInformationFromInsert = await PortalHttpClientHelper
                        .GetAsync<ApiResponse<GetSapRateInformationResponse>>
                        (ApiMethodName.Appointment.GetSapRateInformationFromInsert + gatewayServiceFeeCurrency,
                            AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                        .ConfigureAwait(false);
                    if (getSapRateInformationFromInsert.Data.DailyRateCheck)
                    {
                        claimAmountDecimal =
                            (gatewayServicePrice * getSapRateInformationFromInsert.Data.RateInformation) +
                            (visaPrice * getSapRateInformationFromInsert.Data.RateInformation);
                        claimAmount = claimAmountDecimal.Value.ToString("0.00",
                            System.Globalization.CultureInfo.InvariantCulture);
                    }
                    else
                    {
                        await PortalHttpClientHelper
                            .GetAsync<ApiResponse<GetSapRateInformationResponse>>
                            (ApiMethodName.Accounting.GetSapRateInformation, AppSettings.PortalGatewayApiUrl,
                                PortalGatewayApiDefaultRequestHeaders)
                            .ConfigureAwait(false);

                        var newDetSapRateInformationFromInsert = await PortalHttpClientHelper
                            .GetAsync<ApiResponse<GetSapRateInformationResponse>>
                            (ApiMethodName.Appointment.GetSapRateInformationFromInsert + gatewayServiceFeeCurrency,
                                AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                            .ConfigureAwait(false);

                        if (newDetSapRateInformationFromInsert.Data.DailyRateCheck)
                        {
                            claimAmountDecimal =
                                (gatewayServicePrice * newDetSapRateInformationFromInsert.Data.RateInformation) +
                                (visaPrice * newDetSapRateInformationFromInsert.Data.RateInformation);
                            claimAmount = claimAmountDecimal.Value.ToString("0.00",
                                System.Globalization.CultureInfo.InvariantCulture);
                        }
                    }
                }
                else
                {
                    claimAmountDecimal = gatewayServicePrice + visaPrice;
                    claimAmount =
                        claimAmountDecimal.Value.ToString("0.00", System.Globalization.CultureInfo.InvariantCulture);
                }

                var apiRequest = new AddClaimLossEntryLog
                {
                    ApplicationId = id,
                    ClaimNo = "",
                    ClaimAmount = claimAmount,
                    ServiceResponse = "",
                    CreatedAt = DateTime.Now,
                    CreatedBy = UserSession.UserId
                };

                var apiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                    (apiRequest, ApiMethodName.Appointment.AddClaimLossEntryLog, AppSettings.PortalGatewayApiUrl,
                        PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                return Json(new ResultModel
                { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
            }
            else
            {
                return Json(new ResultModel
                { Message = SiteResources.ErrorOpenedBeforeClaimLoss, ResultType = ResultType.Danger });
            }
        }
        public async Task<IActionResult> PartialReleatedInvidualCreateInsurance(string encryptedApplicationId, string encryptedReleatedInsuranceApplicationId)
        {
            int id = encryptedApplicationId.ToDecryptInt();
            int releatedInsuranceId = encryptedReleatedInsuranceApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                 .GetAsync<ApiResponse<ApplicationApiResponse>>

                 (ApiMethodName.Appointment.GetApplicationInsuranceInformation + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                 .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            var apiResponse2 = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationRelatedServicesApiResponse>>
                    (ApiMethodName.Appointment.GetRelatedServicesApplications + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse2.Validate(out ResultModel result2).ConfigureAwait(false))
                return Json(result2);

            var viewModel = new CreateInsuranceViewModel
            {
                Id = id,
                ApplicationId = apiResponse.Data.Id,
                ApplicationNumber = apiResponse.Data.Id.ToApplicationNumber(),
                PassportNumber = apiResponse2.Data.RelatedServices.FirstOrDefault(q => q.Id == releatedInsuranceId).PassportNo,
                ReleatedInsuranceId = releatedInsuranceId,
                EntryDate = apiResponse.Data.ApplicationTime.DateTime,
                BranchCountryIso3 = apiResponse.Data.BranchCountryIso3,
                VisaCategoryId = apiResponse.Data.Document.VisaCategoryId,
                SelectedInsurance = new CreateInsuranceViewModel.Insurance()
            };


            if (apiResponse.Data.ExtraFees != null &&
                apiResponse.Data.ExtraFees.Any(p => p.Category == (int)ExtraFeeCategoryType.RelatedInsurance))
            {
                var policyName = "";

                policyName = apiResponse.Data.ExtraFees
                        .First(p => p.Category == (int)ExtraFeeCategoryType.RelatedInsurance).ExtraFeeName;


                var policyDays = apiResponse.Data.ExtraFees
                    .First(p => p.Category == (int)ExtraFeeCategoryType.RelatedInsurance)?.PolicyPeriod ?? 0;

                if (policyDays == 0)
                {
                    if (!string.IsNullOrEmpty(policyName))
                    {
                        var numberInPolicyNameString = string.Join("", policyName.ToCharArray().Where(char.IsDigit));
                        int.TryParse(numberInPolicyNameString, out int numberInPolicyName);

                        if (policyName.Contains("Opsiyonel Hizmet", StringComparison.OrdinalIgnoreCase) ||
                            policyName.Contains("Optional Service", StringComparison.OrdinalIgnoreCase))
                        {
                            policyDays = numberInPolicyName;
                        }
                    }
                }

                viewModel.SelectedInsurance.PolicyName = policyName;
                viewModel.SelectedInsurance.PolicyDays = policyDays;
                viewModel.ExitDate = viewModel.EntryDate.AddDays(policyDays);
            }

            return PartialView("_CreateInsurance", viewModel);
        }

        public async Task<IActionResult> PartialReleatedInvidualUpdateInsurance(string encryptedApplicationId, string encryptedReleatedInsuranceApplicationId)
        {
            int id = encryptedApplicationId.ToDecryptInt();
            int releatedInsuranceId = encryptedReleatedInsuranceApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>
                    (ApiMethodName.Appointment.GetApplicationInsuranceInformation + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            var apiResponse2 = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationRelatedServicesApiResponse>>
                    (ApiMethodName.Appointment.GetRelatedServicesApplications + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse2.Validate(out ResultModel result2).ConfigureAwait(false))
                return Json(result2);


            var viewModel = new CreateInsuranceViewModel
            {
                Id = id,
                ApplicationId = apiResponse.Data.Id,
                ApplicationNumber = apiResponse.Data.Id.ToApplicationNumber(),
                ReleatedInsuranceId = releatedInsuranceId,
                PassportNumber = apiResponse2.Data.RelatedServices.FirstOrDefault(q => q.Id == releatedInsuranceId).PassportNo,
                EntryDate = DateTime.Now,
                BranchCountryIso3 = apiResponse.Data.BranchCountryIso3,
                SelectedInsurance = new CreateInsuranceViewModel.Insurance()
            };


            if (apiResponse.Data.ExtraFees != null &&
                apiResponse.Data.ExtraFees.Any(p => p.Category == (int)ExtraFeeCategoryType.RelatedInsurance))
            {
                var policyName = "";

                policyName = apiResponse.Data.ExtraFees
                    .First(p => p.Category == (int)ExtraFeeCategoryType.RelatedInsurance).ExtraFeeName;


                var policyDays = apiResponse.Data.ExtraFees
                    .First(p => p.Category == (int)ExtraFeeCategoryType.RelatedInsurance)?.PolicyPeriod ?? 0;

                if (policyDays == 0)
                {
                    if (!string.IsNullOrEmpty(policyName))
                    {
                        var numberInPolicyNameString = string.Join("", policyName.ToCharArray().Where(char.IsDigit));
                        int.TryParse(numberInPolicyNameString, out int numberInPolicyName);

                        if (policyName.Contains("Opsiyonel Hizmet", StringComparison.OrdinalIgnoreCase) ||
                            policyName.Contains("Optional Service", StringComparison.OrdinalIgnoreCase))
                        {
                            policyDays = numberInPolicyName;
                        }
                    }
                }

                viewModel.SelectedInsurance.PolicyName = policyName;
                viewModel.SelectedInsurance.PolicyDays = policyDays;
                viewModel.ExitDate = viewModel.EntryDate.AddDays(policyDays);
            }

            return PartialView("_UpdateInsurance", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> CreateReleatedInsurance(CreateInsuranceViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel
                { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiResponseOfGetApplication = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>
                (ApiMethodName.Appointment.GetApplicationInsuranceInformation + viewModel.Id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponseOfGetApplication.Validate(out ResultModel resultOfGetApplication)
                    .ConfigureAwait(false))
                return Json(resultOfGetApplication);


            var apiResponse2 = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationRelatedServicesApiResponse>>
                    (ApiMethodName.Appointment.GetRelatedServicesApplications + viewModel.Id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse2.Validate(out ResultModel result2).ConfigureAwait(false))
                return Json(result2);


            if (apiResponse2.Data.RelatedServices.First(q => q.Id == viewModel.ReleatedInsuranceId).PhoneNumber.Count() < 7)
                return Json(new ResultModel
                { Message = EnumResources.PhoneNumberLenghtError, ResultType = ResultType.Danger });

            string visaCategoryQuestionsValue = "1";

            if (apiResponseOfGetApplication.Data.Document.VisaCategoryId == (int)VisaCategoryTypeEnum.TouristicResidencePermit)
                visaCategoryQuestionsValue = "4";
            else if (apiResponseOfGetApplication.Data.Document.VisaCategoryId == (int)VisaCategoryTypeEnum.VisitSpecialResidancePermit)
                visaCategoryQuestionsValue = "5";

            var apiRequest = new CreatePolicyApiRequest
            {
                UserId = UserSession.UserId,
                ApplicationId = viewModel.Id,
                StartDate = viewModel.EntryDate,
                EndDate = viewModel.ExitDate,
                BranchId = apiResponseOfGetApplication.Data.BranchId,
                VisaCategoryQuestionsValue = visaCategoryQuestionsValue,
                ReleatedInsuranceId = viewModel.ReleatedInsuranceId,
                ProviderId = ProviderType.EmaaRi.ToInt(), // Emaa Releated Insurance
                InsuredInfo = new CreatePolicyApiRequest.InsuredInformation()
                {
                    BirthDate = apiResponse2.Data.RelatedServices.First(q => q.Id == viewModel.ReleatedInsuranceId).BirthDate,
                    CountryCode = apiResponseOfGetApplication.Data.NationalityIso2,
                    FirstName = apiResponse2.Data.RelatedServices.First(q => q.Id == viewModel.ReleatedInsuranceId).Name,
                    SurName = apiResponse2.Data.RelatedServices.First(q => q.Id == viewModel.ReleatedInsuranceId).Surname,
                    PassportNo = apiResponse2.Data.RelatedServices.First(q => q.Id == viewModel.ReleatedInsuranceId).PassportNo,
                    Gender = apiResponseOfGetApplication.Data.GenderId,
                    GsmPhone = apiResponse2.Data.RelatedServices.First(q => q.Id == viewModel.ReleatedInsuranceId).PhoneNumber.Substring(0, 3) + "-" +
                               apiResponse2.Data.RelatedServices.First(q => q.Id == viewModel.ReleatedInsuranceId).PhoneNumber.Substring(3, 3) + "-" +
                               apiResponse2.Data.RelatedServices.First(q => q.Id == viewModel.ReleatedInsuranceId).PhoneNumber.Substring(6),
                    EmailAdress = apiResponse2.Data.RelatedServices.First(q => q.Id == viewModel.ReleatedInsuranceId).Email
                },
                ApplicationExtraFeeId = apiResponseOfGetApplication.Data.ExtraFees
                    .Where(q => q.Category == (int)ExtraFeeCategoryType.RelatedInsurance).Select(q => q.ApplicationExtraFeeId)
                    .FirstOrDefault()
            };

            var apiResponseOfCreatePolicy = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<CreatePolicyApiResponse>>
                (apiRequest, ApiMethodName.Insurance.CreatePolicy, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponseOfCreatePolicy.Validate(out ResultModel resultOfCreatePolicy).ConfigureAwait(false))
                return Json(resultOfCreatePolicy);

            return Json(new ResultModel
            { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpPost]
        public async Task<IActionResult> CancelReleatedInsurance(CreateInsuranceViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel
                { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiResponseOfGetApplication = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>

                (ApiMethodName.Appointment.GetApplicationInsuranceInformation + viewModel.Id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponseOfGetApplication.Validate(out ResultModel resultOfGetApplication)
                    .ConfigureAwait(false))
                return Json(resultOfGetApplication);

            var apiResponse2 = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationRelatedServicesApiResponse>>
                    (ApiMethodName.Appointment.GetRelatedServicesApplications + viewModel.Id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse2.Validate(out ResultModel result2).ConfigureAwait(false))
                return Json(result2);


            for (int i = 0; i < apiResponseOfGetApplication.Data.AllInsurance.Number.Count; i++)
            {

                if (apiResponseOfGetApplication.Data.AllInsurance.RelatedIndividualInsuraneId[i] == viewModel.ReleatedInsuranceId)
                {
                    var apiRequest = new CancelPolicyApiRequest
                    {
                        PolicyNo = apiResponseOfGetApplication.Data.AllInsurance.Number[i],
                        AppointmentId = viewModel.Id,
                        UserId = UserSession.UserId,
                        ProviderId = ProviderType.EmaaRi.ToInt()
                    };

                    var apiResponseOfCancelPolicy = await PortalHttpClientHelper
                        .PostAsJsonAsync<ApiResponse<CancelPolicyApiResponse>>
                        (apiRequest, ApiMethodName.Insurance.CancelPolicy, AppSettings.PortalGatewayApiUrl,
                            PortalGatewayApiDefaultRequestHeaders)
                        .ConfigureAwait(false);

                    var updateOldInsurancePassive = await PortalHttpClientHelper
                        .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                        (null,
                            $"{ApiMethodName.Appointment.UpdateOldInsurancePassive + viewModel.Id}/{apiResponseOfGetApplication.Data.AllInsurance.Number[i]}",
                            AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                        .ConfigureAwait(false);

                    if (!await apiResponseOfCancelPolicy.Validate(out ResultModel resultOfCancelPolicy)
                            .ConfigureAwait(false))
                        return Json(resultOfCancelPolicy);
                }
            }

            string visaCategoryQuestionsValue = "1";

            if (apiResponseOfGetApplication.Data.Document.VisaCategoryId == (int)VisaCategoryTypeEnum.TouristicResidencePermit)
                visaCategoryQuestionsValue = "4";
            else if (apiResponseOfGetApplication.Data.Document.VisaCategoryId == (int)VisaCategoryTypeEnum.VisitSpecialResidancePermit)
                visaCategoryQuestionsValue = "5";

            var relatedService = apiResponse2.Data.RelatedServices.FirstOrDefault(q => q.Id == viewModel.ReleatedInsuranceId);
       
            var relatedInsuranceApplicationInsuranceId = relatedService?.ApplicationInsuranceId;

            var apiRequestCreate = new CreatePolicyApiRequest
            {
                UserId = UserSession.UserId,
                OldCancelledInsuranceId = relatedInsuranceApplicationInsuranceId,
                ApplicationId = viewModel.Id,
                StartDate = viewModel.EntryDate,
                EndDate = viewModel.ExitDate,
                BranchId = apiResponseOfGetApplication.Data.BranchId,
                VisaCategoryQuestionsValue = visaCategoryQuestionsValue,
                ReleatedInsuranceId = viewModel.ReleatedInsuranceId,
                ProviderId = ProviderType.EmaaRi.ToInt(), // Emaa Releated Insurance
                InsuredInfo = new CreatePolicyApiRequest.InsuredInformation()
                {
                    BirthDate = apiResponse2.Data.RelatedServices.First(q => q.Id == viewModel.ReleatedInsuranceId).BirthDate,
                    CountryCode = apiResponseOfGetApplication.Data.NationalityIso2,
                    FirstName = apiResponse2.Data.RelatedServices.First(q => q.Id == viewModel.ReleatedInsuranceId).Name,
                    SurName = apiResponse2.Data.RelatedServices.First(q => q.Id == viewModel.ReleatedInsuranceId).Surname,
                    PassportNo = apiResponse2.Data.RelatedServices.First(q => q.Id == viewModel.ReleatedInsuranceId).PassportNo,
                    Gender = apiResponseOfGetApplication.Data.GenderId,
                    GsmPhone = apiResponse2.Data.RelatedServices.First(q => q.Id == viewModel.ReleatedInsuranceId).PhoneNumber.Substring(0, 3) + "-" +
                               apiResponse2.Data.RelatedServices.First(q => q.Id == viewModel.ReleatedInsuranceId).PhoneNumber.Substring(3, 3) + "-" +
                               apiResponse2.Data.RelatedServices.First(q => q.Id == viewModel.ReleatedInsuranceId).PhoneNumber.Substring(6),
                    EmailAdress = apiResponse2.Data.RelatedServices.First(q => q.Id == viewModel.ReleatedInsuranceId).Email
                },
                ApplicationExtraFeeId = apiResponseOfGetApplication.Data.ExtraFees
                    .Where(q => q.Category == (int)ExtraFeeCategoryType.RelatedInsurance).Select(q => q.ApplicationExtraFeeId)
                    .FirstOrDefault()
            };
            var apiResponseOfCreatePolicy = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<CreatePolicyApiResponse>>
                (apiRequestCreate, ApiMethodName.Insurance.CreatePolicy, AppSettings.PortalGatewayApiUrl,
                    PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponseOfCreatePolicy.Validate(out ResultModel resultOfCreatePolicy)
                    .ConfigureAwait(false))
                return Json(resultOfCreatePolicy);


            return Json(new ResultModel
            { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpGet]
        public async Task<IActionResult> CertificateReleatedInsurance(string encryptedApplicationId, string encryptedReleatedInsuranceApplicationId, int languageId,
            bool isNotificationEnabled)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var id = encryptedApplicationId.ToDecryptInt();
            int releatedInsuranceId = encryptedReleatedInsuranceApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>

                (ApiMethodName.Appointment.GetApplicationInsuranceInformation + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification",
                    new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Application", new { Area = "Appointment" });
            }

            var apiResponse2 = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationRelatedServicesApiResponse>>
                    (ApiMethodName.Appointment.GetRelatedServicesApplications + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse2.Validate(out ResultModel result2).ConfigureAwait(false))
                return Json(result2);


            var filePath_OutPdf = "";
            var filePath_backGroundTurkishPdf = "";
            var filePath_backGroundEnglishPdf = "";
            var filePath_backGroundArabicPdf = "";

            if (apiResponse.Data.Document.VisaCategoryId == (int)VisaCategoryTypeEnum.TouristicResidencePermit || apiResponse.Data.Document.VisaCategoryId == (int)VisaCategoryTypeEnum.VisitSpecialResidancePermit)
            {
				 filePath_backGroundTurkishPdf = Path.Combine(_webHostEnvironment.WebRootPath,
												"documents", "InsurancePolicy" ,"Tazminat_Talep_Bilgilendirme_Formu_Ikame.pdf");
				 filePath_backGroundEnglishPdf = Path.Combine(_webHostEnvironment.WebRootPath,
													"documents", "InsurancePolicy", "Tazminat_Talep_Bilgilendirme_Formu_English_Ikame.pdf");
				filePath_backGroundArabicPdf = Path.Combine(_webHostEnvironment.WebRootPath,
												  "documents", "InsurancePolicy", "Tazminat_Talep_Bilgilendirme_Formu_Arabic.pdf");
			}
            else
            {
				 filePath_backGroundTurkishPdf = Path.Combine(_webHostEnvironment.WebRootPath,
												"documents", "InsurancePolicy", "Tazminat_Talep_Bilgilendirme_Formu.pdf");
				 filePath_backGroundEnglishPdf = Path.Combine(_webHostEnvironment.WebRootPath,
													"documents", "InsurancePolicy" ,"Tazminat_Talep_Bilgilendirme_Formu_English.pdf");
				 filePath_backGroundArabicPdf = Path.Combine(_webHostEnvironment.WebRootPath,
												   "documents", "InsurancePolicy", "Tazminat_Talep_Bilgilendirme_Formu_Arabic.pdf");
			}

            filePath_OutPdf = languageId switch
            {
                10 => filePath_backGroundTurkishPdf,
                11 => filePath_backGroundEnglishPdf,
                12 => filePath_backGroundArabicPdf,
                20 => filePath_backGroundTurkishPdf,
                21 => filePath_backGroundEnglishPdf,
                22 => filePath_backGroundArabicPdf,
                _ => filePath_OutPdf
            };

            var mergeStream = new MemoryStream();
            var finalStream = new MemoryStream();
            PdfCopyFields copy = new PdfCopyFields(finalStream);

            for (var i = 0; i < apiResponse.Data.AllInsurance.Number.Count; i++)
            {
                if (apiResponse.Data.AllInsurance.RelatedIndividualInsuraneId[i] == releatedInsuranceId)
                {
                    int firstLanguageId = languageId;

                    if (i != apiResponse.Data.AllInsurance.Number.Count - 1)
                    {
                        if (languageId == 20)
                            firstLanguageId = 10;
                        else if (languageId == 21)
                            firstLanguageId = 11;
                        else if (languageId == 22)
                            firstLanguageId = 12;
                    }

                    var apiRequest = new CertificatePolicyApiRequest()
                    {
                        PolicyNumber = Convert.ToInt64(apiResponse.Data.AllInsurance.Number[i]),
                        LanguageId = firstLanguageId,
                        ApplicationId = id,
                        IsNotificationEnabled = isNotificationEnabled,
                        UserId = UserSession.UserId,
                        ProviderId = ProviderType.EmaaRi.ToInt()
                    };

                    var apiResponseOfCertificateInsurance = await PortalHttpClientHelper
                        .PostAsJsonAsync<ApiResponse<CertificatePolicyApiResponse>>
                        (apiRequest, ApiMethodName.Insurance.CertificatePolicy, AppSettings.PortalGatewayApiUrl,
                            PortalGatewayApiDefaultRequestHeaders)
                        .ConfigureAwait(false);

                    if (!await apiResponseOfCertificateInsurance.Validate(out ResultModel resultOfCertificateInsurance)
                            .ConfigureAwait(false))
                    {
                        TempData.Put("Notification",
                            new ResultModel
                            { Message = resultOfCertificateInsurance.Message, ResultType = ResultType.Danger });
                        return RedirectToAction("List", "Application", new { Area = "Appointment" });
                    }

                    var certificateStream = new MemoryStream();

                    var byteInfo = Convert.FromBase64String(apiResponseOfCertificateInsurance.Data.Certificate);

                    if (apiResponseOfCertificateInsurance.Data.ProviderId == ProviderType.Emaa.ToInt() || apiResponseOfCertificateInsurance.Data.ProviderId == ProviderType.EmaaRi.ToInt())
                    {
                        var decodedString = System.Text.Encoding.UTF8.GetString(byteInfo);

                        byteInfo = Convert.FromBase64String(decodedString);
                    }

                    certificateStream.Write(byteInfo, 0, byteInfo.Length);
                    certificateStream.Position = 0;
                    copy.AddDocument(new PdfReader(certificateStream));
                    certificateStream.Dispose();
                }
            }

            var pdfStream = new MemoryStream(System.IO.File.ReadAllBytes(filePath_OutPdf));
            pdfStream.Position = 0;
            copy.AddDocument(new PdfReader(pdfStream));
            pdfStream.Dispose();
            copy.Close();
            mergeStream.Write(finalStream.GetBuffer(), 0, finalStream.ToArray().Length);
            mergeStream.Position = 0;

            var notificationApiRequest = new SendInsuranceNotificationApiRequest
            {
                PolicyAttachment = new List<byte[]> { mergeStream.ToArray() },
                LanguageId = languageId,
                ApplicationNo = id,
                IsNotificationEnabled = isNotificationEnabled,
                EmailList = new List<string> { apiResponse2.Data.RelatedServices.FirstOrDefault(q => q.Id == releatedInsuranceId).Email },
                BranchEmailProviderId = apiResponse.Data.BranchEmailProviderId,
                BranchSmsProviderId = apiResponse.Data.BranchSmsProviderId,
                UserId = UserSession.UserId
            };

            await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<CertificatePolicyApiResponse>>
                (notificationApiRequest, ApiMethodName.Appointment.ApplicationInsuranceNotification,
                    AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            return File(mergeStream, "application/pdf");
        }

        public async Task<IActionResult> CreateInsuranceWithPrint(string encryptedApplicationId)
        {
            int id = encryptedApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>
                (ApiMethodName.Appointment.GetApplicationInsuranceInformation + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            var viewModel = new CreateInsuranceViewModel
            {
                Id = id,
                ApplicationId = apiResponse.Data.Id,
                ApplicationNumber = apiResponse.Data.Id.ToApplicationNumber(),
                EntryDate = apiResponse.Data.ApplicationTime.DateTime,
                BranchCountryIso3 = apiResponse.Data.BranchCountryIso3,
                VisaCategoryId = apiResponse.Data.Document.VisaCategoryId,
                SelectedInsurance = new CreateInsuranceViewModel.Insurance()
            };

            var policyName = "";
            int policyDays = 0;

            if (apiResponse.Data.ExtraFees != null && apiResponse.Data.ExtraFees.Any(a => a.Category == (int)ExtraFeeCategoryType.Insurance || a.Category == (int)ExtraFeeCategoryType.TS))
            {
                if (apiResponse.Data.ExtraFees.Any(a => a.Category == (int)ExtraFeeCategoryType.Insurance)) //Insurance
                {
                    if (apiResponse.Data.ExtraFees.OrderByDescending(o => o.CreatedAt).FirstOrDefault(f => f.Category == (int)ExtraFeeCategoryType.Insurance)?.CreatedAt >= apiResponse.Data.Insurance?.CreatedAt) //Update
                        viewModel.EntryDate = DateTime.Now;

                    policyName = apiResponse.Data.ExtraFees.First(p => p.Category == (int)ExtraFeeCategoryType.Insurance).ExtraFeeName;
                    policyDays = apiResponse.Data.ExtraFees.First(p => p.Category == (int)ExtraFeeCategoryType.Insurance).PolicyPeriod ?? 0;

                    if (policyDays == 0 || !string.IsNullOrEmpty(policyName))
                        policyDays = GetPolicyDaysFromPolicyName(policyName);

                    viewModel.SelectedInsurance.PolicyName = policyName;
                    viewModel.SelectedInsurance.PolicyDays = policyDays;
                    viewModel.ExitDate = viewModel.EntryDate.AddDays(policyDays);

                    if (apiResponse.Data.ExtraFees.OrderByDescending(o => o.CreatedAt).FirstOrDefault(f => f.Category == (int)ExtraFeeCategoryType.Insurance)?.CreatedAt >= apiResponse.Data.Insurance?.CreatedAt) //Update
                    {
                        viewModel.EntryDate = DateTime.Now;

                        await CancelInsurance(viewModel);
                    }
                    else
                    {
                        viewModel.ShowMontageVisaType = apiResponse.Data.ShowMontageVisaType;

                        await CreateInsurance(viewModel);
                    }
                }
                else if (apiResponse.Data.ExtraFees != null && apiResponse.Data.ExtraFees.Any(a => a.Category == (int)ExtraFeeCategoryType.TS) && apiResponse.Data.Insurance == null) //TS
                {
                    policyName = apiResponse.Data.ExtraFees.First(p => p.Category == (int)ExtraFeeCategoryType.TS).ExtraFeeName;
                    policyDays = apiResponse.Data.ExtraFees.First(p => p.Category == (int)ExtraFeeCategoryType.TS).PolicyPeriod ?? 0;

                    if (policyDays == 0)
                    {
                        var numberInPolicyNameString = string.Join("", policyName.ToCharArray().Where(char.IsDigit));

                        if (int.TryParse(numberInPolicyNameString, out int numberInPolicyName)
                            && (policyName.Contains("Gün", StringComparison.OrdinalIgnoreCase) || policyName.Contains("Day", StringComparison.OrdinalIgnoreCase)))
                        {
                            switch (numberInPolicyName)
                            {
                                case 090: //0-90
                                    policyDays = 90;
                                    break;
                                case 91180: //91-180
                                    policyDays = 180;
                                    break;
                                case 181270: //181-270
                                    policyDays = 270;
                                    break;
                                case 271: //271
                                    policyDays = 360;
                                    break;
                                default:
                                    break;
                            }                            
                        }
                    }

                    viewModel.SelectedInsurance.PolicyName = policyName;
                    viewModel.SelectedInsurance.PolicyDays = policyDays;
                    viewModel.ExitDate = viewModel.EntryDate.AddDays(policyDays);

                    await TsCreateInsurance(viewModel);
                }
            }

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #region Private Methods

        private int GetPolicyDaysFromPolicyName(string policyName)
        {
            int policyDays = 0;
            int numberInPolicyName = ExtractNumberFromPolicyName(policyName);

            // Check for "Service Fee" policies
            if (policyName.Contains("SERVICE FEE", StringComparison.OrdinalIgnoreCase) ||
                policyName.Contains("Online Service Fee", StringComparison.OrdinalIgnoreCase) ||
                policyName.Contains("Offline Service Fee", StringComparison.OrdinalIgnoreCase) ||
                policyName.Contains("Service Fee", StringComparison.OrdinalIgnoreCase))
            {
                if (policyName.Equals("SERVICE FEE", StringComparison.OrdinalIgnoreCase) ||
                    policyName.Equals("Service Fee", StringComparison.OrdinalIgnoreCase))
                {
                    policyDays = 364;
                }
                else if (policyName.Equals("Online Service Fee", StringComparison.OrdinalIgnoreCase) ||
                         policyName.Equals("Offline Service Fee", StringComparison.OrdinalIgnoreCase))
                {
                    policyDays = 179;
                }
                else
                {
                    policyDays = GetDaysBasedOnNumber(numberInPolicyName);
                }
            }

            // Check for "Year" or "Month" policies
            else if (policyName.Contains("Yıl", StringComparison.OrdinalIgnoreCase) ||
                     policyName.Contains("Year", StringComparison.OrdinalIgnoreCase) ||
                     policyName.Contains("Ay", StringComparison.OrdinalIgnoreCase) ||
                     policyName.Contains("Month", StringComparison.OrdinalIgnoreCase))
            {
                if (policyName.Contains("Yıl", StringComparison.OrdinalIgnoreCase) ||
                    policyName.Contains("Year", StringComparison.OrdinalIgnoreCase))
                {
                    policyDays = 364 * numberInPolicyName;
                }
                else
                {
                    policyDays = GetDaysBasedOnNumber(numberInPolicyName);
                }
            }

            // Check for "Days" policies
            else if (policyName.Contains("Günlük", StringComparison.OrdinalIgnoreCase) ||
                     policyName.Contains("Days", StringComparison.OrdinalIgnoreCase))
            {
                policyDays = GetDaysPolicyDays(numberInPolicyName);
            }

            return policyDays;
        }

        private int ExtractNumberFromPolicyName(string policyName)
        {
            var numberInPolicyNameString = string.Join("", policyName.ToCharArray().Where(char.IsDigit));
            int.TryParse(numberInPolicyNameString, out int numberInPolicyName);
            return numberInPolicyName;
        }

        private int GetDaysBasedOnNumber(int numberInPolicyName)
        {
            switch (numberInPolicyName)
            {
                case 1: return 29;
                case 2: return 59;
                case 3: return 89;
                case 6: return 179;
                case 12: return 364;
                default: return 0;
            }
        }

        private int GetDaysPolicyDays(int numberInPolicyName)
        {
            switch (numberInPolicyName)
            {
                case 18: return 8;       //1-8
                case 915: return 15;      //9-15
                case 1631: return 31;    //16-31
                case 3262: return 62;    //32-62
                case 6392: return 92;    //63-92
                case 93180: return 180;  //93-180
                case 181365: return 365; //181-365
                default: return 0;
            }
        }

        #endregion
    }
}