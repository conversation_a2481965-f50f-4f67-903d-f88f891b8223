﻿using Gateway.ObjectStoring;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Agency.AgencyFile;
using Portal.Gateway.ApiModel.Requests.DocumentManagement;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Agency.Agency;
using Portal.Gateway.ApiModel.Responses.Agency.AgencyFile;
using Portal.Gateway.ApiModel.Responses.DocumentManagement;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Agency.ViewModels.AgencyFile;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Agency.Controllers
{
    [Area("Agency")]
    public class AgencyFileController : BaseController<AgencyFileController>
    {
        private readonly IFileStorage _fileStorage;

        public AgencyFileController(
          IOptions<AppSettings> appSettings,
          ICacheHelper cacheHelper,
          IFileStorage fileStorage,IConfiguration configuration)
        : base(appSettings, cacheHelper)
        {
            _fileStorage = fileStorage;
        }

        #region AddUpdate

        public IActionResult PartialAddAgencyFile(string encryptedAgencyId, string encryptedAgencyTypeFileId)
        {
            if (string.IsNullOrEmpty(encryptedAgencyId) || string.IsNullOrEmpty(encryptedAgencyTypeFileId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("/Agency/Agency/List");
            }

            var viewModel = new AddUpdateAgencyFileViewModel();
            viewModel.EncryptedAgencyId = encryptedAgencyId;
            viewModel.EncryptedAgencyTypeFileId = encryptedAgencyTypeFileId;
            viewModel.FileSessionId = Guid.NewGuid().ToString();

            return PartialView("_AddUpdateAgencyFile", viewModel);
        }

        public async Task<IActionResult> PartialUpdateAgencyFile(string encryptedAgencyId, string encryptedAgencyTypeFileId)
        {
            if (string.IsNullOrEmpty(encryptedAgencyId) || string.IsNullOrEmpty(encryptedAgencyTypeFileId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("/Agency/Agency/List");
            }

            var apiRequest = new AgencyFileApiRequest
            {
                AgencyId = encryptedAgencyId.ToDecryptInt(),
                AgencyTypeFileId = encryptedAgencyTypeFileId.ToDecryptInt()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AgencyFileApiResponse>>
                (apiRequest, ApiMethodName.Agency.GetAgencyFile, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            var viewModel = new AddUpdateAgencyFileViewModel();

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            viewModel.EncryptedAgencyId = encryptedAgencyId;
            viewModel.EncryptedAgencyTypeFileId = encryptedAgencyTypeFileId;
            viewModel.FileSessionId = Guid.NewGuid().ToString();

            var documentsApiRequest = new DocumentsApiRequest
            {
                Ids = new List<int> { encryptedAgencyId.ToDecryptInt() },
                IsFileContentIncluded = false
            };

            var getDocumentsApiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<DocumentsApiResponse>>
                (documentsApiRequest, ApiMethodName.DocumentManagement.GetDocuments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await getDocumentsApiResponse.Validate(out ResultModel getDocumentsResult).ConfigureAwait(false))
                return Json(getDocumentsResult);

            viewModel.FileName = getDocumentsApiResponse.Data.Documents.FirstOrDefault()?.OriginalFileName;

            return PartialView("_AddUpdateAgencyFile", viewModel);
        }


        [HttpPost]
        public async Task<IActionResult> SessionUploadFile([FromForm] IFormFile file, string fileSessionId)
        {
            if (file != null && !string.IsNullOrEmpty(fileSessionId))
            {
                using (var ms = new MemoryStream())
                {
                    file.CopyTo(ms);
                    await _fileStorage.SaveFileAsync(FileStorage.FileStorageBucketName, $"{FileStorage.SessionUploadFileFolderName}/{fileSessionId}", ms, "");
                }

                var fileModel = FileHelper.GetFileInfo(file);
                Extensions.SessionExtensions.Set(HttpContext.Session, $"SessionUploadFile{fileSessionId}", fileModel);
            }

            return Content("");
        }

        [HttpPost]
        public async Task<IActionResult> AddUpdateAgencyFile(AddUpdateAgencyFileViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var fileModel = Extensions.SessionExtensions.Get<FileModel>(HttpContext.Session, $"SessionUploadFile{viewModel.FileSessionId}");
            
            if (fileModel == null)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            byte[] bytes;
            using (var ms = new MemoryStream())
            {
                var stream = await _fileStorage.GetFileStreamAsync(FileStorage.FileStorageBucketName, $"{FileStorage.SessionUploadFileFolderName}/{viewModel.FileSessionId}");
                await stream.CopyToAsync(ms);
                bytes = ms.ToArray();
            }

            var addDocumentsApiRequest = new AddDocumentsApiRequest { Documents = new List<AddDocumentApiRequest>() };

            var document = new AddDocumentApiRequest
            {
                DocumentTypeId = (int)DocumentType.Agency,
                ReferenceId = viewModel.EncryptedAgencyId.ToDecryptInt(),
                UploadPath = $"{DocumentType.Agency}/{viewModel.EncryptedAgencyId}",
                FileName = viewModel.FileName,
                FileExtension = fileModel.FileExtension,
                FileContent = bytes,
                CreatedBy = UserSession.UserId
            };

            addDocumentsApiRequest.Documents.Add(document);

            var addDocumentsApiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddMultipleApiResponse>>
                (addDocumentsApiRequest, ApiMethodName.DocumentManagement.AddDocuments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await addDocumentsApiResponse.Validate(out ResultModel addDocumentsResult).ConfigureAwait(false))
                return Json(addDocumentsResult);

            var addUpdateAgencyFileApiRequest = new AddUpdateAgencyFileApiRequest
            {
                AgencyId = viewModel.EncryptedAgencyId.ToDecryptInt(),
                AgencyTypeFileId = viewModel.EncryptedAgencyTypeFileId.ToDecryptInt(),
                DocumentId = addDocumentsApiResponse.Data.Ids.First()
            };

            var AddUpdateAgencyFileApiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (addUpdateAgencyFileApiRequest, ApiMethodName.Agency.AddUpdateAgencyFile, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await AddUpdateAgencyFileApiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region List

        public async Task<IActionResult> List(string encryptedAgencyId)
        {
            if (string.IsNullOrEmpty(encryptedAgencyId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Agency", new { Area = "Agency" });
            }

            int agencyId = encryptedAgencyId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<AgencyApiResponse>>
                (ApiMethodName.Agency.GetAgency + agencyId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", result);
                return RedirectToAction("List", "Agency", new { Area = "Agency" });
            }

            var agencyFileViewModel = new AgencyFileViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Name = apiResponse.Data.Name
            };

            ViewData["AgencyFile_List"] = agencyFileViewModel;
            return View();
        }

        public async Task<IActionResult> GetPaginatedAgencyFiles([DataSourceRequest] DataSourceRequest request, FilterAgencyFileViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedAgencyFilesApiRequest
            {
                AgencyId = filterViewModel.EncryptedAgencyId.ToDecryptInt(),
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<AgencyFilesApiResponse>>>
                (apiRequest, ApiMethodName.Agency.GetPaginatedAgencyFiles, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<AgencyFileItemViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().AgencyFiles
                    .Select(p => new AgencyFileItemViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        EncryptedAgencyId = p.AgencyId.ToEncrypt(),
                        EncryptedAgencyTypeFileId = p.AgencyTypeFileId.ToEncrypt(),
                        AgencyTypeFileTypeId = p.AgencyTypeFileTypeId,
                        EncryptedDocumentId = p.DocumentId.ToEncrypt(),
                        IsUploaded = p.IsUploaded,
                        AgencyTypeFileType = p.AgencyTypeFileType
                    }).ToList();
            }

            if (paginatedData.Select(p => p.EncryptedDocumentId).Any())
            {
                var documentsApiRequest = new DocumentsApiRequest
                {
                    Ids = paginatedData.Select(p => p.EncryptedDocumentId.ToDecryptInt()).ToList(),
                    IsFileContentIncluded = false
                };

                var getDocumentsApiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<DocumentsApiResponse>>
                    (documentsApiRequest, ApiMethodName.DocumentManagement.GetDocuments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                if (!await getDocumentsApiResponse.Validate(out ResultModel getDocumentsResult).ConfigureAwait(false))
                    return Json(getDocumentsResult);

                foreach (var item in paginatedData)
                {
                    var document = getDocumentsApiResponse.Data.Documents.FirstOrDefault(p => p.Id == item.EncryptedDocumentId.ToDecryptInt());

                    if (document != null)
                    {
                        item.OriginalFileName = document.OriginalFileName;
                        item.UniqueFileName = document.UniqueFileName;
                        item.FileExtension = document.FileExtension;
                    }
                }
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteAgencyFile(string encryptedAgencyFileId)
        {
            int id = encryptedAgencyFileId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Agency.DeleteAgencyFile + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Download

        public async Task<IActionResult> DownloadFile(string encryptedDocumentId)
        {
            var documentsApiRequest = new DocumentsApiRequest
            {
                Ids = new List<int> { encryptedDocumentId.ToDecryptInt() },
                IsFileContentIncluded = true
            };

            var getDocumentsApiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<DocumentsApiResponse>>
                (documentsApiRequest, ApiMethodName.DocumentManagement.GetDocuments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await getDocumentsApiResponse.Validate(out ResultModel getDocumentsResult).ConfigureAwait(false))
                return Json(null);

            var document = getDocumentsApiResponse.Data.Documents.FirstOrDefault();

            if (document == null || document.FileContent == null)
                return Json(null);

            return File(document.FileContent, "application/octet-stream", document.UniqueFileNameWithExtension);
        }

        #endregion
    }
}
