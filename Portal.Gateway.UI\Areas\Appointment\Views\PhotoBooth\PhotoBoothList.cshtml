﻿@using Microsoft.AspNetCore.Http
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using Microsoft.Extensions.Caching.Memory
@using Portal.Gateway.ApiModel.Responses.Management.RoleAction
@using SessionExtensions = Portal.Gateway.UI.Extensions.SessionExtensions
@using Kendo.Mvc.TagHelpers
@using Portal.Gateway.UI.Constants
@using Portal.Gateway.UI.Models
@inject IHttpContextAccessor HttpContextAccessor
@inject ICacheHelper CacheHelper
@{
	var currentUser = SessionExtensions.Get<UserModel>(HttpContextAccessor.HttpContext?.Session, SessionKeys.UserSession);
	var isAuthorizedForDeletePhotoBooth = false;
	var roleActions = await CacheHelper.GetRoleActionsAsync();
	isAuthorizedForDeletePhotoBooth = roleActions.RoleActionSites.Where(r => currentUser.RoleIds.Contains(r.Role.Id))
		.Any(p => p.RoleActions.Any(q => q.Action.ActionTranslations.Any(r => r.Name == SiteResources.isAuthorizedForDeletePhotoBooth) && q.Action.IsActive));

	ViewData["Title"] = @SiteResources.ReferencedPhotoBoothOperations.ToTitleCase();
}

@model FilterPhotoBoothViewModel

<form id="report-form" class="card card-custom card-stretch" method="post">
	<div class="card card-custom card-stretch">
		<div class="card-header">
			<div class="card-title">
				<h3 class="card-label">
					@SiteResources.ReferencedPhotoBoothOperations.ToTitleCase()
				</h3>
			</div>
			<div class="card-toolbar">
				<div class="btn-group">
					<button type="submit" class="btn btn-outline-primary font-weight-bold mr-2" asp-action="GetPhotoBoothReport" asp-area="Appointment" asp-controller="PhotoBooth">
						<i class="la la-list-alt"></i> @SiteResources.ExcelExport.ToTitleCase()
					</button>
				</div>
			</div>
		</div>
		<div class="card-body">
			@Html.HiddenFor(m => m.IsReferenced)
			@Html.HiddenFor(m => m.IsPhotoBoothReferenced)
			@Html.HiddenFor(m => m.IsPageAuthorized)
			@Html.HiddenFor(m => m.BranchId)
			<div class="form-group row">
				<div class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.ApplicationStatus.ToTitleCase()</label>
					@(Html.Kendo().DropDownListFor(m => m.Status)
						.HtmlAttributes(new { @class = "form-control" })
						.Filter(FilterType.Contains)
						.OptionLabel(SiteResources.Select)
						.DataTextField("Text")
						.DataValueField("Value")
						.DataSource(source =>
						{
							source.Read(read =>
							{
								read.Action("GetPhotoBoothStatusSelectList", "Parameter", new { Area = "" });
							});
						}))
				</div>
				<div class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.Name.ToTitleCase()</label>
					<input type="text" asp-for="FilterName" class="form-control" />
				</div>
				<div class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.Surname.ToTitleCase()</label>
					<input type="text" asp-for="FilterSurname" class="form-control" />
				</div>
				<div class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.PassportNumber.ToTitleCase()</label>
					<input type="text" asp-for="FilterPassportNumber" class="form-control" />
				</div>
				<div class="col-lg-3 col-md-6">
					@(Html.Kendo().CheckBoxFor(m => m.FilterExpired).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.ExpiredPhotoBooth.ToTitleCase()))
				</div>
				<div class="col-lg-3 col-md-6">
					@(Html.Kendo().CheckBoxFor(m => m.FilterUsed).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.UsedPhotoBooth.ToTitleCase()))
				</div>
				<div class="col-lg-3 col-md-6">
					@(Html.Kendo().CheckBoxFor(m => m.FilterDeleted).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.DeletedPhotoBooth.ToTitleCase()))

				</div>
				<div class="col-lg-3 col-md-6">
					@(Html.Kendo().CheckBoxFor(m => m.FilterBadShot).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.BadShotPhotoBooth.ToTitleCase()))
				</div>
			</div>
		</div>
		<div class="form-group row">
			@{
				if (Model.IsPageAuthorized)
				{
					<div class="col-lg-3" style="margin-bottom: 12px; height: 14px !important; align-self: flex-end;">
						@(Html.Kendo().CheckBoxFor(m => m.FilterShowPastDays).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.ShowPast.ToTitleCase()))
					</div>
				}
			}
			<div class="col-lg-3 col-md-6 filterDays" style="display: none">
				<label class="font-weight-bold">@SiteResources.StartDate.ToTitleCase()</label>
				@(Html.Kendo().DatePickerFor(m => m.FilterStartDate).Format(SiteResources.DatePickerFormatView))
			</div>
			<div class="col-lg-3 col-md-6 filterDays" style="display: none">
				<label class="font-weight-bold">@SiteResources.EndDate.ToTitleCase()</label>
				@(Html.Kendo().DatePickerFor(m => m.FilterEndDate).Format(SiteResources.DatePickerFormatView))
			</div>
		</div>
		<div class="form-group row">
			<div class="col-lg-6">
				<div>
					<button id="filterGridPhotoBooth" type="button" class="btn btn-primary mr-1"><i class="la la-search"></i>@SiteResources.Filter</button>
					<button id="clearGridPhotoBoothFilter" type="reset" class="btn btn-secondary mr-1"><i class="la la-times"></i>@SiteResources.Clear</button>
				</div>
			</div>
		</div>
		<div class="form-group row">
			<div class="col-lg-12">
				@(Html.Kendo().Grid<PhotoBoothViewModel>()
					.Name("gridPhotoBooth")
					.Columns(columns =>
					{
						columns.Bound(o => o.EncryptedId).Visible(false);
						columns.Bound(o => o.EncryptedApplicationId).Visible(false);
						columns.Bound(o => o.Name).Title(SiteResources.Name.ToTitleCase());
						columns.Bound(o => o.Surname).Title(SiteResources.Surname.ToTitleCase());
						columns.Bound(o => o.PassportNumber).Title(SiteResources.PassportNumber.ToTitleCase()).Media("lg");
						columns.Bound(o => o.Price).Title(SiteResources.Price.ToTitleCase()).Media("lg");
						columns.Bound(o => o.Currency).Title(SiteResources.Currency.ToTitleCase()).Media("lg");
						columns.Bound(o => o.Status).Title(SiteResources.Status.ToTitleCase()).Media("lg");
						columns.Bound(o => o.EncryptedId).ClientTemplateId("gridOperationsTemplate").Title(SiteResources.Operations).Width(150).Exportable(false);
					})
					.DataSource(dataSource => dataSource
					.Ajax()
					.Read(read => read.Action("GetPaginatedPhotoBooths", "PhotoBooth", new { Area = "Appointment" }).Data("gridPhotoBoothFilterData"))
					.PageSize(10)
					)
					.Scrollable(s => s.Height("auto"))
					.Pageable(pager => pager
					.Refresh(true)
					.PageSizes(new[] { 10, 20, 50 }))
					)
			</div>
		</div>
	</div>
</form>

<div class="modal fade" id="modalAddPhotoBooth" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
	<div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title">@SiteResources.AddPhotoBooth.ToTitleCase()</h5>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<i aria-hidden="true" class="ki ki-close"></i>
				</button>
			</div>
			<div class="modal-body">
				<div id="divPartialAddPhotoBooth"></div>
			</div>
		</div>
	</div>
</div>

@section Scripts {
	<script src="~/js/Appointment/PhotoBooth/photoBooth.js"></script>

    <script id="gridOperationsTemplate" type="text/x-kendo-tmpl">
		<div class="btn-group">
			<button class="btn btn-sm btn-light-primary font-weight-bold btn-sm dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-placement="right">
		@Html.Raw(SiteResources.Select)
			</button>
			<div class="dropdown-menu dropdown-menu-right">
				#if(Status=="Kullanıldı" || Status=="Used"){#
					<a class='dropdown-item' href='javascript:void(0);' onclick="badShotPhotoBooth('#=EncryptedId#');">@Html.Raw(SiteResources.BadShotPhotoBooth)</a>
				#}#
				#if(Status=="Alındı" || Status=="Ordered"){#
					<a class='dropdown-item' href='javascript:void(0);' onclick="serviceErrorPhotoBooth('#=EncryptedId#');">@Html.Raw(SiteResources.ServiceError)</a>
				
					#if('@isAuthorizedForDeletePhotoBooth'.toLowerCase() == "true"){#
						 <a class='dropdown-item' href='javascript:void(0);' onclick="deleteOrderedPhotoBooth('#=EncryptedId#');">@Html.Raw(SiteResources.Delete)</a>
				    #}#
				#}else{#
					#if (IsAvailable == false && IsNewStatus == false) { #
					    <a class='dropdown-item' href='javascript:void(0);' onclick="deletePhotoBooth('#=EncryptedId#');">@Html.Raw(SiteResources.Delete)</a>
				    #}#
				#}#
			</div>
		</div>
	
    </script>

	<script>
		var showPastCheckbox = document.getElementById('FilterShowPastDays');
		showPastCheckbox.addEventListener('change', function (element) {
			if ($("#FilterShowPastDays").is(":checked")) {
				$(".filterDays").show();
			}
			else {
				$(".filterDays").hide();
			}
		});
	</script>

	<script>
		var clearButtonCheck = false;
		$("#clearGridPhotoBoothFilter").click(function (e) {
			$("#FilterName").val('');
			$("#FilterSurname").val('');
			$("#FilterPassportNumber").val('');
			$("#FilterStartDate").val('');
			$("#FilterEndDate").val('');
			$("input[name*='FilterShowPastDays']").prop('checked', false);
			$("#Status").data("kendoDropDownList").select(null);
			clearButtonCheck = true;
			$("#FilterExpired").prop('disabled', false);
			$("#FilterUsed").prop('disabled', false);
			$("#FilterDeleted").prop('disabled', false);
			$("#FilterBadShot").prop('disabled', false);
			$("input[name*='FilterExpired']").prop('checked', false);
			$("input[name*='FilterUsed']").prop('checked', false);
			$("input[name*='FilterDeleted']").prop('checked', false);
			$("input[name*='FilterBadShot']").prop('checked', false);
            $('#gridPhotoBooth').data('kendoGrid').dataSource.read();
            $('#gridPhotoBooth').data('kendoGrid').refresh();
			clearButtonCheck = false;
		});


		$(function () {
			$('#FilterExpired').change(function () {
				if (!clearButtonCheck)
					checkFilterExpired();
			});
			$('#FilterUsed').change(function () {
				if (!clearButtonCheck)
					checkFilterUsed();
			});
			$('#FilterDeleted').change(function () {
				if (!clearButtonCheck)
					checkFilterDeleted();
			});

			$('#FilterBadShot').change(function () {
				if (!clearButtonCheck)
					checkFilterBadShot();
			});

		});

		function checkFilterExpired() {
			if ($('#FilterExpired').is(":checked")) {
				$("#FilterUsed").prop('disabled', true);
				$("#FilterDeleted").prop('disabled', true);
				$("#FilterBadShot").prop('disabled', true);
			}
			else {
				$("#FilterUsed").prop('disabled', false);
				$("#FilterDeleted").prop('disabled', false);
				$("#FilterBadShot").prop('disabled', false);
			}
		}
		function checkFilterUsed() {
			if ($('#FilterUsed').is(":checked")) {
				$("#FilterExpired").prop('disabled', true);
				$("#FilterDeleted").prop('disabled', true);
				$("#FilterBadShot").prop('disabled', true);
			}
			else {
				$("#FilterExpired").prop('disabled', false);
				$("#FilterDeleted").prop('disabled', false);
				$("#FilterBadShot").prop('disabled', false);
			}
		}

		function checkFilterDeleted() {
			if ($('#FilterDeleted').is(":checked")) {
				$("#FilterExpired").prop('disabled', true);
				$("#FilterBadShot").prop('disabled', true);
				$("#FilterUsed").prop('disabled', true);
			}
			else {
				$("#FilterExpired").prop('disabled', false);
				$("#FilterBadShot").prop('disabled', false);
				$("#FilterUsed").prop('disabled', false);
			}
		}

		function checkFilterBadShot() {
			if ($('#FilterBadShot').is(":checked")) {
				$("#FilterDeleted").prop('disabled', true);
				$("#FilterUsed").prop('disabled', true);
				$("#FilterExpired").prop('disabled', true);
			}
			else {
				$("#FilterDeleted").prop('disabled', false);
				$("#FilterExpired").prop('disabled', false);
				$("#FilterUsed").prop('disabled', false);
			}
		}

	</script>

}
