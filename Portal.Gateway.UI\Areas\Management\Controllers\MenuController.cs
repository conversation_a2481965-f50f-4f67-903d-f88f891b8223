﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.Contracts.Entities.Constants;
using Kendo.Mvc.UI;
using Portal.Gateway.ApiModel.Responses;
using System.Linq;
using System.Collections.Generic;
using Kendo.Mvc.Extensions;
using Portal.Gateway.UI.Validators;
using Portal.Gateway.UI.Models;
using Portal.Gateway.Resources;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.UI.Extensions;
using Microsoft.Extensions.Caching.Memory;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.ApiModel.Responses.Management.Menu;
using Portal.Gateway.UI.Areas.Management.ViewModels.Menu;
using Portal.Gateway.ApiModel.Requests.Management.Menu;
using Portal.Gateway.UI.Helpers;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class MenuController : BaseController<MenuController>
    {
        public MenuController(
               IOptions<AppSettings> appSettings,
               ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        public async Task<IActionResult> Manage()
        {
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> GetMenu()
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<MenuApiResponse>>
                (ApiMethodName.Management.GetMenu, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            var model = new 
            {
                data = apiResponse.Data.MainMenuList.OrderBy(o => o.Order).Select(q => new
                {
                    id = q.Id,
                    text = q.IsAction ? q.Name : $"{q.Name} (*)",
                    state = new { opened = true },
                    children = q.SubMenu.OrderBy(o => o.Order).Select(p => new
                    {
                        id = p.Id,
                        text = p.IsAction ? p.Name : $"{p.Name} (*)",
                        state = new { opened = true },
                        children = p.Node.OrderBy(o => o.Order).Select(r => new 
                        {
                            id = r.Id,
                            text = r.IsAction ? r.Name : $"{r.Name} (*)",
                        }).ToList()
                    }).ToList()
                }).ToList()
            };

            return Json(new ResultModel { Message = EnumResources.OperationIsSuccessful, ResultType = ResultType.Success, Data = model });
        }

        [HttpPut]
        public async Task<IActionResult> UpdateMenu(UpdateMenuViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateMenuApiRequest()
            {
                MainMenuList = viewModel.MainMenuList.Select(q => new UpdateMenuApiRequest.MainMenuApiRequest()
                {
                    Order = q.Order,
                    Id = q.Id,
                    SubMenuList = q.SubMenuList?.Select(w => new UpdateMenuApiRequest.SubMenuApiRequest()
                    {
                        Order = w.Order,
                        Id = w.Id,
                        NodeList = w.NodeList?.Select(r => new UpdateMenuApiRequest.NodeApiRequest()
                        {
                            Order = r.Order,
                            Id = r.Id
                        })
                    })
                })
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateMenu, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }
    }
}
