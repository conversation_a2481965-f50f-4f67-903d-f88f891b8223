﻿using ClosedXML.Excel;
using Gateway.Extensions;
using Gateway.Http;
using Kendo.Mvc;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Portal.Gateway.ApiModel.Requests.Report;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.ApiModel.Responses.Report;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Dto.Report.Common.QmsReport_1;
using Portal.Gateway.Contracts.Entities.Dto.Report.Common.QmsReport_12;
using Portal.Gateway.Contracts.Entities.Dto.Report.Common.QmsReport_2;
using Portal.Gateway.Contracts.Entities.Dto.Report.Common.QmsReport_3;
using Portal.Gateway.Contracts.Entities.Dto.Report.Common.QmsReport_4;
using Portal.Gateway.Contracts.Entities.Dto.Report.Common.QmsReport_5;
using Portal.Gateway.Contracts.Entities.Dto.Report.Common.QmsReport_6;
using Portal.Gateway.Contracts.Entities.Dto.Report.Common.QmsReport_7;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.QMS.Filters;
using Portal.Gateway.UI.Areas.QMS.Models;
using Portal.Gateway.UI.Areas.QMS.Models.QMS.Requests;
using Portal.Gateway.UI.Areas.QMS.Models.QMS.Responses;
using Portal.Gateway.UI.Areas.QMS.ViewModels;
using Portal.Gateway.UI.Areas.QMS.ViewModels.Reports;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.QMS.Controllers
{
    [Area("QMS")]
    [QmsReportPermissionFilter]
    public class QmsReportController : BaseController<QmsReportController>
    {
        public QmsReportController(ICacheHelper cacheHelper, IOptions<AppSettings> appSettings)
            : base(appSettings, cacheHelper) { }

        [Action(IsMenuItem = true)]
        public IActionResult ActionPaginatedReport()
        {
            return View(new FilterQmsReportViewModel
            {
                FilterBranchId = UserSession.BranchId,
                FilterStartDate = DateTime.Today.AddMonths(-1),
                FilterEndDate = DateTime.Today
            });
        }

        [Action(IsMenuItem = true)]
        public IActionResult TokenPaginatedReport()
        {
            return View(new FilterTokenReportViewModel
            {
                FilterBranchId = UserSession.BranchId,
                FilterStartDate = DateTime.Today.AddMonths(-1),
                FilterEndDate = DateTime.Today
            });
        }

        public IActionResult PartialTokenHistory(string tokenId)
        {
            ViewData["TokenId"] = tokenId.ToInt();

            return View("_TokenHistories");
        }

        [HttpGet]
        public async Task<IActionResult> GetTokenHistory(int tokenId)
        {
            if (!tokenId.IsNumericAndGreaterThenZero())
                return NoContent();

            var apiResponse = await RestHttpClient.Create().Get<GetTokenHistoryResult>(
                AppSettings.Qms.BaseApiUrl + QmsEndPoint.GetTokenHistory + tokenId, QMSApiDefaultRequestHeaders);

            if (apiResponse.Data == null)
                return Json(apiResponse.Message);

            var viewModel = apiResponse.Data.Select(p => new TokenHistoryViewModel
            {
                ProcessAt = p.ProcessAt,
                ProcessBy = $"{p.CounterName} / {p.ProcessBy}",
                Subtitle = p.DepartmentName,
                Status = EnumHelper.GetEnumDescription(typeof(TokenState), p.Status.ToString().ToTitleCase())
            }).ToList();

            return Json(viewModel);
        }

        public async Task<IActionResult> GetPaginatedTokenReport([DataSourceRequest] DataSourceRequest request, FilterTokenReportViewModel filterViewModel)
        {
            if (filterViewModel == null || request == null)
                return Content(EnumResources.MissingOrInvalidData);

            ModifyFilters(request.Filters);

            var apiRequest = new GetPaginatedTokenReportRequestModel
            {
                LineId = filterViewModel.FilterLineId,
                BranchId = filterViewModel.FilterBranchId,
                DepartmentId = filterViewModel.FilterDepartmentId,
                StartDate = filterViewModel.FilterStartDate,
                EndDate = filterViewModel.FilterEndDate,
                AppointmentNumber = filterViewModel.FilterAppointmentNumber,
                TokenNumber = filterViewModel.FilterTokenNumber,
                ActionStatusId = filterViewModel.FilterActionStatusId
            };

            var apiResponse = await RestHttpClient.Create().Post<GetPaginatedTokenReportResult>(
                AppSettings.Qms.BaseApiUrl + QmsEndPoint.GetPaginatedTokenReport, QMSApiDefaultRequestHeaders, apiRequest);

            if (apiResponse.Data == null)
                return Json(new List<TokenReportViewModel>());

            var tokenEnum = EnumHelper.GetEnumAsDictionary(typeof(TokenState));

            var paginatedData = apiResponse.Data.OrderByDescending(r => r.LastProcessCreatedAt).Select(p =>
                new TokenReportViewModel
                {
                    GeneratedTokenId = p.Department.Id != null ? p.GeneratedTokenId.ToString() : null,
                    Token = p.Token,
                    OwnerToken = p.OwnerToken,
                    AppointmentNumber = p.AppointmentNumber,
                    Line = new BaseQmsReportNestedDto
                    {
                        Id = p.Line.Id,
                        Name = p.Line.Name
                    },
                    Branch = new BaseQmsReportNestedDto
                    {
                        Id = p.Branch.Id,
                        Name = p.Branch.Name
                    },
                    Department = new BaseQmsReportNestedDto
                    {
                        Id = p.Department.Id,
                        Name = p.Department.Name
                    },
                    Counter = new BaseQmsReportNestedDto
                    {
                        Id = p.Counter.Id,
                        Name = p.Counter.Name
                    },
                    LastProcessStatus = new BaseQmsReportNestedDto
                    {
                        Id = (short)p.LastProcessStatus,
                        Name = tokenEnum.FirstOrDefault(r => r.Key == p.LastProcessStatus).Value,
                    },
                    TicketCreatedBy = p.TicketCreatedBy,
                    TicketCreatedAt = p.TicketCreatedAt.ToString("dd/MM/yyyy HH:mm"),
                    LastProcessCreatedAt = p.LastProcessCreatedAt.ToString("dd/MM/yyyy HH:mm"),
                    LastProcessCreatedBy = p.LastProcessCreatedBy
                }).ToList();

            var dataSource = await paginatedData.ToDataSourceResultAsync(request);
            return Json(dataSource);
        }

        public async Task<IActionResult> GetPaginatedActionReport([DataSourceRequest] DataSourceRequest request, FilterQmsReportViewModel filterViewModel)
        {
            if (filterViewModel == null || request == null)
                return Content(EnumResources.MissingOrInvalidData);

            ModifyFilters(request.Filters);

            var apiRequest = new ExternalReportBaseRequest<BaseExternalReportRequestByBranch>
            {
                UserId = UserSession.UserId,
                Request = new BaseExternalReportRequestByBranch()
                {
                    BranchId = filterViewModel.FilterBranchId.GetValueOrDefault(),
                    StartDate = filterViewModel.FilterStartDate.GetValueOrDefault(),
                    EndDate = filterViewModel.FilterEndDate.GetValueOrDefault(),
                    StatusId = filterViewModel.FilterStatusId,
                }
            };

            var qmsActionResponse = await RestHttpClient.Create().Post<GenerateExternalReportApiResponse<Report_QmsActionResponse>>($"{AppSettings.Qms.BaseApiUrl}{QmsReportEndPoint.QmsActionReport}",
                QMSApiDefaultRequestHeaders, apiRequest);

            if (qmsActionResponse.Data == null)
                return Json(qmsActionResponse.Message);

            var tokenEnum = EnumHelper.GetEnumAsDictionary(typeof(TokenState));

            var paginatedData = qmsActionResponse.Data.Actions.Select(p => new ActionReportViewModel
            {
                Branch = new BaseQmsReportNestedDto
                {
                    Name = p.BranchName
                },
                AppointmentNumber = p.AppointmentNumber,
                ApplicationNumber = p.ApplicationNumber,
                ApplicationCreatedBy = p.ApplicationCreatedBy,
                TokenNumber = p.TokenNumber,
                Counter = new BaseQmsReportNestedDto
                {
                    Name = p.Counter
                },
                ProcessedBy = p.ProcessedBy,
                ActionStatus = new BaseQmsReportNestedDto
                {
                    Id = p.Status,
                    Name = tokenEnum.FirstOrDefault(r => r.Key == p.Status).Value,
                },
                ActionDate = p.ActionAt.ToShortDateString(),
                Assignee = p.Assignee,
                PostponedDate = p.PostponedDate != null ? p.PostponedDate.Value.ToString("dd/MM/yyyy HH:mm") : string.Empty,
                NotCompletedReason = p.NotCompletedReasonId != null ? p.NotCompletedReason : string.Empty
            }).ToList();

            var dataSource = await paginatedData.ToDataSourceResultAsync(request);

            return Json(dataSource);
        }

        [Action(IsMenuItem = true)]
        public IActionResult PersonalPaginatedReport()
        {
            return View(new FilterQmsReportViewModel
            {
                FilterBranchId = UserSession.BranchId,
                FilterStartDate = DateTime.Today.AddMonths(-1),
                FilterEndDate = DateTime.Today
            });
        }

        public async Task<IActionResult> GetPaginatedPersonalReport([DataSourceRequest] DataSourceRequest request, FilterQmsReportViewModel filterViewModel)
        {
            if (filterViewModel == null || request == null)
                return Content(EnumResources.MissingOrInvalidData);

            ModifyFilters(request.Filters);

            var apiRequest = new ExternalReportBaseRequest<BaseExternalReportRequestByBranch>
            {
                UserId = UserSession.UserId,
                Request = new BaseExternalReportRequestByBranch()
                {
                    BranchId = filterViewModel.FilterBranchId.GetValueOrDefault(),
                    StartDate = filterViewModel.FilterStartDate.GetValueOrDefault(),
                    EndDate = filterViewModel.FilterEndDate.GetValueOrDefault(),
                }
            };

            var qmsPersonalResponse = await RestHttpClient.Create().Post<GenerateExternalReportApiResponse<Report_QmsPersonalResponse>>($"{AppSettings.Qms.BaseApiUrl}{QmsReportEndPoint.QmsPersonalReport}",
                QMSApiDefaultRequestHeaders, apiRequest);

            if (qmsPersonalResponse.Data == null)
                return Json(qmsPersonalResponse.Message);

            var paginatedData = qmsPersonalResponse.Data.Personnel.Select(p => new PersonalReportViewModel
            {
                StaffName = p.Staff,
                TokenCalledCount = p.CalledCount,
                CompletedRatio = p.TotalActionCount == 0 ? "0.00%" : Decimal.Divide(p.CompletedCount, p.TotalActionCount).ToString("0.00%"),
                NotFoundRatio = p.TotalActionCount == 0 ? "0.00%" : Decimal.Divide(p.NotFoundCount, p.TotalActionCount).ToString("0.00%"),
                CancelledRatio = p.TotalActionCount == 0 ? "0.00%" : Decimal.Divide(p.CancelledCount, p.TotalActionCount).ToString("0.00%"),
                HoldOnRatio = p.TotalActionCount == 0 ? "0.00%" : Decimal.Divide(p.HoldOnCount, p.TotalActionCount).ToString("0.00%"),
                PostponeRatio = p.TotalActionCount == 0 ? "0.00%" : Decimal.Divide(p.PostponeCount, p.TotalActionCount).ToString("0.00%"),
                AssignRatio = p.TotalActionCount == 0 ? "0.00%" : Decimal.Divide(p.AssignCount, p.TotalActionCount).ToString("0.00%"),
                Recall = p.TotalActionCount == 0 ? "0.00%" : Decimal.Divide(p.RecallCount, p.TotalActionCount).ToString("0.00%"),
                TotalServeTime = p.TotalServeTimePeriod,
                AverageServeTimeLength = p.AverageServeTimeLengthPeriod,
            }).ToList();

            var dataSource = await paginatedData.ToDataSourceResultAsync(request);
            return Json(dataSource);
        }

        [Action(IsMenuItem = true)]
        public IActionResult QmsMainPaginatedReport()
        {
            return View(new FilterQmsReportViewModel
            {
                FilterBranchId = UserSession.BranchId,
                FilterStartDate = DateTime.Today.AddMonths(-1),
                FilterEndDate = DateTime.Today
            });
        }

        public async Task<IActionResult> GetPaginatedQmsMainReport([DataSourceRequest] DataSourceRequest request, FilterQmsReportViewModel filterViewModel)
        {
            if (filterViewModel == null || request == null)
                return Content(EnumResources.MissingOrInvalidData);

            ModifyFilters(request.Filters);

            List<int> branches = new List<int>();
            if (filterViewModel.FilterBranchId == null) branches.Add(0); else branches.Add((int)filterViewModel.FilterBranchId);

            var apiRequest = new ExternalReportBaseRequest<BaseExternalReportRequestByBranches>
            {
                UserId = UserSession.UserId,
                Request = new BaseExternalReportRequestByBranches()
                {
                    BranchIds = branches,
                    StartDate = filterViewModel.FilterStartDate.GetValueOrDefault(),
                    EndDate = filterViewModel.FilterEndDate.GetValueOrDefault(),
                    FilterCCError = filterViewModel.FilterCCError.GetValueOrDefault(),
                }
            };

            var qmsResponse = await RestHttpClient.Create().Post<GenerateExternalReportApiResponse<Report_QmsMainResponse>>($"{AppSettings.Qms.BaseApiUrl}{QmsReportEndPoint.QmsMainReport}",
                QMSApiDefaultRequestHeaders, apiRequest);

            if (qmsResponse.Data == null)
                return Json(qmsResponse.Message);

            var appointmentStatusEnum = EnumHelper.GetEnumAsDictionary(typeof(AppointmentStatus));
            var applicantTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(ApplicantType));
            var applicationTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(ApplicationType));
            var vasTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(VasType));
            var qmsApplicationEnum = EnumHelper.GetEnumAsDictionary(typeof(QmsApplicationStatus));
            var qmsApplicationCancellationEnum = EnumHelper.GetEnumAsDictionary(typeof(QmsApplicationCancellationStatus));

            var paginatedData = qmsResponse.Data.MainReportDataList.Select(p => new QmsReportViewModel
            {
                BranchName = p.BranchName,
                TokenNumber = p.TokenNumber,
                AppointmentStatus = appointmentStatusEnum.FirstOrDefault(r => r.Key == p.AppointmentStatus).Value,
                AppointmentNumber = p.AppointmentNumber,
                ApplicationNumber = p.ApplicationNumber,
                ApplicationCreatedBy = p.ApplicationCreatedBy,
                PreApplicationCreatedBy = p.PreApplicationCreatedBy,
                PreApplicationUpdatedBy = p.PreApplicationUpdatedBy,
                NameSurname = p.NameSurname,
                PassportNumber = p.PassportNumber,
                ApplicationType = applicationTypeEnum.FirstOrDefault(r => r.Key == p.ApplicationType).Value,
                ApplicantType = applicantTypeEnum.FirstOrDefault(r => r.Key == p.ApplicantType).Value,
                VasType = vasTypeEnum.FirstOrDefault(r => r.Key == p.VasType).Value,
                Description = p.Description,
                ApplicationStatus = qmsApplicationEnum.FirstOrDefault(r => r.Key == p.ApplicationStatus).Value,
                EntireProcedureCompleteTimeLength = p.EntireProcessTimePeriod,
                TokenCreatedBy = p.TokenCreatedBy,
                TokenCreatedAt = p.TokenCreatedAt.HasValue ? p.TokenCreatedAt.Value.ToShortDateString() : string.Empty,
                IsWhiteListApplicant = p.IsWhiteListApplicant ? SiteResources.Yes : p.IsWhiteListApplicant is false ? SiteResources.No : "-",
                IsApplicationCreated = p.IsApplicationCreated ? SiteResources.Yes : p.IsApplicationCreated is false ? SiteResources.No : "-",
                ApplicationCancellationStatus = qmsApplicationCancellationEnum.FirstOrDefault(r => r.Key == p.ApplicationCancellationStatusId).Value,
            }).ToList();

            var dataSource = await paginatedData.ToDataSourceResultAsync(request);
            return Json(dataSource);
        }

        [Action(IsMenuItem = true)]
        public IActionResult QmsCompletedPaginatedReport()
        {
            return View(new FilterQmsReportViewModel
            {
                FilterBranchId = UserSession.BranchId,
                FilterStartDate = DateTime.Today.AddMonths(-1),
                FilterEndDate = DateTime.Today
            });
        }

        public async Task<IActionResult> GetPaginatedQmsCompletedReport([DataSourceRequest] DataSourceRequest request, FilterQmsReportViewModel filterViewModel)
        {
            if (filterViewModel == null || request == null)
                return Content(EnumResources.MissingOrInvalidData);

            ModifyFilters(request.Filters);

            List<int> branches = new List<int>();
            if (filterViewModel.FilterBranchId == null) branches.Add(0); else branches.Add((int)filterViewModel.FilterBranchId);

            var apiRequest = new ExternalReportBaseRequest<BaseExternalReportRequestByBranches>
            {
                UserId = UserSession.UserId,
                Request = new BaseExternalReportRequestByBranches
                {
                    BranchIds = branches,
                    StartDate = filterViewModel.FilterStartDate.GetValueOrDefault(),
                    EndDate = filterViewModel.FilterEndDate.GetValueOrDefault(),
                    FilterCCError = filterViewModel.FilterCCError.GetValueOrDefault(),
                }
            };

            var qmsResponse = await RestHttpClient.Create().Post<GenerateExternalReportApiResponse<Report_QmsCompletedReportResponse>>($"{AppSettings.Qms.BaseApiUrl}{QmsReportEndPoint.QmsCompletedReport}",
                QMSApiDefaultRequestHeaders, apiRequest);

            if (qmsResponse.Data == null)
                return Json(qmsResponse.Message);

            var appointmentStatusEnum = EnumHelper.GetEnumAsDictionary(typeof(AppointmentStatus));
            var applicantTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(ApplicantType));
            var applicationTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(ApplicationType));
            var vasTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(VasType));
            var qmsApplicationCancellationEnum = EnumHelper.GetEnumAsDictionary(typeof(QmsApplicationCancellationStatus));

            var paginatedData = qmsResponse.Data.QmsCompletedReportDataList.Select(p => new QmsCompletedReportViewModel
            {
                BranchName = p.BranchName,
                TokenNumber = p.TokenNumber,
                AppointmentStatus = appointmentStatusEnum.FirstOrDefault(r => r.Key == p.AppointmentStatus).Value,
                AppointmentNumber = p.AppointmentNumber,
                ApplicationNumber = p.ApplicationNumber,
                ApplicationCreatedBy = p.ApplicationCreatedBy,
                NameSurname = p.NameSurname,
                PassportNumber = p.PassportNumber,
                ApplicationType = applicationTypeEnum.FirstOrDefault(r => r.Key == p.ApplicationType).Value,
                ApplicantType = applicantTypeEnum.FirstOrDefault(r => r.Key == p.ApplicantType).Value,
                VasType = vasTypeEnum.FirstOrDefault(r => r.Key == p.VasType).Value,
                Description = p.Description,
                EntireProcedureCompleteTimeLength = p.EntireProcessTimePeriod,
                TokenCreatedBy = p.TokenCreatedBy,
                TokenCreatedAt = p.TokenCreatedAt.HasValue ? p.TokenCreatedAt.Value.ToShortDateString() : string.Empty,
                IsWhiteListApplicant = p.IsWhiteListApplicant ? SiteResources.Yes : p.IsWhiteListApplicant is false ? SiteResources.No : "-",
                IsApplicationCreated = p.IsApplicationCreated ? SiteResources.Yes : p.IsApplicationCreated is false ? SiteResources.No : "-",
                ApplicationCancellationStatus = qmsApplicationCancellationEnum.FirstOrDefault(r => r.Key == p.ApplicationCancellationStatusId).Value
            }).ToList();

            var dataSource = await paginatedData.ToDataSourceResultAsync(request);
            return Json(dataSource);
        }

        [Action(IsMenuItem = true)]
        public IActionResult QmsAllBranchesWhiteListReport()
        {
            return View(new FilterQmsReportViewModel
            {
                FilterStartDate = DateTime.Today,
                FilterEndDate = DateTime.Today
            });
        }

        public async Task<IActionResult> GetPaginatedAllBranchesWhiteListReport([DataSourceRequest] DataSourceRequest request, FilterQmsReportViewModel filterViewModel)
        {
            if (filterViewModel == null || request == null)
                return Content(EnumResources.MissingOrInvalidData);

            ModifyFilters(request.Filters);

            List<int> branches = new List<int>();
            if (filterViewModel.FilterBranchIds == null)
            {
                var branchList = await CacheHelper.GetBranchesAsync();
                branches = branchList.Branches.Where(r => r.Country.Id == UserSession.BranchCountryId).Select(x => x.Id).ToList();
            }
            else
                branches.AddRange(filterViewModel.FilterBranchIds);

            var apiRequest = new ExternalReportBaseRequest<BaseExternalReportRequestByBranches>
            {
                UserId = UserSession.UserId,
                Request = new BaseExternalReportRequestByBranches()
                {
                    BranchIds = branches,
                    StartDate = filterViewModel.FilterStartDate.GetValueOrDefault(),
                    EndDate = filterViewModel.FilterEndDate.GetValueOrDefault()
                }
            };

            var qmsResponse = await RestHttpClient.Create().Post<GenerateExternalReportApiResponse<Report_QmsMainResponse>>($"{AppSettings.Qms.BaseApiUrl}{QmsReportEndPoint.QmsAllBranchesWhiteList}",
                QMSApiDefaultRequestHeaders, apiRequest);

            if (qmsResponse.Data == null)
                return Json(qmsResponse.Message);

            var appointmentStatusEnum = EnumHelper.GetEnumAsDictionary(typeof(AppointmentStatus));
            var applicantTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(ApplicantType));
            var applicationTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(ApplicationType));
            var vasTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(VasType));
            var qmsApplicationEnum = EnumHelper.GetEnumAsDictionary(typeof(QmsApplicationStatus));

            var paginatedData = qmsResponse.Data.MainReportDataList.Select(p => new QmsAllBranchWhiteListReportViewModel
            {
                BranchName = p.BranchName,
                TokenNumber = p.TokenNumber,
                AppointmentStatus = appointmentStatusEnum.FirstOrDefault(r => r.Key == p.AppointmentStatus).Value,
                AppointmentNumber = p.AppointmentNumber,
                ApplicationNumber = p.ApplicationNumber,
                ApplicationCreatedBy = p.ApplicationCreatedBy,
                NameSurname = p.NameSurname,
                PassportNumber = p.PassportNumber,
                ApplicationType = applicationTypeEnum.FirstOrDefault(r => r.Key == p.ApplicationType).Value,
                ApplicantType = applicantTypeEnum.FirstOrDefault(r => r.Key == p.ApplicantType).Value,
                VasType = vasTypeEnum.FirstOrDefault(r => r.Key == p.VasType).Value,
                Description = p.Description,
                ApplicationStatus = qmsApplicationEnum.FirstOrDefault(r => r.Key == p.ApplicationStatus).Value,
                EntireProcedureCompleteTimeLength = p.EntireProcessTimePeriod,
                TokenCreatedBy = p.TokenCreatedBy,
                TokenCreatedAt = p.TokenCreatedAt.HasValue ? p.TokenCreatedAt.Value.ToShortDateString() : string.Empty,
                IsWhiteListApplicant = p.IsWhiteListApplicant ? SiteResources.Yes : p.IsWhiteListApplicant is false ? SiteResources.No : "-",
                IsApplicationCreated = p.IsApplicationCreated ? SiteResources.Yes : p.IsApplicationCreated is false ? SiteResources.No : "-",
            }).ToList();

            var dataSource = await paginatedData.ToDataSourceResultAsync(request);
            return Json(dataSource);
        }

        [Action(IsMenuItem = true)]
        public IActionResult QmsNoShowReport()
        {
            return View(new FilterQmsReportViewModel
            {
                FilterBranchId = UserSession.BranchId,
                FilterStartDate = DateTime.Today,
                FilterEndDate = DateTime.Today
            });
        }

        public async Task<IActionResult> GetPaginatedNoShowReport([DataSourceRequest] DataSourceRequest request, FilterQmsReportViewModel filterViewModel)
        {
            if (filterViewModel == null || request == null)
                return Content(EnumResources.MissingOrInvalidData);

            ModifyFilters(request.Filters);

            List<int> branches = new List<int>();
            if (filterViewModel.FilterBranchId == null) branches.Add(0); else branches.Add((int)filterViewModel.FilterBranchId);

            var apiRequest = new ExternalReportBaseRequest<BaseExternalReportRequestByBranches>
            {
                UserId = UserSession.UserId,
                Request = new BaseExternalReportRequestByBranches()
                {
                    BranchIds = branches,
                    StartDate = filterViewModel.FilterStartDate.GetValueOrDefault(),
                    EndDate = filterViewModel.FilterEndDate.GetValueOrDefault()
                }
            };

            var qmsResponse = await RestHttpClient.Create().Post<GenerateExternalReportApiResponse<Report_QmsMainResponse>>($"{AppSettings.Qms.BaseApiUrl}{QmsReportEndPoint.QmsNoShowReport}",
                QMSApiDefaultRequestHeaders, apiRequest);

            if (qmsResponse.Data == null)
                return Json(qmsResponse.Message);

            var appointmentStatusEnum = EnumHelper.GetEnumAsDictionary(typeof(AppointmentStatus));
            var applicantTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(ApplicantType));
            var applicationTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(ApplicationType));
            var vasTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(VasType));


            var paginatedData = qmsResponse.Data.MainReportDataList.Select(p => new QmsNoShowReportViewModel()
            {
                BranchName = p.BranchName,
                TokenNumber = SiteResources.NoShow,
                AppointmentStatus = appointmentStatusEnum.FirstOrDefault(r => r.Key == p.AppointmentStatus).Value,
                AppointmentNumber = p.AppointmentNumber,
                NameSurname = p.NameSurname,
                PassportNumber = p.PassportNumber,
                ApplicationType = applicationTypeEnum.FirstOrDefault(r => r.Key == p.ApplicationType).Value,
                ApplicantType = applicantTypeEnum.FirstOrDefault(r => r.Key == p.ApplicantType).Value,
                VasType = vasTypeEnum.FirstOrDefault(r => r.Key == p.VasType).Value,
                ApplicationStatus = GetPreApplicationStatus(p),
                PreApplicationUpdatedAt = p.IsDeleted != true ? p.PreApplicationUpdatedAt.ToString() : string.Empty,
            }).ToList();

            var dataSource = await paginatedData.ToDataSourceResultAsync(request);
            return Json(dataSource);
        }

        private string GetPreApplicationStatus(QmsMainReportDto data)
        {
            var status = EnumResources.ApplicationNotReceived;

            if (data.PreApplicationUpdatedAt.HasValue)
                status = EnumResources.Updated;

            if (data.IsDeleted)
                status = EnumResources.CancelledStatus;

            return status;
        }

        [Action(IsMenuItem = true)]
        public IActionResult QmsVIPReport()
        {
            return View(new FilterQmsReportViewModel
            {
                FilterBranchId = UserSession.BranchId,
                FilterStartDate = DateTime.Today,
                FilterEndDate = DateTime.Today
            });
        }

        public async Task<IActionResult> GetPaginatedQmsVIPReport([DataSourceRequest] DataSourceRequest request, FilterQmsReportViewModel filterViewModel)
        {
            if (filterViewModel == null || request == null)
                return Content(EnumResources.MissingOrInvalidData);

            ModifyFilters(request.Filters);

            List<int> branches = new List<int>();
            if (filterViewModel.FilterBranchId == null) branches.Add(0); else branches.Add((int)filterViewModel.FilterBranchId);

            var apiRequest = new ExternalReportBaseRequest<BaseExternalReportRequestByBranches>
            {
                UserId = UserSession.UserId,
                Request = new BaseExternalReportRequestByBranches()
                {
                    BranchIds = branches,
                    StartDate = filterViewModel.FilterStartDate.GetValueOrDefault(),
                    EndDate = filterViewModel.FilterEndDate.GetValueOrDefault()
                }
            };

            var qmsResponse = await RestHttpClient.Create().Post<GenerateExternalReportApiResponse<Report_QmsMainResponse>>($"{AppSettings.Qms.BaseApiUrl}{QmsReportEndPoint.QmsVIPReport}",
                QMSApiDefaultRequestHeaders, apiRequest);

            if (qmsResponse.Data == null)
                return Json(qmsResponse.Message);

            var appointmentStatusEnum = EnumHelper.GetEnumAsDictionary(typeof(AppointmentStatus));
            var applicantTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(ApplicantType));
            var applicationTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(ApplicationType));
            var vasTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(VasType));
            var qmsApplicationEnum = EnumHelper.GetEnumAsDictionary(typeof(QmsApplicationStatus));

            var paginatedData = qmsResponse.Data.MainReportDataList.Select(p => new QmsReportViewModel
            {
                BranchName = p.BranchName,
                TokenNumber = p.TokenNumber,
                AppointmentStatus = appointmentStatusEnum.FirstOrDefault(r => r.Key == p.AppointmentStatus).Value,
                AppointmentNumber = p.AppointmentNumber,
                ApplicationNumber = p.ApplicationNumber,
                ApplicationCreatedBy = p.ApplicationCreatedBy,
                PreApplicationCreatedBy = p.PreApplicationCreatedBy,
                PreApplicationUpdatedBy = p.PreApplicationUpdatedBy,
                NameSurname = p.NameSurname,
                PassportNumber = p.PassportNumber,
                ApplicationType = applicationTypeEnum.FirstOrDefault(r => r.Key == p.ApplicationType).Value,
                ApplicantType = applicantTypeEnum.FirstOrDefault(r => r.Key == p.ApplicantType).Value,
                VasType = vasTypeEnum.FirstOrDefault(r => r.Key == p.VasType).Value,
                Description = p.Description,
                ApplicationStatus = qmsApplicationEnum.FirstOrDefault(r => r.Key == p.ApplicationStatus).Value,
                EntireProcedureCompleteTimeLength = p.EntireProcessTimePeriod,
                TokenCreatedBy = p.TokenCreatedBy,
                TokenCreatedAt = p.TokenCreatedAt.HasValue ? p.TokenCreatedAt.Value.ToShortDateString() : string.Empty,
                IsWhiteListApplicant = p.IsWhiteListApplicant ? SiteResources.Yes : p.IsWhiteListApplicant is false ? SiteResources.No : "-",
                IsApplicationCreated = p.IsApplicationCreated ? SiteResources.Yes : p.IsApplicationCreated is false ? SiteResources.No : "-",
            }).ToList();

            var dataSource = await paginatedData.ToDataSourceResultAsync(request);
            return Json(dataSource);
        }

        [Action(IsMenuItem = true)]
        public IActionResult QmsProcessTimeReport()
        {
            return View(new FilterQmsReportViewModel
            {
                FilterBranchId = UserSession.BranchId,
                FilterStartDate = DateTime.Today,
                FilterEndDate = DateTime.Today
            });
        }

        public async Task<IActionResult> GetPaginatedQmsProcessTimeReport([DataSourceRequest] DataSourceRequest request, FilterQmsReportViewModel filterViewModel)
        {
            if (filterViewModel == null || request == null)
                return Content(EnumResources.MissingOrInvalidData);

            ModifyFilters(request.Filters);

            var apiRequest = new ExternalReportBaseRequest<BaseExternalReportRequestByBranch>
            {
                UserId = UserSession.UserId,
                Request = new BaseExternalReportRequestByBranch()
                {
                    BranchId = filterViewModel.FilterBranchId.GetValueOrDefault(),
                    StartDate = filterViewModel.FilterStartDate.GetValueOrDefault(),
                    EndDate = filterViewModel.FilterEndDate.GetValueOrDefault()
                }
            };

            var qmsResponse = await RestHttpClient.Create().Post<GenerateExternalReportApiResponse<Report_QmsProcessTimeReportResponse>>($"{AppSettings.Qms.BaseApiUrl}{QmsReportEndPoint.QmsProcessTimeReport}",
                QMSApiDefaultRequestHeaders, apiRequest);

            if (qmsResponse.Data == null)
                return Json(qmsResponse.Message);

            var paginatedData = qmsResponse.Data.ProcessTimeReportDataList.Select(p => new QmsProcessTimeReportViewModel
            {
                PassportNumber = p.PassportNumber,
                TokenNumber = p.TokenNumber,
                BeforeSubmissionProcessTime = p.BeforeSubmissionProcessTimePeriod,
                SubmissionProcessTime = p.SubmissionProcessTimePeriod,
                SubmissionCreatedBy = p.SubmissionCreatedBy,
                BeforeBiometryProcessTime = p.BeforeBiometryProcessTimePeriod,
                BiometryProcessTime = p.BiometryProcessTimePeriod,
                BiometryCreatedBy = p.BiometryCreatedBy,
                EntireProcessTime = p.EntireProcessTimePeriod,
            }).ToList();

            var dataSource = await paginatedData.ToDataSourceResultAsync(request);
            return Json(dataSource);
        }


        [Action(IsMenuItem = true)]
        public IActionResult DepartmentPaginatedReport()
        {
            return View(new FilterQmsReportViewModel
            {
                FilterBranchId = UserSession.BranchId,
                FilterStartDate = DateTime.Today.AddMonths(-1),
                FilterEndDate = DateTime.Today
            });
        }

        public async Task<IActionResult> GetPaginatedDepartmentReport([DataSourceRequest] DataSourceRequest request, FilterQmsReportViewModel filterViewModel)
        {
            if (filterViewModel == null || request == null)
                return Content(EnumResources.MissingOrInvalidData);

            ModifyFilters(request.Filters);

            var apiRequest = new ExternalReportBaseRequest<BaseExternalReportRequestByBranch>
            {
                UserId = UserSession.UserId,
                Request = new BaseExternalReportRequestByBranch()
                {
                    BranchId = filterViewModel.FilterBranchId.GetValueOrDefault(),
                    DepartmentId = filterViewModel.FilterDepartmentId.GetValueOrDefault(),
                    StartDate = filterViewModel.FilterStartDate.GetValueOrDefault(),
                    EndDate = filterViewModel.FilterEndDate.GetValueOrDefault(),
                }
            };

            var qmsDepartmentResponse = await RestHttpClient.Create().Post<GenerateExternalReportApiResponse<Report_QmsDepartmentResponse>>($"{AppSettings.Qms.BaseApiUrl}{QmsReportEndPoint.QmsDepartmentReport}",
                QMSApiDefaultRequestHeaders, apiRequest);

            if (qmsDepartmentResponse.Data == null)
                return Json(qmsDepartmentResponse.Message);

            var appointmentStatusEnum = EnumHelper.GetEnumAsDictionary(typeof(AppointmentStatus));
            var applicantTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(ApplicantType));
            var applicationTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(ApplicationType));
            var tokenEnum = EnumHelper.GetEnumAsDictionary(typeof(TokenState));

            var paginatedData = qmsDepartmentResponse.Data.Departments.Select(p => new DepartmentReportViewModel
            {
                BranchName = p.BranchName,
                ProcessedBy = p.ProcessedBy,
                AppointmentStatus = appointmentStatusEnum.FirstOrDefault(r => r.Key == p.AppointmentStatus).Value,
                AppointmentNumber = p.AppointmentNumber,
                ApplicationNumber = p.ApplicationNumber,
                ApplicationCreatedBy = p.ApplicationCreatedBy,
                NameSurname = p.NameSurname,
                PassportNumber = p.PassportNumber,
                ApplicantType = applicantTypeEnum.FirstOrDefault(r => r.Key == p.ApplicantType).Value,
                ApplicationStatus = applicationTypeEnum.FirstOrDefault(r => r.Key == p.ApplicationType).Value,
                WaitingTimeLength = p.WaitingTimePeriod,
                ProcessTimeLength = p.ProcessTimePeriod,
                RecallTimeLength = p.RecallTimePeriod,
                Status = new BaseQmsReportNestedDto
                {
                    Id = p.TokenStatus,
                    Name = tokenEnum.FirstOrDefault(r => r.Key == p.TokenStatus).Value,
                },
                Department = p.DepartmentName,
                TokenCreatedAt = p.HistoryCreatedAt.ToShortDateString()
            }).ToList();

            var dataSource = await paginatedData.ToDataSourceResultAsync(request);
            return Json(dataSource);
        }

        [Action(IsMenuItem = true)]
        public IActionResult QmsNotCompletedReport()
        {
            return View(new FilterQmsReportViewModel
            {
                FilterBranchId = UserSession.BranchId,
                FilterStartDate = DateTime.Today,
                FilterEndDate = DateTime.Today
            });
        }

        public async Task<IActionResult> GetPaginatedNotCompletedReport([DataSourceRequest] DataSourceRequest request, FilterQmsReportViewModel filterViewModel)
        {
            if (filterViewModel == null || request == null)
                return Content(EnumResources.MissingOrInvalidData);

            ModifyFilters(request.Filters);

            List<int> branches = new List<int>();
            if (filterViewModel.FilterBranchId == null) branches.Add(0); else branches.Add((int)filterViewModel.FilterBranchId);

            var apiRequest = new ExternalReportBaseRequest<BaseExternalReportRequestByBranches>
            {
                UserId = UserSession.UserId,
                Request = new BaseExternalReportRequestByBranches()
                {
                    BranchIds = branches,
                    StartDate = filterViewModel.FilterStartDate.GetValueOrDefault(),
                    EndDate = filterViewModel.FilterEndDate.GetValueOrDefault(),
                    FilterCCError = filterViewModel.FilterCCError.GetValueOrDefault()
                }
            };

            var qmsResponse = await RestHttpClient.Create().Post<GenerateExternalReportApiResponse<Report_QmsNotCompletedResponse>>($"{AppSettings.Qms.BaseApiUrl}{QmsReportEndPoint.QmsNotCompletedReport}",
                QMSApiDefaultRequestHeaders, apiRequest);

            if (qmsResponse.Data == null)
                return Json(qmsResponse.Message);

            var appointmentStatusEnum = EnumHelper.GetEnumAsDictionary(typeof(AppointmentStatus));
            var applicantTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(ApplicantType));
            var applicationTypeEnum = EnumHelper.GetEnumAsDictionary(typeof(ApplicationType));

            var paginatedData = qmsResponse.Data.Data.Select(p => new QmsNotCompletedReportViewModel()
            {
                BranchName = p.BranchName,
                TokenNumber = p.TokenNumber,
                AppointmentStatus = appointmentStatusEnum.FirstOrDefault(r => r.Key == p.AppointmentStatus).Value,
                AppointmentNumber = p.AppointmentNumber,
                NameSurname = p.NameSurname,
                PassportNumber = p.PassportNumber,
                ApplicationType = applicationTypeEnum.FirstOrDefault(r => r.Key == p.ApplicationType).Value,
                ApplicantType = applicantTypeEnum.FirstOrDefault(r => r.Key == p.ApplicantType).Value,
                EntireProcedureCompleteTimeLength = p.EntireProcessTimePeriod,
                TokenCreatedBy = p.TokenCreatedBy,
                TokenCreatedAt = p.TokenCreatedAt.HasValue ? p.TokenCreatedAt.Value.ToShortDateString() : string.Empty,
                IsWhiteListApplicant = p.IsWhiteListApplicant ? SiteResources.Yes : p.IsWhiteListApplicant is false ? SiteResources.No : "-",
                NotCompletedReason = p.IsCompletedSubToken ? SiteResources.Completed : p.NotCompletedReasonId != null ? p.NotCompletedReason : string.Empty,
                PreApplicationCreatedBy = p.PreApplicationCreatedBy,
                PreApplicationUpdatedBy = p.PreApplicationUpdatedBy,
            }).ToList();

            var dataSource = await paginatedData.ToDataSourceResultAsync(request);
            return Json(dataSource);
        }

        [Action(IsMenuItem = true)]
        public IActionResult QmsTatReport()
        {
            return View(new FilterQmsReportViewModel
            {
                FilterBranchId = UserSession.BranchId,
                FilterStartDate = DateTime.Today,
                FilterEndDate = DateTime.Today
            });
        }

        public async Task<IActionResult> GetPaginatedQmsTatReport([DataSourceRequest] DataSourceRequest request, FilterQmsReportViewModel filterViewModel)
        {
            if (filterViewModel == null || request == null)
                return Content(EnumResources.MissingOrInvalidData);

            ModifyFilters(request.Filters);

            var apiRequest = new ExternalReportBaseRequest<BaseExternalReportRequestByBranch>
            {
                UserId = UserSession.UserId,
                Request = new BaseExternalReportRequestByBranch()
                {
                    BranchId = filterViewModel.FilterBranchId.GetValueOrDefault(),
                    StartDate = filterViewModel.FilterStartDate.GetValueOrDefault(),
                    EndDate = filterViewModel.FilterEndDate.GetValueOrDefault()
                }
            };

            var qmsResponse = await RestHttpClient.Create().Post<GenerateExternalReportApiResponse<Report_QmsTatReportResponse>>($"{AppSettings.Qms.BaseApiUrl}{QmsReportEndPoint.QmsTatReport}",
                QMSApiDefaultRequestHeaders, apiRequest);

            if (qmsResponse.Data == null)
                return Json(qmsResponse.Message);

            var paginatedData = qmsResponse.Data.Data.Select(p => new QmsTatReportViewModel()
            {
                BranchName = p.BranchName,
                BranchApplicationCountryName = p.BranchApplicationCountryName,
                CustomerType = p.CustomerType,
                Date = p.ProcessDate,
                SlaTime = p.SlaTime,
                TatService = p.TatService,
                TimeDivision = p.TimeDivision,
                TokenCompletedAt = p.TokenCompletedAt,
                TokenCreatedAt = p.TokenCreatedAt,
                TokenProcessedBy = p.TokenProcessedBy,
                TokenServiceTime = p.TokenServiceTime,
                TokenTatTime = p.TokenTatTime,
                TokenWaitTime = p.TokenWaitTime,
                TotalApplicationCount = p.TotalApplicationCount
            }).ToList();

            var dataSource = await paginatedData.ToDataSourceResultAsync(request);
            return Json(dataSource);
        }

        [Action(IsMenuItem = true)]
        public IActionResult QmsAllReportsPage()
        {
            var reportTypes = EnumHelper.GetEnumAsDictionary(typeof(QmsReportType));

            return View(new FilterQmsAllReportsViewModel
            {
                FilterBranchId = (int)UserSession.BranchId,
                FilterStartDate = DateTime.Today,
                FilterEndDate = DateTime.Today,
                ReportTypes = reportTypes.Select(s => new QmsAllReportTypeDto
                {
                    Id = s.Key,
                    Name = s.Value,
                    Checked = false
                }).ToList()
            });
        }

        public async Task<IActionResult> GetQmsMultiReport(FilterQmsAllReportsViewModel filterViewModel)
        {
            if (filterViewModel == null)
                return Content(EnumResources.MissingOrInvalidData);

            var checkedReports = filterViewModel.ReportTypes.Where(s => s.Checked).Select(s => s.Id);

            var enumerable = checkedReports.ToList();

            if (!enumerable.Any())
                return Content(EnumResources.MissingOrInvalidData);

            using (var workbook = new XLWorkbook())
            {
                foreach (var type in enumerable.Select(report => (QmsReportType)report))
                {
                    var reportData = await GenerateReport(type, filterViewModel);
                    var sheetName = type.GetDescription();
                    var worksheet = workbook.Worksheets.Add(sheetName);

                    if (reportData == null) continue;
                    if (reportData.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>)) && (reportData as ICollection).Count == 0) continue;

                    var currentRow = 1;
                    var maxCell = 0;
                    var columnHeaderIndex = 0;
                    for (var i = 1; i < reportData[0].GetType().GetProperties().Length + 1; i++)
                    {
                        var propertyInfo = reportData[0].GetType().GetProperties()[i -1];

                        if (propertyInfo.GetCustomAttributes(typeof(LocalizedSiteDescriptionAttribute), true) is Attribute[] { Length: > 0 } attributes)
                        {
                            if (attributes[0] is LocalizedSiteDescriptionAttribute descriptionAttribute)
                            {
                                maxCell++;
                                columnHeaderIndex++;
                                worksheet.Cell(currentRow, columnHeaderIndex).SetValue(descriptionAttribute.Description);
                            }
                        }
                    }

                    var rangeTableHeader =
                        worksheet.Range(worksheet.Cell(currentRow, 1), worksheet.Cell(currentRow, 30));
                    rangeTableHeader.Style.Font.SetBold();
                    currentRow++;

                    for (var i = 0; i < reportData.Count; i++)
                    {
                        var columnDataIndex = 0;
                        var dataElement = reportData[i];
                        for (var j = 1; j < reportData[0].GetType().GetProperties().Length + 1; j++)
                        {
                            var propertyInfo = reportData[0].GetType().GetProperties()[j-1];

                            if (propertyInfo.GetCustomAttributes(typeof(LocalizedSiteDescriptionAttribute), true) is Attribute[] { Length: > 0 } attributes)
                            {
                                if (attributes[0] is LocalizedSiteDescriptionAttribute)
                                {
                                    if (propertyInfo.GetCustomAttributes(typeof(PropertyValueAttribute), true) is
                                        Attribute[] { Length: > 0 } propAttributes)
                                    {
                                        if (propAttributes[0] is PropertyValueAttribute)
                                        {
                                            columnDataIndex++;
                                            worksheet.Cell(currentRow, columnDataIndex)
                                                .SetValue(((BaseQmsReportNestedDto)(dataElement.GetType().GetProperties()[j - 1].GetValue(dataElement, null))).Name);
                                        }
                                    }
                                    else
                                    {
                                        columnDataIndex++;
                                        if (dataElement.GetType().GetProperties()[j - 1]
                                                .GetValue(dataElement, null) == null)
                                        {
                                            worksheet.Cell(currentRow, columnDataIndex).SetValue("");
                                        }
                                        else
                                        {
                                            worksheet.Cell(currentRow, columnDataIndex).SetValue(
                                                (string)dataElement.GetType().GetProperties()[j - 1]
                                                    .GetValue(dataElement, null).ToString());
                                        }
                                    }
                                }
                            }
                        }

                        currentRow++;
                    }

                    var rangeTable = worksheet.Range(worksheet.Cell(1, 1), worksheet.Cell(currentRow - 1, maxCell));
                    rangeTable.Style.Border.SetTopBorder(XLBorderStyleValues.Thin);
                    rangeTable.Style.Border.SetBottomBorder(XLBorderStyleValues.Thin);
                    rangeTable.Style.Border.SetLeftBorder(XLBorderStyleValues.Thin);
                    rangeTable.Style.Border.SetRightBorder(XLBorderStyleValues.Thin);
                    rangeTable.Style.Border.SetInsideBorder(XLBorderStyleValues.Thin);
                    rangeTable.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                    var rangeAlignment = worksheet.Range(worksheet.Cell(1, 1), worksheet.Cell(currentRow - 1, maxCell));
                    rangeAlignment.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);

                    for (var i = 1; i <= maxCell; i++)
                    {
                        worksheet.Column(i).AdjustToContents();
                    }

                    #region Same Data Duplication Coloring

                    var passportSameDataReports = new List<QmsReportType>()
                    {
                        QmsReportType.QmsVIPReport,
                        QmsReportType.QmsReport,
                        QmsReportType.NoShowReport,
                        QmsReportType.ProcessTimeReport
                    };

                    if (passportSameDataReports.Contains(type))
                    {
                        var passportNumberCol = 0;
                        var headerRow = worksheet.FirstRow();
                        for (var col = 1; col <= headerRow.LastCellUsed().Address.ColumnNumber; col++)
                        {
                            if (headerRow.Cell(col).GetString() != SiteResources.PassportNumber) continue;
                            passportNumberCol = col;
                            break;
                        }

                        var passportNumberColumn = worksheet.Column(passportNumberCol);
                        var passportNumbers = new List<string>();

                        foreach (var cell in passportNumberColumn.CellsUsed().Skip(1)) // Skip header
                        {
                            passportNumbers.Add(cell.GetString());
                        }

                        var duplicates = passportNumbers.GroupBy(x => x)
                            .Where(g => g.Count() > 1)
                            .Select(g => g.Key)
                            .ToList();

                        var customColor = XLColor.FromArgb(252,213,180);

                        foreach (var cell in passportNumberColumn.CellsUsed().Skip(1))
                        {
                            if (duplicates.Contains(cell.GetString()))
                            {
                                var row = cell.WorksheetRow();
                                row.Style.Fill.BackgroundColor = customColor;
                            }
                        }
                    }

                    #endregion

                    #region Value Equality Coloring

                    var valueEqualityReports = new List<QmsReportType>()
                    {
                        QmsReportType.NotCompletedReport,
                    };

                    if (valueEqualityReports.Contains(type))
                    {
                        var notCompletedReasonColumn = 0;
                        var headerRow = worksheet.FirstRow();
                        for (var col = 1; col <= headerRow.LastCellUsed().Address.ColumnNumber; col++)
                        {
                            if (headerRow.Cell(col).GetString() != SiteResources.NotCompletedReason) continue;
                            notCompletedReasonColumn = col;
                            break;
                        }

                        var notCompletedColumn = worksheet.Column(notCompletedReasonColumn);
                        var customColor = XLColor.FromArgb(59, 177, 67);

                        foreach (var cell in notCompletedColumn.CellsUsed().Skip(1)) // Skip header
                        {
                            if (cell.GetString() == SiteResources.Completed)
                            {
                                var row = cell.WorksheetRow();
                                row.Style.Fill.BackgroundColor = customColor;
                            }
                        }
                    }

                    #endregion

                    #region Completed Report Case Coloring

                    var completedReport = new List<QmsReportType>()
                    {
                        QmsReportType.CompletedReport,
                    };

                    if (completedReport.Contains(type))
                    {
                        var appointmentNumberCol = 0;
                        var isApplicationCreatedOnThisTokenCol = 0;
                        var headerRow = worksheet.FirstRow();
                        for (var col = 1; col <= headerRow.LastCellUsed().Address.ColumnNumber; col++)
                        {
                            if (headerRow.Cell(col).GetString() != SiteResources.AppointmentNumber) continue;
                            appointmentNumberCol = col;
                            break;
                        }

                        for (var col = 1; col <= headerRow.LastCellUsed().Address.ColumnNumber; col++)
                        {
                            if (headerRow.Cell(col).GetString() != SiteResources.IsApplicationCreatedOnThisToken) continue;
                            isApplicationCreatedOnThisTokenCol = col;
                            break;
                        }

                        var appointmentNumberColumn = worksheet.Column(appointmentNumberCol);
                        var appointmentNumbers = new List<string>();

                        // Collect appointmentNumbers
                        foreach (var cell in appointmentNumberColumn.CellsUsed().Skip(1)) // Skip header
                        {
                            appointmentNumbers.Add(cell.GetString());
                        }

                        // Find duplicates
                        var duplicates = appointmentNumbers.GroupBy(x => x)
                            .Where(g => g.Count() > 1)
                            .Select(g => g.Key)
                            .ToList();

                        var customColor1 = XLColor.FromArgb(252, 213, 180);
                        var customColor2 = XLColor.FromArgb(177, 160, 199);

                        foreach (var cell in appointmentNumberColumn.CellsUsed().Skip(1))
                        {
                            if (duplicates.Contains(cell.GetString()))
                            {
                                var row = cell.WorksheetRow();
                                row.Style.Fill.BackgroundColor = (string)worksheet.Cell(row.RowNumber(), isApplicationCreatedOnThisTokenCol).Value == SiteResources.Yes ? customColor1 : customColor2;
                            }
                        }
                    }

                    #endregion
                }

                using (var finalStream = new MemoryStream())
                {
                    workbook.SaveAs(finalStream);
                    finalStream.Position = 0;

                    return File(finalStream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        $"{SiteResources.QmsAllReport}.xlsx");
                }
            }
        }
        

        #region Private Methods

        private async Task<dynamic> GenerateReport(QmsReportType reportType, FilterQmsAllReportsViewModel request)
        { 
            var reportGenerators = new Dictionary<QmsReportType, Func<Task<dynamic>>>
            {        
                {
                    QmsReportType.ActionReport, async () => (await GetPaginatedActionReport(new DataSourceRequest(),GenerateMultiReportRequest<FilterQmsReportViewModel>(request)))
                },
                {
                    QmsReportType.AllBranchWhiteListReport, async () => (await GetPaginatedAllBranchesWhiteListReport(new DataSourceRequest(),GenerateMultiReportRequest<FilterQmsReportViewModel>(request)))
                },
                {
                    QmsReportType.CompletedReport, async () => (await GetPaginatedQmsCompletedReport(new DataSourceRequest(),GenerateMultiReportRequest<FilterQmsReportViewModel>(request)))
                },
                {
                    QmsReportType.DepartmentReport, async () =>(await GetPaginatedDepartmentReport(new DataSourceRequest(),GenerateMultiReportRequest<FilterQmsReportViewModel>(request)))
                },
                {
                    QmsReportType.PersonnelReport, async () => (await GetPaginatedPersonalReport(new DataSourceRequest(),GenerateMultiReportRequest<FilterQmsReportViewModel>(request)))
                },
                {
                    QmsReportType.ProcessTimeReport, async () =>(await GetPaginatedQmsProcessTimeReport(new DataSourceRequest(),GenerateMultiReportRequest<FilterQmsReportViewModel>(request)))
                },
                {
                    QmsReportType.NotCompletedReport, async () => (await GetPaginatedNotCompletedReport(new DataSourceRequest(),GenerateMultiReportRequest<FilterQmsReportViewModel>(request)))
                },
                {
                    QmsReportType.NoShowReport, async () => (await GetPaginatedNoShowReport(new DataSourceRequest(),GenerateMultiReportRequest<FilterQmsReportViewModel>(request)))
                },
                {
                    QmsReportType.TatReport, async () => (await GetPaginatedQmsTatReport(new DataSourceRequest(),GenerateMultiReportRequest<FilterQmsReportViewModel>(request)))
                },
                {
                    QmsReportType.TokenReport, async () => (await GetPaginatedTokenReport(new DataSourceRequest(),GenerateMultiReportRequest<FilterTokenReportViewModel>(request)))
                },
                {
                    QmsReportType.QmsVIPReport, async () => (await GetPaginatedQmsVIPReport(new DataSourceRequest(),GenerateMultiReportRequest<FilterQmsReportViewModel>(request)))
                },
                {
                    QmsReportType.QmsReport, async () => (await GetPaginatedQmsMainReport(new DataSourceRequest(),GenerateMultiReportRequest<FilterQmsReportViewModel>(request)))
                }
            };


            return reportGenerators.TryGetValue(reportType, out var generateReportFunc)
                ? ConvertJsonToDynamic(await generateReportFunc(), GetQmsReportResultClass(reportType))
                : null;
        }

        private static Type GetQmsReportResultClass(QmsReportType reportType)
        {
            return reportType switch
            {
                QmsReportType.ActionReport => typeof(List<ActionReportViewModel>),
                QmsReportType.AllBranchWhiteListReport => typeof(List<QmsAllBranchWhiteListReportViewModel>),
                QmsReportType.CompletedReport => typeof(List<QmsCompletedReportViewModel>),
                QmsReportType.DepartmentReport => typeof(List<DepartmentReportViewModel>),
                QmsReportType.PersonnelReport => typeof(List<PersonalReportViewModel>),
                QmsReportType.ProcessTimeReport => typeof(List<QmsProcessTimeReportViewModel>),
                QmsReportType.NotCompletedReport => typeof(List<QmsNotCompletedReportViewModel>),
                QmsReportType.NoShowReport => typeof(List<QmsNoShowReportViewModel>),
                QmsReportType.TatReport => typeof(List<QmsTatReportViewModel>),
                QmsReportType.TokenReport => typeof(List<TokenReportViewModel>),
                QmsReportType.QmsVIPReport => typeof(List<QmsReportViewModel>),
                QmsReportType.QmsReport => typeof(List<QmsReportViewModel>),
                _ => throw new ArgumentOutOfRangeException(nameof(reportType), reportType, null)
            };
        }

        private static dynamic ConvertJsonToDynamic(JsonResult json, Type type)
        {
            if (json.Value is not DataSourceResult dataSource) return null;

            var jsonData = JsonConvert.SerializeObject(dataSource.Data);

            return JsonConvert.DeserializeObject(jsonData, type);
        }

        private static T GenerateMultiReportRequest<T>(FilterQmsAllReportsViewModel request) where T : new()
        {
            var reportRequest = new T();

            switch (reportRequest)
            {
                case FilterQmsReportViewModel model:
                    model.FilterBranchId = request.FilterBranchId;
                    model.FilterStartDate = request.FilterStartDate.GetValueOrDefault();
                    model.FilterEndDate = request.FilterEndDate.GetValueOrDefault();
                    break;
                case FilterTokenReportViewModel model:
                    model.FilterBranchId = request.FilterBranchId;
                    model.FilterStartDate = request.FilterStartDate.GetValueOrDefault();
                    model.FilterEndDate = request.FilterEndDate.GetValueOrDefault();
                    break;
            }

            return reportRequest;
        }

        private static void ModifyFilters(IList<IFilterDescriptor> filters)
        {
            if (filters == null || !filters.Any()) return;
            foreach (var filter in filters)
            {
                switch (filter)
                {
                    case FilterDescriptor descriptor:
                        descriptor.Value = descriptor.Value.ToString();
                        descriptor.MemberType = typeof(string);
                        break;
                    case CompositeFilterDescriptor filterDescriptor:
                        ModifyFilters(filterDescriptor.FilterDescriptors);
                        break;
                }
            }
        }

        #endregion

    }
}
