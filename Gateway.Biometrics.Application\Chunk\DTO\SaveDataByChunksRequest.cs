﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.Chunk.DTO
{
    public class SaveDataByChunksRequest : BaseServiceRequest
    {
        public int RecordId { get; set; }
        public string UniqueId { get; set; }
        public int Part { get; set; } = 0;
        public int TotalPart { get; set; } = 0;
        public bool IsFirst => Part == 0;
        public bool IsLast => Part >= TotalPart - 1;
        public bool FromOffline { get; set; }
        public string ChunkData { get; set; }
    }
}
