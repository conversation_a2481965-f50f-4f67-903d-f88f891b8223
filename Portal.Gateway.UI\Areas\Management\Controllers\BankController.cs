﻿using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Management.Bank;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Management.Bank;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.Bank;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class BankController : BaseController<BankController>
    {
        public BankController(
                IOptions<AppSettings> appSettings,
                ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        { }

        #region BankPos

        #region Add

        public IActionResult PartialAddBankPos()
        {
            return PartialView("_AddBankPos");
        }

        [HttpPost]
        public async Task<IActionResult> AddBankPos(AddBankPosViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddBankPosApiRequest
            {
                BankId = viewModel.BankId,
                EncryptionKey = viewModel.EncryptionKey,
                Is3dEnabled = viewModel.Is3dEnabled,
                MerchantCode = viewModel.MerchantCode,
                Password = viewModel.Password,
                Payment3dConfirmUrl = viewModel.Payment3dConfirmUrl,
                Payment3dUrl = viewModel.Payment3dUrl,
                PaymentUrl = viewModel.PaymentUrl,
                StoreKey = viewModel.StoreKey,
                TerminalCode = viewModel.TerminalCode,
                Username = viewModel.Username
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Management.AddBankPos, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateBankPos(string encryptedBankPosId)
        {
            int id = encryptedBankPosId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BankPosApiResponse>>
                (ApiMethodName.Management.GetBankPos + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdateBankPosViewModel
            {
                Id = apiResponse.Data.Id,
                BankId = apiResponse.Data.BankId,
                EncryptionKey = apiResponse.Data.EncryptionKey,
                Is3dEnabled = apiResponse.Data.Is3dEnabled,
                IsActive = apiResponse.Data.IsActive,
                MerchantCode = apiResponse.Data.MerchantCode,
                Password = apiResponse.Data.Password,
                Payment3dConfirmUrl  = apiResponse.Data.Payment3dConfirmUrl,
                Payment3dUrl = apiResponse.Data.Payment3dUrl,
                PaymentUrl = apiResponse.Data.PaymentUrl,
                StoreKey = apiResponse.Data.StoreKey,
                TerminalCode = apiResponse.Data.TerminalCode,
                Username = apiResponse.Data.Username
            };

            return PartialView("_UpdateBankPos", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateBankPos(UpdateBankPosViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateBankPosApiRequest
            {
                Id = viewModel.Id,
                BankId = viewModel.BankId,
                EncryptionKey = viewModel.EncryptionKey,
                Is3dEnabled = viewModel.Is3dEnabled,
                IsActive = viewModel.IsActive,
                MerchantCode = viewModel.MerchantCode,
                Password = viewModel.Password,
                Payment3dConfirmUrl = viewModel.Payment3dConfirmUrl,
                Payment3dUrl = viewModel.Payment3dUrl,
                PaymentUrl = viewModel.PaymentUrl,
                StoreKey = viewModel.StoreKey,
                TerminalCode = viewModel.TerminalCode,
                Username = viewModel.Username
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateBankPos, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteBankPos(string encryptedBankPosId)
        {
            int id = encryptedBankPosId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Management.DeleteBankPos + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> PartialBankPos(string encryptedBankPosId)
        {
            int id = encryptedBankPosId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BankPosApiResponse>>
                (ApiMethodName.Management.GetBankPos + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new BankPosViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                BankTypeId = apiResponse.Data.BankId,
                EncryptionKey = apiResponse.Data.EncryptionKey,
                Is3dEnabled = apiResponse.Data.Is3dEnabled,
                IsActive = apiResponse.Data.IsActive,
                MerchantCode = apiResponse.Data.MerchantCode,
                Password = apiResponse.Data.Password,
                Payment3dConfirmUrl = apiResponse.Data.Payment3dConfirmUrl,
                Payment3dUrl = apiResponse.Data.Payment3dUrl,
                PaymentUrl = apiResponse.Data.PaymentUrl,
                StoreKey = apiResponse.Data.StoreKey,
                TerminalCode = apiResponse.Data.TerminalCode,
                Username = apiResponse.Data.Username
            };

            return PartialView("_BankPos", viewModel);
        }

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult BankPosList()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedBankPos([DataSourceRequest] DataSourceRequest request, FilterBankPosViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedBankPosApiRequest
            {
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<BankPosesApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedBankPoses, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<BankPosViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().BankPoses
                    .Select(p => new BankPosViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        BankTypeId = p.BankId,
                        EncryptionKey = p.EncryptionKey,
                        Is3dEnabled = p.Is3dEnabled,
                        IsActive = p.IsActive,
                        MerchantCode = p.MerchantCode,
                        Password = p.Password,
                        Payment3dConfirmUrl = p.Payment3dConfirmUrl,
                        Payment3dUrl = p.Payment3dUrl,
                        PaymentUrl = p.PaymentUrl,
                        StoreKey = p.StoreKey,
                        TerminalCode = p.TerminalCode,
                        Username = p.Username
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #endregion

        #endregion

        #region BankPosInstallment

        #region Add

        [HttpPost]
        public async Task<IActionResult> AddUpdateBankPosInstallment(AddUpdateBankPosInstallmentViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddUpdateBankPosInstallmentApiRequest
            {
                ChannelId = viewModel.ChannelId,
                BankPosInstallments = viewModel.BankPosInstallments.Select(p => new AddUpdateBankPosInstallmentApiRequest.BankPosInstallment() 
                {
                    BankPosId = p.BankPosId,
                    CommissionRate = p.CommissionRate,
                    Id = p.Id,
                    InstallmentCount = p.InstallmentCount,
                    IsActive = p.IsActive
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Management.AddUpdateBankPosInstallment, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> BankPosInstallmentList()
        {
            return View();
        }

        public async Task<IActionResult> PartialAddUpdateBankPosInstallment(int channelId, int bankPosId)
        {
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<BankPosInstallmentApiResponse>>
                (new BankPosInstallmentApiRequest() 
                {
                    ChannelId = channelId
                }, ApiMethodName.Management.GetBankPosInstallment, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new AddUpdateBankPosInstallmentViewModel
            {
                ChannelId = channelId,
                BankPosInstallments = Enumerable.Range(1, 18).Select((p, index) => 
                {
                    var existingInstallment = apiResponse.Data.BankPosInstallments.Where(p => p.BankPosId == bankPosId).FirstOrDefault(p => p.InstallmentCount == index + 1);

                    if(existingInstallment != null)
                    {
                        return new AddUpdateBankPosInstallmentViewModel.BankPosInstallment()
                        {
                            BankPosId = existingInstallment.BankPosId,
                            CommissionRate = existingInstallment.CommissionRate,
                            Id = existingInstallment.Id,
                            InstallmentCount = existingInstallment.InstallmentCount,
                            IsActive = existingInstallment.IsActive
                        };
                    }
                    else
                    {
                        return new AddUpdateBankPosInstallmentViewModel.BankPosInstallment()
                        {
                            BankPosId = bankPosId,
                            CommissionRate = 0,
                            Id = null,
                            InstallmentCount = index + 1,
                            IsActive = false
                        };
                    }                    
                }).ToArray()
            };

            return PartialView("_AddUpdateBankPosInstallment", viewModel);
        }

        #endregion

        #endregion

    }
}
