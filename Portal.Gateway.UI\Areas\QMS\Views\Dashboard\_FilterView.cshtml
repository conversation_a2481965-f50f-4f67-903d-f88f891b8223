@using Microsoft.AspNetCore.Http
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using Microsoft.Extensions.Caching.Memory
@using Portal.Gateway.ApiModel.Responses.Management.RoleAction
@using SessionExtensions = Portal.Gateway.UI.Extensions.SessionExtensions
@using Kendo.Mvc.TagHelpers
@using Portal.Gateway.UI.Constants
@using Portal.Gateway.UI.Models
@inject IHttpContextAccessor HttpContextAccessor
@inject ICacheHelper CacheHelper
@{
    var currentUser = SessionExtensions.Get<UserModel>(HttpContextAccessor.HttpContext?.Session, SessionKeys.UserSession);
    var qmsDashboardFilterAccessedBranchOnly = false;
    var roleActions = await CacheHelper.GetRoleActionsAsync();

    qmsDashboardFilterAccessedBranchOnly = roleActions.RoleActionSites.Where(r => currentUser.RoleIds.Contains(r.Role.Id))
        .Any(p => p.RoleActions.Any(q => q.Action.ActionTranslations.Any(r => r.Name == SiteResources.QmsDashboardFilterAccessedBranchOnly) && q.Action.IsActive));
}

<link href="~/assets/plugins/custom/select2/select2.min.css" rel="stylesheet" />
<script src="~/assets/plugins/custom/select2/select2.min.js"></script>

<style>
    .select2-result-description {
        font-size: 13px;
        color: #777777;
        margin-top: 4px;
    }

    .select2-results__option--highlighted .select2-result-description {
        color: #fff;
    }
</style>

<input id="permission" type="hidden" value="@qmsDashboardFilterAccessedBranchOnly.ToString().ToLower()">
<input type="hidden" id="hdnStartDate" value="" />
<input type="hidden" id="hdnEndDate" value="" />

<div>
    <div>
        <div class="row">
            <div class="col-md-3">
                <div class="form-group">
                    <label>@SiteResources.DateRange</label>
                    <div id="dateRange" class="pull-right form-control">
                        <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>
                        <span></span>
                    </div>
                </div>
            </div>
            <div class="col-md-9">
                <div class="form-group">
                    <div class="row">
                        <div class="col-lg-3">
                            <label>@SiteResources.Country</label>
                            <select class="custom-select" id="cmbCountry" onchange="onChangeCountry(this.value)">
                            </select>
                        </div>
                        <div class="col-lg-3">
                            <label>@SiteResources.Branch</label>
                            <div class="input-group">
                                <select class="custom-select" id="cmbBranch">
                                    <option value="0">All</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-lg-2" style="margin-bottom:10px">
                            <label>@SiteResources.FilterVip.ToTitleCase()</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="" id="cmbVip">
                            </div>
                        </div>
                        <div class="col-lg-4"  style="margin: auto">
                            <div>
                                <button type="button" id="btnResetFilter" class="btn btn-light waves-effect waves-light"><i class=" fas fa-undo"></i> @SiteResources.QmsDashboardResetButtonLabelName</button>
                                <button type="button" id="btnSearch" class="btn btn-primary waves-light">
                                    <i class=" fas fa-filter"></i> @SiteResources.QmsDashboardApplyFiltersButtonLabelName
                                </button>
                            </div>
                            </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>

    $(document).ready(function () {

        $('#dateRange').daterangepicker({
            format: "MM/DD/YYYY",
            startDate: moment().subtract(29, "days"),
            endDate: moment(),
            ranges: {
                Today: [moment(), moment()],
                Yesterday: [moment().subtract(1, "days"), moment().subtract(1, "days")],
                "Last 7 Days": [moment().subtract(6, "days"), moment()],
                "Last 30 Days": [moment().subtract(29, "days"), moment()],
                "This Month": [moment().startOf("month"), moment().endOf("month")],
                "Last Month": [moment().subtract(1, "month").startOf("month"), moment().subtract(1, "month").endOf("month")],
            },
            opens: "left",
            drops: "down",
            buttonClasses: ["btn", "btn-sm"],
            applyClass: "btn-success",
            cancelClass: "btn-secondary",
            separator: " to ",
            locale: {
                applyLabel: "Submit",
                cancelLabel: "Cancel",
                fromLabel: "From",
                toLabel: "To",
                customRangeLabel: "Custom",
                daysOfWeek: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"],
                monthNames: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
                firstDay: 1
            }
        },
            function (start, end) {
                $('#hdnStartDate').val(start.format('MM/DD/YYYY'));
                $('#hdnEndDate').val(end.format('MM/DD/YYYY'));
                $("#dateRange span").html(start.format("MMMM D, YYYY") + " - " + end.format("MMMM D, YYYY"));
            }
        );

        $('#dateRange span').html(moment().subtract(29, "days").format("MMMM D, YYYY") + " - " + moment().format("MMMM D, YYYY"));

        $('#hdnStartDate').val(moment().subtract(29, "days").format('MM/DD/YYYY'));
        $('#hdnEndDate').val(moment().format('MM/DD/YYYY'));

        initCountries();
    });

    function initCountries() {

        const cmbCountry = $('#cmbCountry');
        var permissionValue = document.getElementById('permission').value;

        $.ajax({
            type: 'POST',
            url: permissionValue === 'true' ?
                '/Parameter/GetPermissionCountriesHasBranchSelectList' :
                '/Parameter/GetCountriesHasBranchSelectList',
            dataType: 'JSON',
            success: function (data) {
                $.each(data,
                    function(key, val) {
                        cmbCountry.append('<option value="' + val.Value + '">' + val.Text + '</option>');
                    });

                var selectElement = document.getElementById("cmbCountry");
                selectElement.selectedIndex = 0;
                onChangeCountry(selectElement.value);
            },
            error: function () {
                cmbCountry.html('<option value="-1">none available</option>');
            }
        });
    }

    function onChangeCountry(countryId) {

        const cmbBranch = $('#cmbBranch');

        $.ajax({
            type: 'POST',
            url: "/Parameter/GetCachedBranchesByCountrySelectList?countryId=" + countryId,
            dataType: 'JSON',
            success: function (data) {

                cmbBranch.html('');

                cmbBranch.append('<option value="' + 0 + '">' + jsResources.All + '</option>');

                $.each(data, function (key, val) {
                    cmbBranch.append('<option value="' + val.Value + '">' + val.Text + '</option>');
                })
            },
            error: function () {
                cmbBranch.html('<option value="-1">none available</option>');
            }
        });
    }

    $("#btnResetFilter").on('click', function () {
        $('#dateRange span').html(moment().subtract(29, "days").format("MMMM D, YYYY") + " - " + moment().format("MMMM D, YYYY"));
        $('#hdnStartDate').val(moment().subtract(29, "days").format('MM/DD/YYYY'));
        $('#hdnEndDate').val(moment().format('MM/DD/YYYY'));
        $('select#cmbCountry').prop('selectedIndex', 0);
        $('select#cmbBranch').html('');
        $('select#cmbBranch').append('<option value="' + 0 + '">' + jsResources.All + '</option>');
        $('select#cmbBranch').prop('selectedIndex', 0);
        $('#cmbVip').prop('checked', false);
    });

</script>