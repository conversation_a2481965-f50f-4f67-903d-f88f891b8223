﻿using FluentValidation.Resources;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Agency.AgencyUser;
using Portal.Gateway.ApiModel.Requests.DocumentManagement;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Agency.AgencyUser;
using Portal.Gateway.ApiModel.Responses.DocumentManagement;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Agency.ViewModels.AgencyUser;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Agency.Controllers
{
    [Area("Agency")]
    public class AgencyUserController : BaseController<AgencyUserController>
    {
        public AgencyUserController(
          IOptions<AppSettings> appSettings,
          ICacheHelper cacheHelper)
        : base(appSettings, cacheHelper)
        {

        }

        #region Add

        public async Task<IActionResult> PartialAddAgencyUser()
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<AgenciesApiResponse>>
                (ApiMethodName.Parameter.GetAgencies, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            var viewModel = new AddAgencyUserViewModel()
            {
                AgencySelectList = apiResponse.Data.Agencies?.Select(p => new SelectListItem()
                {
                    Text = p.Name,
                    Value = p.Id.ToEncrypt()
                }).OrderBy(q => q.Text).ToList(),
                ActivationStatusList = EnumHelper.GetEnumAsDictionary(typeof(Contracts.Entities.Enums.ActivationStatusType)).Select(p => new SelectListItem()
                {
                    Text = p.Value.ToTitleCase(),
                    Value = p.Key.ToEncrypt()
                }).OrderBy(q => q.Text).ToList()
            };

            viewModel.AgencySelectList.Insert(0, new SelectListItem() { Text = SiteResources.Select, Value = "" });
            viewModel.ActivationStatusList.Insert(0, new SelectListItem() { Text = SiteResources.Select, Value = "" });

            return PartialView("_AddAgencyUser", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddAgencyUser(AddAgencyUserViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddAgencyUserApiRequest
            {
                Position = viewModel.Position,
                Name = viewModel.Name,
                Surname = viewModel.Surname,
                Email = viewModel.Email,
                PhoneNumber = viewModel.PhoneNumber,
                AgencyId = viewModel.EncryptedAgencyId.ToDecryptInt(),
                IsAuthorized = viewModel.IsAuthorized,
                ActivationStatusId = viewModel.EncryptedActivationStatusId.ToDecryptInt(),
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddAgencyUserApiResponse>>
                (apiRequest, ApiMethodName.Agency.AddAgencyUser, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateAgencyUser(string encryptedAgencyUserId)
        {
            var apiRequest = new GetAgencyUserApiRequest
            {
                Id = encryptedAgencyUserId.ToDecryptInt()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AgencyUserApiResponse>>
                (apiRequest, ApiMethodName.Agency.GetAgencyUser, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var apiResponseAgency = await PortalHttpClientHelper
                .GetAsync<ApiResponse<AgenciesApiResponse>>
                (ApiMethodName.Parameter.GetAgencies, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            var viewModel = new UpdateAgencyUserViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Name = apiResponse.Data.Name,
                Surname = apiResponse.Data.Surname,
                Email = apiResponse.Data.Email,
                PhoneNumber = apiResponse.Data.PhoneNumber,
                Position = apiResponse.Data.Position,
                EncryptedActivationStatusId = apiResponse.Data.ActivationStatusId.ToEncrypt(),
                IsAuthorized = apiResponse.Data.IsAuthorized,
                EncryptedAgencyId = apiResponse.Data.AgencyId.ToEncrypt(),
                AgencySelectList = apiResponseAgency.Data.Agencies?.Select(p => new SelectListItem()
                {
                    Text = p.Name,
                    Value = p.Id.ToEncrypt()
                }).OrderBy(q => q.Text).ToList(),
                ActivationStatusList = EnumHelper.GetEnumAsDictionary(typeof(Contracts.Entities.Enums.ActivationStatusType)).Select(p => new SelectListItem()
                {
                    Text = p.Value.ToTitleCase(),
                    Value = p.Key.ToEncrypt()
                }).OrderBy(q => q.Text).ToList()
            };

            return PartialView("_UpdateAgencyUser", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateAgencyUser(UpdateAgencyUserViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateAgencyUserApiRequest
            {
                Id = viewModel.EncryptedId.ToDecryptInt(),
                Position = viewModel.Position,
                Name = viewModel.Name,
                Surname = viewModel.Surname,
                Email = viewModel.Email,
                PhoneNumber = viewModel.PhoneNumber,
                AgencyId = viewModel.EncryptedAgencyId.ToDecryptInt(),
                IsAuthorized = viewModel.IsAuthorized,
                ActivationStatusId = viewModel.EncryptedActivationStatusId.ToDecryptInt(),
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Agency.UpdateAgencyUser, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteAgencyUser(string encryptedAgencyUserId)
        {
            int id = encryptedAgencyUserId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Agency.DeleteAgencyUser + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> PartialAgencyUser(string encryptedAgencyUserId)
        {
            var apiRequest = new GetAgencyUserApiRequest
            {
                Id = encryptedAgencyUserId.ToDecryptInt()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AgencyUserApiResponse>>
                (apiRequest, ApiMethodName.Agency.GetAgencyUser, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new AgencyUserViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                AgencyName = apiResponse.Data.AgencyName,
                Position = apiResponse.Data.Position,
                Name = apiResponse.Data.Name,
                Surname = apiResponse.Data.Surname,
                Email = apiResponse.Data.Email,
                PhoneNumber = apiResponse.Data.PhoneNumber,
                ActivationStatusId = apiResponse.Data.ActivationStatusId,
                IsAuthorized = apiResponse.Data.IsAuthorized,
                Documents = apiResponse.Data.Documents.Select(q => new AgencyUserViewModel.Document()
                {
                    EncryptedDocumentId = q.DocumentId.ToEncrypt(),
                    EncryptedFileId = q.FileId.ToEncrypt(),
                    FileTypeId = q.FileTypeId,
                    DisplayFileName = q.DisplayFileName,
                    DisplayUniqueFileName = q.DisplayUniqueFileName
                }).ToList()
            };

            return PartialView("_AgencyUser", viewModel);
        }

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult List(string encryptedAgencyId)
        {
            var viewModel = new FilterAgencyUserViewModel() {};
            if (!string.IsNullOrEmpty(encryptedAgencyId))
                viewModel.FilterAgencyId = encryptedAgencyId.ToDecryptInt();

            return View(viewModel);
        }

        public async Task<IActionResult> GetPaginatedAgencyUsers([DataSourceRequest] DataSourceRequest request, FilterAgencyUserViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedAgencyUsersApiRequest
            {
                Name = filterViewModel.FilterName,
                Surname = filterViewModel.FilterSurname,
                Email = filterViewModel.FilterEmail,
                AgencyId = filterViewModel.FilterAgencyId,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<AgencyUsersApiResponse>>>
                (apiRequest, ApiMethodName.Agency.GetPaginatedAgencyUsers, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<AgencyUserViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().Users
                    .Select(p => new AgencyUserViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        AgencyName = p.AgencyName,
                        Position = p.Position,
                        Name = p.Name,
                        Surname = p.Surname,
                        Email = p.Email,
                        PhoneNumber = p.PhoneNumber,
                        ActivationStatusId = p.ActivationStatusId,
                        IsAuthorized = p.IsAuthorized
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #endregion

        public async Task<IActionResult> DownloadFile(string encryptedDocumentId)
        {
            var documentsApiRequest = new DocumentsApiRequest
            {
                Ids = new List<int> { encryptedDocumentId.ToDecryptInt() },
                IsFileContentIncluded = true
            };

            var getDocumentsApiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<DocumentsApiResponse>>
                (documentsApiRequest, ApiMethodName.DocumentManagement.GetDocuments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await getDocumentsApiResponse.Validate(out ResultModel getDocumentsResult).ConfigureAwait(false))
                return Json(null);

            var document = getDocumentsApiResponse.Data.Documents.FirstOrDefault();

            if (document == null || document.FileContent == null)
                return Json(null);

            return File(document.FileContent, "application/octet-stream", document.UniqueFileNameWithExtension);
        }
    }
}
