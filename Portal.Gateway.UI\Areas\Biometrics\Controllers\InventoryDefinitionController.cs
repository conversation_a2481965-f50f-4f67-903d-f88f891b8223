﻿using DocumentFormat.OpenXml.Wordprocessing;
using Gateway.Extensions;
using Gateway.Http;
using Gateway.Redis;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.QmsFolder;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.UI.Areas.Biometrics.Models.InventoryDefinition.RequestModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.InventoryDefinition.Results;
using Portal.Gateway.UI.Areas.Biometrics.Models.InventoryDefinition.ViewModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.InventoryDefinition;
using Portal.Gateway.UI.Models;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using System;
using System.Linq;
using Newtonsoft.Json;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Biometrics.Models.Office.Results;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Areas.Biometrics.Models.InventoryValueSet.Results;
using Portal.Gateway.UI.Constants;

namespace Portal.Gateway.UI.Areas.Biometrics.Controllers
{
    
    [Area("Biometrics")]
    public class InventoryDefinitionController : BaseController<InventoryDefinitionController>
    {
        public InventoryDefinitionController(
            IOptions<AppSettings> appSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper) { }

        #region Add

        public async Task<IActionResult> PartialAddInventoryDefinition()
        {
            var viewModel = new AddInventoryDefinitionViewModel();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<InventoryValueSetsEmptyResponse>>(null, BiometricsEndPoint.ParameterGetInventoryValueSetsEmpty, AppSettings.Biometrics.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (apiResponse.Data != null)
            {
                viewModel.InventoryValueSets = apiResponse.Data.InventoryValueSets;
            }

            return PartialView("_AddInventoryDefinition", viewModel);
        }

        [HttpPost]
        public async Task<JsonResult> AddInventoryDefinition(AddInventoryDefinitionViewModel viewModel)
        {
            //var viewModel = JsonConvert.DeserializeObject<AddInventoryDefinitionViewModel>(jsondata);

            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var request = new AddInventoryDefinitionRequestModel
            {
                InventoryTypeId = viewModel.InventoryTypeId,
                Description = viewModel.Description,
                InventoryValueSets = viewModel.InventoryValueSets,
            };

            var apiResponse = await RestHttpClient.Create().Post<AddInventoryDefinitionResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.AddInventoryDefinition, headers, request);

            if (apiResponse.Data == null)
                return Json(new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateInventoryDefinition(string encryptedInventoryDefinitionId)
        {
            if (encryptedInventoryDefinitionId.IsNullOrWhitespace())
            {
                return Content("Missing InventoryDefinition id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedInventoryDefinitionId.ToDecrypt());

            var apiResponse = await RestHttpClient.Create().Get<GetInventoryDefinitionResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetInventoryDefinition + resourceId, headers);

            var viewModel = new UpdateInventoryDefinitionViewModel
            {
                Id = apiResponse.Data.Id,
                InventoryTypeId = apiResponse.Data.InventoryTypeId,
                Description = apiResponse.Data.Description,
                InventoryTypeModel = apiResponse.Data.InventoryType,
                InventoryValueSets = apiResponse.Data.InventoryValueSets,
            };

            return PartialView("_UpdateInventoryDefinition", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateInventoryDefinition(UpdateInventoryDefinitionViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid) //buraya fluent val yazılıcak viewmodellere
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var apiRequest = new UpdateInventoryDefinitionRequestModel
            {
                InventoryTypeId = viewModel.InventoryTypeId,
                Description = viewModel.Description,
                InventoryValueSets = viewModel.InventoryValueSets,
            };

            var apiResponse = await RestHttpClient.Create().Put<UpdateInventoryDefinitionResult>(
                AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.UpdateInventoryDefinition + viewModel.Id, headers,
                apiRequest);


            if (apiResponse.Data == null)
                return Json(new ResultModel { Message = apiResponse.ValidationMessages.ToPlainText(), ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion
        

        #region Get

        public async Task<IActionResult> PartialDetailInventoryDefinition(string encryptedInventoryDefinitionId)
        {
            if (encryptedInventoryDefinitionId.IsNullOrWhitespace())
            {
                return Content("Missing InventoryDefinition id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedInventoryDefinitionId.ToDecrypt());

            var apiResponse = await RestHttpClient.Create().Get<GetInventoryDefinitionResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetInventoryDefinition + resourceId, headers);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var viewModel = new InventoryDefinitionViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                InventoryType = apiResponse.Data.InventoryType,
                InventoryTypeId = apiResponse.Data.InventoryTypeId,
                InventoryValueSets = apiResponse.Data.InventoryValueSets,
                
            };

            return PartialView("_DetailInventoryDefinition", viewModel);
        }


        [Action(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedInventoryDefinitions([DataSourceRequest] DataSourceRequest request, FilterInventoryDefinitionViewModel filterViewModel)
        {
            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedInventoryDefinitionApiRequest
            {
                FilterInventoryTypeId = filterViewModel.FilterInventoryTypeId,
                Pagination = new QmsPaginationApiRequest
                {
                    PageNumber = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = "Id",
                    Ascending = true
                }
            };

            var apiResponse = await RestHttpClient.Create().Post<GetPaginatedInventoryDefinitionsResult>(
                AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.GetPaginatedInventoryDefinitions, headers, apiRequest);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var paginatedData = apiResponse.Data.Select(p =>
                new InventoryDefinitionViewModel
                {
                    EncryptedId = p.Id.ToEncrypt(),
                    InventoryType = p.InventoryType,
                    InventoryTypeId = p.InventoryTypeId,
                    Description = p.Description,
                    InventoryValueSets = p.InventoryValueSets
                   
                }).ToList();

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.TotalNumberOfRecords });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteInventoryDefinition(string encryptedInventoryDefinitionId)
        {
            if (encryptedInventoryDefinitionId.IsNullOrWhitespace())
            {
                return Content("Missing InventoryDefinition id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedInventoryDefinitionId.ToDecrypt());

            var apiResponse = await RestHttpClient.Create().Delete<DeleteInventoryDefinitionResult>(AppSettings.Biometrics.BaseApiUrl + BiometricsEndPoint.DeleteInventoryDefinition + resourceId, headers);

            if (apiResponse.Data == null)
                return Json(apiResponse.Message);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

    }

}
