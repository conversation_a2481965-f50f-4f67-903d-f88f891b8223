﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.Contracts.Entities.Constants;
using Kendo.Mvc.UI;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Requests;
using System.Linq;
using System.Collections.Generic;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.ApplicationStatus;
using Kendo.Mvc.Extensions;
using Portal.Gateway.UI.Validators;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.UI.Models;
using Portal.Gateway.Resources;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.ApiModel.Requests.Appointment.ApplicationStatus;
using Portal.Gateway.ApiModel.Responses.Appointment.ApplicationStatus;
using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using Microsoft.Extensions.Caching.Memory;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.ApiModel.Responses.General;

namespace Portal.Gateway.UI.Areas.Appointment.Controllers
{
    [Area("Appointment")]
    public class ApplicationStatusController : BaseController<ApplicationStatusController>
    {
        public ApplicationStatusController(
            IOptions<AppSettings> appSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        #region Add

        public IActionResult PartialAddApplicationStatus()
        {
            var viewModel = new AddApplicationStatusViewModel() { };

            foreach (var item in EnumHelper.GetEnumAsDictionary(typeof(Language)))
            {
                viewModel.ApplicationStatusTranslations.Add(new AddApplicationStatusTranslationViewModel { LanguageId = item.Key });
            }

            return PartialView("_AddApplicationStatus", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddApplicationStatus(AddApplicationStatusViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddApplicationStatusApiRequest
            {
                IsEmailNeed = viewModel.IsEmailNeed,
                IsSmsNeed = viewModel.IsSmsNeed,
                ApplicationStatusTranslations = viewModel.ApplicationStatusTranslations.Select(p => new AddApplicationStatusTranslationApiRequest
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                    Description = p.Description
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Appointment.AddApplicationStatus, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateApplicationStatus(string encryptedApplicationStatusId)
        {
            int id = encryptedApplicationStatusId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationStatusApiResponse>>
                (ApiMethodName.Appointment.GetApplicationStatus + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdateApplicationStatusViewModel
            {
                Id = apiResponse.Data.Id,
                IsSmsNeed = apiResponse.Data.IsSmsNeed,
                IsEmailNeed = apiResponse.Data.IsEmailNeed,
                IsActive = apiResponse.Data.IsActive,
                IsStatic = apiResponse.Data.IsStatic,
                ApplicationStatusTranslations = apiResponse.Data.ApplicationStatusTranslations.Select(p => new UpdateApplicationStatusTranslationViewModel
                {
                    Id = p.Id,
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                    Description = p.Description
                }).ToList()
            };

            return PartialView("_UpdateApplicationStatus", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateApplicationStatus(UpdateApplicationStatusViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateApplicationStatusApiRequest
            {
                Id = viewModel.Id,
                IsSmsNeed = viewModel.IsSmsNeed,
                IsEmailNeed = viewModel.IsEmailNeed,
                IsActive = viewModel.IsActive,
                ApplicationStatusTranslations = viewModel.ApplicationStatusTranslations.Select(p => new UpdateApplicationStatusTranslationApiRequest
                {
                    Id = p.Id,
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                    Description = p.Description
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<EmptyApiResponse>>
                (apiRequest, ApiMethodName.Appointment.UpdateApplicationStatus, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteApplicationStatus(string encryptedApplicationStatusId)
        {
            int id = encryptedApplicationStatusId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Appointment.DeleteApplicationStatus + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> PartialApplicationStatus(string encryptedApplicationStatusId)
        {
            int id = encryptedApplicationStatusId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationStatusApiResponse>>
                (ApiMethodName.Appointment.GetApplicationStatus + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new ApplicationStatusViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                IsEmailNeed = apiResponse.Data.IsEmailNeed,
                IsSmsNeed = apiResponse.Data.IsSmsNeed,
                IsActive = apiResponse.Data.IsActive,
                IsStatic = apiResponse.Data.IsStatic,
                ApplicationStatusTranslations = apiResponse.Data.ApplicationStatusTranslations.Select(p => new AddApplicationStatusTranslationViewModel()
                {
                    Name = p.Name,
                    LanguageId = p.LanguageId,
                    Description = p.Description
                }).ToList()
            };

            return PartialView("_ApplicationStatus", viewModel);
        }

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult List()
        {
            var viewModel = new FilterApplicationStatusViewModel()
            {
                LanguageId = LanguageId,
                BoolSelectList = new List<SelectListItem>()
            };

            viewModel.BoolSelectList.Insert(0, new SelectListItem() { Text = SiteResources.Select, Value = "" });
            viewModel.BoolSelectList.Insert(1, new SelectListItem() { Text = SiteResources.Yes, Value = "1" });
            viewModel.BoolSelectList.Insert(2, new SelectListItem() { Text = SiteResources.No, Value = "0" });

            return View(viewModel);
        }

        public async Task<IActionResult> GetPaginatedApplicationStatus([DataSourceRequest] DataSourceRequest request, FilterApplicationStatusViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedApplicationStatusApiRequest
            {
                Name = filterViewModel.FilterName,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            if (filterViewModel.FilterIsEmailNeed != null)
                apiRequest.IsEmailNeed = Convert.ToBoolean(filterViewModel.FilterIsEmailNeed);

            if (filterViewModel.FilterIsSmsNeed != null)
                apiRequest.IsSmsNeed = Convert.ToBoolean(filterViewModel.FilterIsSmsNeed);

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedApplicationStatusApiResponse>>>
                (apiRequest, ApiMethodName.Appointment.GetPaginatedApplicationStatus, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<ApplicationStatusItemViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().ApplicationStatus
                    .Select(p => new ApplicationStatusItemViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        Name = p.Name,
                        IsActive = p.IsActive,
                        IsEmailNeed = p.IsEmailNeed,
                        IsSmsNeed = p.IsSmsNeed,
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        [HttpGet]
        public async Task<IActionResult> IncorrectApplicationStatus(string encryptedApplicationId)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("/");
            }

            var apiResponse = await PortalHttpClientHelper
                                    .GetAsync<ApiResponse<IncorrectApplicationStatusApiResponse>>
                                    (ApiMethodName.Parameter.GetIncorrectApplicationStatus + encryptedApplicationId.ToDecryptInt(), AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                                    .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", result);
                return RedirectToAction("/");
            }

            var viewModel = new IncorrectApplicationStatusViewModel()
            {
                EncryptedApplicationId = apiResponse.Data.ApplicationId.ToEncrypt(),

                IncorrectApplcationStatuses = apiResponse.Data.IncorrectApplcationStatuses
                                        .Select(p => new IncorrectApplicationStatusesViewModel()
                                        {
                                            ApplicationStatusHistoryId = p.ApplicationStatusHistoryId,
                                            IsActive = p.IsActive,
                                            IsPreChecked = p.IsPreChecked,
                                            Status = p.IdName.NameTranslation.Any(q => q.LanguageId == LanguageId) ?
                                                p.IdName.NameTranslation.First(q => q.LanguageId == LanguageId).Name :
                                                p.IdName.NameTranslation.FirstOrDefault().Name,
                                            UpdateBy = p.UpdateBy,
                                            UpdateDate = p.UpdateDate
                                        }
                                        ).ToList()
            };

            return View(viewModel);
        }
        [HttpPost]
        public async Task<IActionResult> UpdateIncorrectApplicationStatus(IncorrectApplicationStatusViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateIncorrectApplicationStatusApiRequest
            {
                ApplicationId = viewModel.EncryptedApplicationId.ToDecryptInt(),
                ReasonId = viewModel.ReasonId,
                Reason = viewModel.Reason == null ? string.Empty : viewModel.Reason,
                CreatedBy = UserSession.UserId,
                IncorrectApplicationStatusIds = viewModel.IncorrectApplcationStatuses.Where(p => !p.IsActive)
                                           .Select(p => p.ApplicationStatusHistoryId)
                                           .ToList(),
                IncorrectApplicationPreChecked = viewModel.IncorrectApplcationStatuses.Select(p => p.IsPreChecked)
                                           .ToList()
            };

            var apiResponse = await PortalHttpClientHelper
            .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
            (apiRequest, ApiMethodName.Appointment.UpdateIncorrectApplicationStatus, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
            .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel
            {
                Message = ResultMessage.OperationIsSuccessful.ToDescription(),
                ResultType = ResultType.Success
            });
        }
        #endregion
    }
}
