﻿using System;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using Portal.Gateway.Api.Hubs;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.PrinterAgent;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Dto.PrinterAgent.Response;
using Portal.Gateway.Contracts.Services;
using Serilog;
using Swashbuckle.AspNetCore.Annotations;
using System.Collections.Generic;
using System.Net;
using System.Reflection;
using System.Threading.Tasks;

namespace Portal.Gateway.Api.Controllers
{
	public class PrinterInfoController : ControllerBase
	{
		private readonly IHubContext<PrinterHub> _hubContext;
		private readonly IMapper _mapper;
		private readonly IPrinterInfoService _printerInfoService;
        private static readonly ILogger Log = Serilog.Log.ForContext<PrinterInfoController>();
        private IDistributedCache _cache;
		public PrinterInfoController(IHubContext<PrinterHub> context, IDistributedCache cache, IPrinterAgentService printerAgentService, IPrinterInfoService printerInfoService, IPrinterTypeService printerTypeService, IMapper mapper)
		{
			_hubContext = context;
			_cache = cache;

			_printerInfoService = printerInfoService;
			_mapper = mapper;
		}

		private async Task<string> GetPrinterInfoFromCache(string printerAgentName)
		{
			string printerInfo  = _cache.GetString(printerAgentName);
			if (printerInfo != null)
			{
				return printerInfo;
			}
			await Task.Run(async () =>
			{
				var coun = 0;
				do
				{
					await Task.Delay(100);
					coun++;
					printerInfo = _cache.GetString(printerAgentName);
				} while (printerInfo == null && coun < 100);
				return printerInfo;
			});
			return printerInfo;
		}

		//request printer list
		[SwaggerOperation(
			Summary = "Provides clients to get printer list of agents",
			Description = "Get printer list of client")]
		[HttpGet(ApiMethodName.PrinterAgent.RequestPrinterList + "{printerAgentName}")]
		public async Task<bool> RequestPrinterList(string printerAgentName)
		{
			//await _hubContext.Clients.Group(printerAgentName).SendAsync("ServerToClient", "SERVER TO CLIENT");
			await _hubContext.Clients.Group(printerAgentName).SendAsync("GetPrinterInfo", null);
			//var printerInfo = await GetPrinterInfoFromCache(printerAgentName);
			return true;
		}


		[SwaggerOperation(
			Summary = "Provides clients to get printer list of agents",
			Description = "Get printer list of client")]
		[HttpGet(ApiMethodName.PrinterAgent.GetClientPrinterList + "{printerAgentName}")]
		public async Task<ClientPrinterListApiResponse> GetClientPrinterList(string printerAgentName)
		{

			await _hubContext.Clients.Group(printerAgentName).SendAsync("GetPrinterInfo", null);
			var printerInfo = await GetPrinterInfoFromCache(printerAgentName);

			if (printerInfo != null)
			{
				return new ClientPrinterListApiResponse()
				{
					ClientPrinterList = JsonConvert.DeserializeObject<IList<string>>(printerInfo)
				};
			}

			return new ClientPrinterListApiResponse() { ClientPrinterList = null };
		}
		private async Task<List<PrinterInfoApiResponse>> _getPrinterInfos(string hostName)
		{
			var result = (await _printerInfoService.GetPrinterInfos(hostName));
			var mapped = _mapper.Map<List<PrinterInfoApiResponse>>(result);

			return mapped;
		}

		[SwaggerOperation(
			Summary = "Provides clients to get printer list of agents",
			Description = "Get printer list of agents")]
		[HttpGet(ApiMethodName.PrinterAgent.GetPrinterInfos + "{HostName}")]
		public async Task<PrinterInfoListApiResponse> GetPrinterInfos(string HostName)
		{
			var result = await _getPrinterInfos(HostName);

			return new PrinterInfoListApiResponse() { PrinterInfoList = result };
		}

		[SwaggerOperation(
			Summary = "Add Printer",
			Description = "Add Printer")]
		[HttpPost(ApiMethodName.PrinterAgent.CreatePrinterInfo)]
		public async Task<AddApiResponse> CreatePrinterInfo([FromBody] PrinterInfoApiRequest printerInfoApiRequest)
		{
			var maped = _mapper.Map<PrinterInfoDto>(printerInfoApiRequest);
			var result = await _printerInfoService.AddPrinterInfo(maped);

			var hostName = await _printerInfoService.GetHostNameByAgentId(printerInfoApiRequest.PrinterAgentId);

			await _hubContext.Clients.Group(hostName).SendAsync("UpdateClientInfo", null);
			return new AddApiResponse() { Id = result.Id };
		}

		[SwaggerOperation(
			Summary = "Provides clients to update printer info",
			Description = "Update printer info")]
		[HttpPut(ApiMethodName.PrinterAgent.UpdatePrinterInfo)]
		public async Task<UpdateApiResponse> UpdatePrinterInfo([FromBody] PrinterInfoApiRequest printerInfoApiRequest)
		{
            try
            {
                var maped = _mapper.Map<PrinterInfoDto>(printerInfoApiRequest);
                var result = await _printerInfoService.UpdatePrinterInfo(maped);

                var hostName = await _printerInfoService.GetHostNameByAgentId(printerInfoApiRequest.PrinterAgentId);
                await _hubContext.Clients.Group(hostName).SendAsync("UpdateClientInfo", null);

                if (result == null)
                    return new UpdateApiResponse() { Result = false };
                return new UpdateApiResponse() { Result = true };
            }
            catch (Exception e)
            {
                Log.Error(e, $"Exception: {e}");
                return new UpdateApiResponse() { Message = nameof(HttpStatusCode.InternalServerError) };
            }
		}

		[SwaggerOperation(
			Summary = "Delete Printer Agent",
			Description = "Delete Printer Agent by id")]
		[HttpDelete(ApiMethodName.PrinterAgent.DeletePrinterInfo + "{Id}")]
		public async Task<DeleteApiResponse> DeletePrinterInfo(int Id)
		{
			var hostName = await _printerInfoService.GetHostNameByAgentInfo(Id);
			var result = await _printerInfoService.DeletePrinterInfo(Id);

			await _hubContext.Clients.Group(hostName).SendAsync("UpdateClientInfo", null);

			return new DeleteApiResponse() { Result = result };
		}
	}

}
