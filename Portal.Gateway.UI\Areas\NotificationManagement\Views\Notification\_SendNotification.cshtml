﻿@model Portal.Gateway.UI.Areas.NotificationManagement.ViewModels.Notification.SendNotificationViewModel

@{
    var isReadOnly = Model.EncryptedId != null;
    var htmlAttributes = new Dictionary<string, object>
    {
        { "class", "form-control" }
    };

    if (isReadOnly)
    {
        htmlAttributes.Add("readonly", "readonly");
        htmlAttributes.Add("style", "pointer-events: none;");
    }
}

<form id="formSendNotification" class="card card-custom card-stretch form">
    <div class="card-body">
        <div class="row">
            <div class="col-xl-12">
                <div class="form-group row">
                    <div class="col-lg-9 mb-5">
                        <label class="font-weight-bold">@SiteResources.SendNotification.ToTitleCase()</label>
                        @(Html.Kendo().DropDownListFor(m => m.EncryptedId)
                            .HtmlAttributes(htmlAttributes)
                            .Filter(FilterType.Contains)
                            .OptionLabel(SiteResources.Select)
                            .DataValueField("Value")
                            .DataTextField("Text")
                            .Events(e => {
                                e.Change("onNotificationChange");
                                e.DataBound("onNotificationDataBound");
                                e.Open("onOpenChangeFocus");
                            })
                            .DataSource(source => { source.Read(read => { read.Action("GetAllNotifications", "Notification", new { Area = "NotificationManagement" }); }).ServerFiltering(false); }))
                    </div>
                </div>
                <div class="informationDiv" style="display:none">
                    <div class="form-group row">
                        <div class="col-lg-2">
                            <label class="font-weight-bold">@SiteResources.Active.ToTitleCase()</label>
                        </div>
                        <div class="col-lg-3">
                            <label class="font-weight-bold">@SiteResources.Title.ToTitleCase()</label>
                        </div>
                        <div class="col-lg-2">
                            <label class="font-weight-bold">@SiteResources.Languages.ToTitleCase()</label>
                        </div>
                        <div class="col-lg-5">
                            <label class="font-weight-bold">@SiteResources.Notification.ToTitleCase()</label>
                        </div>
                    </div>
                    <hr />
                    @for (var i = 0; i < Model.Translations.Count; i++)
                    {
                        <div class="form-group row">
                            @Html.HiddenFor(m => m.Translations[i].Id) 
                            @Html.HiddenFor(m => m.Translations[i].LanguageId)
                            <div class="col-lg-2 mb-5">
                                <div>
                                    <div class="active_checkbox">
                                        @(Html.Kendo().CheckBoxFor(m => m.Translations[i].IsActive)
                                            .Checked(Model.Translations[i].IsActive)) 
                                        </div>
                                </div>
                            </div>
                             <div class="col-lg-3 mb-5">
                                <div>
                                    <div>
                                        @Html.TextBoxFor(m => m.Translations[i].Subject, null, new { @class = "form-control" })
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-2 mb-5">
                                <div>
                                    <div>
                                        <label class="font-weight-bold">
                                        @Model.Translations[i].Language.ToTitleCase()
                                        @if (Model.Translations[i].LanguageId == (int)PushNotificationTranslationLanguage.English)
                                        {
                                            <span style="color: red;">*</span>
                                        }
                                            </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-5 mb-5">
                                <div>
                                    <div>
                                        @Html.TextBoxFor(m => m.Translations[i].Name, null, new { @class = "form-control" })
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    <div class="form-group row">
                        <div class="col-lg-3 mb-5">
                            <label class="font-weight-bold">@SiteResources.Location.ToTitleCase()</label>
                            @(Html.Kendo().DropDownListFor(m => m.LocationId)
                                .HtmlAttributes(new { @class = "form-control" })
                                .OptionLabel(SiteResources.Select)
                                .DataValueField("Value")
                                .DataTextField("Text")
                                .DataSource(source => { source.Read(read => { read.Action("GetCountriesSelectList", "Parameter", new { Area = "" }); }); }))
                        </div>
                        <div class="col-lg-3 mb-5">
                            <label class="font-weight-bold">@SiteResources.Nationality.ToTitleCase()</label>
                            @(Html.Kendo().DropDownListFor(m => m.NationalityId)
                                .HtmlAttributes(new { @class = "form-control" })
                                .OptionLabel(SiteResources.Select)
                                .DataValueField("Value")
                                .DataTextField("Text")
                                .DataSource(source => { source.Read(read => { read.Action("GetCountriesSelectList", "Parameter", new { Area = "" }); }); }))
                        </div>
                        <div class="col-lg-6 mb-5">
                            <div class="form-check">
                                @(Html.Kendo().CheckBoxFor(m => m.IsSendNow)
                                    .Label(SiteResources.SendNow.ToTitleCase())
                                    .HtmlAttributes(new { @class = "form-check-input", id = "IsSendNow" }))
                            </div>
                            <div class="form-check mt-2">
                                @(Html.Kendo().CheckBoxFor(m => m.IsSendScheduled)
                                    .Label(SiteResources.ScheduledTime.ToTitleCase())
                                    .HtmlAttributes(new { @class = "form-check-input", id = "IsSendScheduled" }))
                            </div>

                            <div id="scheduledTimeWrapper" style="display: none;" class="mt-3">
                                <label class="font-weight-bold">@SiteResources.ScheduledTime.ToTitleCase()</label>
                                @(Html.Kendo().DateTimePickerFor(m => m.ScheduleTime)
                                    .Format(SiteResources.DateTimePickerFormatView))
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">@SiteResources.Close</button>
        <button type="submit" disabled="disabled" class="btn btn-primary font-weight-bold">@SiteResources.Send</button>
    </div>
</form>

<script src="~/js/NotificationManagement/notification.js"></script>
<script src="~/js/site.js"></script>