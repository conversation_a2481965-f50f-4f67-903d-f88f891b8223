﻿using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Appointment.Application;
using Portal.Gateway.ApiModel.Requests.Dashboard;
using Portal.Gateway.ApiModel.Requests.General;
using Portal.Gateway.ApiModel.Responses.Appointment.Application;
using Portal.Gateway.ApiModel.Responses.Dashboard;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.ApiModel.Responses.Management.RoleAction;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static Portal.Gateway.UI.Config.CacheSettings;

namespace Portal.Gateway.UI.Helpers
{
    public class CacheHelper : ICacheHelper
    {
        private readonly AppSettings _appSettings;
        private readonly CacheSettings _cacheSettings;
        private readonly Dictionary<string, string> _headers;
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<CacheHelper> _logger;

        public CacheHelper(
            IOptions<AppSettings> appSettings,
            IOptions<CacheSettings> cacheSettings,
            IMemoryCache memoryCache,
            ILogger<CacheHelper> logger)
        {
            _appSettings = appSettings.Value;
            _cacheSettings = cacheSettings.Value;
            _headers = PortalGatewayApiDefaultRequestHeaders;
            _memoryCache = memoryCache;
            _logger = logger;
        }

        public async Task<bool> CacheAllAsync()
        {
            var cacheFailed = false;

            if (_appSettings != null &&
                _cacheSettings != null &&
                _cacheSettings.CacheItems.Length > 0)
            {
                for (int i = 0; i < _cacheSettings.CacheItems.Length; i++)
                {
                    var cacheItem = _cacheSettings.CacheItems[i];
                    if (cacheItem.AbsoluteExpirationRelativeToNow < DateTime.UtcNow)
                    {
                        bool isCached = false;
                        switch (cacheItem.CacheKey)
                        {
                            case CacheKeys.CountryCache:
                                isCached = await CacheCountriesAsync();
                                break;
                            case CacheKeys.ForeignCityCache:
                                isCached = await CacheForeignCitiesAsync();
                                break;
                            case CacheKeys.BranchCache:
                                isCached = await CacheBranchesAsync();
                                break;
                            case CacheKeys.RoleActionCache:
                                isCached = await CacheRoleActionsAsync();
                                break;
                            case CacheKeys.ApplicationStatusOrderCache:
                                isCached = await CacheApplicationStatusOrderAsync();
                                break;
                            case CacheKeys.BranchApplicationStatusCache:
                                isCached = await CacheBranchApplicationStatusAsync();
                                break;
                            case CacheKeys.ActiveCountriesCache:
                                isCached = await CacheActiveCountriesAsync();
                                break;
                            case CacheKeys.DepartmentsCache:
                                isCached = await CacheDepartmentsAsync();
                                break;
                            case CacheKeys.DepartmentsDictionaryCache:
                                isCached = await CacheDepartmentsDictionaryAsync();
                                break;
                            case CacheKeys.VisaCategoryCache:
                                isCached = await CacheVisaCategoriesAsync();
                                break;
                        }

                        if (isCached)
                        {
                            cacheItem.AbsoluteExpirationRelativeToNow = cacheItem.AbsoluteExpirationRelativeToNow.AddMinutes(cacheItem.CacheRefreshInterval);
                        }
                        else
                        {
                            cacheFailed = true;
                        }
                    }
                }
            }

            return !cacheFailed;
        }

        private async Task<bool> CacheVisaCategoriesAsync()
        {
            var response = await GetVisaCategoriesApiResponse();
            if (response == null)
            {
                _logger.LogWarning("Visa categories API response is null, cannot cache visa categories.");
                return false;
            }

            _memoryCache.Set(CacheKeys.VisaCategoryCache, response);
            return true;
        }

        private async Task<bool> CacheDepartmentsDictionaryAsync()
        {
            var response = await GetDepartmentsDictionaryApiResponse();
            if (response == null)
            {
                _logger.LogWarning("Departments dictionary API response is null, cannot cache departments dictionary.");
                return false;
            }

            _memoryCache.Set(CacheKeys.DepartmentsDictionaryCache, response);
            return true;
        }

        private async Task<bool> CacheDepartmentsAsync()
        {
            var response = await GetDepartmentsApiResponse();
            if (response == null)
            {
                _logger.LogWarning("Departments API response is null, cannot cache departments.");
                return false;
            }

            _memoryCache.Set(CacheKeys.DepartmentsCache, response);
            return true;
        }

        private async Task<bool> CacheActiveCountriesAsync()
        {
            var response = await GetActiveCountriesApiResponse();
            if (response == null)
            {
                _logger.LogWarning("Active countries API response is null, cannot cache active countries.");
                return false;
            }

            _memoryCache.Set(CacheKeys.ActiveCountriesCache, response);
            return true;
        }

        private async Task<bool> CacheBranchApplicationStatusAsync()
        {
            var response = await GetBranchApplicationStatusApiResponse();
            if (response == null)
            {
                _logger.LogWarning("Branch application status API response is null, cannot cache branch application status.");
                return false;
            }

            _memoryCache.Set(CacheKeys.BranchApplicationStatusCache, response);
            return true;
        }

        private async Task<bool> CacheApplicationStatusOrderAsync()
        {
            var response = await GetApplicationStatusOrderApiResponse();
            if (response == null)
            {
                _logger.LogWarning("Application status order API response is null, cannot cache application status order.");
                return false;
            }

            _memoryCache.Set(CacheKeys.ApplicationStatusOrderCache, response);
            return true;
        }

        private async Task<bool> CacheRoleActionsAsync()
        {
            var response = await GetRoleActionsApiResponse();
            if (response == null)
            {
                _logger.LogWarning("Role actions API response is null, cannot cache role actions.");
                return false;
            }

            _memoryCache.Set(CacheKeys.RoleActionCache, response);
            return true;
        }

        private async Task<bool> CacheBranchesAsync()
        {
            var response = await GetBranchesApiResponse();
            if (response == null)
            {
                _logger.LogWarning("Branches API response is null, cannot cache branches.");
                return false;
            }

            _memoryCache.Set(CacheKeys.BranchCache, response);
            return true;
        }

        private async Task<bool> CacheForeignCitiesAsync()
        {
            var response = await GetForeignCitiesApiResponse();
            if (response == null)
            {
                _logger.LogWarning("Foreign cities API response is null, cannot cache foreign cities.");
                return false;
            }

            _memoryCache.Set(CacheKeys.ForeignCityCache, response);
            return true;
        }

        private async Task<bool> CacheCountriesAsync()
        {
            var response = await GetCountriesApiResponse();
            if (response == null)
            {
                _logger.LogWarning("Countries API response is null, cannot cache countries.");
                return false;
            }

            _memoryCache.Set(CacheKeys.CountryCache, response);
            return true;
        }

        public async Task<CountriesApiResponse> GetCountriesAsync()
        {
            return await _memoryCache.GetOrCreateAsync<CountriesApiResponse>(CacheKeys.CountryCache, async entry =>
            {
                return await GetCountriesApiResponse();
            }).ConfigureAwait(false);
        }

        public async Task<ForeignCitiesApiResponse> GetForeignCitiesAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ForeignCitiesApiResponse>(CacheKeys.ForeignCityCache, async entry =>
            {
                return await GetForeignCitiesApiResponse();
            }).ConfigureAwait(false);
        }
        public async Task<BranchesApiResponse> GetBranchesAsync()
        {
            return await _memoryCache.GetOrCreateAsync<BranchesApiResponse>(CacheKeys.BranchCache, async entry =>
            {
                return await GetBranchesApiResponse();
            }).ConfigureAwait(false);
        }

        public async Task<RoleActionApiResponse> GetRoleActionsAsync()
        {
            return await _memoryCache.GetOrCreateAsync<RoleActionApiResponse>(CacheKeys.RoleActionCache, async entry =>
            {
                return await GetRoleActionsApiResponse();
            }).ConfigureAwait(false);
        }

        public async Task<ApplicationStatusOrdersApiResponse> GetApplicationStatusOrderAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApplicationStatusOrdersApiResponse>(CacheKeys.ApplicationStatusOrderCache, async entry =>
            {
                return await GetApplicationStatusOrderApiResponse();
            }).ConfigureAwait(false);
        }

        public async Task<BranchApplicationStatusesApiResponse> GetBranchApplicationStatusAsync()
        {
            return await _memoryCache.GetOrCreateAsync<BranchApplicationStatusesApiResponse>(CacheKeys.BranchApplicationStatusCache, async entry =>
            {
                return await GetBranchApplicationStatusApiResponse();
            }).ConfigureAwait(false);
        }

        public async Task<ActiveCountriesApiResponse> GetActiveCountriesAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ActiveCountriesApiResponse>(CacheKeys.ActiveCountriesCache, async entry =>
            {
                return await GetActiveCountriesApiResponse();
            }).ConfigureAwait(false);
        }

        public async Task<DepartmentsApiResponse> GetDepartmentsAsync()
        {
            return await _memoryCache.GetOrCreateAsync<DepartmentsApiResponse>(CacheKeys.DepartmentsCache, async entry =>
            {
                return await GetDepartmentsApiResponse();
            }).ConfigureAwait(false);
        }

        public async Task<Dictionary<int, DepartmentCacheModel>> GetDepartmentsDictionaryAsync()
        {
            return await _memoryCache.GetOrCreateAsync<Dictionary<int, DepartmentCacheModel>>(CacheKeys.DepartmentsDictionaryCache, async entry =>
            {
                return await GetDepartmentsDictionaryApiResponse();
            }).ConfigureAwait(false);
        }

        public async Task<List<BranchApplicationCountryVisaCategoriesApiResponse.VisaCategory>> GetVisaCategoriesAsync()
        {
            return await _memoryCache.GetOrCreateAsync<List<BranchApplicationCountryVisaCategoriesApiResponse.VisaCategory>>(CacheKeys.VisaCategoryCache, async entry =>
            {
                return await GetVisaCategoriesApiResponse();
            }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<ApplicationStatsApiResponse>> GetPeriodicApplicationStatsAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<ApplicationStatsApiResponse>>(CacheKeys.PeriodicApplicationStatsCache, async entry =>
            {
                return await PeriodicApplicationStatsApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<ExtraFeeStatsApiResponse>> GetExtraFeeStatsAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<ExtraFeeStatsApiResponse>>(CacheKeys.ExtraFeeStatsCache, async entry =>
            {
                return await GetExtraFeeStatsApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<DailyPassportDeliveryStatsApiResponse>> GetDailyPassportDeliveriesAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<DailyPassportDeliveryStatsApiResponse>>(CacheKeys.DailyPassportDeliveriesCache, async entry =>
            {
                return await GetDailyPassportDeliveriesApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<StatsFromBeginningApiResponse>> GetStatsFromBeginningAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<StatsFromBeginningApiResponse>>(CacheKeys.StatsFromBeginningCache, async entry =>
            {
                return await GetStatsFromBeginningApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<BranchInsuranceStatsApiResonse>> GetBranchInsuranceStatsAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<BranchInsuranceStatsApiResonse>>(CacheKeys.BranchInsuranceStatsCache, async entry =>
            {
                return await GetBranchInsuranceStatsApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<PreviousDayGeneralStatsApiResponse>> GetOfficeManagerPreviousDayGeneralStatsAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<PreviousDayGeneralStatsApiResponse>>(CacheKeys.OfficeManagerPreviousDayGeneralStatsCache, async entry =>
            {
                return await GetOfficeManagerPreviousDayGeneralStatsApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<OfficersApiResponse>> GetOfficersAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<OfficersApiResponse>>(CacheKeys.OfficerCache, async entry =>
            {
                return await GetOfficersApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<QuarterApplicationSummaryApiResponse>> GetQuarterApplicationSummaryAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<QuarterApplicationSummaryApiResponse>>(CacheKeys.QuarterApplicationSummaryCache, async entry =>
            {
                return await GetQuarterApplicationSummaryApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<PreviousDayGeneralStatsApiResponse>> GetPreviousDayGeneralStatsAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<PreviousDayGeneralStatsApiResponse>>(CacheKeys.PreviousDayGeneralStatsCache, async entry =>
            {
                return await GetPreviousDayGeneralStatsApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<InsuranceStatsApiResponse>> GetInsuranceStatsAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<InsuranceStatsApiResponse>>(CacheKeys.InsuranceStatsCache, async entry =>
            {
                return await GetInsuranceStatsApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<InsuranceStatsApiResponse>> GetInsuranDailyInsuranceStatsAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<InsuranceStatsApiResponse>>(CacheKeys.DailyInsuranceStatsCache, async entry =>
            {
                return await GetInsuranDailyInsuranceStatsApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<MonthComparativeInsuranceApiResponse>> GetMonthComparativeInsuranceStatsAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<MonthComparativeInsuranceApiResponse>>(CacheKeys.MonthComparativeInsuranceStatsCache, async entry =>
            {
                return await GetMonthComparativeInsuranceStatsApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<PeriodicExtraFeeStatsApiResponse>> GetMonthlyExtraFeeStatsAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<PeriodicExtraFeeStatsApiResponse>>(CacheKeys.MonthlyExtraFeeStatsCache, async entry =>
            {
                return await GetMonthlyExtraFeeStatsApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<PreviousDayChangeStatsApiReponse>> GetPreviousDayChangeStatsAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<PreviousDayChangeStatsApiReponse>>(CacheKeys.PreviousDayChangeStatsCache, async entry =>
            {
                return await GetPreviousDayChangeStatsApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<AverageIstizanResultTimeApiResponse>> GetAverageIstizanResultTimeAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<AverageIstizanResultTimeApiResponse>>(CacheKeys.AverageIstizanResultTimeCache, async entry =>
            {
                return await GetAverageIstizanResultTimeApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<AverageNormalApplicationResultTimeApiResponse>> GetAverageNormalApplicationResultTimeAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<AverageNormalApplicationResultTimeApiResponse>>(CacheKeys.AverageNormalApplicationResultTimeCache, async entry =>
            {
                return await GetAverageNormalApplicationResultTimeApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<PeriodicExtraFeeStatsApiResponse>> GetQuarterExtraFeeStatsAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<PeriodicExtraFeeStatsApiResponse>>(CacheKeys.QuarterExtraFeeStatsCache, async entry =>
            {
                return await GetQuarterExtraFeeStatsApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<QuarterApplicationStatsApiResponse>> GetQuarterApplicationStatsAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<QuarterApplicationStatsApiResponse>>(CacheKeys.QuarterApplicationStatsCache, async entry =>
            {
                return await GetQuarterApplicationStatsApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        public async Task<ApiResponse<PreviousDayGeneralStatsApiResponse>> GetConsularPreviousDayGeneralStatsAsync()
        {
            return await _memoryCache.GetOrCreateAsync<ApiResponse<PreviousDayGeneralStatsApiResponse>>(CacheKeys.ConsularPreviousDayGeneralStatsCache, async entry =>
            {
                return await GetConsularPreviousDayGeneralStatsApiResponse();
            }, new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_cacheSettings.CacheExpiryTime) }).ConfigureAwait(false);
        }

        private async Task<BranchesApiResponse> GetBranchesApiResponse()
        {
            var apiRequest = new BranchesApiRequest();
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<BranchesApiResponse>>
                (apiRequest, ApiMethodName.Parameter.GetBranches, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            return apiResponse.Data;
        }

        private async Task<DepartmentsApiResponse> GetDepartmentsApiResponse()
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<DepartmentsApiResponse>>
                (ApiMethodName.Parameter.GetDepartments, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            return apiResponse.Data;
        }

        private async Task<List<BranchApplicationCountryVisaCategoriesApiResponse.VisaCategory>> GetVisaCategoriesApiResponse()
        {
            var apiRequest = new BranchApplicationCountryVisaCategoriesApiRequest
            {
                IsDeactivatedDataIncluded = true,
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<BranchApplicationCountryVisaCategoriesApiResponse>>
                (apiRequest, ApiMethodName.Appointment.GetBranchApplicationCountryVisaCategories, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            return (List<BranchApplicationCountryVisaCategoriesApiResponse.VisaCategory>)apiResponse.Data.VisaCategoryTypes;
        }

        private async Task<ApplicationStatusOrdersApiResponse> GetApplicationStatusOrderApiResponse()
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationStatusOrdersApiResponse>>
                (ApiMethodName.Parameter.GetApplicationStatusOrders, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            return apiResponse.Data;
        }

        private async Task<Dictionary<int, DepartmentCacheModel>> GetDepartmentsDictionaryApiResponse()
        {
            var apiResponse = await GetDepartmentsApiResponse();

            return apiResponse.Departments.ToDictionary(p => p.Id, p => new DepartmentCacheModel
            {
                Id = p.Id,
                Names = p.NameTranslations.ToDictionary(p2 => p2.LanguageId, p2 => p2.Name)
            });
        }

        private async Task<BranchApplicationStatusesApiResponse> GetBranchApplicationStatusApiResponse()
        {
            var apiRequest = new BranchApplicationStatusesApiRequest
            {
                IsActive = true
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<BranchApplicationStatusesApiResponse>>
                (apiRequest, ApiMethodName.Parameter.GetBranchApplicationStatuses, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            return apiResponse.Data;
        }

        private async Task<ApiResponse<ApplicationStatsApiResponse>> PeriodicApplicationStatsApiResponse()
        {
            var apiRequest = new GetStatsApiRequest();
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<ApplicationStatsApiResponse>>
                (apiRequest, ApiMethodName.Dashboard.GetPeriodicApplicationStats, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetPeriodicApplicationStats API response is null");

            return apiResponse;
        }

        private async Task<ApiResponse<ExtraFeeStatsApiResponse>> GetExtraFeeStatsApiResponse()
        {
            var apiRequest = new GetStatsApiRequest();
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<ExtraFeeStatsApiResponse>>
                (apiRequest, ApiMethodName.Dashboard.GetExtraFeeStats, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetExtraFeeStats API response is null");

            return apiResponse;
        }

        private async Task<ApiResponse<DailyPassportDeliveryStatsApiResponse>> GetDailyPassportDeliveriesApiResponse()
        {
            var apiRequest = new GetStatsApiRequest();
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<DailyPassportDeliveryStatsApiResponse>>
                (apiRequest, ApiMethodName.Dashboard.GetDailyPassportDeliveryStats, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetDailyPassportDeliveryStats API response is null");

            return apiResponse;
        }

        private async Task<ApiResponse<StatsFromBeginningApiResponse>> GetStatsFromBeginningApiResponse()
        {
            var apiRequest = new GetStatsApiRequest();
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<StatsFromBeginningApiResponse>>
                (apiRequest, ApiMethodName.Dashboard.GetStatsFromBeginning, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetStatsFromBeginning API response is null");

            return apiResponse;
        }

        private async Task<ApiResponse<BranchInsuranceStatsApiResonse>> GetBranchInsuranceStatsApiResponse()
        {
            var apiRequest = new GetStatsApiRequest();
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<BranchInsuranceStatsApiResonse>>
                (apiRequest, ApiMethodName.Dashboard.GetBranchInsuranceStatsAsync, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetBranchInsuranceStatsAsync API response is null");

            return apiResponse;
        }

        private async Task<ApiResponse<PreviousDayGeneralStatsApiResponse>> GetOfficeManagerPreviousDayGeneralStatsApiResponse()
        {
            var apiRequest = new GetStatsApiRequest();
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PreviousDayGeneralStatsApiResponse>>
                (apiRequest, ApiMethodName.Dashboard.GetOfficeManagerPreviousDayGeneralStats, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetOfficeManagerPreviousDayGeneralStats API response is null");

            return apiResponse;
        }

        private async Task<ApiResponse<OfficersApiResponse>> GetOfficersApiResponse()
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<OfficersApiResponse>>
                (ApiMethodName.Dashboard.GetOfficers, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetOfficers API response is null");

            return apiResponse;
        }

        private async Task<ApiResponse<QuarterApplicationSummaryApiResponse>> GetQuarterApplicationSummaryApiResponse()
        {
            var apiRequest = new GetStatsApiRequest();
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<QuarterApplicationSummaryApiResponse>>
                (apiRequest, ApiMethodName.Dashboard.GetQuarterApplicationSummary, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetQuarterApplicationSummary API response is null");

            return apiResponse;
        }

        private async Task<RoleActionApiResponse> GetRoleActionsApiResponse()
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<RoleActionApiResponse>>
                (ApiMethodName.Management.GetRoleAction + "-1", _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            return apiResponse.Data;
        }

        private async Task<ForeignCitiesApiResponse> GetForeignCitiesApiResponse()
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ForeignCitiesApiResponse>>
                (new EmptyApiRequest(), ApiMethodName.Parameter.GetForeignCities, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            return apiResponse.Data;
        }

        private async Task<CountriesApiResponse> GetCountriesApiResponse()
        {
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<CountriesApiResponse>>
                (new EmptyApiRequest(), ApiMethodName.Parameter.GetCountries, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            return apiResponse.Data;
        }

        private async Task<ActiveCountriesApiResponse> GetActiveCountriesApiResponse()
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ActiveCountriesApiResponse>>
                (ApiMethodName.Dashboard.GetActiveCountries, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            return apiResponse.Data;
        }

        private async Task<ApiResponse<PreviousDayGeneralStatsApiResponse>> GetPreviousDayGeneralStatsApiResponse()
        {
            var apiRequest = new GetStatsApiRequest();
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PreviousDayGeneralStatsApiResponse>>
                (apiRequest, ApiMethodName.Dashboard.GetPreviousDayGeneralStats, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetPreviousDayGeneralStats API response is null");

            return apiResponse;
        }

        private async Task<ApiResponse<InsuranceStatsApiResponse>> GetInsuranceStatsApiResponse()
        {
            var apiRequest = new GetStatsApiRequest();
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<InsuranceStatsApiResponse>>
                (apiRequest, ApiMethodName.Dashboard.GetInsuranceStats, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetInsuranceStats API response is null");

            return apiResponse;
        }

        private async Task<ApiResponse<InsuranceStatsApiResponse>> GetInsuranDailyInsuranceStatsApiResponse()
        {
            var apiRequest = new GetStatsApiRequest();
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<InsuranceStatsApiResponse>>
                (apiRequest, ApiMethodName.Dashboard.GetDailyInsuranceStats, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetDailyInsuranceStats API response is null");

            return apiResponse;
        }

        private async Task<ApiResponse<MonthComparativeInsuranceApiResponse>> GetMonthComparativeInsuranceStatsApiResponse()
        {
            var apiRequest = new GetStatsApiRequest();
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<MonthComparativeInsuranceApiResponse>>
                (apiRequest, ApiMethodName.Dashboard.GetInsuranceMonthComparisonStats, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetInsuranceMonthComparisonStats API response is null");

            return apiResponse;
        }

        private async Task<ApiResponse<PeriodicExtraFeeStatsApiResponse>> GetMonthlyExtraFeeStatsApiResponse()
        {
            var apiRequest = new GetPeriodicExtraFeeStatsApiRequest()
            {
                Period = 1
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PeriodicExtraFeeStatsApiResponse>>
                (apiRequest, ApiMethodName.Dashboard.GetPeriodicExtraFeeStats, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetPeriodicExtraFeeStats API response is null");

            return apiResponse;
        }

        private async Task<ApiResponse<PreviousDayChangeStatsApiReponse>> GetPreviousDayChangeStatsApiResponse()
        {
            var apiRequest = new GetStatsApiRequest();
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PreviousDayChangeStatsApiReponse>>
                (apiRequest, ApiMethodName.Dashboard.GetPreviousDayChangeStats, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetPreviousDayChangeStats API response is null");

            return apiResponse;
        }

        private async Task<ApiResponse<AverageIstizanResultTimeApiResponse>> GetAverageIstizanResultTimeApiResponse()
        {
            var apiRequest = new GetStatsApiRequest();
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AverageIstizanResultTimeApiResponse>>
                (apiRequest, ApiMethodName.Dashboard.GetAverageIstizanResultTime, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetAverageIstizanResultTime API response is null");

            return apiResponse;
        }

        private async Task<ApiResponse<AverageNormalApplicationResultTimeApiResponse>> GetAverageNormalApplicationResultTimeApiResponse()
        {
            var apiRequest = new GetStatsApiRequest();
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AverageNormalApplicationResultTimeApiResponse>>
                (apiRequest, ApiMethodName.Dashboard.GetAverageNormalApplicationResultTime, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetAverageNormalApplicationResultTime API response is null");

            return apiResponse;
        }

        private async Task<ApiResponse<PeriodicExtraFeeStatsApiResponse>> GetQuarterExtraFeeStatsApiResponse()
        {
            var apiRequest = new GetPeriodicExtraFeeStatsApiRequest()
            {
                Period = 3
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PeriodicExtraFeeStatsApiResponse>>
                (apiRequest, ApiMethodName.Dashboard.GetPeriodicExtraFeeStats, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetPeriodicExtraFeeStats API response is null");

            return apiResponse;
        }

        private async Task<ApiResponse<QuarterApplicationStatsApiResponse>> GetQuarterApplicationStatsApiResponse()
        {
            var apiRequest = new GetStatsApiRequest();
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<QuarterApplicationStatsApiResponse>>
                (apiRequest, ApiMethodName.Dashboard.GetQuarterApplicationStats, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetQuarterApplicationStats API response is null");

            return apiResponse;
        }

        private async Task<ApiResponse<PreviousDayGeneralStatsApiResponse>> GetConsularPreviousDayGeneralStatsApiResponse()
        {
            var apiRequest = new GetStatsApiRequest();
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PreviousDayGeneralStatsApiResponse>>
                (apiRequest, ApiMethodName.Dashboard.GetConsularPreviousDayGeneralStats, _appSettings.PortalGatewayApiUrl, _headers)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                throw new InvalidOperationException("GetConsularPreviousDayGeneralStats API response is null");

            return apiResponse;
        }

        internal Dictionary<string, string> PortalGatewayApiDefaultRequestHeaders
        {
            get
            {
                var headers = new Dictionary<string, string>();
                headers.Add("apiKey", _appSettings.PortalGatewayApiKey);
                headers.Add("languageId", LanguageId.ToString());
                return headers;
            }
        }

        internal int LanguageId
        {
            get
            {
                var currentCulture = Thread.CurrentThread.CurrentCulture;
                int currentLanguageId = (int)Language.Turkish;

                try
                {
                    currentLanguageId = EnumExtensions.GetValueFromDescription<Culture>(currentCulture.Name).ToInt();
                }
                catch (Exception)
                {
                    // cant log error cause logger has not been initialized yet
                }

                return currentLanguageId;
            }
        }
    }
}