﻿using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Management.Announcement;
using Portal.Gateway.ApiModel.Requests.Management.User;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.ApiModel.Responses.Management.Announcement;
using Portal.Gateway.ApiModel.Responses.Management.RoleAction;
using Portal.Gateway.ApiModel.Responses.Management.User;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Contracts.Extensions;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using Portal.Gateway.UI.ViewModels;
using Portal.Gateway.UI.ViewModels.Announcement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Controllers
{
    public class UserController : BaseController<UserController>
    {
        private readonly CacheSettings _cacheSettings;
        private readonly SessionSettings _sessionSettings;

        public UserController(
            IOptions<AppSettings> appSettings,
            IOptions<CacheSettings> cacheSettings,
            IOptions<SessionSettings> sessionSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {
            _cacheSettings = cacheSettings.Value;
            _sessionSettings = sessionSettings.Value;
        }

        [AllowAnonymous]
        public ActionResult Login()
        {
            var currentUser = SessionExtensions.Get<UserModel>(HttpContext.Session, SessionKeys.UserSession);

            if (currentUser != null)
                return Redirect("/");

            return View();
        }

        [AllowAnonymous]
        [HttpPost]
        public async Task<IActionResult> LoginAsync(UserLoginViewModel model)
        {
            if (model == null || !ModelState.IsValid || !model.Username.Contains("/"))
            {
                ClearModelStateErrors();
                ModelState.AddModelError("InvalidModel", ResultMessage.MissingOrInvalidData.ToDescription());
                return View("Login", model);
            }

            var headers = PortalGatewayApiDefaultRequestHeaders;
            if (headers.TryGetValue("corporateId", out string corporateId))
                headers["corporateId"] = model.Username.Split("/")[0];

            ApiResponse<UserLoginApiResponse> apiResponse = null;

            if (_sessionSettings.Authentication == SessionSettings.LdapAuthenticationOptionValue)
            {
                var apiRequest = new UserLdapLoginApiRequest
                {
                    CorporateId = model.Username.Split("/")[0],
                    Username = model.Username.Split("/")[1],
                    Password = model.Password
                };

                apiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<UserLoginApiResponse>>
                    (apiRequest, ApiMethodName.User.UserLdapLogin, AppSettings.PortalGatewayApiUrl, headers)
                    .ConfigureAwait(false);
            }
            else
            {
                var apiRequest = new UserLoginApiRequest
                {
                    CorporateId = model.Username.Split("/")[0],
                    Email = model.Username.Split("/")[1],
                    Password = model.Password,
                };

                apiResponse = await PortalHttpClientHelper
                     .PostAsJsonAsync<ApiResponse<UserLoginApiResponse>>
                     (apiRequest, ApiMethodName.User.UserLogin, AppSettings.PortalGatewayApiUrl, headers)
                     .ConfigureAwait(false);
            }           

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                ClearModelStateErrors();
                ModelState.AddModelError("ApiResponse", result.Message);
                CheckIfPasswordExpired(result.Message);
                return View("Login", model);
            }

            if (apiResponse.Data.PasswordChange)
            {
                var newPasswordModel = new UserPasswordChangeViewModel
                {
                    EncryptedApplicationId = apiResponse.Data.UserId.ToEncrypt(),
                    FullName = apiResponse.Data.FullName,
                };

                return View("LoginNewPassword", newPasswordModel);
            }

            var userModel = new UserModel
            {
                CompanyId = apiResponse.Data.CompanyId,
                CorporateId = apiResponse.Data.CorporateId,
                UserId = apiResponse.Data.UserId,
                Email = apiResponse.Data.Email,
                Name = apiResponse.Data.Name,
                Surname = apiResponse.Data.Surname,
                RoleIds = apiResponse.Data.RoleIds,
                ModuleIds = apiResponse.Data.ModuleIds,
                BranchIds = apiResponse.Data.BranchIds,
                CountryIds = apiResponse.Data.CountryIds,
                Shortcuts = apiResponse.Data.Shortcuts.Select(p => new UserShortcutModel()
                {
                    Id = p.Id,
                    Area = p.Area,
                    Controller = p.Controller,
                    Method = p.Method,
                    Names = p.Names.ToDictionary(q => q.LanguageId, q => q.Name)
                }).ToList()
            };

            SetUserModules(userModel);
            await SetUserDepartments(userModel);
            SessionExtensions.Set(HttpContext.Session, SessionKeys.UserSession, userModel);

            return Redirect("/");
        }

        private void CheckIfPasswordExpired(string errorMessage)
        {
            var isPasswordExpired = errorMessage.Contains(SiteResources.LdapAccountExpired, StringComparison.OrdinalIgnoreCase);
            ModelState.TryAddModelError("PasswordExpired", isPasswordExpired ? "true" : "false");
        }

        [AllowAnonymous]
        public IActionResult Logout()
        {
            HttpContext.Session.Remove(SessionKeys.UserSession);
            return RedirectToAction("Login", "User", new { Area = "" });
        }

        [AllowAnonymous]
        [HttpPost]
        public async Task<IActionResult> UserPasswordChange(UserPasswordChangeViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
            
            if (string.IsNullOrEmpty(viewModel.OldPassword) || string.IsNullOrEmpty(viewModel.NewPassword) || string.IsNullOrEmpty(viewModel.RepeatedPassword))
                return Json(new ResultModel { Message = @SiteResources.FillRequiredFields, ResultType = ResultType.Warning });

            var pass = viewModel.NewPassword.ToCharArray();
            var upperCase = false;
            var lowerCase = false;
            var isNumber = false;
            var isSymbol = false;
            foreach (char c in pass)
            {
                if (c >= 'A' && c <= 'Z')
                    upperCase = true;

                if (c >= 'a' && c <= 'z')
                    lowerCase = true;

                if (Char.IsNumber(c))
                    isNumber = true;

                if (!Char.IsLetterOrDigit(c))
                    isSymbol = true;
            }

            var message = "";

            if (!(upperCase && lowerCase && isNumber && isSymbol && pass.Count() >= 8 && pass.Count() <= 20))
                message = nameof(SiteResources.UserPasswordRequirements).ToSiteResourcesValue(LanguageId);
            else if (viewModel.NewPassword != viewModel.RepeatedPassword)
                message = nameof(SiteResources.NewPasswordNotMatch).ToSiteResourcesValue(LanguageId);

            if (message != "")
                return Json(new ResultModel { Message = message, ResultType = ResultType.Warning });

            var apiRequest = new UpdateUserPasswordApiRequest()
            {
                Id = viewModel.EncryptedApplicationId.ToDecryptInt(),
                Password = viewModel.NewPassword,
                OldPassword = viewModel.OldPassword,
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateUserPassword, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = @SiteResources.OperationIsSuccessful, ResultType = ResultType.Success });
        }

        #region RoleAction

        #endregion

        public ActionResult SelectModuleBranch()
        {
            return View();
        }

        public async Task<IActionResult> SetModuleBranch(string encryptedModuleId, string encryptedBranchId)
        {
            var userModel = UserSession;
            userModel.ModuleId = encryptedModuleId.ToDecryptInt();
            userModel.BranchId = encryptedBranchId.ToDecryptInt();
            userModel.CounterId = null;

            await SetBranchName(userModel);

            SessionExtensions.Set(HttpContext.Session, SessionKeys.UserSession, userModel);
            return Redirect("/");
        }

        public async Task<IActionResult> SetBranch(string encryptedBranchId)
        {
            var userModel = UserSession;
            userModel.BranchId = encryptedBranchId.ToDecryptInt();
            userModel.CounterId = null;

            await SetBranchName(userModel);

            SessionExtensions.Set(HttpContext.Session, SessionKeys.UserSession, userModel);
            return Redirect("/");
        }

        public async Task SetBranchName(UserModel userModel)
        {
            var branchName = string.Empty;
            var isValidAMS = false;
            var countryId = 0;
            var isPassportScanRequired = true;
            byte qmsCompanyId = 1;

            var cacheItem = await CacheHelper.GetBranchesAsync();
            branchName = cacheItem.Branches.FirstOrDefault(q => q.Id == userModel.BranchId).BranchTranslations.Any(q => q.LanguageId == LanguageId)
                ? cacheItem.Branches.FirstOrDefault(q => q.Id == userModel.BranchId)?.BranchTranslations.First(q => q.LanguageId == LanguageId).Name
                : cacheItem.Branches.FirstOrDefault(q => q.Id == userModel.BranchId)?.BranchTranslations.FirstOrDefault()?.Name;
            isValidAMS = cacheItem.Branches.FirstOrDefault(q => q.Id == userModel.BranchId).IsValidAMS;
            countryId = cacheItem.Branches.FirstOrDefault(q => q.Id == userModel.BranchId).CountryId;
            isPassportScanRequired = cacheItem.Branches.FirstOrDefault(q => q.Id == userModel.BranchId).IsPassportScanRequired;
            qmsCompanyId = cacheItem.Branches.FirstOrDefault(q => q.Id == userModel.BranchId).QmsCompanyId;

            userModel.BranchName = branchName;
            userModel.IsValidAMS = isValidAMS;
            userModel.BranchCountryId = countryId;
            userModel.IsPassportScanRequired = isPassportScanRequired;
            userModel.QmsCompanyId = qmsCompanyId;
        }

        private void SetUserModules(UserModel userModel)
        {
            foreach (var item in userModel.ModuleIds)
            {
                userModel.UserModules.Add(new UserModuleModel
                {
                    UserId = userModel.UserId,
                    ModuleId = item,
                    ModuleName = ((ModuleType)item).ToDescription(),
                    ReturnUrl = $"http://{StringHelper.RandomString(10)}.xyz" //TODO: use the original value
                });
            }
        }

        public async Task<IActionResult> PartialManageUserShortcuts()
        {
            if (UserSession == null)
                return Content(EnumResources.MissingOrInvalidData);

            var viewModel = new UserShortcutViewModel() { ActionList = new List<UserShortcutViewModel.ActionViewModel>() };

            var response = await CacheHelper.GetRoleActionsAsync();

            var roleFilteredResponse = UserSession.IsSysAdmin ?
                    response.RoleActionSites :
                    response.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id)).ToList();

            foreach (var roleActionSite in roleFilteredResponse)
            {
                foreach (var roleAction in roleActionSite.RoleActions.Where(p => p.Action.ParentId != null && p.Action.IsActive && p.Action.IsMenuItem && !string.IsNullOrEmpty(p.Action.Method) && !string.IsNullOrEmpty(p.Action.Controller)))
                {
                    if (!viewModel.ActionList.Any(p => p.Id == roleAction.Action.Id))
                    {
                        viewModel.ActionList.Add(new UserShortcutViewModel.ActionViewModel()
                        {
                            Id = roleAction.Action.Id,
                            Name = roleAction.Action.ActionTranslations.Any(p => p.LanguageId == LanguageId) ?
                                roleAction.Action.ActionTranslations.FirstOrDefault(p => p.LanguageId == LanguageId)?.Name :
                                roleAction.Action.ActionTranslations.FirstOrDefault()?.Name,
                            IsSelected = UserSession.Shortcuts.Any(q => q.Id == roleAction.Action.Id)
                        });
                    }
                }
            }

            viewModel.ActionList = viewModel.ActionList.OrderByDescending(p => p.Name).ToList();

            return PartialView("_ManageShortcuts", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateUserShortcuts(UserShortcutViewModel viewModel)
        {
            if (UserSession == null)
                return Json(null);

            var apiRequest = new UpdateUserShortcutApiRequest()
            {
                UserId = UserSession.UserId,
                SelectedActions = viewModel.ActionList.Select(p => new UpdateUserShortcutApiRequest.ActionApiRequest()
                {
                    ActionId = p.Id
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UserShortcutApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateUserShortcuts, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            var userSessioModel = UserSession;

            userSessioModel.Shortcuts.Clear();
            userSessioModel.Shortcuts = apiResponse.Data.ActionList.Select(p => new UserShortcutModel()
            {
                Id = p.Id,
                Area = p.Area,
                Controller = p.Controller,
                Method = p.Method,
                Names = p.Names.ToDictionary(q => q.LanguageId, q => q.Name)
            }).ToList();

            SessionExtensions.Set(HttpContext.Session, SessionKeys.UserSession, userSessioModel);

            return Json(new ResultModel { Message = SiteResources.PageReloadAfterSuccess, ResultType = ResultType.Success });
        }

        private async Task SetUserDepartments(UserModel userModel)
        {
            var cacheItem = await CacheHelper.GetDepartmentsAsync();

            if (userModel.BranchIds.Any())
            {
                if (cacheItem != null)
                {
                    userModel.DepartmentIds = cacheItem.Departments.Select(p => p.Id).ToList();
                }
            }
        }

        public IActionResult SetDepartment(string encryptedDepartmentId, int counterId)
        {
            var userModel = UserSession;
            userModel.DepartmentId = encryptedDepartmentId.ToDecryptInt();
            userModel.CounterId = counterId;
            SessionExtensions.Set(HttpContext.Session, SessionKeys.UserSession, userModel);
            return RedirectToAction("List", "InfoDesk", new { Area = "QueueMatic" });
        }

        #region Announcement

        public async Task<IActionResult> PartialAnnouncement(string encryptedAnnouncementId)
        {
            var apiRequest = new AnnouncementApiRequest
            {
                AnnouncementId = encryptedAnnouncementId.ToDecryptInt(),
                UserId = UserSession.UserId
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AnnouncementApiResponse>>
                (apiRequest, ApiMethodName.Management.GetAnnouncement, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new AnnouncementViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                BranchIds = apiResponse.Data.BranchIds,
                BranchNames = apiResponse.Data.BranchNames,
                DueDate = apiResponse.Data.DueDate,
                Subject = apiResponse.Data.Subject,
                Message = apiResponse.Data.Message,
                CreatedAt = apiResponse.Data.CreatedAt,
                CreatedBy = apiResponse.Data.CreatedBy,
                CreatedByNameSurname = apiResponse.Data.CreatedByNameSurname,
                IsReadByUser = apiResponse.Data.IsReadByUser
            };

            return PartialView("_Announcement", viewModel);
        }

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult AnnouncementList()
        {
            if (UserSession.BranchId == null)
            {
                return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
            }

            return View();
        }

        public async Task<IActionResult> GetPaginatedAnnouncements([DataSourceRequest] DataSourceRequest request, FilterAnnouncementViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedAnnouncementsApiRequest
            {
                BranchId = UserSession.BranchId ?? 0,
                StartDate = filterViewModel.FilterStartDate,
                UserId = UserSession.UserId,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<AnnouncementsApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedAnnouncements, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<AnnouncementViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().Announcements
                    .Select(p => new AnnouncementViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        BranchIds = p.BranchIds,
                        BranchNames = p.BranchNames,
                        DueDate = p.DueDate,
                        Subject = p.Subject,
                        Message = p.Message,
                        IsReadByUser = p.IsReadByUser
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        [HttpPut]
        public async Task<IActionResult> MarkAnnouncementAsRead(string encryptedAnnouncementId)
        {
            if (UserSession == null)
                return Json(null);

            var apiRequest = new MarkAnnouncementAsReadApiRequest()
            {
                Id = encryptedAnnouncementId.ToDecryptInt(),
                UserId = UserSession.UserId
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Management.MarkAnnouncementAsRead, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = SiteResources.OperationIsSuccessful, ResultType = ResultType.Success });
        }

        #endregion
    }
}

