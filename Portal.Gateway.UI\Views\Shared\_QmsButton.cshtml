﻿@using Microsoft.AspNetCore.Http
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using Microsoft.Extensions.Caching.Memory
@using Portal.Gateway.ApiModel.Responses.Management.RoleAction
@using Microsoft.Extensions.Configuration
@using SessionExtensions = Portal.Gateway.UI.Extensions.SessionExtensions
@using Kendo.Mvc.TagHelpers
@inject IHttpContextAccessor HttpContextAccessor
@inject ICacheHelper CacheHelper
@inject IConfiguration Configuration
@{
    var currentUser = SessionExtensions.Get<UserModel>(HttpContextAccessor.HttpContext.Session, SessionKeys.UserSession);
    var isAuthorizedForQmsButton = false;
    var roleActions = await CacheHelper.GetRoleActionsAsync();
    isAuthorizedForQmsButton = roleActions.RoleActionSites.Where(r => currentUser.RoleIds.Contains(r.Role.Id))
        .Any(p => p.RoleActions.Any(q => q.Action.ActionTranslations.Any(r => r.Name == SiteResources.IsAuthorizedForQmsButton) && q.Action.IsActive));

    var qmsChatHubName = @Configuration.GetSection("AppSettings")["QmsChatHubAddress"];
}

<div id="chatHub" style="display: none">@Html.Raw(qmsChatHubName)</div>

<script src="~/js/QMS/Qms/qmsButton.js" asp-append-version="true"></script>
<script src="~/js/QMS/Qms/callNextPage.js" asp-append-version="true"></script>
<script src="~/lib/signalr/dist/signalr.js" asp-append-version="true"></script>

<style>
    #MainQmsButton {
        position: fixed;
        right: 0px;
        bottom: 0px;
        background-color: #663259;
        cursor: pointer;
        font-size: 16px;
        font-weight: 800;
        color: #f1f1f2;
        width: 120px;
        display: table-cell;
        text-align: center;
        padding: 20px;
        transition: 0.5s;
        box-shadow: rgb(0 0 0 / 25%) 0px 54px 55px, rgb(0 0 0 / 12%) 0px -12px 30px, rgb(0 0 0 / 12%) 0px 4px 6px, rgb(0 0 0 / 17%) 0px 12px 13px, rgb(0 0 0 / 9%) 0px -3px 5px;
        z-index: 99;
    }

    #SideNavPartial {
        float: right;
        text-align: center;
        padding-left: 30px;
        height: 100%;
        width: 220px;
        right: 0px;
        background-color: #ebf0f0;
        padding-top: 75px;
        box-shadow: rgb(0 0 0 / 25%) 0px 54px 55px, rgb(0 0 0 / 12%) 0px -12px 30px, rgb(0 0 0 / 12%) 0px 4px 6px, rgb(0 0 0 / 17%) 0px 12px 13px, rgb(0 0 0 / 9%) 0px -3px 5px;
    }

    .SideButton {
        width: 185px;
        height: 50px;
        font-weight: 800;
        margin-bottom: 10px;
    }

    #QmsSideNav {
        height: 75%;
        width: 0;
        overflow-y: hidden;
        z-index: 99;
        right: 0;
        background-color: #fefefe;
        overflow-x: hidden;
        bottom: 40px;
        margin-bottom: 20px;
        transition: 0.5s;
        box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
    }

    #SideNavContent {
        width: 1150px;
        display: none;
        height: auto;
    }

    #QmsSideNav a {
        /*padding: 8px 8px 8px 32px;*/
        text-decoration: none;
        font-size: 25px;
        /*color: #fff;*/
        /*        display: block;*/
        transition: 0.3s
    }

    #QmsSideNav .closebtn {
        position: absolute;
        top: -8px;
        right: 2px;
        font-size: 36px;
        margin-left: 50px;
        color: #58244b;
    }

    #QmsSideNav .closebtnx {
        position: absolute;
        bottom: 0;
        right: 25px;
        font-size: 36px;
        color: #58244b;
        margin-left: 50px;
    }

    .btn-circle {
        width: 45px;
        height: 45px;
        line-height: 45px;
        text-align: center;
        padding: 0;
        border-radius: 50%;
    }

        .btn-circle i {
            position: relative;
            top: -1px;
        }

    .btn-circle-sm {
        width: 35px;
        height: 35px;
        line-height: 35px;
        font-size: 0.9rem;
    }

    .btn-circle-lg {
        width: 55px;
        height: 55px;
        line-height: 55px;
        font-size: 1.1rem;
    }

    .btn-circle-xl {
        width: 70px;
        height: 70px;
        line-height: 70px;
        font-size: 1.3rem;
    }

    .waitingButton {
        float: left;
        /*        font-size: 14px;*/
        margin-right: 15px;
        margin-bottom: 15px;
        border-radius: 15px;
    }

    .ConnectedLineButton {
        visibility: hidden;
        float: left;
        /*        font-size: 14px;*/
        margin-right: 15px;
        margin-bottom: 15px;
        border-radius: 15px;
    }

    .WaitingButtons {
        margin-top: 15px;
        height: 200px;
        overflow-y: auto;
        width: 210px;
    }

    .WaitingButtons::-webkit-scrollbar {
        width: 10px;
    }

    /* Track */
    .WaitingButtons::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    /* Handle */
    .WaitingButtons::-webkit-scrollbar-thumb {
        background: #888;
    }

    /* Handle on hover */
    .WaitingButtons::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
</style>

<div id="QmsSideNav" style="position: fixed">
    <div id="SideNavContent" style="float: left;"></div>
    <a href="javascript:void(0)" class="closebtn" onclick="closeNav()">&times;</a>
    <div id="SideNavPartial">
        <div id="dropdowns" style="margin-bottom: 40px" class="row"></div>
        <div class="row">
            <button type="button" id="btnCallNext" class="btn btn-primary font-weight-bold" style="width: 195px;margin-right: 15px;margin-bottom: 15px;height: 40px;border-radius: 15px;height: 45px;font-size: 14px;">
                <i class=""></i>
                @SiteResources.CallNext.ToTitleCase()
            </button>
            <button type="button" id="btnCallNextFromHoldOn" class="btn btn-primary font-weight-bold" style="width: 195px;margin-right: 15px;margin-bottom: 15px;height: 40px;border-radius: 15px;height: 55px;font-size: 14px;">
                <i class=""></i>
                @SiteResources.CallNextFromHoldOn.ToTitleCase()
            </button>
            <button type="button" id="btnCompleted" class="btn btn-primary font-weight-bold actionButtons_" disabled="disabled" style="width: 195px;margin-right: 15px;height: 40px;border-radius: 15px;height: 45px;font-size: 14px;">
                <i class=""></i>
                @SiteResources.Completed.ToTitleCase()
            </button>
            <div class="btn-group-lg WaitingButtons" id="button-container">
                <button class="btn btn-primary waitingButton">Call Next :  <i id="callNextCount"></i></button>
                <button class="btn btn-primary waitingButton">Hold On :  <i id="HoldOnCount"></i></button>
            </div>
        </div>
    </div>
</div>
@{
    if (isAuthorizedForQmsButton)
    {
        <div id="MainQmsButton">
            <span id="btnQms">@SiteResources.QMS</span>
        </div>
    }
}
<div class="modal fade" id="searchPreApplicationApplicantByAppointmentNumber" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.ApplicantList.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divPartialApplicationHistoryByAppointmentNumber"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="Assign" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xs" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.Assign.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divActionAssign"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="CallNextFromHoldOn" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.CallNextFromHoldOn.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divCallNextFromHoldOn"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="Postpone" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.NewSlotSelection.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divActionPostpone"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="NotFound" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.NotCompleted.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divActionNotFound"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="familyOrGroupSelection" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-s" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.AddToTheExistingFamilyOrGroupApplication.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divFamilyOrGroupSelection"></div>
            </div>
        </div>
    </div>
</div>
<div id="session_user_id" style="display: none">@Html.Raw(SessionExtensions.Get<UserModel>(HttpContextAccessor.HttpContext.Session, SessionKeys.UserSession).UserId)</div>
<div id="session_branch_id" style="display: none">@Html.Raw(SessionExtensions.Get<UserModel>(HttpContextAccessor.HttpContext.Session, SessionKeys.UserSession).BranchId)</div>

<script>

    $("#btnQms").click(function (e) {
    @{
        if (currentUser.BranchId == null)
        {
            @:window.location.href = '@Html.Raw(Url.Action("SelectModuleBranch", "User", new { Area = "" }))';
        }
        else
        {
            @:loadState(false);
        }
    }
        });


    document.addEventListener('DOMContentLoaded', function () {
        document.getElementById('btnCallNext').addEventListener('click', function () {
            clickCallNext();
        });

        document.getElementById('btnCallNextFromHoldOn').addEventListener('click', function () {
            clickCallNextFromHoldOn();
        });

        document.getElementById('btnCompleted').addEventListener('click', function () {
            CompletedAction();
        });
    });

    const buttonCount = 15; // Number of ConnectedLine buttons
    const container = document.getElementById('button-container');

    for (let i = 1; i <= buttonCount; i++) {
        const button = document.createElement('button');
        button.id = `ConnectedLineButton-${i}`;
        button.className = 'btn btn-primary ConnectedLineButton';

        const icon = document.createElement('i');
        icon.id = `ConnectedLineCount-${i}`;

        button.appendChild(icon);
        container.appendChild(button);
    }
</script>

