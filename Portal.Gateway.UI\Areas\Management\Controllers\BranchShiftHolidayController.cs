﻿using Kendo.Mvc.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Management.BranchApplicationCountry;
using Portal.Gateway.ApiModel.Requests.Management.BranchShiftHoliday;
using Portal.Gateway.ApiModel.Requests.Public.SlotType;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Management.BranchApplicationCountry;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.UI.Areas.Management.ViewModels.BranchShiftHoliday;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using static Portal.Gateway.UI.Areas.Management.ViewModels.BranchShiftHoliday.AddShiftHolidayViewModel;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
	[Area("Management")]
	public class BranchShiftHolidayController : BaseController<BranchShiftHolidayController>
	{
		public BranchShiftHolidayController(
			   IOptions<AppSettings> appSettings,
			   ICacheHelper cacheHelper)
			: base(appSettings, cacheHelper)
		{

		}

		#region AddUpdate

		public async Task<IActionResult> Add()
		{
			var apiRequest = new PaginatedBranchApplicationCountryApiRequest
			{
				CountryId = null,
				BranchId = null,
				Pagination = new PaginationApiRequest
				{
					Page = 1,
					PageSize = 500,
					OrderBy = null,
					SortDirection = new ListSortDirection()
				}
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedBranchApplicationCountryApiResponse>>>
				(apiRequest, ApiMethodName.Management.GetPaginatedBranchApplicationCountry, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			var viewModel = new AddShiftHolidayViewModel()
			{
				BranchApplicationCountrySelectList = apiResponse.Data.Items.FirstOrDefault().BranchApplicationCountries
				.Where(p => p.IsActive && UserSession.BranchIds.Any(q => q == p.BranchId))
				.OrderBy(p => p.BranchName)
				.Select(p => new SelectListItem
				{
					Text = $"{p.BranchName} -> {p.CountryName}",
					Value = p.Id.ToString()
				}).ToList(),
				DayOfWeeks = EnumHelper.GetEnumAsDictionary(typeof(Contracts.Entities.Enums.DayOfWeek)).Select(x => new AddShiftHolidayViewModel.DayOfWeekViewModel
				{
					IsActive = false,
					Id = x.Key,
					Text = x.Value.ToTitleCase()
				}).ToArray(),
				StartOfShift = new TimeSpan(),
				EndOfShift = new TimeSpan(),
				StartOfLunch = new TimeSpan(),
				EndOfLunch = new TimeSpan(),
				AppointmentPeriod = 5,
				Holidays = new List<HolidayViewModel> { new HolidayViewModel { } }

			};

			return View(viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> Add(AddShiftHolidayViewModel viewModel)
		{
			var apiRequest = new AddBranchShiftHolidayApiRequest()
			{
				BranchApplicationCountryId = viewModel.BranchApplicationCountryId,
				StartOfShift = viewModel.StartOfShift,
				EndOfShift = viewModel.EndOfShift,
				StartOfLunch = viewModel.StartOfLunch,
				EndOfLunch = viewModel.EndOfLunch,
				AppointmentPeriod = viewModel.AppointmentPeriod,
				DayOfWeeks = viewModel.DayOfWeeks.Where(x => x.IsActive).Select(p => new AddBranchShiftHolidayApiRequest.DayOfWeekApiRequest()
				{
					Id = p.Id,
					IsActive = p.IsActive,
					Text = p.Text
				}).ToList(),
				Holidays = viewModel.Holidays?.Select(x => new AddBranchShiftHolidayApiRequest.HolidayApiRequest() 
				{ 
					HolidayDate = x.HolidayDate,
					Description = x.Description 
				}).ToList(),

			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<AddApiResponse>>
				(apiRequest, ApiMethodName.Management.AddBranchShiftHoliday, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

        [HttpGet]
        public async Task<IActionResult> GetShiftHolidayByBranchApplicationCountry(int branchApplicationCountryId)
        {
            var apiResponse = await PortalHttpClientHelper
                           .GetAsync<ApiResponse<BranchShiftHolidayDays>>
                           (ApiMethodName.Management.GetShiftHolidayByBranchApplicationCountry + branchApplicationCountryId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                           .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(new List<SelectListItem>());

            return Json(apiResponse.Data);
        }
    }
	#endregion
}