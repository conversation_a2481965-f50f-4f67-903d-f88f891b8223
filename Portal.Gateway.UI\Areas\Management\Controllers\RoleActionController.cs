﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.Management.RoleAction;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Management.RoleAction;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.General;
using Portal.Gateway.UI.Areas.Management.ViewModels.RoleAction;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class RoleActionController : BaseController<RoleActionController>
    {
        public RoleActionController(
               IOptions<AppSettings> appSettings,
               ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        [HttpGet]
        public async Task<IActionResult> List(string encryptedRoleId)
        {
            if (string.IsNullOrEmpty(encryptedRoleId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("/");
            }

            var apiResponse = await PortalHttpClientHelper
                                    .GetAsync<ApiResponse<RoleActionApiResponse>>
                                    (ApiMethodName.Management.GetRoleAction + "-1", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                                    .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", result);
                return RedirectToAction("/");
            }

            var viewModel = new RoleActionViewModel
            {
                EncryptedRoleId = encryptedRoleId,
                RoleActions = apiResponse.Data.RoleActionSites
                                .Where(p => p.Role.Id == encryptedRoleId.ToDecryptInt())
                                .FirstOrDefault()?
                                .RoleActions?.Where(q => !q.Action.IsPublic)?
                                .Select(p => new RoleActionsViewModel() 
                                {
                                    Action = new ActionViewModel()
                                    {
                                        EncryptedId = p.Action.Id.ToEncrypt(),
                                        IsActive = p.Action.IsActive,
                                        IsMenuItem = p.Action.IsMenuItem,
                                        Name = p.Action.ActionTranslations.Any(x => x.LanguageId == LanguageId) ?
                                            p.Action.ActionTranslations.First(x => x.LanguageId == LanguageId).Name :
                                            p.Action.ActionTranslations.FirstOrDefault()?.Name,
                                        Description = p.Action.ActionTranslations.Any(x => x.LanguageId == LanguageId) ?
                                            p.Action.ActionTranslations.First(x => x.LanguageId == LanguageId).Description :
                                            p.Action.ActionTranslations.FirstOrDefault()?.Description,
                                    }
                                }).ToList() ?? null
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateRoleAction(RoleActionViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateRoleActionApiRequest
            {
                RoleId = viewModel.EncryptedRoleId.ToDecryptInt(),
                Actions = viewModel.RoleActions.Select(p => new UpdateRoleActionsApiRequest() 
                {
                    ActionId = p.Action.EncryptedId.ToDecryptInt(),
                    IsActive = p.Action.IsActive
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateRoleAction, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }
    }
}