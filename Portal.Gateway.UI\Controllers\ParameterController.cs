﻿using Gateway.Extensions;
using Gateway.Http;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Appointment.Application;
using Portal.Gateway.ApiModel.Requests.Appointment.ExtraFee;
using Portal.Gateway.ApiModel.Requests.General;
using Portal.Gateway.ApiModel.Requests.Management.Announcement;
using Portal.Gateway.ApiModel.Requests.Management.BranchApplicationCountry;
using Portal.Gateway.ApiModel.Requests.Management.Slot;
using Portal.Gateway.ApiModel.Requests.Public.SlotType;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Admin.Company;
using Portal.Gateway.ApiModel.Responses.Appointment.Application;
using Portal.Gateway.ApiModel.Responses.Appointment.ExtraFee;
using Portal.Gateway.ApiModel.Responses.DigitalSignature;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.ApiModel.Responses.Management.BranchApplicationCountry;
using Portal.Gateway.ApiModel.Responses.Management.Slot;
using Portal.Gateway.ApiModel.Responses.Report;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.UI.Areas.Biometrics.Models.Cabin.RequestModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.Cabin.Results;
using Portal.Gateway.UI.Areas.Biometrics.Models.InventoryDefinition.RequestModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.InventoryDefinition.Results;
using Portal.Gateway.UI.Areas.Biometrics.Models.InventoryType.RequestModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.InventoryType.Results;
using Portal.Gateway.UI.Areas.Biometrics.Models.NeurotecLicense.RequestModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.NeurotecLicense.Results;
using Portal.Gateway.UI.Areas.Biometrics.Models.Office.RequestModels;
using Portal.Gateway.UI.Areas.Biometrics.Models.Office.Results;
using Portal.Gateway.UI.Areas.Cargo.Models.Cargo.Responses;
using Portal.Gateway.UI.Areas.Management.ViewModels.Screen;
using Portal.Gateway.UI.Areas.QMS.Models.QMS.Requests;
using Portal.Gateway.UI.Areas.QMS.Models.QMS.Responses;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using static Portal.Gateway.UI.Config.CacheSettings;

namespace Portal.Gateway.UI.Controllers
{
    [AllowAnonymous]
    public class ParameterController : BaseController<ParameterController>
    {
        private readonly CurrentUserDataHelper _currentUserDataHelper;
        private readonly ILogger<ParameterController> _logger;

        public ParameterController(ILogger<ParameterController> logger, IOptions<AppSettings> appSettings,
            CurrentUserDataHelper currentUserDataHelper,
            ICacheHelper cacheHelper) : base(appSettings, cacheHelper)
        {
            _currentUserDataHelper = currentUserDataHelper;
            _logger = logger;
        }

        #region Enums

        public IActionResult GetYesNoSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(YesNo)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetOnlineOfflineSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(OnlineOffline)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetModuleTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(ModuleType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetApplicantFamilyRelationShipSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(ApplicantFamilyRelationship)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetApplicantGroupRelationShipSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(ApplicantGroupRelationship)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetApplicantTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(ApplicantType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetApplicationConfirmationTypeSelectList(int confirmationCodeType)
        {
            var applicationInformationType = EnumHelper.GetEnumAsDictionary(typeof(ConfirmationType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() });

            switch (confirmationCodeType)
            {
                case (int)ConfirmationCodeType.SmsCode:
                    applicationInformationType = applicationInformationType.Where(r => r.Value != ConfirmationType.MailSendingError.ToInt().ToString());
                    break;
                case (int)ConfirmationCodeType.MailCode:
                    applicationInformationType = applicationInformationType.Where(r => r.Value != ConfirmationType.SmsSendingError.ToInt().ToString());
                    break;
            }

            return Json(applicationInformationType);
        }

        public IActionResult GetAdditionalServiceTypeSelectList()
        {
	        return Json(EnumHelper.GetEnumAsDictionary(typeof(AdditionalServiceType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }
		public IActionResult GetVehicleTypeSelectList()
		{
			return Json(EnumHelper.GetEnumAsDictionary(typeof(VehicleType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
		}
		public IActionResult GetApplicationTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(ApplicationType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetIcrTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(IcrRelationType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetApplicationPassportStatusSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(ApplicationPassportStatus)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetTitleSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(Title)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetGenderSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(Gender)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetMaritalStatusSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(MaritalStatus)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public async Task<IActionResult> GetVisaCategoryTypeSelectList(int branchApplicationCountryId, bool takeFromSession, bool isReturnTypeBranchBased) //dynamic method
        {
            if (UserSession is null)
                return Json(null);

            var apiRequest = new BranchApplicationCountryVisaCategoriesApiRequest
            {
                BranchApplicationCountryId = branchApplicationCountryId,
                IsDeactivatedDataIncluded = false
            };

            if (apiRequest.BranchApplicationCountryId == 0 && takeFromSession) apiRequest.BranchId = UserSession.BranchId;

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<BranchApplicationCountryVisaCategoriesApiResponse>>
                (apiRequest, ApiMethodName.Appointment.GetBranchApplicationCountryVisaCategories, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            if(isReturnTypeBranchBased)
                return Json(apiResponse.Data.VisaCategoryTypes.Where(s => apiResponse.Data.BranchVisaCategories.Select(r => r.Id).Contains(s.Id)).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.Translations.First(s => s.LanguageId == LanguageId).Name.ToTitleCase() }));

            return Json(apiResponse.Data.VisaCategoryTypes.Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.Translations.First(s => s.LanguageId == LanguageId).Name.ToTitleCase() }));
        }

        public async Task<IActionResult> GetVisaCategoryTypeByDefinitionSelectList()
        {
            var apiRequest = new BranchApplicationCountryVisaCategoriesApiRequest
            {
                BranchId = UserSession?.BranchId,
                IsDeactivatedDataIncluded = false,
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<BranchApplicationCountryVisaCategoriesApiResponse>>
                (apiRequest, ApiMethodName.Appointment.GetBranchApplicationCountryVisaCategories, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            return Json(apiResponse.Data.VisaCategoryTypes.Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.Translations.First(s => s.LanguageId == LanguageId).Name.ToTitleCase() }));
        }

        public IActionResult GetVisaDurationSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(VisaDurationType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }
        public IActionResult GetVisaDurationOtherSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(VisaDurationOtherType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetReimbursementTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(ReimbursementType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetVisaEntryTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(VisaEntryType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetCurrencyTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(CurrencyType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetCargoProviderTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(CargoProviderType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetQmsCompanyTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(QmsCompanyType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetBranchEmailProviderTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(EmailProviderType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetBranchSmsProviderTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(SmsProviderType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetCommissionTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(CommissionType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetPhotoBoothStatusSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(PhotoBoothStatus)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetApplicationCancellationTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(ApplicationCancellationType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetApplicationCancellationReasonSelectList(bool isTurkmenApplication)
        {
            return UserSession.BranchId is 37 || isTurkmenApplication ?
                Json(EnumHelper.GetEnumAsDictionary(typeof(ApplicationCancellationReason)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() })) :
                Json(EnumHelper.GetEnumAsDictionary(typeof(ApplicationCancellationReason)).Where(r => r.Key != 8).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetApplicationCancellationStatusSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(ApplicationCancellationStatus)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetReportTypeSelectList()
        {
            var enumDictionary = EnumHelper.GetEnumAsDictionary(typeof(ReportType)).OrderBy(q => q.Value)
                .Where(w => w.Key != 71).Select(x => new SelectListItem
                    { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() });

           //branch + role check

           var isUserHasIndiaPermission = CheckUserHasIndiaBranch();

           if (!isUserHasIndiaPermission)
               enumDictionary = enumDictionary.Where(s => s.Value != ((int)ReportType.IndiaAllApplications).ToString());

           return Json(enumDictionary);
        }

        public IActionResult GetlimitedAuthorizedReportTypeSelectList()
        {
            var enumDictionary = EnumHelper.GetEnumAsDictionary(typeof(NonAuthorizedReportType)).OrderBy(q => q.Value).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() });

            //branch + role check

            var isUserHasIndiaPermission = CheckUserHasIndiaBranch();

            if (!isUserHasIndiaPermission)
                enumDictionary = enumDictionary.Where(s => s.Value != ((int)ReportType.IndiaAllApplications).ToString());

            return Json(enumDictionary);
        }

        public IActionResult GetConsularReportTypeSelectList(bool isAuthorizedForTmStatisticReport)
        {
	        return isAuthorizedForTmStatisticReport 
		        ?
					Json(EnumHelper.GetEnumAsDictionary(typeof(ConsularReportType)).OrderBy(q => q.Value).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() })) 
				: 
					Json(EnumHelper.GetEnumAsDictionary(typeof(ConsularReportType)).Where(r => r.Key != (int)ConsularReportType.TurkmenistanStatisticReport).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
		
        }

        public IActionResult GetPolicyLanguageTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(PolicyLanguage)).OrderBy(q => q.Value).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }
		public IActionResult GetTsPolicyLanguageTypeSelectList()
		{
			return Json(EnumHelper.GetEnumAsDictionary(typeof(TsPolicyLanguage)).OrderBy(q => q.Value).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
		}
		public IActionResult GetInfoDeskTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(InfoDeskTypes)).OrderBy(q => q.Value).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetActiveQueueMaticStatusSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(QueueMaticStatusTypes)).Where(q => q.Key != (int)QueueMaticStatusTypes.Completed).OrderBy(q => q.Value).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetQMSScreenTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(QMSScreenType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetQMSContentTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(QMSContentType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetQMSLastHeartbeatTime()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(QMSLastHeartbeatTime)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetQMSWhiteListBiometricData()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(QMSWhiteListBiometricData)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetQMSWhiteListDocumentExemption()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(QMSWhiteListDocumentExemption)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetPreApplicationInsuranceType()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(PreApplicationInsuranceType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetUtcTimeZoneSelectList()
        {
            string seperator = "";

            if (LanguageId == 1)
                seperator = ",";
            else
                seperator = ".";

            var model = new List<SelectListItem>()
            {
                new SelectListItem { Value = "-12", Text = "(GMT-12:00)" },
                new SelectListItem { Value = "-11" + seperator + "30", Text = "(GMT-11:30)" },
                new SelectListItem { Value = "-11", Text = "(GMT-11:00)" },
                new SelectListItem { Value = "-10" + seperator + "30", Text = "(GMT-10:30)" },
                new SelectListItem { Value = "-10", Text = "(GMT-10:00)" },
                new SelectListItem { Value = "-9" + seperator + "30", Text = "(GMT-09:30)" },
                new SelectListItem { Value = "-9", Text = "(GMT-09:00)" },
                new SelectListItem { Value = "-8" + seperator + "30", Text = "(GMT-08:30)" },
                new SelectListItem { Value = "-8", Text = "(GMT-08:00)" },
                new SelectListItem { Value = "-7" + seperator + "30", Text = "(GMT-07:30)" },
                new SelectListItem { Value = "-7", Text = "(GMT-07:00)" },
                new SelectListItem { Value = "-6" + seperator + "30", Text = "(GMT-06:30)" },
                new SelectListItem { Value = "-6", Text = "(GMT-06:00)" },
                new SelectListItem { Value = "-5" + seperator + "30", Text = "(GMT-05:30)" },
                new SelectListItem { Value = "-5", Text = "(GMT-05:00)" },
                new SelectListItem { Value = "-4" + seperator + "30", Text = "(GMT-04:30)" },
                new SelectListItem { Value = "-4", Text = "(GMT-04:00)" },
                new SelectListItem { Value = "-3" + seperator + "30", Text = "(GMT-03:30)" },
                new SelectListItem { Value = "-3", Text = "(GMT-03:00)" },
                new SelectListItem { Value = "-2" + seperator + "30", Text = "(GMT-02:30)" },
                new SelectListItem { Value = "-2", Text = "(GMT-02:00)" },
                new SelectListItem { Value = "-1" + seperator + "30", Text = "(GMT-01:30)" },
                new SelectListItem { Value = "-1", Text = "(GMT-01:00)" },
                new SelectListItem { Value = "0", Text = "(GMT+00:00)" },
                new SelectListItem { Value = "0" + seperator + "30", Text = "(GMT+00:30)" },
                new SelectListItem { Value = "1", Text = "(GMT+01:00)" },
                new SelectListItem { Value = "1" + seperator + "30", Text = "(GMT+01:30)" },
                new SelectListItem { Value = "2", Text = "(GMT+02:00)" },
                new SelectListItem { Value = "2" + seperator + "30", Text = "(GMT+02:30)" },
                new SelectListItem { Value = "3", Text = "(GMT+03:00)" },
                new SelectListItem { Value = "3" + seperator + "30", Text = "(GMT+03:30)" },
                new SelectListItem { Value = "4", Text = "(GMT+04:00)" },
                new SelectListItem { Value = "4" + seperator + "30", Text = "(GMT+04:30)" },
                new SelectListItem { Value = "5", Text = "(GMT+05:00)" },
                new SelectListItem { Value = "5" + seperator + "30", Text = "(GMT+05:30)" },
                new SelectListItem { Value = "6", Text = "(GMT+06:00)" },
                new SelectListItem { Value = "6" + seperator + "30", Text = "(GMT+06:30)" },
                new SelectListItem { Value = "7", Text = "(GMT+07:00)" },
                new SelectListItem { Value = "7" + seperator + "30", Text = "(GMT+07:30)" },
                new SelectListItem { Value = "8", Text = "(GMT+08:00)" },
                new SelectListItem { Value = "8" + seperator + "30", Text = "(GMT+08:30)" },
                new SelectListItem { Value = "9", Text = "(GMT+09:00)" },
                new SelectListItem { Value = "9" + seperator + "30", Text = "(GMT+09:30)" },
                new SelectListItem { Value = "10", Text = "(GMT+10:00)" },
                new SelectListItem { Value = "10" + seperator + "30", Text = "(GMT+10:30)" },
                new SelectListItem { Value = "11", Text = "(GMT+11:00)" },
                new SelectListItem { Value = "11" + seperator + "30", Text = "(GMT+11:30)" },
                new SelectListItem { Value = "12", Text = "(GMT+12:00)" },
                new SelectListItem { Value = "12" + seperator + "30", Text = "(GMT+12:30)" },
                new SelectListItem { Value = "13", Text = "(GMT+13:00)" },
                new SelectListItem { Value = "13" + seperator + "30", Text = "(GMT+13:30)" },
            };

            return Json(model);
        }

        public IActionResult GetAgencyCategorySelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(AgencyCategory)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetAgencyTypeFileTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(AgencyTypeFileType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetAgencyStatusSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(AgencyStatus)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetActivationStatusSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(ActivationStatusType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetApplicationFileTypeSelectList(bool isDamageRejection, bool isExtraFile, bool isDetailTurkmenistan, bool isLocalAuthorized)
        {
            var damageRejectionList = new[] {
                    ApplicationFileType.DamageEntryStamp,
                    ApplicationFileType.DamageMedicalReport,
                    ApplicationFileType.DamageExpenseBill,
                    ApplicationFileType.DamageStatement,
                    ApplicationFileType.RejectionPassport,
                    ApplicationFileType.RejectionDataPage,
                    ApplicationFileType.RejectionReturnStatement
                };

            var extraFilesList = new[]
            {
                ApplicationFileType.ExtraFile,
                ApplicationFileType.Permit

            };

            var digitalSignatureDocumentList = EnumHelper.GetEnumAsDictionary(typeof(DigitalSignatureDocument)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() });

            var result = EnumHelper.GetEnumAsDictionary(typeof(ApplicationFileType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() });

            result = result.Union(digitalSignatureDocumentList);

            if (!isDetailTurkmenistan)
            {
                result = result.Where(p => p.Value != ((int)ApplicationFileType.IHB).ToString());

                if (isDamageRejection)
                    result = result.Where(p => damageRejectionList.Select(p => p.ToInt()).ToList().Contains(p.Value.ToInt()));
                else if (isExtraFile)
                    result = result.Where(p => extraFilesList.Select(p => p.ToInt()).ToList().Contains(p.Value.ToInt()));
                else
                    result = result.Where(p => !damageRejectionList.Select(p => p.ToInt()).ToList().Contains(p.Value.ToInt()) && !extraFilesList.Select(p => p.ToInt()).ToList().Contains(p.Value.ToInt()));

                return Json(result);
            }

            ApplicationFileType[] turkmenistanFilesList;

            if (isLocalAuthorized)
            {
                turkmenistanFilesList = new[]
                {
                    ApplicationFileType.IHB
                };
            }
            else
            {
                turkmenistanFilesList = new[]
                {
                    ApplicationFileType.Passport
                };
            }

            result = result.Where(p => turkmenistanFilesList.Select(p => p.ToInt()).ToList().Contains(p.Value.ToInt()));

            return Json(result);
        }

        public IActionResult GetBranchApplicationCountryFileTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(BranchApplicationCountryFileType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetVasTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(VasType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetVasTypeSelectListFilter()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(VasTypeFilter)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetTravelDocumentTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(TravelDocumentType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetTravelDocumentCategoryTypeSelectList(int? travelDocumentTypeId)
        {
            if (travelDocumentTypeId.HasValue && travelDocumentTypeId > 0)
            {
                var result = ReflectionHelper.GetTravelDocumentCategoryType((TravelDocumentType)travelDocumentTypeId);

                return Json(result.Select(x => new SelectListItem { Value = x.ToInt().ToString(), Text = x.ToDescription() }));
            }
            else
            {
                return Json(EnumHelper.GetEnumAsDictionary(typeof(TravelDocumentCategoryType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
            }
        }

        public IActionResult GetTravelPurposeTypeSelectList(int travelDocumentCategoryTypeId)
        {
            if (travelDocumentCategoryTypeId > 0)
            {
                var result = ReflectionHelper.GetTravelPurposeType((TravelDocumentCategoryType)travelDocumentCategoryTypeId);

                return Json(result.Select(x => new SelectListItem { Value = x.ToInt().ToString(), Text = x.ToDescription() }));
            }
            else
            {
                return Json(new SelectList(Enumerable.Empty<SelectListItem>()));
            }
        }

        public IActionResult GetRevocationDocumentTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(RevocationDocumentType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetDeportationInstitutionTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(DeportationInstitutionType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetDeportationReasonTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(DeportationReasonType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetRejectionTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(RejectionType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetRejectionInstitutionTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(RejectionInstitutionType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetRefusingAuthorityTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(RefusingAuthorityType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetMeansOfTransportTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(MeansOfTransportType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetExpensesCoverByTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(ExpensesCoverByType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetDataOccupationTypeSelectList()
        {
            var occupations = EnumHelper.GetEnumAsDictionary(typeof(DataOccupationType))
                .Select(x => new SelectListItem
                {
                    Value = x.Key.ToString(),
                    Text = x.Value.ToTitleCase()
                })
                .OrderBy(item => item.Text, StringComparer.Create(new CultureInfo("tr-TR"), true))
                .ToList();

            return Json(occupations);
        }


        public IActionResult GetQualityCheckStatusSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(QualityCheckStatus)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetBankTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(BankType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetChannelTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(ChannelType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetInsuranceProviderTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(ProviderType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }
        public IActionResult GetVerificationTypeList()
        {
            var verificationTypes = EnumHelper.GetEnumAsDictionary(typeof(VeriyfContactInformationMethod)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }).ToList();

            verificationTypes.Add(new SelectListItem { Value = "0", Text = "-" });

            return Json(verificationTypes);
        }

        public IActionResult GetInventoryTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(InventoryType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetInventoryStatusSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(InventoryStatus)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
		}

		public IActionResult GetICRTypes(string branchCountryIso3)
		{
            if(branchCountryIso3 == "DZA") //Algeria
				return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English || q.Key == (int)PDFDocumentTypes.French || q.Key == (int)PDFDocumentTypes.EnglishAllItemsTaxed || q.Key == (int)PDFDocumentTypes.FrenchAllItemsTaxed).OrderBy(q => q.Key).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
			else if (branchCountryIso3 == "IND" || branchCountryIso3 == "NPL") //India, Nepal
				return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English || q.Key == (int)PDFDocumentTypes.EnglishTaxed || q.Key == (int)PDFDocumentTypes.EnglishAllItemsTaxed).OrderBy(q => q.Key).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
			else if (branchCountryIso3 == "IRQ" || branchCountryIso3 == "KWT" || branchCountryIso3 == "LBY") //Iraq, Kuwait, Libya
				return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English || q.Key == (int)PDFDocumentTypes.Arabic).OrderBy(q => q.Key).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
			else if (branchCountryIso3 == "RUS") //Russia
				return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English || q.Key == (int)PDFDocumentTypes.Russian || q.Key == (int)PDFDocumentTypes.EnglishTaxed || q.Key == (int)PDFDocumentTypes.RussianTaxed).OrderBy(q => q.Key).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
			else if (branchCountryIso3 == "SAU") //Saudi Arabia
				return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.EnglishTaxed || q.Key == (int)PDFDocumentTypes.ArabicTaxed || q.Key == (int)PDFDocumentTypes.EnglishAllItemsTaxed || q.Key == (int)PDFDocumentTypes.ArabicAllItemsTaxed).OrderBy(q => q.Key).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
			else if (branchCountryIso3 == "TKM") //Turkmenistan
				return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English || q.Key == (int)PDFDocumentTypes.Turkmen || q.Key == (int)PDFDocumentTypes.Russian).OrderBy(q => q.Key).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
			else if (branchCountryIso3 == "ARE") //United Arab Emirates
				return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English || q.Key == (int)PDFDocumentTypes.EnglishTaxed).OrderBy(q => q.Key).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
			else if	(branchCountryIso3 == "AccountingICR")
                return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English || q.Key == (int)PDFDocumentTypes.Arabic || q.Key == (int)PDFDocumentTypes.Turkmen || q.Key == (int)PDFDocumentTypes.Russian).OrderBy(q => q.Key).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
			else
				return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
		}

        public IActionResult GetPrinterICRLanguages()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English || q.Key == (int)PDFDocumentTypes.Arabic || q.Key == (int)PDFDocumentTypes.French || q.Key == (int)PDFDocumentTypes.Turkmen || q.Key == (int)PDFDocumentTypes.Russian).OrderBy(q => q.Key).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetEntryInformationFormTypes(string branchCountryIso3)
		{
			if (branchCountryIso3 == "DZA") //Algeria
                return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English || q.Key == (int)PDFDocumentTypes.French || q.Key == (int)PDFDocumentTypes.Arabic).OrderBy(q => q.Key).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
            else if (branchCountryIso3 == "IND" || branchCountryIso3 == "NPL") //India, Nepal
				return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
			else if (branchCountryIso3 == "IRQ" || branchCountryIso3 == "KWT" || branchCountryIso3 == "LBY" || branchCountryIso3 == "SAU" || branchCountryIso3 == "ARE") //Iraq, Kuwait, Libya, Saudi Arabia, United Arab Emirates
				return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English || q.Key == (int)PDFDocumentTypes.Arabic).OrderBy(q => q.Key).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
			else if (branchCountryIso3 == "RUS") //Russia
				return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English || q.Key == (int)PDFDocumentTypes.Russian).OrderBy(q => q.Key).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
			else if (branchCountryIso3 == "TKM") //Turkmenistan
				return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.TkmEnglish || q.Key == (int)PDFDocumentTypes.TkmTurkish || q.Key == (int)PDFDocumentTypes.TkmRussian || q.Key == (int)PDFDocumentTypes.Turkmen).OrderBy(q => q.Key).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
			else
				return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
		}

		public IActionResult GetCargoTypes(string branchCountryIso3)
		{
			if (branchCountryIso3 == "DZA") //Algeria
				return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English || q.Key == (int)PDFDocumentTypes.French || q.Key == (int)PDFDocumentTypes.Arabic).OrderBy(q => q.Key).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
			else if (branchCountryIso3 == "IND" || branchCountryIso3 == "NPL") //India, Nepal
				return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
			else if (branchCountryIso3 == "IRQ" || branchCountryIso3 == "KWT" || branchCountryIso3 == "LBY" || branchCountryIso3 == "SAU" || branchCountryIso3 == "ARE") //Iraq, Kuwait, Libya, Saudi Arabia, United Arab Emirates
				return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English || q.Key == (int)PDFDocumentTypes.Arabic).OrderBy(q => q.Key).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
			else if (branchCountryIso3 == "RUS") //Russia
				return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English || q.Key == (int)PDFDocumentTypes.Russian).OrderBy(q => q.Key).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
			else
				return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
		}

		public IActionResult GetApplicationPageTypes()
		{
			return Json(EnumHelper.GetEnumAsDictionary(typeof(PDFDocumentTypes)).Where(q => q.Key == (int)PDFDocumentTypes.English || q.Key == (int)PDFDocumentTypes.Turkish).OrderBy(q => q.Key).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
		}

        public IActionResult GetRelatedInsuranceApplicationCancellationTypes()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(RelatedInsuranceApplicationCancellationType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        #endregion

        #region Cache

        public async Task<IActionResult> GetDepartmentsSelectList(int? departmentId)
        {
            var departments = await CacheHelper.GetDepartmentsAsync();
            return Json(departments.Departments
                .Select(x => new SelectListItem
                {
                    Value = x.Id.ToString(),
                    Text = x.NameTranslations.Any(q => q.LanguageId == LanguageId) ?
                        x.NameTranslations.First(q => q.LanguageId == LanguageId).Name :
                        x.NameTranslations.FirstOrDefault().Name
                }));
        }

        public IActionResult GetIncorrectApplicationStatusReason()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(IncorrectApplicationStatusReason)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public async Task<IActionResult> GetCachedApplicationStatusList(string EncryptedBranchId, string FilterBranchIds)
        {
            List<int> BranchIds = new List<int>();
            List<int> ApplicationStatusIds = new List<int>();

            var apiRequest = new BranchApplicationStatusesApiRequest();

            if (!string.IsNullOrEmpty(FilterBranchIds))
            {
                string[] FilterBranchId = FilterBranchIds.Split(' ');
                foreach (var id in FilterBranchId)
                {
                    BranchIds.Add(id.ToInt());
                }
            }
            else if (!string.IsNullOrEmpty(EncryptedBranchId))
            {
                BranchIds.Add(EncryptedBranchId.ToDecryptInt());
            }

            if (BranchIds.Count > 0)
            {
                foreach (var id in BranchIds)
                {
                    apiRequest.BranchId = id;

                    var apiResponse = await PortalHttpClientHelper
                  .PostAsJsonAsync<ApiResponse<BranchApplicationStatusesApiResponse>>(apiRequest, ApiMethodName.Parameter.GetBranchApplicationStatuses, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                  .ConfigureAwait(false);

                    foreach (var totalId in apiResponse.Data.BranchApplicationStatuses)
                    {
                        if (totalId.IsActive && !ApplicationStatusIds.Contains(totalId.ApplicationStatusId))
                            ApplicationStatusIds.Add(totalId.ApplicationStatusId);
                    }
                }
            }

            var cacheItem = await CacheHelper.GetApplicationStatusOrderAsync();

            if (EncryptedBranchId == null && FilterBranchIds == null)
            {
                return Json(cacheItem.ApplicationStatusOrders
                            .Select(x => new SelectListItem
                            {
                                Value = x.ApplicationStatusId.ToString(),
                                Text = x.NameTranslation.Any(q => q.LanguageId == LanguageId) ?
                                        x.NameTranslation.First(q => q.LanguageId == LanguageId).Name :
                                        x.NameTranslation.FirstOrDefault().Name
                            }).OrderBy(x => x.Text));
            }
            else
            {
                return Json(cacheItem.ApplicationStatusOrders
                            .Select(x => new SelectListItem
                            {
                                Value = x.ApplicationStatusId.ToString(),
                                Text = x.NameTranslation.Any(q => q.LanguageId == LanguageId) ?
                                        x.NameTranslation.First(q => q.LanguageId == LanguageId).Name :
                                        x.NameTranslation.FirstOrDefault().Name
                            }).Where(x => ApplicationStatusIds.Contains(x.Value.ToInt())).OrderBy(x => x.Text));
            }
        }

        public async Task<IActionResult> GetCachedCountrySelectList()
        {
            var countries = await CacheHelper.GetCountriesAsync();

            return Json(countries.Countries.Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.Name }).OrderBy(o => o.Text));
        }
        public async Task<IActionResult> GetCachedCountryReleatedInsuranceSelectList()
        {
            var countries = await CacheHelper.GetCountriesAsync();

            return Json(countries.Countries.Select(x => new { Id = x.Id.ToString(), Name = x.Name }).OrderBy(o => o.Name));
        }

        public async Task<IActionResult> GetCachedBranchDepartmentSelectList()
        {
            var departmentList = await CacheHelper.GetDepartmentsAsync();

            return Json(departmentList.Departments.Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.NameTranslations?.FirstOrDefault(p => p.LanguageId == LanguageId)?.Name }));
        }

        public async Task<IActionResult> GetCachedBranchesByCountrySelectList(int? countryId)
        {
            var branchList = await CacheHelper.GetBranchesAsync();

            return Json(countryId == 0 ?
                new List<SelectListItem>() :
                branchList.Branches.Where(r => r.Country.Id == countryId).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.BranchTranslations?.FirstOrDefault(p => p.LanguageId == LanguageId)?.Name }));
        }

        #endregion

        public async Task<IActionResult> GetCitySelectList()
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<CitiesApiResponse>>
                (ApiMethodName.Parameter.GetCities, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            return Json(apiResponse.Data.Cities.OrderBy(p => p.Name).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = $"{x.Name}" }));
        }

        public async Task<IActionResult> GetForeignCitySelectList()
        {
            if (UserSession is null)
                return Json(null);

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<CitiesApiResponse>>
                    (ApiMethodName.Parameter.GetForeignCities, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            return Json(apiResponse.Data.Cities
                .Where(p => p.CountryId == UserSession.BranchCountryId).OrderBy(p => p.Name)
                .Select(x => new SelectListItem { Value = x.Id.ToString(), Text = $"{x.Name}" }));
        }

        public async Task<IActionResult> GetHealthInstitutionSelectList()
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<HealthInstitutionsApiResponse>>
                (ApiMethodName.Parameter.GetHealthInstitutions, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            return Json(apiResponse.Data.HealthInstitutions.OrderBy(p => p.Name).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = $"{x.Name}" }));
        }

        public async Task<IActionResult> GetBranchApplicationCountrySelectList()
        {
            var apiRequest = new PaginatedBranchApplicationCountryApiRequest
            {
                CountryId = null,
                BranchId = null,
                Pagination = new PaginationApiRequest
                {
                    Page = 1,
                    PageSize = 500,
                    OrderBy = null,
                    SortDirection = new ListSortDirection()
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedBranchApplicationCountryApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedBranchApplicationCountry, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            return Json(apiResponse.Data.Items.FirstOrDefault().BranchApplicationCountries.Where(p => p.IsActive).OrderBy(p => p.BranchName).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = $"{x.BranchName} -> {x.CountryName}" }));
        }

        public async Task<IActionResult> GetBranchApplicationCountrySelectListByCountryId(int countryId)
        {
            var apiRequest = new PaginatedBranchApplicationCountryApiRequest
            {
                CountryId = countryId,
                BranchId = null,
                Pagination = new PaginationApiRequest
                {
                    Page = 1,
                    PageSize = 500,
                    OrderBy = null,
                    SortDirection = new ListSortDirection()
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedBranchApplicationCountryApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedBranchApplicationCountry, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            return Json(apiResponse.Data.Items.FirstOrDefault()?.BranchApplicationCountries.Where(p => p.IsActive).OrderBy(p => p.BranchName).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.BranchName }));
        }

        public async Task<IActionResult> GetAuthorizedBranchApplicationCountrySelectListByCountryId(int countryId)
        {
            if (UserSession is null)
                return Json(null);
            
            var apiRequest = new PaginatedBranchApplicationCountryApiRequest
            {
                CountryId = countryId,
                BranchId = null,
                Pagination = new PaginationApiRequest
                {
                    Page = 1,
                    PageSize = 500,
                    OrderBy = null,
                    SortDirection = new ListSortDirection()
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedBranchApplicationCountryApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedBranchApplicationCountry, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var branches = apiResponse.Data.Items.FirstOrDefault().BranchApplicationCountries.Where(p => UserSession.BranchIds.Any(q => q == p.BranchId)).ToList();

            return Json(branches.OrderBy(p => p.BranchName).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.BranchName }));
        }
        public async Task<IActionResult> GetAuthorizedBranchApplicationCountryNameSelectListByCountryId(int countryId)
        {
            if (UserSession is null)
                return Json(null);

            var apiRequest = new PaginatedBranchApplicationCountryApiRequest
            {
                CountryId = countryId,
                BranchId = null,
                Pagination = new PaginationApiRequest
                {
                    Page = 1,
                    PageSize = 500,
                    OrderBy = null,
                    SortDirection = new ListSortDirection()
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedBranchApplicationCountryApiResponse>>>
                    (apiRequest, ApiMethodName.Management.GetPaginatedBranchApplicationCountry, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var branches = apiResponse.Data.Items.FirstOrDefault().BranchApplicationCountries.Where(p => UserSession.BranchIds.Any(q => q == p.BranchId)).ToList();

            return Json(branches.OrderBy(p => p.BranchName).Select(x => new SelectListItem { Value = x.Id.ToString(), Text =  $"{x.BranchName} -> {x.CountryName}" }));
            
        }
        public async Task<IActionResult> GetSlotTypeByBranchApplicationCountry(int branchApplicationCountryId)
        {
            var apiResponse = await PortalHttpClientHelper
                           .GetAsync<ApiResponse<GetSlotTypeByBranchApplicationCountryApiResponse>>
                           (ApiMethodName.Parameter.GetSlotTypeByBranchApplicationCountry + branchApplicationCountryId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                           .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(new List<SelectListItem>());

            var enumDictionary = EnumHelper.GetEnumAsDictionary(typeof(Contracts.Entities.Enums.SlotType));

            var slotTypes = apiResponse.Data.SlotTypes
                .Select(x => new SelectListItem
                {
                    Value = x.Value.ToString(),
                    Text = enumDictionary.ContainsKey(x.Value) ? enumDictionary[x.Value].ToTitleCase() : x.Name
                })
                .ToList();

            return Json(slotTypes);
        }

        [HttpPost]
        public async Task<IActionResult> GetPaymentProviderTypeSelectList([FromForm] int[] channelIds)
        {
            if(channelIds.Length == 0)
                return Json(new List<SelectListItem>());
            
            var request = new PaymentProvidersByChannelApiRequest
            {
                ChannelIds = channelIds.ToList(),
                LanguageId = LanguageId
            };

            var apiResponse = await PortalHttpClientHelper
                           .PostAsJsonAsync<ApiResponse<PaymentProviderByChannelApiResponse>>
                           (request, ApiMethodName.Parameter.GetPaymentProvidersByChannel, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                           .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false) || apiResponse.Data.Providers.Count == 0)
                return Json(new List<SelectListItem>());

            var paymentProviders = apiResponse.Data.Providers
                .Select(x => new SelectListItem
                {
                    Value = x.Id.ToString(),
                    Text = x.Name
                })
                .ToList();

            return Json(paymentProviders);
        }

        public async Task<IActionResult> GetSlotTypeByBranch(int branchId)
        {
            var apiResponse = await PortalHttpClientHelper
                           .GetAsync<ApiResponse<GetSlotTypeByBranchApiResponse>>
                           (ApiMethodName.Parameter.GetSlotTypeByBranch + branchId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                           .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(new List<SelectListItem>());

            return Json(apiResponse.Data.SlotTypes.Select(x => new SelectListItem { Value = x.Value.ToString(), Text = $"{x.Name}" }));
        }

        public async Task<IActionResult> GetBranchSelectList()
        {
            var apiRequest = new BranchesApiRequest();

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<BranchesApiResponse>>
                (apiRequest, ApiMethodName.Parameter.GetBranches, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            return Json(apiResponse.Data.Branches.OrderBy(o => o.Name).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.Name }));
        }

        public async Task<IActionResult> GetAuthorizedBranchSelectList(int? countryId)
        {
            if (UserSession is null)
                return Json(null);

            var apiRequest = new BranchesApiRequest();
            apiRequest.CountryId = countryId;
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<BranchesApiResponse>>
                (apiRequest, ApiMethodName.Parameter.GetBranches, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var branches = apiResponse.Data.Branches.Where(p => UserSession.BranchIds.Any(q => q == p.Id)).ToList();

            return Json(branches.OrderBy(q => q.Name).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.Name }));
        }

        public async Task<IActionResult> GetAgencySelectList(int? countryId)
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<AgenciesApiResponse>>
                (ApiMethodName.Parameter.GetAgencies, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            if (countryId.HasValue)
                return Json(apiResponse.Data.Agencies.Where(p => p.CountryId == countryId.Value && p.ActivationStatusId == ActivationStatusType.Active.ToInt()).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = $"{x.Name} ({x.CityName})" }));

            return Json(apiResponse.Data.Agencies.Where(p => p.ActivationStatusId == ActivationStatusType.Active.ToInt()).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = $"{x.Name} ({x.CityName})" }));
        }

        public async Task<IActionResult> GetAgencyByBranchApplicationCountrySelectList(int branchApplicationCountryId)
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<AgenciesApiResponse>>
                (ApiMethodName.Parameter.GetAgenciesByBranchApplicationCountryId + branchApplicationCountryId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(new List<SelectListItem>());

            return Json(apiResponse.Data.Agencies.Where(p => p.ActivationStatusId == ActivationStatusType.Active.ToInt()).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = $"{x.Name} ({x.CityName})" }));
        }

        public async Task<IActionResult> GetCompanyActiveModuleSelectList()
        {
            if (UserSession is null)
                return Json(null);

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<CompanyApiResponse>>
                (ApiMethodName.Admin.GetCompany + UserSession.CompanyId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var activeModuleIdList = apiResponse.Data.CompanyModules.Where(p => p.IsActive).Select(p => p.ModuleId);

            return Json(EnumHelper.GetEnumAsDictionary(typeof(ModuleType)).Where(p => activeModuleIdList.Contains(p.Key)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value }));
        }

        public async Task<IActionResult> GetBranchWithCountrySelectList()
        {
            var apiRequest = new BranchesApiRequest();

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<BranchesApiResponse>>
                (apiRequest, ApiMethodName.Parameter.GetBranches, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            return Json(apiResponse.Data.Branches.Select(x => new SelectListItem { Value = x.Id.ToString(), Text = $"{x.Country.Name} - {x.Name}" }));
        }

        public async Task<IActionResult> GetExtraFeeSelectList()
        {
            var apiRequest = new ExtraFeesApiRequest();

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<ExtraFeesApiResponse>>
                (apiRequest, ApiMethodName.Parameter.GetExtraFees, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            return Json(apiResponse.Data.ExtraFees.Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.Name }));
        }

        public async Task<IActionResult> GetSmsProvidersSelectList()
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<SmsProvidersApiResponse>>
                (ApiMethodName.Parameter.GetSmsProviders, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            return Json(apiResponse.Data.SmsProvider.Select(x => new SelectListItem { Value = x.Id.ToEncrypt(), Text = x.Name }));
        }

        public async Task<IActionResult> GetEmailProvidersSelectList()
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<EmailProvidersApiResponse>>
                (ApiMethodName.Parameter.GetEmailProviders, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            return Json(apiResponse.Data.EmailProvider.Select(x => new SelectListItem { Value = x.Id.ToEncrypt(), Text = x.Name }));
        }

        public async Task<IActionResult> GetBranchByBranchApplicationCountryId(int branchApplicationCountryId)
        {
            if (UserSession is null)
                return Json(null);

            if (!branchApplicationCountryId.IsNumericAndGreaterThenZero())
                return Json(null);

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<BranchApiResponse>>
                (ApiMethodName.Parameter.GetBranchByBranchApplicationCountryId + branchApplicationCountryId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            if (UserSession.RoleIds.Any(p => p == RoleType.Supervisor.ToInt()
                                             || p == RoleType.CallCenterLead.ToInt()
                                             || p == RoleType.CallCenterManager.ToInt()))
                apiResponse.Data.MaxAppointmentDay = (int?)null;

            return Json(new { CallingCode = apiResponse.Data.CallingCode, Id = apiResponse.Data.Id, MaxAppointmentDay = apiResponse.Data.MaxAppointmentDay });
        }

        public async Task<IActionResult> GetSlots(DateTime date, int branchApplicationCountryId, int slotTypeId, int? agencyId, int? applicantCount, int? slotId)
        {
            var apiRequest = new GetSlotApiRequest()
            {
                Date = date,
                BranchApplicationCountryId = branchApplicationCountryId,
                AgencyId = (SlotType)slotTypeId == SlotType.Agency ? agencyId : null,
                ApplicantCount = applicantCount,
                SlotTypeId = slotTypeId,
                SlotId = slotId
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<SlotsApiResponse>>
                (apiRequest, ApiMethodName.Management.GetSlots, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            if (!apiResponse.Data.Slots.Any())
                return Json(new { SlotTime = apiResponse.Data.FirstAvailableSlot });
            else
                return Json(apiResponse.Data.Slots.OrderBy(x => x.SlotTime).Select(x => new { Id = x.Id, SlotTime = x.SlotTime, SlotTimeText = x.SlotTimeText, Quota = x.Quota, FirstAvaibleDateTime = apiResponse.Data.FirstAvailableSlot }));
        }

        public async Task<IActionResult> GetAgencyTypesSelectList()
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<AgencyTypesApiResponse>>
                (ApiMethodName.Parameter.GetAgencyTypes, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            return Json(apiResponse.Data.AgencyTypes.Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.Name }));
        }

        public async Task<IActionResult> GetApplicationBrandModelsDataSelectList()
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationBrandModelsApiResponse>>
                (ApiMethodName.Parameter.GetApplicationBrandModelsData, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            return Json(apiResponse.Data.ApplicationBrandModels.OrderBy(p => p.Brand).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = $"{x.Brand}" + " " + $"{x.Model}" }));
        }



        public IActionResult GetLocalAuthorityStatusSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(LocalAuthorityStatus)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetWaitingTimeForDocumentSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(WaitingTimeForDocumentStatus)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetActionStatusSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(TokenState)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetPaymentChannelTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(PaymentChannelType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetOldVisaDecisionSelectList()
		{
			return Json(EnumHelper.GetEnumAsDictionary(typeof(OldVisaDecisionType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
		}

        public IActionResult GetSlotTypeSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(SlotType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetMinorApplicantParentSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(MinorApplicantParentType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.Replace('_', ' ').ToTitleCase() }));
        }

        public IActionResult GetYesNoQuestionSelectList(bool hasUnspecified)
        {
            if(hasUnspecified)
                return Json(EnumHelper.GetEnumAsDictionary(typeof(YesNoQuestion)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
            else
                return Json(EnumHelper.GetEnumAsDictionary(typeof(YesNoQuestion)).Where(w => w.Key < 3).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetBasicGuidelineTypeList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(BasicGuidelineType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        #region PerformanceManagementSystem
        public IActionResult GetPMSDataGroup()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(PMSDataGroup)).Where(w => w.Key == (int)PMSDataGroup.Operation || w.Key == (int)PMSDataGroup.Vas || w.Key == (int)PMSDataGroup.Mistakes || w.Key == (int)PMSDataGroup.Warnings || w.Key == (int)PMSDataGroup.ThanksComplaints || w.Key == (int)PMSDataGroup.ManagerEvaluation).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public IActionResult GetPMSDataType(int dataGroupId)
        {
            if (dataGroupId == (int)PMSDataGroup.Operation)
                return Json(EnumHelper.GetEnumAsDictionary(typeof(PMSOperationDataType)).Where(w => w.Key == (int)PMSOperationDataType.Redata || w.Key == (int)PMSOperationDataType.Multitask || w.Key == (int)PMSOperationDataType.Check || w.Key == (int)PMSOperationDataType.Cashier).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
            else if (dataGroupId == (int)PMSDataGroup.Vas)
                return Json(EnumHelper.GetEnumAsDictionary(typeof(PMSVASDataType)).Where(w => w.Key == (int)PMSVASDataType.VIP).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
            else if (dataGroupId == (int)PMSDataGroup.Mistakes)
                return Json(EnumHelper.GetEnumAsDictionary(typeof(PMSMistakesDataType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
            else if (dataGroupId == (int)PMSDataGroup.Warnings)
                return Json(EnumHelper.GetEnumAsDictionary(typeof(PMSWarningsDataType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
            else if (dataGroupId == (int)PMSDataGroup.ThanksComplaints)
                return Json(EnumHelper.GetEnumAsDictionary(typeof(PMSThanksComplaintsDataType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
            else if (dataGroupId == (int)PMSDataGroup.ManagerEvaluation)
                return Json(EnumHelper.GetEnumAsDictionary(typeof(PMSManagerEvaluationDataType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
            else
                return Json(new List<SelectListItem>());
        }

        public async Task<IActionResult> GetBranchSelectListByCountry(int countryId)
        {
            if (UserSession is null)
                return Json(null);

            if (countryId == 0)
                return Json(new List<SelectListItem>());

            var apiResponse = await PortalHttpClientHelper
                           .GetAsync<ApiResponse<BranchesApiResponse>>
                           ($"{ApiMethodName.Parameter.GetBranchSelectListByCountry + countryId}/{UserSession.UserId}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)                          
                           .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(new List<SelectListItem>());

            var resultData = apiResponse.Data.Branches.OrderBy(q => q.Name).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.Name });
            return Json(resultData);
        }

        public async Task<IActionResult> GetCountriesSelectList()
        {
            if (UserSession is null)
                return Json(null);

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<CountriesApiResponse>>
                    (ApiMethodName.Parameter.GetCountriesSelectList + UserSession.UserId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var resultData = apiResponse.Data.Countries.OrderBy(q => q.Name).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.Name });

            return Json(resultData);
        }
        #endregion

        #region Notification

        public IActionResult GetNotificationStatusSelectList()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(NotificationStatusType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.Replace('_', ' ').ToTitleCase() }));
        }

        #endregion

        #region Report

        public async Task<IActionResult> GetReportBranchWithCountrySelectList(int reportType)
        {
            if (UserSession is null)
                return Json(null);

            var apiRequest = new BranchesApiRequest();

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<BranchesApiResponse>>
                (apiRequest, ApiMethodName.Parameter.GetBranches, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var branches = apiResponse.Data.Branches;
            if (reportType == (int)ReportType.IndiaAllApplications)
                branches = apiResponse.Data.Branches.Where(w => w.CountryId == 77 && UserSession.BranchIds.Contains(w.Id)).ToList(); //IND
            else if (UserSession.BranchId.HasValue)
                branches = branches.Where(p => UserSession.BranchIds.Any(q => q == p.Id)).ToList();

            try
            {
                return Json(branches.OrderBy(q => q.Country.Name).ThenBy(q => q.Name).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = $"{x.Country.Name} - {x.Name}" }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in GetReportBranchWithCountrySelectList. branches: {branches == null}");
                if (branches != null)
                {
                    _logger.LogError(ex, "Error in GetReportBranchWithCountrySelectList. " + JsonConvert.SerializeObject(branches));
                }
                throw;
            }
        }

        public async Task<IActionResult> GetReportUserSelectList()
        {
            if (UserSession is null)
                return Json(null);

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UsersApiResponse>>
                (new EmptyApiRequest(), ApiMethodName.Parameter.GetUsers, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var users = apiResponse.Data.Users;

            if (UserSession.BranchId.HasValue)
                users = users.Where(p => p.BranchIds != null && p.BranchIds.Contains(UserSession.BranchId.Value)).ToList();

            return Json(users.OrderBy(q => q.Name).ThenBy(q => q.Surname).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = $"{x.Name} {x.Surname}" }));
        }

        public async Task<IActionResult> GetReportBranchApplicationCountrySelectList()
        {
            if (UserSession is null)
                return Json(null);

            var apiRequest = new BranchApplicationCountriesApiRequest();

            if (UserSession.BranchId.HasValue)
                apiRequest.BranchId = UserSession.BranchId.Value;

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<BranchApplicationCountriesApiResponse>>
                (apiRequest, ApiMethodName.Appointment.GetBranchApplicationCountries, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            return Json(apiResponse.Data.BranchApplicationCountries.OrderBy(q => q.CountryName).ThenBy(q => q.CountryName).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = $"{x.CountryName} - {x.BranchName}" }));
        }
        public async Task<IActionResult> GetAnnouncementPersonSelectList(List<int> branchIds)
        {

            var apiRequest = new AnnouncementPersonSelectApiRequest()
            {
                BranchIds = branchIds
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UsersApiResponse>>
               (apiRequest, ApiMethodName.Parameter.GetAnnouncementPersons, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var users = apiResponse.Data.Users;

            return Json(users.OrderBy(q => q.Name).ThenBy(q => q.Surname).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = $"{x.Name} {x.Surname}" }));
        }
        public async Task<IActionResult> GetCountriesHasBranchSelectList()
        {
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<CountriesApiResponse>>
                    (new EmptyApiRequest(), ApiMethodName.Parameter.GetCountriesHasBranchSelectList, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            return Json(apiResponse.Data.Countries.OrderBy(q => q.Id).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.Name }));
        }

        public async Task<IActionResult> GetPermissionCountriesHasBranchSelectList()
        {
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<CountriesApiResponse>>
                    (new EmptyApiRequest(), ApiMethodName.Parameter.GetCountriesHasBranchSelectList, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var currentUser = SessionExtensions.Get<UserModel>(HttpContext.Session, SessionKeys.UserSession);
            var userBranches = await _currentUserDataHelper.BuildUserBranches(currentUser);

            var resultData = apiResponse.Data.Countries
                .Where(w => userBranches.Select(s => s.CountryId).Contains(w.Id))
                .OrderBy(q => q.Id).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.Name });

            return Json(resultData);
        }

        #endregion

        #region QMS

        public async Task<IActionResult> GetLinesByBranchSelectList(int? branchId, bool isForSelectedList)
        {
            if (UserSession is null)
                return Json(null);

            if (!isForSelectedList) branchId ??= UserSession.BranchId;

            branchId ??= 0;

            var apiResponse = await RestHttpClient.Create().Get<GetLinesByBranchResult>(
                $"{AppSettings.Qms.BaseApiUrl}/api/branches/{branchId}/lines", QMSApiDefaultRequestHeaders);

            if (apiResponse.Data is null)
                return Json(new List<SelectListItem>());

            return Json(apiResponse.Data.OrderBy(p => p.Name).Select(x => new SelectListItem
            {
                Value = x.Id.ToString(),
                Text = $"{x.Name}"
            }));
        }

        public async Task<IActionResult> GetDevicesByBranchSelectList()
        {
            if (UserSession is null)
                return Json(null);

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<GetDevicesByBranchApiResponse>>
                    (null, ApiMethodName.DigitalSignature.GetDevicesByBranchSelectList + UserSession.BranchId, AppSettings.PrintData.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (apiResponse.Data == null)
                return Json(new List<SelectListItem>());

            return Json(apiResponse.Data.Devices.OrderBy(p => p.Name).Select(x => new SelectListItem
            {
                Value = x.Id.ToEncrypt().ToString(),
                Text = $"{x.Name}"
            }));
        }

        public async Task<IActionResult> GetCountersByBranchSelectList(int lineId)
        {
            if (UserSession is null)
                return Json(null);

            var request = new GetCountersByBranchRequest
            {
                PageSize = 10000,
                OrderBy = "Id"
            };

            if (lineId == 0)
                lineId = UserSession.QmsDropdownsModel.LineId ?? 0;

            var department = 0;
            var apiResponse = await RestHttpClient.Create().Post<GetCountersByBranchResult>($"{AppSettings.Qms.BaseApiUrl}/api/branches/{UserSession.BranchId.GetValueOrDefault()}/lines/{lineId}/departments/{department}/counters", QMSApiDefaultRequestHeaders, request);

            if (apiResponse.Data == null)
                return Json(new List<SelectListItem>());

            return Json(apiResponse.Data.OrderBy(p => p.Name).Select(x => new SelectListItem
            {
                Value = x.Id.ToString(),
                Text = $"{x.Name}"
            }));
        }

        public async Task<IActionResult> GetScreenCountersByBranchSelectList(UpdateScreenViewModel viewModel)
        {
            var request = new GetCountersByBranchRequest
            {
                PageSize = 10000,
                OrderBy = "Id"
            };

            var apiResponse = await RestHttpClient.Create().Post<GetCountersByBranchResult>($"{AppSettings.Qms.BaseApiUrl}/api/branches/{viewModel?.BranchId}/lines/{viewModel?.LineId}/departments/{viewModel?.DepartmentId}/counters", QMSApiDefaultRequestHeaders, request);

            if (apiResponse.Data == null)
                return Json(new List<SelectListItem>());

            return Json(apiResponse.Data.OrderBy(p => p.Name).Select(x => new SelectListItem
            {
                Value = x.Id.ToString(),
                Text = $"{x.Name}"
            }));
        }

        public async Task<IActionResult> GetDepartments(UpdateScreenViewModel viewModel)
        {
            var branchId = viewModel.BranchId > 0 ? viewModel.BranchId : 0;
            var lineId = viewModel.LineId > 0 ? viewModel.LineId : 0;

            var apiResponse = await RestHttpClient.Create().Get<GetDepartmentsResult>($"{AppSettings.Qms.BaseApiUrl}/api/branches/{branchId}/lines/{lineId}/department", QMSApiDefaultRequestHeaders);

            if (apiResponse.Data is null)
                return Json(new List<SelectListItem>());

            return Json(apiResponse.Data.OrderBy(p => p.Name).Select(x => new SelectListItem
            {
                Value = x.Id.ToString(),
                Text = $"{x.Name}"
            }));
        }

        public IActionResult GetDepartmentStatus()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(DepartmentStatus)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
        }

        public async Task<IActionResult> GetAssigneeSelectList()
        {
            if (UserSession is null)
                return Json(null);

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UsersApiResponse>>
                    (new EmptyApiRequest(), ApiMethodName.Parameter.GetUsers, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var users = apiResponse.Data.Users;


            if (UserSession.BranchId.HasValue)
                users = users.Where(p => p.BranchIds != null && p.BranchIds.Contains(UserSession.BranchId.Value)).ToList();

            if (UserSession.UserId.IsNumericAndGreaterThenZero())
                users = users.Where(p => p.Id != UserSession.UserId).ToList();

            return Json(users.OrderBy(q => q.Name).ThenBy(q => q.Surname).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = $"{x.Name} {x.Surname}" }));
        }

        public async Task<IActionResult> GetAssignerSelectList()
        {
            if (UserSession is null)
                return Json(null);

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UsersApiResponse>>
                    (new EmptyApiRequest(), ApiMethodName.Parameter.GetUsers, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var users = apiResponse.Data.Users;

            var selectedUsers = users.Where(p => p.BranchIds != null && p.Id == UserSession.UserId).ToList();

            return Json(selectedUsers.OrderBy(q => q.Name).ThenBy(q => q.Surname).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = $"{x.Name} {x.Surname}" }));
        }

        public async Task<IActionResult> GetUsersByParametersSelectList(int? branchId, int? roleId)
        {
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<UsersApiResponse>>
                    (new EmptyApiRequest(), ApiMethodName.Parameter.GetUsers, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var users = apiResponse.Data.Users;

            if (branchId.HasValue)
                users = users.Where(p => p.BranchIds != null && p.BranchIds.Contains(branchId.Value)).ToList();

            if (roleId.HasValue)
                users = users.Where(p => p.UserRoleIds != null && p.UserRoleIds.Contains(roleId.Value)).ToList();

            return Json(users.OrderBy(q => q.Name).ThenBy(q => q.Surname).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = $"{x.Name} {x.Surname}" }));
        }

        public async Task<IActionResult> GetUsersByBranch(int? branchId)
        {
            if (UserSession is null)
                return Json(null);

            if (branchId == null)
                return Json(new List<SelectListItem>());

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<UsersApiResponse>>
                    ($"{ApiMethodName.Parameter.GetUsersByBranch + branchId}/{UserSession.UserId}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            return Json(apiResponse.Data.Users.OrderBy(q => q.Name).ThenBy(q => q.Surname).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = $"{x.Name} {x.Surname}" }));
        }

        public async Task<IActionResult> GetNotCompletedReasonSelectList()
        {
            if (UserSession is null)
                return Json(null);

            var apiResponse = await RestHttpClient.Create().Get<GenerateExternalApiResponse<GetNotCompletedReasonsResponse>>($"{AppSettings.Qms.BaseApiUrl}{QmsReportEndPoint.GetNotCompletedReasons}", QMSApiDefaultRequestHeaders);

            if (apiResponse.Data?.NotCompletedReasons == null)
                return Json(apiResponse.Message);

            List<NotCompletedReasonResponse> reasons;

            switch (UserSession.BranchCountryId.GetValueOrDefault())
            {
                case 93: //Kuwait branches
                    reasons = apiResponse.Data.NotCompletedReasons.Where(p => p.CountryId == 93 || p.OtherReason).ToList();
                    break;
                default:
                    reasons = apiResponse.Data.NotCompletedReasons.Where(p => p.CountryId != 93 || p.OtherReason).ToList();
                    break;
            }

            return Json(reasons.Select(x => new SelectListItem 
            { 
                Value = x.Id.ToString(), 
                Text = x.ReasonTranslations?.Find(p => p.LanguageId == LanguageId)?.Name?.ToTitleCase()
            }));
        }

        #endregion

        #region Biometrics

        public async Task<IActionResult> GetOfficeSelectList()
        {
            var apiRequest = new OfficesSelectListApiRequest();

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<OfficesSelectListApiResponse>>
                    (apiRequest, BiometricsEndPoint.ParameterGetOfficesSelectList, AppSettings.Biometrics.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            return Json(apiResponse.Data.Offices.OrderBy(o => o.Name).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.Name }));
        }

        public async Task<IActionResult> GetCabinsSelectList()
        {
            var apiRequest = new CabinsSelectListApiRequest();

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<CabinsSelectListApiResponse>>
                    (apiRequest, BiometricsEndPoint.ParameterGetCabinsSelectList, AppSettings.Biometrics.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            return Json(apiResponse.Data.Cabins.OrderBy(o => o.Name).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.Name }));
        }


        public async Task<IActionResult> GetBiometricsInventoryTypeSelectList()
        {
            var apiRequest = new InventoryTypesSelectListApiRequest();

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<InventoryTypesSelectListApiResponse>>
                    (apiRequest, BiometricsEndPoint.ParameterGetInventoryTypesSelectList, AppSettings.Biometrics.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            return Json(apiResponse.Data.InventoryTypes.OrderBy(o => o.Name).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.Name }));
        }

        public async Task<IActionResult> GetInventoryDefinitionSelectList()
        {
            var apiRequest = new InventoryDefinitionsSelectListApiRequest();

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<InventoryDefinitionsSelectListApiResponse>>
                    (apiRequest, BiometricsEndPoint.ParameterGetInventoryDefinitionsSelectList, AppSettings.Biometrics.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            return Json(apiResponse.Data.InventoryDefinitions.OrderBy(o => o.Id).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = $"{x.InventoryDefinition.InventoryType.Name}: {x.InventoryDefinition.Label}" }));
        }

        public async Task<IActionResult> GetNeurotecLicensesSelectList(int? ClientConfigurationId)
        {
            var apiRequest = new NeurotecLicensesSelectListApiRequest() { ClientConfigurationId = ClientConfigurationId};

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<NeurotecLicensesSelectListApiResponse>>
                    (apiRequest, BiometricsEndPoint.ParameterGetNeurotecLicensesSelectList, AppSettings.Biometrics.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            return Json(apiResponse.Data.Licenses.OrderBy(o => o.Id).Select(x => new SelectListItem { Value = x.LicenseNumber, Text = x.LicenseNumber }));
        }

        #endregion

        #region Cargo

        public async Task<IActionResult> GetGovernorateSelectList()
        {
            var apiResponse = await RestHttpClient.Create().Get<GovernorateResult>($"{AppSettings.Cargo.BaseApiUrl}{CargoEndpoint.GetGovernorate}", QMSApiDefaultRequestHeaders);

            if (apiResponse.Data == null)
                return Json(new List<SelectListItem>());

            return Json(apiResponse.Data.OrderBy(p => p.Title).Select(x => new SelectListItem
            {
                Value = x.Id.ToString(),
                Text = x.Title
            }));
        }

        public async Task<IActionResult> GetAreaSelectList(string governorateId, string text)
        {
            var apiResponse = await RestHttpClient.Create().Get<AreaResult>($"{AppSettings.Cargo.BaseApiUrl}{CargoEndpoint.GetArea}{governorateId}", QMSApiDefaultRequestHeaders);

            if (apiResponse.Data == null)
                return Json(new List<SelectListItem>());

            if(!text.IsNullOrWhitespace())
                apiResponse.Data = apiResponse.Data.Where(r => r.Title.ToLower().Contains(text)).ToList();

            return Json(apiResponse.Data.OrderBy(p => p.Title).Select(x => new SelectListItem
            {
                Value = x.Id.ToString(),
                Text = x.Title
            }));
        }

        #endregion

        #region Private Methods

        private bool CheckUserHasIndiaBranch()
        {
            return UserSession.CountryIds.Any(s => s == 77);
        }

        #endregion

    }
}
