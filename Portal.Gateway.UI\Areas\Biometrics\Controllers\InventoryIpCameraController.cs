﻿using Gateway.Extensions;
using Gateway.Http;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.QmsFolder;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Biometrics.Models.Inventory.ViewModels;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using System;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Biometrics.Controllers
{

    [Area("Biometrics")]
    public class InventoryIpCameraController : BaseController<InventoryIpCameraController>
    {
        public InventoryIpCameraController(
            IOptions<AppSettings> appSettings, 
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper) { }

        #region Add

        public IActionResult PartialAddInventoryIpCamera()
        {
            var viewModel = new InventoryIpCameraViewModel();

            return PartialView("_FormInventoryIpCamera", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddInventoryIpCamera(InventoryIpCameraViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");                       

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateInventoryIpCamera(InventoryIpCameraViewModel viewModel)
        {
            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");                        

            return PartialView("_FormInventoryIpCamera", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateInventoryIpCamera(InventoryIpCameraViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid) //buraya fluent val yazılıcak viewmodellere
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");
                        
            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion
        
               

    }

}
