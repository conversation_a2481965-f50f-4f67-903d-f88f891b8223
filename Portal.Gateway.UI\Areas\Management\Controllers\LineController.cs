﻿using Gateway.Extensions;
using Gateway.Http;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.BranchDepartment;
using Portal.Gateway.UI.Areas.Management.ViewModels.Line.Requests;
using Portal.Gateway.UI.Areas.Management.ViewModels.Line.Results;
using Portal.Gateway.UI.Areas.Management.ViewModels.Line.ViewModels;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class LineController : BaseController<LineController>
    {
        public LineController(IOptions<AppSettings> appSettings, ICacheHelper cacheHelper) : base(appSettings, cacheHelper) { }

        #region Add

        public async Task<IActionResult> PartialAddLine(string encryptedBranchId)
        {
            var departmentList = await CacheHelper.GetDepartmentsAsync();

            ViewData["availableDepartments"] = departmentList.Departments.Select(q => new
            {
                Id = q.Id.ToString(),
                Value = q.NameTranslations.Any(p => p.LanguageId == LanguageId) ?
                    q.NameTranslations.FirstOrDefault(p => p.LanguageId == LanguageId)?.Name :
                    q.NameTranslations.FirstOrDefault()?.Name
            }).AsEnumerable();

            var viewModel = new AddUpdateLineViewModel
            {
                EncryptedBranchId = encryptedBranchId,
                EncryptedLineId = string.Empty,
                DepartmentList = departmentList.Departments.Select(q => new AddUpdateLineViewModel.DepartmentViewModel
                {
                    Name = q.NameTranslations.Any(p => p.LanguageId == LanguageId) ?
                        q.NameTranslations.FirstOrDefault(p => p.LanguageId == LanguageId)?.Name :
                        q.NameTranslations.FirstOrDefault()?.Name,
                    Id = q.Id
                }).ToList()
            };

            return PartialView("_AddLine", viewModel);
        }     

        [HttpPost]
        public async Task<IActionResult> AddLine(AddUpdateLineViewModel viewModel)
        {
            if (viewModel is null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            if (!viewModel.DepartmentList.Exists(p => p.IsSelected)) 
                return Json(new { Message = SiteResources.AtLeastOneDepartmentIsSelected, ResultType = ResultType.Warning });

            var list = viewModel.DepartmentList
                    .Where(p => p.IsSelected)
                .Select(p => p.Id).AsEnumerable()
                    .GroupBy(p => p)
                    .Select(p => new { p.Key, count = p.Count() })
                    .Where(p => p.count > 1)
                    .Select(p => p.Key);

            if (list.Any()) 
                return Json(new { Message = SiteResources.ADepartmentShouldNotBeSelectedMoreThanOne, ResultType = ResultType.Warning });

            if (viewModel.DepartmentList.Exists(p => p.IsSelected && p.Interval == 0))
                return Json(new { Message = SiteResources.TheIntervalShouldNotBeSelectedZero, ResultType = ResultType.Warning });

            var i = 1;

            var apiRequest = new AddUpdateLineRequestModel
            {
                BranchId = viewModel.EncryptedBranchId.ToDecryptInt(),
                Name = viewModel.Name,
                SearchByPassportNumber = viewModel.SearchByPassportNumber,
                Vip = viewModel.Vip,
                LineDepartments = viewModel.DepartmentList.Where(p => p.IsSelected).Select(q => new LineDepartmentApiRequest
                {
                    DepartmentId = q.Id,
                    Order = i++,
                    IsLineDepartmentCreateApplication = q.IsLineDepartmentCreateApplication,
                    IsProcessSameCounter = q.IsProcessSameCounter,
                    Interval = q.Interval
                }).ToList()
            };

            var apiResponse = await RestHttpClient.Create()
                .Post<AddUpdateLineResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.AddLine,
                    QMSApiDefaultRequestHeaders, apiRequest);

            return apiResponse?.Message == SiteResources.RESOURCE_CREATED 
                ? Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success }) 
                : Json(new ResultModel { Message = apiResponse?.Message, ResultType = ResultType.Danger });
        }       

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateLine(string encryptedLineId, string encryptedBranchId)
        {
            var lineId = encryptedLineId.ToDecryptInt();

            var departmentList = await CacheHelper.GetDepartmentsAsync();

            var apiResponse = await RestHttpClient.Create()
                .Get<GetLineResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.Lines + lineId,
                    QMSApiDefaultRequestHeaders);

            if (apiResponse.Data is null || apiResponse.Status != SiteResources.SUCCESS) 
                return Json(new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Warning });

            ViewData["availableDepartments"] = departmentList.Departments.Select(q => new
            {
                Id = q.Id.ToString(),
                Value = q.NameTranslations.Any(p => p.LanguageId == LanguageId) ?
                    q.NameTranslations.FirstOrDefault(p => p.LanguageId == LanguageId)?.Name :
                    q.NameTranslations.FirstOrDefault()?.Name
            }).AsEnumerable();

            var viewModel = new AddUpdateLineViewModel
            {
                Name = apiResponse.Data.Name,
                SearchByPassportNumber = apiResponse.Data.SearchByPassportNumber,
                Vip = apiResponse.Data.Vip,
                EncryptedBranchId = encryptedBranchId,
                EncryptedLineId = encryptedLineId,
                DepartmentList = apiResponse.Data.LineDepartments.Select(q => new AddUpdateLineViewModel.DepartmentViewModel
                {
                    Id = q.Id,
                    DepartmentId = q.DepartmentId,
                    Order = q.Order,
                    Name = q.DepartmentName,
                    IsSelected = true,
                    IsProcessSameCounter = q.IsProcessSameCounter,
                    IsLineDepartmentCreateApplication = q.IsLineDepartmentCreateApplication,
                    Interval = q.Interval
                }).OrderBy(p => p.Order).ToList()
            };

            return PartialView("_UpdateLine", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateLine(AddUpdateLineViewModel viewModel)
        {
            var lineId = viewModel?.EncryptedLineId.ToDecrypt().ToInt();

            if (viewModel is null || !ModelState.IsValid || lineId == 0)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            if (!viewModel.DepartmentList.Any(p => p.IsSelected)) return Json(new { Message = SiteResources.AtLeastOneDepartmentIsSelected, ResultType = ResultType.Warning });

            var list = viewModel.DepartmentList.Where(p => p.IsSelected).Select(p => p.Id).AsEnumerable()
                    .GroupBy(p => p)
                    .Select(p => new { p.Key, count = p.Count() })
                    .Where(p => p.count > 1)
                    .Select(p => p.Key);

            if (list.Any()) 
                return Json(new { Message = SiteResources.ADepartmentShouldNotBeSelectedMoreThanOne, ResultType = ResultType.Warning });

            if (viewModel.DepartmentList.Any(p => p.IsSelected && p.Interval == 0))
                return Json(new { Message = SiteResources.TheIntervalShouldNotBeSelectedZero, ResultType = ResultType.Warning });

            var i = 1;

            var apiRequest = new AddUpdateLineRequestModel
            {
                BranchId = viewModel.EncryptedBranchId.ToDecryptInt(),
                Name = viewModel.Name,
                SearchByPassportNumber = viewModel.SearchByPassportNumber,
                Vip = viewModel.Vip,
                LineDepartments = viewModel.DepartmentList.Where(p => p.IsSelected).Select(q => new LineDepartmentApiRequest
                {
                    IsSelected = q.IsSelected,
                    DepartmentId = q.DepartmentId,
                    Order = i++,
                    IsLineDepartmentCreateApplication = q.IsLineDepartmentCreateApplication,
                    IsProcessSameCounter = q.IsProcessSameCounter,
                    Interval = q.Interval
                }).ToList()
            };

            var apiResponse = await RestHttpClient.Create()
                .Put<AddUpdateLineResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.Lines + lineId,
                    QMSApiDefaultRequestHeaders, apiRequest);

            return apiResponse?.Message == SiteResources.RESOURCE_UPDATED 
                ? Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success }) 
                : Json(new ResultModel { Message = apiResponse?.Message, ResultType = ResultType.Danger });
        }

        public async Task<IActionResult> PartialTokenNumberProcess(string encryptedBranchId)
        {
            var apiResponse = await RestHttpClient.Create().Get<GetTokenNumberProcessByBranchResult>(AppSettings.Qms.BaseApiUrl + "/api/branches/" + encryptedBranchId.ToDecryptInt() + "/tokennumberprocess",
                QMSApiDefaultRequestHeaders);

            if (apiResponse?.Data == null)
                return Json(new { Message = SiteResources.TheIntervalShouldNotBeSelectedZero, ResultType = ResultType.Danger });

            var viewModel = new AddUpdateBranchTokenNumberProcess
            {
                EncryptedBranchId = encryptedBranchId,
                WhitelistTokenNumberProcess = new WhitelistTokenNumberProcess
                {
                    StartWith = apiResponse.Data.WhitelistTokenNumberProcess.StartWith,
                    EndWith = apiResponse.Data.WhitelistTokenNumberProcess.EndWith
                },
                PriorityTokenNumberProcess = new PriorityTokenNumberProcess
                {
                    StartWith = apiResponse.Data.PriorityTokenNumberProcess.StartWith,
                    EndWith = apiResponse.Data.PriorityTokenNumberProcess.EndWith
                },
                LineTokenNumberProcess = apiResponse.Data.LineTokenNumberProcess.Select(p => new AddUpdateLineTokenNumberProcess
                {
                    EncryptedLineId = p.EncryptedLineId,
                    LineName = p.LineName,
                    StartWith = p.StartWith,
                    EndWith = p.EndWith
                }).ToList()
            };

            return PartialView("_TokenNumberProcess", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateTokenNumberProcess(AddUpdateBranchTokenNumberProcess viewModel)
        {
            var branchId = viewModel?.EncryptedBranchId.ToDecrypt().ToInt();

            if (viewModel == null || !ModelState.IsValid || branchId == 0)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            if (viewModel.LineTokenNumberProcess.Exists(p => p.EndWith == null || p.StartWith == null || p.EndWith == 0 || p.StartWith == 0   || p.StartWith < 100 || p.EndWith < 100) 
                || viewModel.PriorityTokenNumberProcess.StartWith == null || viewModel.PriorityTokenNumberProcess.EndWith == null
                || viewModel.PriorityTokenNumberProcess.StartWith == 0 || viewModel.PriorityTokenNumberProcess.EndWith == 0
                || viewModel.PriorityTokenNumberProcess.StartWith < 100 || viewModel.PriorityTokenNumberProcess.EndWith < 100
                || viewModel.WhitelistTokenNumberProcess.StartWith == null || viewModel.WhitelistTokenNumberProcess.EndWith == null
                || viewModel.WhitelistTokenNumberProcess.StartWith == 0 || viewModel.WhitelistTokenNumberProcess.EndWith == 0
                || viewModel.WhitelistTokenNumberProcess.StartWith < 100 || viewModel.WhitelistTokenNumberProcess.EndWith < 100)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateTokenNumberProcessApiRequest
            {
                BranchId = branchId.GetValueOrDefault(),
                LineTokenNumberProcess = viewModel.LineTokenNumberProcess.Select(p => new AddUpdateLineTokenNumberProcessApiRequest
                {
                    LineId = p.EncryptedLineId.ToDecrypt().ToInt(),
                    StartWith = p.StartWith,
                    EndWith = p.EndWith
                }).ToList(),
                WhitelistTokenNumberProcess = new WhitelistTokenNumberProcessApiRequest
                {
                    StartWith = viewModel.WhitelistTokenNumberProcess.StartWith,
                    EndWith = viewModel.WhitelistTokenNumberProcess.EndWith
                },
                PriorityTokenNumberProcess = new PriorityTokenNumberProcessApiRequest
                {
                    StartWith = viewModel.PriorityTokenNumberProcess.StartWith,
                    EndWith = viewModel.PriorityTokenNumberProcess.EndWith
                }
            };

            var apiResponse = await RestHttpClient.Create()
                .Put<AddUpdateLineResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.TokenNumberProcess + branchId,
                    QMSApiDefaultRequestHeaders, apiRequest);

            return apiResponse?.Message == SiteResources.RESOURCE_UPDATED
                ? Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success })
                : Json(new ResultModel { Message = apiResponse?.ValidationMessages?.FirstOrDefault() ?? SiteResources.ErrorOccurred, ResultType = ResultType.Danger });
        }

        #endregion

        #region Get

        public async Task<IActionResult> List(string encryptedBranchId)
        {
            if (string.IsNullOrEmpty(encryptedBranchId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("List", "Line", new { Area = "Management" });
            }

            var branchId = encryptedBranchId.ToDecryptInt();

            var branchList = await CacheHelper.GetBranchesAsync();

            var lineViewModel = new LineViewModel
            {
                EncryptedBranchId = encryptedBranchId,
                BranchName = branchList?.Branches?.FirstOrDefault(q => q.Id == branchId)?.BranchTranslations?.FirstOrDefault(q => q.LanguageId == LanguageId)?.Name
            };

            ViewData["Line_List"] = lineViewModel;

            return View();
        }

        public async Task<IActionResult> GetLineDepartments([DataSourceRequest] DataSourceRequest request, FilterBranchDepartmentViewModel filterViewModel)
        {
            var branchId = filterViewModel.EncryptedBranchId.ToDecryptInt();

            var apiResponse = await RestHttpClient.Create().Get<GetLinesByBranchResult>(AppSettings.Qms.BaseApiUrl + "/api/branches/" + branchId + "/lines", QMSApiDefaultRequestHeaders);

            var data = new List<LineDepartmentViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Any())
                data = apiResponse.Data.Select(p => new LineDepartmentViewModel
                {
                    EncryptedLineId = p.Id.ToEncrypt(),
                    Name = p.Name,
                    ProcessOrder = string.Join(" -> ", p.LineDepartments.OrderBy(q => q.Order).Select(q => q.DepartmentName))
                }).ToList();

            return Json(await data.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> PartialLine(string encryptedLineId)
        {
            if (encryptedLineId is null)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var lineId = encryptedLineId.ToDecryptInt();

            var apiResponse = await RestHttpClient.Create()
                .Get<GetLineResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.Lines + lineId,
                    QMSApiDefaultRequestHeaders);

            if (apiResponse.Data is null || apiResponse.Status != SiteResources.SUCCESS) 
                return Json(new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Warning });

            var viewModel = new LineDetailViewModel
            {
                LineName = apiResponse.Data.Name,
                SearchByPassportNumber = apiResponse.Data.SearchByPassportNumber,
                Vip = apiResponse.Data.Vip,
                LineDepartments = apiResponse.Data.LineDepartments.Select(q => new LineDepartmentDetailViewModel
                {
                    DepartmentId = q.DepartmentId,
                    DepartmentName = q.DepartmentName,
                    Order = q.Order,
                    IsProcessSameCounter = q.IsProcessSameCounter,
                    IsLineDepartmentCreateApplication = q.IsLineDepartmentCreateApplication,
                    Interval = q.Interval
                }).OrderBy(q => q.Order).ToList()
            };

            var branchId = apiResponse.Data.BranchId;

            var branchList = await CacheHelper.GetBranchesAsync();

            viewModel.BranchName = branchList.Branches?.FirstOrDefault(q => q.Id == branchId)?.BranchTranslations.FirstOrDefault(q => q.LanguageId == LanguageId)?.Name;

            return PartialView("_LineDetail", viewModel);
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteLine(string encryptedLineId)
        {
           if (encryptedLineId.IsNullOrWhitespace()) 
               return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

           var apiResponse = await RestHttpClient.Create()
                .Delete<DeleteLineResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.Lines + encryptedLineId.ToDecrypt().ToInt(),
                    QMSApiDefaultRequestHeaders);

            return apiResponse?.Status == SiteResources.SUCCESS 
                ? Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success }) 
                : Json(new ResultModel { Message = apiResponse?.Message, ResultType = ResultType.Danger });
        }

        #endregion

    }
}
