﻿using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Agency.Agency;
using Portal.Gateway.ApiModel.Requests.Management.BranchApplicationCountry;
using Portal.Gateway.ApiModel.Requests.Management.Slot;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Agency.Agency;
using Portal.Gateway.ApiModel.Responses.Management.BranchApplicationCountry;
using Portal.Gateway.ApiModel.Responses.Management.Slot;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.Application;
using Portal.Gateway.UI.Areas.Management.ViewModels.BranchApplicationCountry;
using Portal.Gateway.UI.Areas.Management.ViewModels.Slot;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using Portal.Gateway.Resources;
using Portal.Gateway.ApiModel.Requests.Management.BranchShiftHoliday;
using Newtonsoft.Json;
using static Portal.Gateway.UI.Areas.Management.ViewModels.Slot.AddSlotViewModel;
using ClosedXML.Excel;
using Portal.Gateway.ApiModel.Requests.Appointment.PreApplication;
using Portal.Gateway.ApiModel.Responses.Appointment.PreApplication;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.PreApplication;
using System.IO;
using Portal.Gateway.Contracts.Entities.Dto.Management.Slot.Responses;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Authorization;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
	[Area("Management")]
	public class SlotController : BaseController<SlotController>
	{
		public SlotController(
			   IOptions<AppSettings> appSettings,
			   ICacheHelper cacheHelper)
			: base(appSettings, cacheHelper)
		{

		}

		#region AddUpdate

		public async Task<IActionResult> Add()
		{
			var apiRequest = new PaginatedBranchApplicationCountryApiRequest
			{
				CountryId = null,
				BranchId = null,
				Pagination = new PaginationApiRequest
				{
					Page = 1,
					PageSize = 500,
					OrderBy = null,
					SortDirection = new ListSortDirection()
				}
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedBranchApplicationCountryApiResponse>>>
				(apiRequest, ApiMethodName.Management.GetPaginatedBranchApplicationCountry, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			var apiResponseAgency = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedAgencyApiResponse>>>
				(
				new PaginatedAgencyApiRequest
				{
					Pagination = new PaginationApiRequest
					{
						Page = 1,
						PageSize = 100,
						OrderBy = string.Empty,
						SortDirection = ListSortDirection.Ascending
					}

				}, ApiMethodName.Agency.GetPaginatedAgencies, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			List<TimeOnly> startTimeList = GenerateHourlyTimeSlots(60);


			var viewModel = new AddSlotViewModel()
			{
				BranchApplicationCountrySelectList = apiResponse.Data.Items.FirstOrDefault().BranchApplicationCountries
				.Where(p => p.IsActive && UserSession.BranchIds.Any(q => q == p.BranchId))
				.OrderBy(p => p.BranchName)
				.Select(p => new SelectListItem
				{
					Text = $"{p.BranchName} -> {p.CountryName}",
					Value = p.Id.ToString()
				}).ToList(),
				TimeOfDay = Enumerable.Range(0, startTimeList.Count).Select(p => new AddSlotViewModel.TimeOfDayViewModel()
				{
					IsActive = true,
					StartTime = startTimeList[p].ToString("HH:mm:ss"),
					Quota = 50
				}).ToArray(),
				SlotTypeSelectList = EnumHelper.GetEnumAsDictionary(typeof(Contracts.Entities.Enums.SlotType)).Select(p => new SelectListItem()
				{
					Text = p.Value.ToTitleCase(),
					Value = p.Key.ToString()
				}).ToList(),
				AgencySelectList = apiResponseAgency.Data.Items?.SelectMany(p => p.Agencies).Where(q => q.ActivationStatusId == ActivationStatusType.Active.ToInt()).Select(p => new SelectListItem()
				{
					Text = p.Name,
					Value = p.Id.ToString()
				}).ToList()
			};

			return View(viewModel);
		}

		public async Task<IActionResult> GetForBranchsShiftHoliday(int branchApplicationCountryId)
		{
			var apiRequest = new PaginatedBranchApplicationCountryApiRequest
			{
				CountryId = null,
				BranchId = null,
				Pagination = new PaginationApiRequest
				{
					Page = 1,
					PageSize = 500,
					OrderBy = null,
					SortDirection = new ListSortDirection()
				}
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedBranchApplicationCountryApiResponse>>>
				(apiRequest, ApiMethodName.Management.GetPaginatedBranchApplicationCountry, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			var apiResponseAgency = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedAgencyApiResponse>>>
				(
				new PaginatedAgencyApiRequest
				{
					Pagination = new PaginationApiRequest
					{
						Page = 1,
						PageSize = 100,
						OrderBy = string.Empty,
						SortDirection = ListSortDirection.Ascending
					}

				}, ApiMethodName.Agency.GetPaginatedAgencies, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			var apiResponseBranchShiftHoliday = await PortalHttpClientHelper
						   .GetAsync<ApiResponse<BranchShiftHolidayDays>>
						   (ApiMethodName.Management.GetShiftHolidayByBranchApplicationCountry + branchApplicationCountryId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
						   .ConfigureAwait(false);

			if (!await apiResponseBranchShiftHoliday.Validate(out ResultModel resultBranchShiftHoliday).ConfigureAwait(false))
				return Json(new List<SelectListItem>());


			int period = 60;
			if (apiResponseBranchShiftHoliday != null && apiResponseBranchShiftHoliday.Data.AppointmentPeriod != 0)
				period = apiResponseBranchShiftHoliday.Data.AppointmentPeriod;

			List<TimeOnly> startTimeList = GenerateHourlyTimeSlots(period);

			var viewModel = new AddSlotViewModel()
			{
				BranchApplicationCountrySelectList = apiResponse.Data.Items.FirstOrDefault().BranchApplicationCountries
				.Where(p => p.IsActive && UserSession.BranchIds.Any(q => q == p.BranchId))
				.OrderBy(p => p.BranchName)
				.Select(p => new SelectListItem
				{
					Text = $"{p.BranchName} -> {p.CountryName}",
					Value = p.Id.ToString()
				}).ToList(),
				TimeOfDay = Enumerable.Range(0, startTimeList.Count).Select(p => new AddSlotViewModel.TimeOfDayViewModel()
				{
					IsActive = (apiResponseBranchShiftHoliday != null && startTimeList[p].ToTimeSpan() >= apiResponseBranchShiftHoliday.Data.StartOfShift && startTimeList[p].ToTimeSpan() < apiResponseBranchShiftHoliday.Data.EndOfShift) ? ((startTimeList[p].ToTimeSpan() < apiResponseBranchShiftHoliday.Data.StartOfLunch || startTimeList[p].ToTimeSpan() >= apiResponseBranchShiftHoliday.Data.EndOfLunch) ? true : false) : false,
					StartTime = startTimeList[p].ToString("HH:mm:ss"),
					Quota = 50
				}).ToArray(),
				SlotTypeSelectList = EnumHelper.GetEnumAsDictionary(typeof(Contracts.Entities.Enums.SlotType)).Select(p => new SelectListItem()
				{
					Text = p.Value.ToTitleCase(),
					Value = p.Key.ToString()
				}).ToList(),
				AgencySelectList = apiResponseAgency.Data.Items?.SelectMany(p => p.Agencies).Where(q => q.ActivationStatusId == ActivationStatusType.Active.ToInt()).Select(p => new SelectListItem()
				{
					Text = p.Name,
					Value = p.Id.ToString()
				}).ToList()
			};

			return PartialView("_Add", viewModel);

		}

		public static List<TimeOnly> GenerateHourlyTimeSlots(int appointmentPeriod)
		{
			// Günü 00:00'dan başlayarak başlatıyoruz
			TimeOnly startOfDay = new TimeOnly(0, 0); // 00:00
			TimeOnly endOfDay = new TimeOnly(23, 59); // 23:59

			List<TimeOnly> timeSlots = new List<TimeOnly>();
			TimeOnly currentTime = startOfDay;

			// Günü saatlik artışlarla dolduruyoruz
			while (currentTime <= endOfDay)
			{
				timeSlots.Add(currentTime);
				currentTime = currentTime.AddMinutes(appointmentPeriod);

				if (timeSlots.Count > 0 && currentTime == startOfDay)
				{
					break;
				}
			}

			return timeSlots;
		}

		[HttpPost]
		public async Task<IActionResult> Add(AddSlotViewModel viewModel)
		{

			var model = JsonConvert.DeserializeObject<DisableDatesModel>(viewModel.DisableDates);
			List<DateTimeOffset> disableDates = model.DisableDatesFormatted;


			var apiRequest = new AddSlotApiRequest()
			{
				SlotTypeId = viewModel.SlotTypeId,
				AgencyId = ((SlotType)viewModel.SlotTypeId) == SlotType.Agency ? viewModel.AgencyId : null,
				BranchApplicationCountryId = viewModel.BranchApplicationCountryId,
				StartDate = viewModel.StartDate,
				EndDate = viewModel.EndDate,
				DisableDates = disableDates,
				SlotTimeQuotas = viewModel.TimeOfDay
									.Where(p => p.IsActive)
									.Select(p => new AddSlotApiRequest.SlotTimeQuotaApiRequest()
									{
										StartTime = p.StartTime,
										SlotQuotas = p.Quota
									}).ToList()
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<AddApiResponse>>
				(apiRequest, ApiMethodName.Management.AddSlot, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		public async Task<IActionResult> Update()
		{
			return View();
		}

		[HttpPost]
		public async Task<IActionResult> Update([DataSourceRequest] DataSourceRequest request,
			[Bind(Prefix = "models")] IEnumerable<UpdateSlotViewModel> slots)
		{
			if (slots != null && ModelState.IsValid)
			{
				var apiRequest = new UpdateSlotApiRequest()
				{
					SlotQuotas = slots.Select(p => new UpdateSlotApiRequest.SlotQuota()
					{
						Id = p.EncryptedId.ToDecryptInt(),
						Quota = p.Quota
					}).ToList()
				};

				var apiResponse = await PortalHttpClientHelper
										.PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
										(apiRequest, ApiMethodName.Management.UpdateSlot, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
										.ConfigureAwait(false);

				if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
					return Json(result);
			}

			return Json(slots.ToDataSourceResult(request, ModelState));
		}

		#endregion

		#region Delete

		public async Task<IActionResult> Delete()
		{
			var apiRequest = new PaginatedBranchApplicationCountryApiRequest
			{
				CountryId = null,
				BranchId = null,
				Pagination = new PaginationApiRequest
				{
					Page = 1,
					PageSize = 500,
					OrderBy = null,
					SortDirection = new ListSortDirection()
				}
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedBranchApplicationCountryApiResponse>>>
				(apiRequest, ApiMethodName.Management.GetPaginatedBranchApplicationCountry, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			var apiResponseAgency = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedAgencyApiResponse>>>
				(
				new PaginatedAgencyApiRequest
				{
					Pagination = new PaginationApiRequest
					{
						Page = 1,
						PageSize = 100,
						OrderBy = string.Empty,
						SortDirection = ListSortDirection.Ascending
					}

				}, ApiMethodName.Agency.GetPaginatedAgencies, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			var viewModel = new DeleteSlotViewModel()
			{
				BranchApplicationCountrySelectList = apiResponse.Data.Items.FirstOrDefault().BranchApplicationCountries
				.Where(p => p.IsActive && UserSession.BranchIds.Any(q => q == p.BranchId))
				.OrderBy(p => p.BranchName)
				.Select(p => new SelectListItem
				{
					Text = $"{p.BranchName} -> {p.CountryName}",
					Value = p.Id.ToString()
				}).ToList(),
                SlotTypeSelectList = EnumHelper.GetEnumAsDictionary(typeof(Contracts.Entities.Enums.SlotType)).Select(p => new SelectListItem()
                {
                    Text = p.Value.ToTitleCase(),
                    Value = p.Key.ToString()
                }).ToList()
            };

			return View(viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> Delete(DeleteSlotViewModel viewModel)
		{
			if (viewModel is null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var apiRequest = new DeleteSlotApiRequest()
			{
				BranchApplicationCountryId = viewModel.BranchApplicationCountryId,
				StartDate = viewModel.StartDate,
				EndDate = viewModel.EndDate,
				SlotTypeId = viewModel.SlotTypeId
            };

			var apiResponse = await PortalHttpClientHelper
				.PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
				(apiRequest, ApiMethodName.Management.DeleteSlot, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		#endregion

		public async Task<IActionResult> GetPaginatedSlot([DataSourceRequest] DataSourceRequest request, FilterUpdateSlotViewModel filterViewModel)
		{
			var paginationFilter = request.GetPaginationFilter(filterViewModel);

			var apiRequest = new PaginatedSlotApiRequest
			{
				BranchApplicationCountryIds = filterViewModel.FilterBranchApplicationCountryIds,
				Date = filterViewModel.FilterSlotDate ?? DateTime.MinValue.ToUniversalTime(),
				EndDate = filterViewModel.FilterSlotEndDate ?? DateTime.MinValue.ToUniversalTime(),
				SlotTypeId = filterViewModel.FilterSlotTypeId,
				Pagination = new PaginationApiRequest
				{
					Page = paginationFilter.Page,
					PageSize = paginationFilter.PageSize,
					OrderBy = paginationFilter.OrderBy,
					SortDirection = paginationFilter.SortDirection
				}
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedSlotApiResponse>>>
				(apiRequest, ApiMethodName.Management.GetPaginatedSlot, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			var paginatedData = new List<UpdateSlotViewModel>();

			if (apiResponse.Data != null && apiResponse.Data.Items.Any())
			{
				paginatedData = apiResponse.Data.Items.First().Slots
					.Select(p => new UpdateSlotViewModel
					{
						EncryptedId = p.Id.ToEncrypt(),
						BranchName = p.BranchName,
						CountryName = p.CountryName,
						AgencyName = p.AgencyName,
						SlotType = p.SlotTypeId.HasValue ? EnumHelper.GetEnumDescription(typeof(SlotType), p.SlotTypeId.ToString()) : string.Empty,
						Quota = p.Quota,
						SlotTime = p.SlotTime.ToString("dd/MM/yyyy HH:mm:ss")
					}).ToList();
			}

			return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
		}

        [HttpPost]
        public async Task<FileContentResult> GetSlotReport(FilterUpdateSlotViewModel filterViewModel)
        {

            var apiRequest = new GetSlotReportApiRequest
            {
                BranchApplicationCountryIds = filterViewModel.FilterBranchApplicationCountryIds,
                StartDate = filterViewModel.FilterSlotDate ?? DateTime.MinValue.ToUniversalTime(),
                EndDate = filterViewModel.FilterSlotEndDate ?? DateTime.MinValue.ToUniversalTime(),
            };

			var r = JsonConvert.SerializeObject(apiRequest);

            var response = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<SlotReportResponseDto>>
                (apiRequest, ApiMethodName.Management.GetSlotReport, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            var reportName = SiteResources.SlotReport.ToTitleCase();

            var reportCreatedBy = UserSession.FullName;
            var reportDate = DateTime.UtcNow.Date;

            Regex rgx = new Regex("[^a-zA-Z0-9 ]");

            using (var workbook = new XLWorkbook())
            {
                foreach (var branchData in response.Data.Branches)
                {
                    var branch = $"{rgx.Replace(branchData.BranchName, string.Empty)}";
                    var sheetName = branch.Length > 30 ? branch.Substring(0, 30) : branch;

                    var worksheet = workbook.Worksheets.Add(sheetName);

                    var currentRow = 1;
                    var headerRow = -1;

                    worksheet.Cell(currentRow, 1).Value = reportName;
                    worksheet.Cell(currentRow, 1).Style.Font.SetBold().Font.FontSize = 14;

                    currentRow++;
                    currentRow++;

                    worksheet.Cell(currentRow, 1).Value = SiteResources.BranchName.ToTitleCase();
                    worksheet.Cell(currentRow, 1).Style.Font.SetBold();

                    currentRow++;

                    worksheet.Cell(currentRow, 1).Value = branchData.BranchName;

                    currentRow++;
                    currentRow++;

                    worksheet.Cell(currentRow, 1).Value = SiteResources.CreatedBy.ToTitleCase();
                    worksheet.Cell(currentRow, 1).Style.Font.SetBold();

                    currentRow++;

                    worksheet.Cell(currentRow, 1).Value = reportCreatedBy;

                    currentRow++;
                    currentRow++;

                    worksheet.Cell(currentRow, 1).Value = SiteResources.ReportDate.ToTitleCase();
                    worksheet.Cell(currentRow, 1).Style.Font.SetBold();
                    worksheet.Cell(currentRow, 3).Value = SiteResources.StartDate.ToTitleCase();
                    worksheet.Cell(currentRow, 3).Style.Font.SetBold();
                    worksheet.Cell(currentRow, 5).Value = SiteResources.EndDate.ToTitleCase();
                    worksheet.Cell(currentRow, 5).Style.Font.SetBold();

                    currentRow++;

                    worksheet.Cell(currentRow, 1).Value = reportDate;
                    worksheet.Cell(currentRow, 1).Style.DateFormat.Format = SiteResources.DatePickerFormatView;
                    worksheet.Cell(currentRow, 3).Value = filterViewModel.FilterSlotDate.GetValueOrDefault();
                    worksheet.Cell(currentRow, 3).Style.DateFormat.Format = SiteResources.DatePickerFormatView;
                    worksheet.Cell(currentRow, 5).Value = filterViewModel.FilterSlotEndDate.GetValueOrDefault();
                    worksheet.Cell(currentRow, 5).Style.DateFormat.Format = SiteResources.DatePickerFormatView;

                    currentRow++;
                    currentRow++;

                    int currentRowMax = 9;
                    headerRow = currentRow;
                    currentRow++;

                    worksheet.Cell(currentRow, 1).Value = SiteResources.Date.ToTitleCase();
                    worksheet.Cell(currentRow, 2).Value = SiteResources.Hour.ToTitleCase();
                    worksheet.Cell(currentRow, 3).Value = SiteResources.Quota.ToTitleCase();
                    worksheet.Cell(currentRow, 4).Value = SiteResources.Individual.ToTitleCase();
                    worksheet.Cell(currentRow, 5).Value = SiteResources.SlotReportAgentColumn.ToTitleCase();
                    worksheet.Cell(currentRow, 6).Value = SiteResources.SlotReportCallCenterColumn.ToTitleCase();
                    worksheet.Cell(currentRow, 7).Value = EnumResources.Mobile.ToTitleCase();
                    worksheet.Cell(currentRow, 8).Value = SiteResources.RemainingAppointment.ToTitleCase();
                    worksheet.Cell(currentRow, 9).Value = SiteResources.SlotReportWalkinAppointment.ToTitleCase();

                    var rangeGroupTableHeader = worksheet.Range(worksheet.Cell(currentRow, 1), worksheet.Cell(currentRow, currentRowMax));
                    rangeGroupTableHeader.Style.Font.SetBold();

                    currentRow++;

                    for (int i = 0; i < branchData.Slots.Count(); i++)
                    {
                        var data = branchData.Slots.ElementAt(i);
                        worksheet.Cell(currentRow, 1).Value = data.SlotDate;
                        worksheet.Cell(currentRow, 1).Style.DateFormat.Format = SiteResources.DatePickerFormatView;
                        worksheet.Cell(currentRow, 2).Value = data.Hour;
                        worksheet.Cell(currentRow, 3).Value = data.Quota;
                        worksheet.Cell(currentRow, 4).Value = data.Individual;
                        worksheet.Cell(currentRow, 5).Value = data.Agent;
                        worksheet.Cell(currentRow, 6).Value = data.CallCenter;
                        worksheet.Cell(currentRow, 7).Value = data.Mobile;
                        worksheet.Cell(currentRow, 8).Value = data.RemainingAppointment;
                        worksheet.Cell(currentRow, 9).Value = data.WalkinAppointment;

                        if (data.SlotTime >= DateTime.UtcNow)
                        {
                            var rowRange = worksheet.Range(worksheet.Cell(currentRow, 2), worksheet.Cell(currentRow, currentRowMax));
                            rowRange.Style.Font.SetBold();
                            rowRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#fbe2d5");
                        }

                        currentRow++;
                    }

                    var rangeGroupTable = worksheet.Range(worksheet.Cell(headerRow + 1, 1), worksheet.Cell(currentRow - 1, currentRowMax));
                    rangeGroupTable.Style.Border.SetTopBorder(XLBorderStyleValues.Thin);
                    rangeGroupTable.Style.Border.SetBottomBorder(XLBorderStyleValues.Thin);
                    rangeGroupTable.Style.Border.SetLeftBorder(XLBorderStyleValues.Thin);
                    rangeGroupTable.Style.Border.SetRightBorder(XLBorderStyleValues.Thin);
                    rangeGroupTable.Style.Border.SetInsideBorder(XLBorderStyleValues.Thin);
                    rangeGroupTable.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                    var rangeAlignment = worksheet.Range(worksheet.Cell(1, 1), worksheet.Cell(currentRow, currentRowMax));
                    rangeAlignment.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);

                    worksheet.Columns().AdjustToContents();

                    currentRow++;
                    currentRow++;

                    var sumTableRow = currentRow;

                    worksheet.Cell(currentRow, 1).Value = SiteResources.Quota.ToTitleCase(); ;
                    worksheet.Cell(currentRow, 2).Value = branchData.TotalQuota;
                    worksheet.Cell(currentRow, 3).Value = "100.00%";
                    currentRow++;
                    worksheet.Cell(currentRow, 1).Value = SiteResources.Individual.ToTitleCase();
                    worksheet.Cell(currentRow, 2).Value = branchData.TotalIndividual;
                    worksheet.Cell(currentRow, 3).Value = $"{branchData.IndividualPercentage:F2}%";
                    currentRow++;
                    worksheet.Cell(currentRow, 1).Value = SiteResources.SlotReportAgentColumn.ToTitleCase();
                    worksheet.Cell(currentRow, 2).Value = branchData.TotalAgent;
                    worksheet.Cell(currentRow, 3).Value = $"{branchData.AgentPercentage:F2}%";
                    currentRow++;
                    worksheet.Cell(currentRow, 1).Value = SiteResources.SlotReportCallCenterColumn.ToTitleCase();
                    worksheet.Cell(currentRow, 2).Value = branchData.TotalCallCenter;
                    worksheet.Cell(currentRow, 3).Value = $"{branchData.CallCenterPercentage:F2}%";
                    currentRow++;
                    worksheet.Cell(currentRow, 1).Value = EnumResources.Mobile.ToTitleCase();
                    worksheet.Cell(currentRow, 2).Value = branchData.TotalMobile;
                    worksheet.Cell(currentRow, 3).Value = $"{branchData.MobilePercentage:F2}%";
                    currentRow++;
                    worksheet.Cell(currentRow, 1).Value = SiteResources.RemainingAppointment.ToTitleCase();
                    worksheet.Cell(currentRow, 2).Value = branchData.TotalRemainingAppointment;
                    worksheet.Cell(currentRow, 3).Value = string.Empty;
                    currentRow++;
                    worksheet.Cell(currentRow, 1).Value = SiteResources.SlotReportWalkinAppointment.ToTitleCase();
                    worksheet.Cell(currentRow, 2).Value = branchData.TotalWalkinAppointment;
                    worksheet.Cell(currentRow, 3).Value = string.Empty;
                    currentRow++;

                    var percentageRange = worksheet.Range(worksheet.Cell(sumTableRow, 1), worksheet.Cell(currentRow - 1, 3));
                    var percentageRange1 = worksheet.Range(worksheet.Cell(sumTableRow, 2), worksheet.Cell(currentRow - 1, 2));
                    percentageRange1.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);
                    percentageRange.Style.Font.SetBold();
                    percentageRange1.Style.Font.SetBold(false);
                    percentageRange.Style.Border.SetTopBorder(XLBorderStyleValues.Thin);
                    percentageRange.Style.Border.SetBottomBorder(XLBorderStyleValues.Thin);
                    percentageRange.Style.Border.SetLeftBorder(XLBorderStyleValues.Thin);
                    percentageRange.Style.Border.SetRightBorder(XLBorderStyleValues.Thin);
                    percentageRange.Style.Border.SetInsideBorder(XLBorderStyleValues.Thin);
                    percentageRange.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                }
                using (var stream = new MemoryStream())
                {
                    workbook.SaveAs(stream);
                    var content = stream.ToArray();
                    return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{reportName} - {reportDate.ToString("ddMMyyyy")}.xlsx");
                }
            }
        }
    }
}