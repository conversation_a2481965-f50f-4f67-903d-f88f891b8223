﻿using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Helpers;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.HostedServices
{
    public class CacheHostedService : BaseHostedService<CacheHostedService>
    {
        private readonly ILogger<CacheHostedService> _logger;
        private Timer _timer;
        private readonly ICacheHelper _cacheHelper;
        private readonly IHostApplicationLifetime _hostApplicationLifetime;
        private readonly SemaphoreSlim _semaphore = new(1, 1);

        public CacheHostedService(
            ILogger<CacheHostedService> logger,
            ICacheHelper cacheHelper2,
            IHostApplicationLifetime hostApplicationLifetime) : base()
        {
            _logger = logger;
            _cacheHelper = cacheHelper2;
            _hostApplicationLifetime = hostApplicationLifetime;
        }

        public override void Dispose()
        {
            _timer?.Dispose();
        }

        public override async Task StartAsync(CancellationToken cancellationToken)
        {
            if (!await _cacheHelper.CacheAllAsync())
            {
                throw new IOException("There are missing cache data, application startup failed.");
            }

            _hostApplicationLifetime.ApplicationStarted.Register(() =>
            {
                _logger.LogInformation("Application is now ready.");
            });

            _logger.LogInformation($"{nameof(CacheHostedService)} started!");

            _timer = new Timer(async o =>
            {
                if (!await _semaphore.WaitAsync(0)) return;

                try
                {
                    await _cacheHelper.CacheAllAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"{nameof(CacheHostedService)} exception! {ex}");
                }
                finally
                {
                    _semaphore.Release();
                }
            },
            null,
            TimeSpan.Zero,
            TimeSpan.FromSeconds(10));
        }

        public override Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation($"{nameof(CacheHostedService)} stopped!");

            return Task.CompletedTask;
        }
    }
}