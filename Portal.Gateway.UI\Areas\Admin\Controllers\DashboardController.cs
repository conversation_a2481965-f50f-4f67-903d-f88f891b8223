﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;

namespace Portal.Gateway.UI.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class DashboardController : BaseController<DashboardController>
    {
        public DashboardController(
                IOptions<AppSettings> appSettings,
                ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        public IActionResult Index()
        {
            return View();
        }
    }
}