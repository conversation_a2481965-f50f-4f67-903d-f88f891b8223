﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.Contracts.Entities.Constants;
using Kendo.Mvc.UI;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Requests;
using System.Linq;
using System.Collections.Generic;
using Kendo.Mvc.Extensions;
using Portal.Gateway.UI.Validators;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.Resources;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.ApiModel.Requests.Management.User;
using Portal.Gateway.UI.Areas.Management.ViewModels.User;
using Portal.Gateway.ApiModel.Responses.Management.User;
using Microsoft.Extensions.Caching.Memory;
using Portal.Gateway.Contracts.Attributes;
using System;
using Portal.Gateway.Contracts.Extensions;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class UserController : BaseController<UserController>
    {
        public UserController(
                IOptions<AppSettings> appSettings,
                ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        #region Add

        public IActionResult PartialAddUser()
        {
            return PartialView("_AddUser");
        }

        [HttpPost]
        public async Task<IActionResult> AddUser(AddUserViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var pass = viewModel.Password.ToCharArray();
            var upperCase = false;
            var lowerCase = false;
            var isNumber = false;
            var isSymbol = false;
            foreach (char c in pass)
            {
                if (c >= 'A' && c <= 'Z')
                    upperCase = true;

                if (c >= 'a' && c <= 'z')
                    lowerCase = true;

                if (Char.IsNumber(c))
                    isNumber = true;

                if (!Char.IsLetterOrDigit(c))
                    isSymbol = true;
            }

            if (!(upperCase && lowerCase && isNumber && isSymbol && pass.Count() >= 8 && pass.Count() <= 20))
                return Json(new ResultModel { Message = nameof(SiteResources.UserPasswordRequirements).ToSiteResourcesValue(LanguageId), ResultType = ResultType.Warning });

            var apiRequest = new AddUserApiRequest
            {
                Name = viewModel.Name,
                Surname = viewModel.Surname,
                Email = viewModel.Email,
                Password = viewModel.Password,
                PhoneNumber = viewModel.PhoneNumber,
                SapUserId = viewModel.SapUserId
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                (apiRequest, ApiMethodName.Management.AddUser, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateUser(string encryptedUserId)
        {
            int id = encryptedUserId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<UserApiResponse>>
                (ApiMethodName.Management.GetUser + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdateUserViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Name = apiResponse.Data.Name,
                Surname = apiResponse.Data.Surname,
                Email = apiResponse.Data.Email,
                PhoneNumber = apiResponse.Data.PhoneNumber,
                IsActive = apiResponse.Data.IsActive,
                SapUserId = apiResponse.Data.SapUserId,
                ShowInPmsPage = apiResponse.Data.ShowInPmsPage,
                IsBiometricsDesktopUser = apiResponse.Data.IsBiometricsDesktopUser,
                AdAccountName = apiResponse.Data.AdAccountName
            };

            return PartialView("_UpdateUser", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateUser(UpdateUserViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateUserApiRequest
            {
                Id = viewModel.EncryptedId.ToDecryptInt(),
                Name = viewModel.Name,
                Surname = viewModel.Surname,
                Email = viewModel.Email,
                UpdatePassword = viewModel.UpdatePassword,
                Password = viewModel.UpdatePassword ? viewModel.Password : "",
                PhoneNumber = viewModel.PhoneNumber,
                IsActive = viewModel.IsActive,
                SapUserId = viewModel.SapUserId,
                ShowInPmsPage = viewModel.ShowInPmsPage,
                IsBiometricsDesktopUser = viewModel.IsBiometricsDesktopUser
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateUser, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            if(!apiResponse.Data.Result && !string.IsNullOrEmpty(apiResponse.Data.Message))
                return Json(new ResultModel { Message = apiResponse.Data.Message, ResultType = ResultType.Warning });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        public async Task<IActionResult> PartialUpdateUserRole(string encryptedUserId)
        {
            int id = encryptedUserId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<UserRoleApiResponse>>
                (ApiMethodName.Management.GetUserRole + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdateUserRoleViewModel
            {
                EncryptedUserId = apiResponse.Data.UserId.ToEncrypt(),
                UserRoles = apiResponse.Data.UserRoles.Select(p => new UpdateUserRolesViewModel()
                {
                    EncryptedRoleId = p.Role.Id.ToEncrypt(),
                    IsActive = p.IsActive,
                    RoleName = p.Role.Name
                }).ToArray()
            };

            return PartialView("_UpdateUserRole", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateUserRole(UpdateUserRoleViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateUserRoleApiRequest
            {
                UserId = viewModel.EncryptedUserId.ToDecryptInt(),
                Roles = viewModel.UserRoles.Select(p => new UpdateUserRolesApiRequest() 
                {
                    IsActive = p.IsActive,
                    RoleId = p.EncryptedRoleId.ToDecryptInt()
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateUserRole, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteUser(string encryptedUserId)
        {
            int id = encryptedUserId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Management.DeleteUser + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> PartialUser(string encryptedUserId)
        {
            int id = encryptedUserId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<UserApiResponse>>
                (ApiMethodName.Management.GetUser + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UserViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Name = apiResponse.Data.Name,
                Surname = apiResponse.Data.Surname,
                Email = apiResponse.Data.Email,
                PhoneNumber = apiResponse.Data.PhoneNumber,
                IsActive = apiResponse.Data.IsActive,
                SapUserId = apiResponse.Data.SapUserId,
                ShowInPmsPage = apiResponse.Data.ShowInPmsPage
            };

            return PartialView("_User", viewModel);
        }

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedUsers([DataSourceRequest] DataSourceRequest request, FilterUserViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedUsersApiRequest
            {
                Name = filterViewModel.FilterName,
                Surname = filterViewModel.FilterSurname,
                Email = filterViewModel.FilterEmail,
                FilterBranchId = filterViewModel.FilterBranchId,
                FilterCountryId = filterViewModel.FilterCountryId,
                FilterAdAccountName = filterViewModel.FilterAdAccountName,
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<UsersApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedUsers, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<UserViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().Users
                    .Select(p => new UserViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        Name = p.Name,
                        Surname = p.Surname,
                        Email = p.Email,
                        PhoneNumber = p.PhoneNumber,
                        IsActive = p.IsActive,
                        SapUserId = p.SapUserId,
                        BranchId = p.BranchId,
                        CountryId = p.CountryId,
                        AdAccountName = p.AdAccountName,
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #endregion
    }
}
