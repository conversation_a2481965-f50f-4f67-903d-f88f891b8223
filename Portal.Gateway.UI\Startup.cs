using FluentValidation.AspNetCore;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Serialization;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.HostedServices;
using Portal.Gateway.UI.Middleware;
using Serilog;
using System.Globalization;
using System.IO.Compression;
using Gateway.EventBus;

namespace Portal.Gateway.UI
{
    public class Startup
    {
        private readonly IWebHostEnvironment _env;

        public Startup(IConfiguration configuration, IWebHostEnvironment env)
        {
            Configuration = configuration;
            _env = env;
        }

        public IConfiguration Configuration { get; }

        public void ConfigureServices(IServiceCollection services)
        {
            services.Configure<GzipCompressionProviderOptions>(options =>
            {
                options.Level = CompressionLevel.Fastest;
            });

            services.AddResponseCompression(options =>
            {
                options.EnableForHttps = true;
                options.Providers.Add<GzipCompressionProvider>();
            });

            services.Configure<FormOptions>(options =>
            {
                options.MultipartBodyLengthLimit = 250 * 1024 * 1024;
            });

            services.AddControllersWithViews();
            services.AddSignalR();

            services.AddPortalLogger(Configuration);

            services.AddMvc()
                .AddRazorRuntimeCompilation()
                .AddFluentValidation(fv => fv.RegisterValidatorsFromAssemblyContaining<Startup>());

            services.Configure<AppSettings>(Configuration.GetSection("AppSettings"));
            services.Configure<CacheSettings>(Configuration.GetSection("CacheSettings"));
            services.Configure<SessionSettings>(Configuration.GetSection("Session"));

            services
                .AddControllersWithViews()
                .SetCompatibilityVersion(CompatibilityVersion.Version_3_0)
                .AddNewtonsoftJson(options => options.SerializerSettings.ContractResolver = new DefaultContractResolver());

            services.AddCors(options =>
            {
                options.AddPolicy("AllowSpecified",
                builder =>
                {
	                if (_env.IsStaging())
		                builder.WithOrigins("https://gwdcstaging01.gateway.com.tr:4445", "https://sta-printdatalbpri.gateway.com.tr").AllowAnyHeader().AllowAnyMethod().AllowCredentials();
	                else if (_env.IsDevelopment())
		                builder.WithOrigins("https://qms-api-dev-k8s.gateway.com.tr", "https://printdata-api-dev-k8s.gateway.com.tr").AllowAnyHeader().AllowAnyMethod().AllowCredentials();
                    else if (_env.IsProduction())
                        builder.WithOrigins("https://qmslbpri.gateway.com.tr", "https://printdatalbpub.gateway.com.tr").AllowAnyHeader().AllowAnyMethod().AllowCredentials();
                    else if (_env.EnvironmentName.EndsWith("K8S"))
                        // TODO: update this urls
                        builder.WithOrigins("http://************", "http://***********", "http://gwdcb2ck8s01.gateway.com.tr/", "https://printdata-api-k8s.gateway.com.tr").AllowAnyHeader().AllowAnyMethod().AllowCredentials();
                    else
                        builder.WithOrigins("https://localhost:4001", "https://localhost:7071").AllowAnyHeader().AllowAnyMethod().AllowCredentials();
				});
            });

            services.AddKendo();

            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddScoped<CurrentUserDataHelper, CurrentUserDataHelper>();
            services.AddSingleton<ICacheHelper, CacheHelper>();

            services.Configure<FormOptions>(x =>
            {
                x.ValueCountLimit = 8192; // old val:2048; Increased due to high form value counts like user role action data entry extc.
            });

            services.Configure<RequestLocalizationOptions>(options =>
            {
                var supportedCultures = new[]
                {
                        new CultureInfo("tr-TR"),
                        new CultureInfo("en-US"),
                };
                foreach (var item in supportedCultures)
                {
                    item.DateTimeFormat.ShortDatePattern = "dd/MM/yyyy";
                    item.DateTimeFormat.DateSeparator = "/";
                }

                options.DefaultRequestCulture = new RequestCulture(supportedCultures[0], supportedCultures[0]);
                options.SupportedCultures = supportedCultures;
                options.SupportedUICultures = supportedCultures;
            });

            #region MemoryCache

            services.AddMemoryCache();

            #endregion

            #region HostedServices

            services.AddHostedService<CacheHostedService>();

            #endregion

            #region RabbitMq           

            services.AddRabbitMq(Configuration);

            #endregion

            services.RegisterFileStorageServices();
            services.AddSessionManagement(Configuration, _env);
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            app.UseSession();

            var requestLocalizationOptions = app.ApplicationServices.GetService<IOptions<RequestLocalizationOptions>>();
            app.UseRequestLocalization(requestLocalizationOptions.Value);

            if (env.IsDevelopment())
            {
                app.UseStatusCodePagesWithRedirects("/Error/{0}");
            }
            else
            {
                app.UseStatusCodePagesWithRedirects("/Error/{0}");
                app.UseHsts();
            }

            app.UseResponseCompression();
            app.UseHttpsRedirection();
            app.UseStaticFiles();
            app.UseRouting();
            app.UseAuthentication();
            app.UseCors("AllowSpecified");
            app.UseAuthorization();

            app.UseMiddleware<ErrorHandler>();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute(
                    name: "areas",
                    pattern: "{area:exists}/{controller=Dashboard}/{action=Index}/{id?}");

                endpoints.MapControllerRoute(
                    name: "default",
                    pattern: "{controller=Dashboard}/{action=Index}/{id?}");
            });

            app.UseSerilogRequestLogging(opts => opts.EnrichDiagnosticContext = ElasticLogHelper.EnrichFromRequest);
        }
    }
}