﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Portal.Gateway.UI.Config;
using System;
using System.Resources;
using Portal.Gateway.Resources;
using System.Globalization;
using System.Linq;
using System.Collections;
using System.Collections.Generic;
using Newtonsoft.Json;
using System.Text;
using Microsoft.Extensions.Caching.Memory;
using Portal.Gateway.UI.Helpers;

namespace Portal.Gateway.UI.Controllers
{
    [AllowAnonymous]
    public class LocalizationController : BaseController<LocalizationController>
    {
        public LocalizationController(
                IOptions<AppSettings> appSettings,
                ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        public IActionResult SetLanguage(string culture, string returnUrl)
        {
            Response.Cookies.Append(CookieRequestCultureProvider.DefaultCookieName, CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(culture)),
                new CookieOptions { Expires = DateTimeOffset.UtcNow.AddYears(1) }
            );

            return LocalRedirect(returnUrl);
        }

        public ContentResult JavascriptResources()
        {
            var myResourceClass = new ResourceManager(typeof(SiteResources));
            var resourceSet = myResourceClass.GetResourceSet(CultureInfo.CurrentUICulture, true, true);
            var resourceDictionary = resourceSet.Cast<DictionaryEntry>()
                .ToDictionary(r => r.Key.ToString(),
                    r => r.Value);

            string data = SerializeResourceDictionary(resourceDictionary, "jsResources") + ";";

            return new ContentResult { Content = data, ContentType = "application/javascript" };
        }

        private string SerializeResourceDictionary(Dictionary<string, object> resxDict, string varname)
        {
            StringBuilder sb = new StringBuilder(2048);

            sb.Append("var " + varname + " = {\r\n");

            int anonymousIdCounter = 0;
            foreach (KeyValuePair<string, object> item in resxDict)
            {
                string value = item.Value as string;
                if (value == null)
                    continue;

                string key = item.Key;
                if (string.IsNullOrEmpty(item.Key))
                    key = "__id" + anonymousIdCounter++;

                key = key.Replace(".", "_");

                sb.Append("\t" + key + ": ");
                sb.Append(JsonConvert.SerializeObject(value, Formatting.Indented,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore,
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                    }));
                sb.Append(",\r\n");
            }

            sb.Append("}");

            sb.Replace(",\r\n}", "\r\n}");
            sb.Append(";\r\n");

            return sb.ToString();
        }
    }
}