﻿using DocumentFormat.OpenXml.Wordprocessing;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.Management.PrinterAgent;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.PrinterAgent;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.PrinterAgent;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class PrinterAgentController : BaseController<PrinterAgentController>
    {
        public PrinterAgentController(
            IOptions<AppSettings> appSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {
        }

        #region Get

        [Action(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPrinterAgentList([DataSourceRequest] DataSourceRequest request, FilterPrinterAgentViewModel filterRequest)
        {
            if (request == null)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new GetPrinterAgentApiRequest
            {
                LanguageId = LanguageId,
                FilterBranchId = filterRequest.FilterBranchId,
                FilterIsOnline = filterRequest.FilterIsOnline,
                FilterHostName = filterRequest.FilterHostName,               
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<PrinterAgentListApiResponse>
                (apiRequest, ApiMethodName.PrinterAgent.GetPrinterAgentList, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);
            
            if (apiResponse == null)
                return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });

            var paginatedData = apiResponse.PrinterAgentList.OrderBy(o => o.HostName).Skip((request.Page-1)*request.PageSize).Take(request.PageSize).Select(p =>
                new PrinterAgentViewModel
                {
                    Id = p.Id,
                    HostName = p.HostName,
                    BuildTime= p.BuildTime,
                    UserName= p.UserName,
                    Online = p.Online,
                    IsOnline = p.Online ? "Online" : "Offline",
                    IsRegistered = p.Id == 0 ? "Not registered" : "Registered",
                    BranchName = p.BranchName,
                    BranchId = p.BranchId,
                }).ToList();

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.PrinterAgentList.Count });
        }

        public IActionResult PrinterAgentDetail(string HostName, int Id)
        {
            var viewmodel = new PrinterAgentViewModel
            {
                HostName = HostName,
                Id = Id
            };
            return View(viewmodel);
        }

        public async Task<IActionResult> GetPrinterInfos(string HostName)
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<PrinterInfoListApiResponse>
                (ApiMethodName.PrinterAgent.GetPrinterInfos + HostName, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (apiResponse == null)
                return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });

            var paginatedData = apiResponse.PrinterInfoList.OrderBy(o => o.Name).Select(p =>
                new PrinterInfoViewModel
                {
                    Id = p.Id,
                    Name = p.Name,
                    PrinterAgentId = p.PrinterAgentId,
                    PrinterType = new PrinterTypeApiResponse
                    {
                        Id = p.PrinterType.Id,
                        TypeName = p.PrinterType.TypeName
                    },
                    CopyCount = p.CopyCount,
                    ICRType = p.ICRTypeId == 0 ? "" : EnumHelper.GetEnumDescription(typeof(PDFDocumentTypes), p.ICRTypeId.ToString())   
                }).ToList();

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.PrinterInfoList.Count });
        }

        #endregion

        #region AddUpdate

        public IActionResult PartialAddPrinterAgentBranch(string HostName, int Id, int BranchId)
        {
            var viewmodel = new AddUpdatePrinterAgentBranchViewModel
            {
                HostName = HostName,
                Id = Id,
                BranchId = BranchId
            };
            return PartialView("_AddUpdatePrinterAgentBranch", viewmodel);
        }

        [HttpPost]
        public async Task<IActionResult> AddPrinterAgent(AddUpdatePrinterAgentBranchViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var printerAgentApiRequest = new PrinterAgentApiRequest
            {
                HostName = viewModel.HostName,
                Id = 0,
                UserId = UserSession.UserId,
                BranchId = viewModel.BranchId
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<AddApiResponse>
                (printerAgentApiRequest, ApiMethodName.PrinterAgent.AddPrinterAgent, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (apiResponse == null)
                return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });
            else
                return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpPut]
        public async Task<IActionResult> UpdatePrinterAgent(AddUpdatePrinterAgentBranchViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new PrinterAgentApiRequest
            {
                Id = viewModel.Id,
                HostName = viewModel.HostName,
                UserId = UserSession.UserId,
                BranchId = viewModel.BranchId
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<UpdateApiResponse>
                (apiRequest, ApiMethodName.PrinterAgent.UpdatePrinterAgent, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!apiResponse.Result)
                return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        public IActionResult PartialAddPrinter(int PrinterAgentId, string PrinterAgentName)
        {
            var viewmodel = new AddUpdatePrinterInfoViewModel
            {
                PrinterAgentId = PrinterAgentId,
                PrinterAgentName = PrinterAgentName,
            };
            return PartialView("_AddPrinter", viewmodel);
        }

        public IActionResult PartialAddUpdatePrinter(int PrinterAgentId, string PrinterAgentName, int Id, string Name, int PrinterTypeId, int CopyCount)
        {
            var viewmodel = new AddUpdatePrinterInfoViewModel
            {
                PrinterAgentId = PrinterAgentId,
                PrinterAgentName = PrinterAgentName,
                PrinterId = Id,
                Name = Name,
                PrinterTypeId = PrinterTypeId,
                CopyCount = CopyCount
            };
            return PartialView("_AddUpdatePrinter", viewmodel);
        }

        public async Task<IActionResult> GetPrinterTypes()
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<PrinterTypeListApiResponse>
                (ApiMethodName.PrinterAgent.GetPrinterTypes, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (apiResponse == null)
                return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });

            return Json(apiResponse.PrinterType.Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.TypeName }));
        }                

        public async Task<IActionResult> GetClientPrinterList(string PrinterAgentName)
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ClientPrinterListApiResponse>
                (ApiMethodName.PrinterAgent.GetClientPrinterList + PrinterAgentName, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (apiResponse.ClientPrinterList == null)
            {
                return Json(new List<SelectListItem>());
            }

            return Json(apiResponse.ClientPrinterList.Select(x => new SelectListItem { Value = x, Text = x }));
        }

        [HttpPost]
        public async Task<IActionResult> CreatePrinterInfo(AddUpdatePrinterInfoViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new PrinterInfoApiRequest
            {
                Id = 0,
                PrinterTypeId = viewModel.PrinterTypeId,
                PrinterAgentId = viewModel.PrinterAgentId,
                Name = viewModel.Name,
                UserId = UserSession.UserId,
                CopyCount = viewModel.CopyCount,
                ICRTypeId = viewModel.ICRTypeId
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<PrinterAgentApiResponse>
                (apiRequest, ApiMethodName.PrinterAgent.CreatePrinterInfo, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (apiResponse == null)
                return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });
            else
                return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpPut]
        public async Task<IActionResult> UpdatePrinterInfo(AddUpdatePrinterInfoViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new PrinterInfoApiRequest
            {
                Id = viewModel.PrinterId,
                PrinterTypeId = viewModel.PrinterTypeId,
                PrinterAgentId = viewModel.PrinterAgentId,
                Name = viewModel.Name,
                UserId = UserSession.UserId,
                CopyCount = viewModel.CopyCount,
                ICRTypeId = viewModel.ICRTypeId
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<UpdateApiResponse>
                (apiRequest, ApiMethodName.PrinterAgent.UpdatePrinterInfo, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!apiResponse.Result)
                return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeletePrinterAgent(int Id)
        {
            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<DeleteApiResponse>
                (ApiMethodName.PrinterAgent.DeletePrinterAgent + Id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!apiResponse.Result)
                return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });
            else
                return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpDelete]
        public async Task<IActionResult> DeletePrinterInfo(int Id)
        {
            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<DeleteApiResponse>
                (ApiMethodName.PrinterAgent.DeletePrinterInfo + Id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!apiResponse.Result)
                return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });
            else
                return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion
    }
}
