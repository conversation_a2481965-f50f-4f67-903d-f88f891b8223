﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.Contracts.Entities.Constants;
using Kendo.Mvc.UI;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Requests;
using System.Linq;
using System.Collections.Generic;
using Portal.Gateway.UI.Areas.Management.ViewModels.General;
using Kendo.Mvc.Extensions;
using Portal.Gateway.UI.Validators;
using Portal.Gateway.UI.Enums;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.UI.Models;
using Portal.Gateway.Resources;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.UI.Areas.Management.ViewModels.Role;
using Portal.Gateway.ApiModel.Requests.Management.Role;
using Portal.Gateway.ApiModel.Responses.Management.Role;
using Microsoft.Extensions.Caching.Memory;
using Portal.Gateway.Contracts.Attributes;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class RoleController : BaseController<RoleController>
    {
        public RoleController(
               IOptions<AppSettings> appSettings,
               ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        #region Add

        public IActionResult PartialAddRole()
        {
            var viewModel = new AddRoleViewModel();

            foreach (var item in EnumHelper.GetEnumAsDictionary(typeof(Language)))
            {
                viewModel.RoleTranslations.Add(new AddRoleTranslationViewModel { LanguageId = item.Key });
            }

            return PartialView("_AddRole", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddRole(AddRoleViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddRoleApiRequest
            {
                RoleTranslations = viewModel.RoleTranslations.Select(p => new AddRoleTranslationApiRequest
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                    Description = p.Description
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<EmptyApiResponse>>
                (apiRequest, ApiMethodName.Management.AddRole, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateRole(string encryptedRoleId)
        {
            int id = encryptedRoleId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<RoleApiResponse>>
                (ApiMethodName.Management.GetRole + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdateRoleViewModel
            {
                Id = apiResponse.Data.Id,
                RoleTranslations = apiResponse.Data.RoleTranslations.Select(p => new UpdateRoleTranslationViewModel
                {
                    Id = p.Id,
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                    Description = p.Description
                }).ToList()
            };

            return PartialView("_UpdateRole", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateRole(UpdateRoleViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateRoleApiRequest
            {
                Id = viewModel.Id,
                RoleTranslations = viewModel.RoleTranslations.Select(p => new UpdateRoleTranslationApiRequest
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                    Description = p.Description
                }).ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<EmptyApiResponse>>
                (apiRequest, ApiMethodName.Management.UpdateRole, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteRole(string encryptedRoleId)
        {
            int id = encryptedRoleId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Management.DeleteRole + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> PartialRole(string encryptedRoleId)
        {
            int id = encryptedRoleId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<RoleApiResponse>>
                (ApiMethodName.Management.GetRole + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new RoleViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Name = apiResponse.Data.Name,
                Description = apiResponse.Data.Description
            };

            return PartialView("_Role", viewModel);
        }

        [ActionAttribute(IsMenuItem = true)]
        public async Task<IActionResult> List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedRoles([DataSourceRequest] DataSourceRequest request, FilterRoleViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedRolesApiRequest
            {
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<RolesApiResponse>>>
                (apiRequest, ApiMethodName.Management.GetPaginatedRoles, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<RoleViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().Roles
                    .Select(p => new RoleViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        Name = p.Name,
                        Description = p.Description
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #endregion
    }
}