﻿using Gateway.Core.Pagination;
using Gateway.External.Application.Appointment.Dto;

namespace Gateway.External.Application.User.Dto.Request
{
    public class UserLogoutRequest:BaseServiceRequest
    {
        public string? DeviceId { get; set; }
    }

    public class UserRegisterRequest: BaseServiceRequest
    {
        public string Email { get; set; }
        public string Password { get; set; }
        public string ConfirmPassword { get; set; }
        public string? Surname { get; set; }
        public string? Name { get; set; }
        public int NationalityId { get; set; }
    }

    public class UpdateUserProfileRequest : BaseServiceRequest
    {
        public string? Surname { get; set; }
        public string? Name { get; set; }
        public int NationalityId { get; set; }
    }

    public class GetUserProfileRequest: BaseServiceRequest { }

    public class AddUpdateUserDeviceEntryRequest : BaseServiceRequest
    {
        public string Location { get; set; }
        public string RegistryToken { get; set; }
        public string DeviceLanguage { get; set; }
        public string DeviceId { get; set; }
    }

    public class GetUserPushNotificationsRequest : BaseServiceRequest
    {
        public PaginationRequest Pagination { get; set; }
    }

    public class UpdateUserSettingsRequest : BaseServiceRequest 
    {
        public bool IsAllowedSendNotification { get; set; }
        public bool IsAllowedSendSms { get; set; }
        public bool IsAllowedSendEmail { get; set; }
    }
}
