﻿using System;
using System.Collections.Generic;

namespace Portal.Gateway.ApiModel.Responses.NotificationManagement.Notification
{
    public class NotificationApiResponse
    {
        public NotificationApiResponse()
        {
            Translations = new List<NotificationTranslationApiResponse>();
        }
        public string EncryptedId { get; set; }
        public string NotificationNumber { get; set; }
        public string NotificationTitle { get; set; }
        public int StatusId { get; set; }
        public string Location { get; set; }
        public int? LocationId { get; set; }
        public string Nationality { get; set; }
        public int? NationalityId { get; set; }
        public string SendTime { get; set; }
        public string CreatedBy { get; set; }
        public string Languages { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? ScheduledAt { get; set; }
        public DateTime? SendAt { get; set; }
        public List<NotificationTranslationApiResponse> Translations { get; set; }
    }

    public class NotificationTranslationApiResponse
    {
        public int Id { get; set; }
        public string Language { get; set; }
        public int LanguageId { get; set; }
        public string Subject { get; set; }
        public string Name { get; set; }
        public bool IsActive { get; set; }
    }
}
