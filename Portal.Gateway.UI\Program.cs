using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using System;
using Serilog;
using Gateway.Logger.Core.Models;
using Gateway.Logger.Core.Configuration;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;


namespace Portal.Gateway.UI
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

            var environmentFile = environment != null ? $"appsettings.{environment}.json" : "appsettings.json";

            var configuration = new ConfigurationBuilder()
                .AddJsonFile(environmentFile, optional: true, reloadOnChange: true)
                .AddJsonFile("config/appsettings.json", optional: true, reloadOnChange: true)
                .AddEnvironmentVariables()
                .AddCommandLine(args)
                .Build();

            var builder = WebApplication.CreateBuilder(args);

            // Configure Kestrel
            builder.WebHost.ConfigureKestrel(serverOptions =>
            {
                serverOptions.Limits.MinRequestBodyDataRate = null; // Disables the minimum rate
                serverOptions.Limits.MaxRequestBodySize = 250 * 1024 * 1024; // Set limit to 200 MB
            });

            Log.Logger = LoggerConfigurationBuilder.GetLoggerConfiguration(new LogConfiguration("Portal.Gateway.UI", Environment.MachineName, configuration));

            try
            {
                Log.Warning("Portal.Gateway.UI started...");

                var app = CreateHostBuilder(args).Build();

                var logger = app.Services.GetRequiredService<ILogger<Program>>();
                var lifetime = app.Services.GetRequiredService<IHostApplicationLifetime>();

                lifetime.ApplicationStarted.Register(() =>
                {
                    logger.LogInformation("Application fully started. Cache is ready.");
                });

                lifetime.ApplicationStopping.Register(() =>
                {
                    logger.LogInformation("Application is shutting down...");
                });

                app.Run();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Portal.Gateway.UI terminated unexpectedly...");
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
            .UseSerilog()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("config/appsettings.json", optional: true, reloadOnChange: true);
            })
            .ConfigureWebHostDefaults(webBuilder =>
            {
                webBuilder.UseStartup<Startup>()
                    .UseKestrel(options =>
                    {
                        options.Limits.MaxRequestBodySize = long.MaxValue;
                    })
                    .UseIISIntegration();
            });
    }
}

