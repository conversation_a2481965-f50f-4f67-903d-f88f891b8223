﻿using System;

namespace Gateway.External.Entity.Entities.CustomerUser
{
    public class CustomerUserDevice
    {
        public int Id { get; set; }
        public int CustomerUserId { get; set; }
        public string DeviceId { get; set; }
        public int LocationId { get; set; }
        public int LanguageId { get; set; }
        public string? RegistryToken { get; set; }
        public string? CreatedBy { get; set; }

        public DateTime? CreatedAt { get; set; }

        public string? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }
        public bool IsActive { get; set; }

        public bool IsDeleted { get; set; }

        public string? DeletedBy { get; set; }

        public DateTime? DeletedAt { get; set; }
        public CustomerUser CustomerUser { get; set; }

    }
}
