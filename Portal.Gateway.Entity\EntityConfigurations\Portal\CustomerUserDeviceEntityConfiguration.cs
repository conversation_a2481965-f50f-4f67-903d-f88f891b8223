﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Portal.Gateway.Entity.Entities.Portal;

namespace Portal.Gateway.Entity.EntityConfigurations.Portal
{
    internal class CustomerUserDeviceEntityConfiguration : IEntityTypeConfiguration<CustomerUserDevice>
    {
        public void Configure(EntityTypeBuilder<CustomerUserDevice> builder)
        {
            builder.ToTable("CustomerUserDevice");

            #region StaticAuditableEntityConfig

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).IsRequired().ValueGeneratedOnAdd();
            builder.Property(e => e.IsActive).IsRequired();
            builder.Property(e => e.IsDeleted).IsRequired();
            builder.Property(e => e.CreatedBy);
            builder.Property(e => e.CreatedAt).HasColumnType("timestamp with time zone");
            builder.Property(e => e.UpdatedBy);
            builder.Property(e => e.UpdatedAt).HasColumnType("timestamp with time zone");
            builder.Property(e => e.DeletedBy);
            builder.Property(e => e.DeletedAt).HasColumnType("timestamp with time zone");

            #endregion

            builder.HasOne(d => d.CustomerUser)
                .WithMany(p => p.CustomerUserDevices)
                .HasForeignKey(d => d.CustomerUserId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            builder.Property(e => e.LanguageId).HasDefaultValueSql("0");

            builder.Property(e => e.RegistryToken).HasColumnType("citext");
            builder.Property(e => e.DeviceId).HasColumnType("citext");
        }
    }
}
