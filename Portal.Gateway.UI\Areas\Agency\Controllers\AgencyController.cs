﻿using FluentValidation.Resources;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Agency.Agency;
using Portal.Gateway.ApiModel.Requests.DocumentManagement;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Agency.Agency;
using Portal.Gateway.ApiModel.Responses.DocumentManagement;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Agency.ViewModels.Agency;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Agency.Controllers
{

    [Area("Agency")]
    public class AgencyController : BaseController<AgencyController>
    {
        public AgencyController(
          IOptions<AppSettings> appSettings,
          ICacheHelper cacheHelper)
        : base(appSettings, cacheHelper)
        {

        }

        #region Add

        public IActionResult PartialAddAgency()
        {
            var viewModel = new AddAgencyViewModel();
           
            return PartialView("_AddAgency", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddAgency(AddAgencyViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new AddAgencyApiRequest
            {
                AgencyTypeId = viewModel.AgencyTypeId,
                AgencyCategoryId = viewModel.AgencyCategoryId,
                CountryId = viewModel.CountryId,
                Description = viewModel.Description,
                Address = viewModel.Address,
                AuthorizedPersonName = viewModel.AuthorizedPersonName,
                AuthorizedPersonSurname = viewModel.AuthorizedPersonSurname,
                AuthorizedPersonEmail = viewModel.AuthorizedPersonEmail,
                AuthorizedPersonPhoneNumber = viewModel.AuthorizedPersonPhoneNumber,
                AuthorizedPersonPosition = viewModel.AuthorizedPersonPosition,
                CityName = viewModel.CityName,
                Email = viewModel.Email,
                InvoiceNumber = viewModel.InvoiceNumber,
                Telephone = viewModel.Telephone,
                Name = viewModel.Name,
                Note = viewModel.Note,
                ActivationStatusId = viewModel.ActivationStatusId
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<EmptyApiResponse>>
                (apiRequest, ApiMethodName.Agency.AddAgency, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateAgency(string encryptedId)
        {
            int id = encryptedId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<AgencyApiResponse>>
                (ApiMethodName.Agency.GetAgency + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new UpdateAgencyViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                IsActive = apiResponse.Data.IsActive,
                AgencyTypeId = apiResponse.Data.AgencyTypeId,
                AgencyCategoryId = apiResponse.Data.AgencyCategoryId,
                CountryId = apiResponse.Data.CountryId,
                Description = apiResponse.Data.Description,
                Address = apiResponse.Data.Address,
                AuthorizedPersonName = apiResponse.Data.AuthorizedPersonName,
                AuthorizedPersonSurname = apiResponse.Data.AuthorizedPersonSurname,
                CityName = apiResponse.Data.CityName,
                Email = apiResponse.Data.Email,
                InvoiceNumber = apiResponse.Data.InvoiceNumber,
                Telephone = apiResponse.Data.Telephone,
                Name = apiResponse.Data.Name,
                Note = apiResponse.Data.Note,
                ActivationStatusId = apiResponse.Data.ActivationStatusId

            };

            return PartialView("_UpdateAgency", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateAgency(UpdateAgencyViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateAgencyApiRequest
            {
                Id = viewModel.EncryptedId.ToDecryptInt(),
                IsActive = viewModel.IsActive,
                AgencyTypeId = viewModel.AgencyTypeId,
                AgencyCategoryId = viewModel.AgencyCategoryId,
                CountryId = viewModel.CountryId,
                Description = viewModel.Description,
                Address = viewModel.Address,
                AuthorizedPersonName = viewModel.AuthorizedPersonName,
                AuthorizedPersonSurname = viewModel.AuthorizedPersonSurname,
                CityName = viewModel.CityName,
                Email = viewModel.Email,
                InvoiceNumber = viewModel.InvoiceNumber,
                Telephone = viewModel.Telephone,
                Name = viewModel.Name,
                Note = viewModel.Note,
                ActivationStatusId = viewModel.ActivationStatusId

            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<EmptyApiResponse>>
                (apiRequest, ApiMethodName.Agency.UpdateAgency, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteAgency(string encryptedId)
        {
            int id = encryptedId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .DeleteAsync<ApiResponse<DeleteApiResponse>>
                (ApiMethodName.Agency.DeleteAgency + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Get

        public async Task<IActionResult> PartialAgency(string encryptedId)
        {
            int id = encryptedId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<AgencyApiResponse>>
                (ApiMethodName.Agency.GetAgency + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Content(result.Message);

            var viewModel = new AgencyViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                AgencyType = apiResponse.Data.AgencyType,
                AgencyCategoryId = apiResponse.Data.AgencyCategoryId,
                Country = apiResponse.Data.Country,
                Description = apiResponse.Data.Description,
                Address = apiResponse.Data.Address,
                AuthorizedPersonName = apiResponse.Data.AuthorizedPersonName,
                AuthorizedPersonSurname = apiResponse.Data.AuthorizedPersonSurname,
                CityName = apiResponse.Data.CityName,
                Email = apiResponse.Data.Email,
                InvoiceNumber = apiResponse.Data.InvoiceNumber,
                Telephone = apiResponse.Data.Telephone,
                Name = apiResponse.Data.Name,
                ActivationStatusId = apiResponse.Data.ActivationStatusId,
                Note = apiResponse.Data.Note,
                Documents = apiResponse.Data.Documents.Select(q => new AgencyViewModel.Document()
                {
                    EncryptedDocumentId = q.DocumentId.ToEncrypt(),
                    EncryptedFileId = q.FileId.ToEncrypt(),
                    FileTypeId = q.FileTypeId,
                    DisplayFileName = q.DisplayFileName,
                    DisplayUniqueFileName = q.DisplayUniqueFileName,
                    IsDeleted = q.IsDeleted
                }).ToList()
            };

            return PartialView("_Agency", viewModel);
        }

        #endregion

        #region List

        public ActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedAgencies([DataSourceRequest] DataSourceRequest request, FilterAgencyViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedAgencyApiRequest
            {
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                },
                AgencyTypeId = filterViewModel.FilterAgencyTypeId,
                Name = filterViewModel.FilterName
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedAgencyApiResponse>>>
                (apiRequest, ApiMethodName.Agency.GetPaginatedAgencies, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<AgencyItemViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().Agencies
                    .Select(p => new AgencyItemViewModel
                    {
                        EncryptedId = p.Id.ToEncrypt(),
                        ActivationStatusId = p.ActivationStatusId,
                        AgencyType = p.AgencyType,
                        AgencyCategoryId = p.AgencyCategoryId,
                        Country = p.Country,
                        Address = p.Address,
                        AuthorizedPersonName = p.AuthorizedPersonName,
                        AuthorizedPersonSurname = p.AuthorizedPersonSurname,
                        CityName = p.CityName,
                        Email = p.Email,
                        Telephone = p.Telephone,
                        Name = p.Name
                    }).ToList();
            }

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        #endregion

        public async Task<IActionResult> DownloadFile(string encryptedDocumentId)
        {
            var documentsApiRequest = new DocumentsApiRequest
            {
                Ids = new List<int> { encryptedDocumentId.ToDecryptInt() },
                IsFileContentIncluded = true
            };

            var getDocumentsApiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<DocumentsApiResponse>>
                (documentsApiRequest, ApiMethodName.DocumentManagement.GetDocuments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await getDocumentsApiResponse.Validate(out ResultModel getDocumentsResult).ConfigureAwait(false))
                return Json(null);

            var document = getDocumentsApiResponse.Data.Documents.FirstOrDefault();

            if (document == null || document.FileContent == null)
                return Json(null);

            return File(document.FileContent, "application/octet-stream", document.UniqueFileNameWithExtension);
        }
    }
}
