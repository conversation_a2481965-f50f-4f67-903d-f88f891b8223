﻿using ClosedXML.Excel;
using Gateway.Extensions;
using Gateway.Http;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.Management.WhiteList;
using Portal.Gateway.ApiModel.Requests.QmsFolder;
using Portal.Gateway.ApiModel.Responses.Management.WhiteList;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.WhiteList;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class WhiteListController : BaseController<WhiteListController>
    {
        public WhiteListController(
            IOptions<AppSettings> appSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {
        }

        #region Get

        [Action(IsMenuItem = true)]
        public IActionResult List()
        {
            if (UserSession.BranchId == null)
            {
                return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
            }

            var filterViewModel = new FilterWhiteListViewModel
            {
                BranchId = (int)UserSession.BranchId
            };

            return View(filterViewModel);
        }

        public async Task<IActionResult> GetPaginatedWhiteListsByBranch([DataSourceRequest] DataSourceRequest request, FilterWhiteListViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedWhiteListApiRequest
            {
                BranchResourceId = filterViewModel.BranchId,
                FilterPassportNumber = filterViewModel.FilterPassportNumber,
                FilterName = filterViewModel.FilterName,
                FilterSurname = filterViewModel.FilterSurname,
                FilterNationalityId = filterViewModel.FilterNationalityId,
                FilterStartDate = filterViewModel.FilterStartDate,
                FilterEndDate = filterViewModel.FilterEndDate,
                FilterBranchId = filterViewModel.FilterBranchId,
                Pagination = new QmsPaginationApiRequest
                {
                    PageNumber = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = "Name",
                    Ascending = true
                }
            };

            var apiResponse = await RestHttpClient.Create().Post<GetPaginatedWhiteListsByBranchResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.GetPaginatedWhiteListsByBranch, QMSApiDefaultRequestHeaders, apiRequest);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var paginatedData = apiResponse.Data.Select(p =>
                new WhiteListViewModel
                {
                    EncryptedId = p.Id.ToEncrypt(),
                    Id = p.Id,
                    BranchId = p.BranchId,
                    PassportNumber = p.PassportNumber,
                    Name = p.Name,
                    Surname = p.Surname,
                    Nationality = p.Nationality,
                    OldWhitelistMember = p.ExpiryDate < DateTime.Now
                }).ToList();

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.TotalNumberOfRecords });
        }

        public async Task<IActionResult> GetWhiteList(string encryptedId)
        {
            var resourceId = encryptedId.ToDecryptInt();

            var apiResponse = await RestHttpClient.Create().Get<GetWhiteListResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.GetWhiteList + resourceId, QMSApiDefaultRequestHeaders);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var viewModel = new WhiteListViewModel
            {
                PassportNumber = apiResponse.Data.PassportNumber,
                Name = apiResponse.Data.Name,
                Surname = apiResponse.Data.Surname,
                Nationality = apiResponse.Data.Nationality,
                NationalityId = apiResponse.Data.NationalityId,
                BiometricDataId = apiResponse.Data?.BiometricDataId,
                BiometricData = apiResponse.Data?.BiometricDataId == null ? "" : EnumHelper.GetEnumDescription(typeof(QMSWhiteListBiometricData), apiResponse.Data.BiometricDataId.ToString()),
                DocumentExemptionId = apiResponse.Data?.DocumentExemptionId,
                DocumentExemption = apiResponse.Data?.DocumentExemptionId == null ? "" : EnumHelper.GetEnumDescription(typeof(QMSWhiteListDocumentExemption), apiResponse.Data.DocumentExemptionId.ToString()),
                RelevantInstitutionPerson = apiResponse.Data?.RelevantInstitutionPerson,
                MissionNotes = apiResponse.Data?.MissionNotes
            };

            return PartialView("_WhiteList", viewModel);
        }

        public async Task<IActionResult> PartialUpdateWhiteList(string encryptedId, int branchId)
        {
            var resourceId = encryptedId.ToDecryptInt();

            var apiResponse = await RestHttpClient.Create().Get<GetWhiteListResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.GetWhiteList + resourceId, QMSApiDefaultRequestHeaders);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var viewModel = new UpdateWhiteListViewModel
            {
                EncryptedId = encryptedId,
                BranchId = branchId,
                PassportNumber = apiResponse.Data.PassportNumber,
                Name = apiResponse.Data.Name,
                Surname = apiResponse.Data.Surname,
                NationalityId = apiResponse.Data.NationalityId,
                BiometricDataId = apiResponse.Data?.BiometricDataId,
                DocumentExemptionId = apiResponse.Data?.DocumentExemptionId,
                RelevantInstitutionPerson = apiResponse.Data?.RelevantInstitutionPerson,
                MissionNotes = apiResponse.Data?.MissionNotes
            };

            return PartialView("_UpdateWhiteList", viewModel);
        }

        #endregion

        #region Add

        public IActionResult PartialAddWhiteListsCount(int BranchId)
        {

            var viewModel = new AddWhiteListsViewModel
            {
                BranchId = BranchId,
                WhiteListsCount = 1
            };

            return PartialView("_AddWhiteListsCount", viewModel);
        }

        public IActionResult PartialAddWhiteLists(int BranchId, int WhiteListsCount)
        {
            var viewModel = new AddWhiteListsViewModel
            {
                BranchId = BranchId,
                WhiteListsCount = WhiteListsCount
            };

            return PartialView("_AddWhiteLists", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddWhiteLists(AddWhiteListsViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var request = new AddWhiteListRequestModel
            {
                BranchId = viewModel.BranchId,
                WhiteListsCount = viewModel.WhiteLists.Count(a => a.WhiteListEnabled),
                WhiteLists = viewModel.WhiteLists.Where(a => a.WhiteListEnabled).Select(p => new AddWhiteListRequestModel.AddWhiteLists()
                {
                    PassportNumber = p.PassportNumber,
                    Name = p.Name,
                    Surname = p.Surname,
                    NationalityId = p.NationalityId,
                    BiometricDataId = p.BiometricDataId,
                    DocumentExemptionId = p.DocumentExemptionId,
                    RelevantInstitutionPerson = p.RelevantInstitutionPerson,
                    MissionNotes = p.MissionNotes
                }).ToList()
            };

            var apiResponse = await RestHttpClient.Create().Post<AddWhiteListResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.AddWhiteList, QMSApiDefaultRequestHeaders, request);

            if (apiResponse.Data == null)
                return Json(new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Danger });
            else if (apiResponse.Data.ExistingWhiteListPassportNumbers != null && apiResponse.Data.ExistingWhiteListPassportNumbers.Any())
            {
                return Json(new ResultModel { Data = apiResponse.Data.ExistingWhiteListPassportNumbers, Message = apiResponse.Message, ResultType = ResultType.Danger });
            }
            else
                return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        [HttpPut]
        public async Task<IActionResult> UpdateWhiteList(UpdateWhiteListViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateWhiteListRequestModel
            {
                Id = viewModel.EncryptedId.ToDecryptInt(),
                BranchId = viewModel.BranchId,
                PassportNumber = viewModel.PassportNumber,
                Name = viewModel.Name,
                Surname = viewModel.Surname,
                NationalityId = viewModel.NationalityId,
                BiometricDataId = viewModel.BiometricDataId,
                DocumentExemptionId = viewModel.DocumentExemptionId,
                RelevantInstitutionPerson = viewModel.RelevantInstitutionPerson,
                MissionNotes = viewModel.MissionNotes
            };

            var apiResponse = await RestHttpClient.Create().Put<UpdateWhiteListResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.UpdateWhiteList, QMSApiDefaultRequestHeaders, apiRequest);

            if (apiResponse.Data == null)
                return Json(new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpPatch]
        public async Task<IActionResult> UpdateWhiteListExpireTime([FromBody] List<WhiteListData> modelArr)
        {
            if (modelArr == null || modelArr.Count == 0)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var request = new UpdateWhiteListExpireTimeRequestModel
            {
                WhiteLists = modelArr
            };

            var apiResponse = await RestHttpClient.Create().Patch<UpdateWhiteListExpireTimeResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.UpdateWhiteListExpireTime, QMSApiDefaultRequestHeaders, request);

            if (apiResponse.Data == false )
                return Json(new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteWhiteList(string EncryptedId)
        {
            if (EncryptedId.IsNullOrWhitespace())
            {
                return Content("Missing WhiteList id");
            }

            var resourceId = EncryptedId.ToDecryptInt();

            var apiResponse = await RestHttpClient.Create().Delete<DeleteWhiteListResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.DeleteWhiteList + resourceId, QMSApiDefaultRequestHeaders);

            if (apiResponse.Data == null)
                return Json(apiResponse.Message);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Report

        [HttpPost]
        public async Task<FileContentResult> QmsWhiteListReport(FilterWhiteListViewModel filterViewModel)
        {
            var reportName = SiteResources.WhitelistReport.ToTitleCase();
            var apiRequest = new PaginatedWhiteListApiRequest
            {
                BranchResourceId = filterViewModel.BranchId,
                FilterPassportNumber = filterViewModel.FilterPassportNumber,
                FilterName = filterViewModel.FilterName,
                FilterSurname = filterViewModel.FilterSurname,
                FilterNationalityId = filterViewModel.FilterNationalityId,
                FilterStartDate = filterViewModel.FilterStartDate,
                FilterEndDate = filterViewModel.FilterEndDate,
                FilterBranchId = filterViewModel.FilterBranchId,
            };

            var apiResponse = await RestHttpClient.Create().Post<QmsWhiteListReportResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.QmsWhiteListReport, QMSApiDefaultRequestHeaders, apiRequest);

            using (var workbook = new XLWorkbook())
            {
                var worksheet = workbook.Worksheets.Add(reportName);
                var currentRow = 1;

                worksheet.Cell(currentRow, 1).Value = SiteResources.OrderNumber.ToTitleCase();
                worksheet.Cell(currentRow, 2).Value = SiteResources.PassportNumber.ToTitleCase();
                worksheet.Cell(currentRow, 3).Value = SiteResources.Name.ToTitleCase();
                worksheet.Cell(currentRow, 4).Value = SiteResources.Surname.ToTitleCase();
                worksheet.Cell(currentRow, 5).Value = SiteResources.Nationality.ToTitleCase();
                worksheet.Cell(currentRow, 6).Value = SiteResources.DocumentExemption.ToTitleCase();
                worksheet.Cell(currentRow, 7).Value = SiteResources.BiometricData.ToTitleCase();
                worksheet.Cell(currentRow, 8).Value = SiteResources.RelevantInstitutionPerson.ToTitleCase();
                worksheet.Cell(currentRow, 9).Value = SiteResources.MissionNotes.ToTitleCase();
                worksheet.Cell(currentRow, 10).Value = SiteResources.CreatedBy.ToTitleCase();
                worksheet.Cell(currentRow, 11).Value = SiteResources.CreatedAt.ToTitleCase();
                worksheet.Cell(currentRow, 12).Value = SiteResources.UpdatedBy.ToTitleCase();
                worksheet.Cell(currentRow, 13).Value = SiteResources.UpdatedAt.ToTitleCase();
                worksheet.Cell(currentRow, 14).Value = SiteResources.UpdatedInformation.ToTitleCase();

                var rangeTableHeader = worksheet.Range(worksheet.Cell(currentRow, 1), worksheet.Cell(currentRow, 14));
                rangeTableHeader.Style.Font.SetBold();
                currentRow++;

                var j = 2;
                for (int i = 0; i < apiResponse.Data.Count; i++)
                {
                    var data = apiResponse.Data.ElementAt(i);                   
                    if(i == 0)
                    {
                        worksheet.Cell(currentRow, 1).Value = 1;
                        worksheet.Cell(currentRow, 2).SetValue(data.PassportNumber);
                        worksheet.Cell(currentRow, 3).Value = data.WhitelistName == null ? "" : data.WhitelistName;
                        worksheet.Cell(currentRow, 4).Value = data.WhitelistSurname == null ? "" : data.WhitelistSurname;
                        worksheet.Cell(currentRow, 5).Value = data.Nationality;
                        worksheet.Cell(currentRow, 6).Value = data.DocumentExemption == null ? "" : EnumHelper.GetEnumDescription(typeof(QMSWhiteListDocumentExemption), data.DocumentExemption.ToString());
                        worksheet.Cell(currentRow, 7).Value = data.BiometricData == null ? "" : EnumHelper.GetEnumDescription(typeof(QMSWhiteListBiometricData), data.BiometricData.ToString());
                        worksheet.Cell(currentRow, 8).Value = data.RelevantInstitutionPerson == null ? "" : data.RelevantInstitutionPerson;
                        worksheet.Cell(currentRow, 9).Value = data.MissionNotes == null ? "" : data.MissionNotes;
                        worksheet.Cell(currentRow, 10).Value = data.CreatedBy;
                        worksheet.Cell(currentRow, 11).Value = data.CreatedAt.AddHours(3);
                        worksheet.Cell(currentRow, 11).Style.DateFormat.Format = SiteResources.DateTimePickerFormatView;
                        worksheet.Cell(currentRow, 12).Value = data.HistoryCreatedBy == null ? "" : data.HistoryCreatedBy;
                        worksheet.Cell(currentRow, 13).Value = data.HistoryCreatedAt == null ? "" : data.HistoryCreatedAt.Value.AddHours(3);
                        worksheet.Cell(currentRow, 13).Style.DateFormat.Format = SiteResources.DateTimePickerFormatView;
                        worksheet.Cell(currentRow, 14).Value = data.UpdatedInformation == null ? "" : data.UpdatedInformation;

                        currentRow++;
                    }
                    else
                    {
                        if (data.WhiteListId != apiResponse.Data.ElementAt(i - 1).WhiteListId)
                        {
                            worksheet.Cell(currentRow, 1).Value = j++;
                            worksheet.Cell(currentRow, 2).SetValue(data.PassportNumber);
                            worksheet.Cell(currentRow, 3).Value = data.WhitelistName == null ? "" : data.WhitelistName;
                            worksheet.Cell(currentRow, 4).Value = data.WhitelistSurname == null ? "" : data.WhitelistSurname;
                            worksheet.Cell(currentRow, 5).Value = data.Nationality;
                            worksheet.Cell(currentRow, 6).Value = data.DocumentExemption == null ? "" : EnumHelper.GetEnumDescription(typeof(QMSWhiteListDocumentExemption), data.DocumentExemption.ToString());
                            worksheet.Cell(currentRow, 7).Value = data.BiometricData == null ? "" : EnumHelper.GetEnumDescription(typeof(QMSWhiteListBiometricData), data.BiometricData.ToString());
                            worksheet.Cell(currentRow, 8).Value = data.RelevantInstitutionPerson == null ? "" : data.RelevantInstitutionPerson;
                            worksheet.Cell(currentRow, 9).Value = data.MissionNotes == null ? "" : data.MissionNotes;
                            worksheet.Cell(currentRow, 10).Value = data.CreatedBy;
                            worksheet.Cell(currentRow, 11).Value = data.CreatedAt.AddHours(3);
                            worksheet.Cell(currentRow, 11).Style.DateFormat.Format = SiteResources.DateTimePickerFormatView;
                            worksheet.Cell(currentRow, 12).Value = data.HistoryCreatedBy == null ? "" : data.HistoryCreatedBy;
                            worksheet.Cell(currentRow, 13).Value = data.HistoryCreatedAt == null ? "" : data.HistoryCreatedAt.Value.AddHours(3);
                            worksheet.Cell(currentRow, 13).Style.DateFormat.Format = SiteResources.DateTimePickerFormatView;
                            worksheet.Cell(currentRow, 14).Value = data.UpdatedInformation == null ? "" : data.UpdatedInformation;

                            currentRow++;
                        }
                        else
                        {
                            worksheet.Cell(currentRow, 12).Value = data.HistoryCreatedBy == null ? "" : data.HistoryCreatedBy;
                            worksheet.Cell(currentRow, 13).Value = data.HistoryCreatedAt == null ? "" : data.HistoryCreatedAt.Value.AddHours(3);
                            worksheet.Cell(currentRow, 13).Style.DateFormat.Format = SiteResources.DateTimePickerFormatView;
                            worksheet.Cell(currentRow, 14).Value = data.UpdatedInformation == null ? "" : data.UpdatedInformation;

                            currentRow++;
                        }
                    }

                }

                var rangeTable = worksheet.Range(worksheet.Cell(1, 1), worksheet.Cell(currentRow - 1, 14));
                rangeTable.Style.Border.SetTopBorder(XLBorderStyleValues.Thin);
                rangeTable.Style.Border.SetBottomBorder(XLBorderStyleValues.Thin);
                rangeTable.Style.Border.SetLeftBorder(XLBorderStyleValues.Thin);
                rangeTable.Style.Border.SetRightBorder(XLBorderStyleValues.Thin);
                rangeTable.Style.Border.SetInsideBorder(XLBorderStyleValues.Thin);
                rangeTable.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                var rangeAlignment = worksheet.Range(worksheet.Cell(1, 1), worksheet.Cell(currentRow - 1, 14));
                rangeAlignment.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);

                for (int i = 1; i <= 14; i++)
                {
                    worksheet.Column(i).AdjustToContents();
                }

                using (var stream = new MemoryStream())
                {
                    workbook.SaveAs(stream);
                    var content = stream.ToArray();
                    return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{reportName} - {DateTime.UtcNow.ToString("ddMMyyyy")}.xlsx");
                }
            }

        }

        #endregion
    }
}
