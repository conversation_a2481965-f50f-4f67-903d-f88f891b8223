﻿
$(function () {
    $("#filterNotificationList").click(function (e) {
        refreshGridgridNotification();
    });
    $("#clearNotificationList").click(function (e) {
        $("#FilterStartDate").val('');
        $("#FilterEndDate").val('');
        $("#FilterStatusId").data("kendoDropDownList").select(null);
        $("#FilterLocationId").data("kendoDropDownList").select(null);
        $("#FilterNationalityId").data("kendoDropDownList").select(null);
        refreshGridgridNotification();
    });

    $("#allCheckBox").change(function () {
        var isChecked = $(this).is(":checked");
        $(".active_checkbox input[type='checkbox']").prop("checked", isChecked);
    });

    toggleScheduleTime();

    $("#IsSendScheduled").change(function () {
        if ($(this).is(':checked')) {
            $('#IsSendNow').prop('checked', false);
        }

        toggleScheduleTime();
    });

    $('#IsSendNow').change(function () {
        if ($(this).is(':checked')) {
            $('#IsSendScheduled').prop('checked', false);
        }

        toggleScheduleTime();
    });
});

function toggleScheduleTime() {
    const isChecked = $("#IsSendScheduled").is(":checked");
    $("#scheduledTimeWrapper").toggle(isChecked);
}
function refreshGridgridNotification() {
    $('#gridNotification').data('kendoGrid').dataSource.read();
    $('#gridNotification').data('kendoGrid').refresh();
}

function partialDetailNotification(EncryptedId) {
    $.get('/NotificationManagement/Notification/GetNotificationDetail', { EncryptedId: EncryptedId },
        function (data) {
            $('#divPartialDetailNotification').html(data);
        }, 'html');
}

function partialAddNotification() {
    $.get('/NotificationManagement/Notification/PartialAddNotification', {},
        function (data) {
            $('#divPartialAddNotification').html(data);
        }, 'html');
}

function partialUpdateNotification(EncryptedId, IsDuplicated) {
    console.log("IsDuplicated:", IsDuplicated); // Verify the value in console
    $.get('/NotificationManagement/Notification/PartialUpdateNotification', {
        encryptedId: EncryptedId,
        isDuplicated: IsDuplicated
    }, function (data) {
        $('#divPartialUpdateNotification').html(data);
    }, 'html');
}

function partialSendNotification(EncryptedId) {
    $.get('/NotificationManagement/Notification/PartialSendNotification', { EncryptedId: EncryptedId },
        function (data) {
            $('#divPartialSendNotification').html(data);
        }, 'html');
}

//form submits

$("#formDeleteNotification").submit(function (e) {

    e.preventDefault();
    var form = $(this);
    var validator = form.kendoValidator().data("kendoValidator");

    if (!validator.validate()) return;

    $.ajax({
        type: "DELETE",
        url: "/NotificationManagement/Notification/DeleteNotification",
        data: form.serialize(),
        success: function (data) {
            if (data.Type === "success") {
                showNotification(data.Type, data.Message);
                refreshGridgridNotification();
            }
            else {
                bootbox.alert({
                    title: jsResources.Warning,
                    message: data.Message,
                    callback: function (result) { }
                });
            }
        },
        error: function (data) {
            showNotification('danger', jsResources.ErrorOccurred);
        }
    });

});

$("#formAddNotification").submit(function (e) {

    e.preventDefault();
    var form = $(this);

    $.ajax({
        type: "POST",
        url: "/NotificationManagement/Notification/AddNotification",
        data: form.serialize(),
        success: function (data) {
            if (data.Type === "success") {
                showNotification(data.Type, data.Message);
                $('#modalAddNotification').modal('hide');
                refreshGridgridNotification();
            }
            else {
                bootbox.alert({
                    title: jsResources.Warning,
                    message: data.Message,
                    callback: function (result) { }
                });
            }
        },
        error: function (data) {
            showNotification('danger', jsResources.ErrorOccurred);
        }
    });

});

$("#formUpdateNotification").submit(function (e) {
    e.preventDefault();
    var form = $(this);
    var isDuplicated = $('#IsDuplicated').val() === 'true';

    $.ajax({
        type: isDuplicated ? "POST" : "PUT",
        url: isDuplicated
            ? "/NotificationManagement/Notification/AddNotification"
            : "/NotificationManagement/Notification/UpdateNotification",
        data: form.serialize(),
        success: function (data) {
            if (data.Type === "success") {
                showNotification(data.Type, data.Message);
                $('#modalUpdateNotification').modal('hide');
                refreshGridgridNotification();
            }
            else {
                bootbox.alert({
                    title: jsResources.Warning,
                    message: data.Message,
                    callback: function (result) { }
                });
            }
        },
        error: function (data) {
            showNotification('danger', jsResources.ErrorOccurred);
        }
    });
});

$("#formSendNotification").submit(function (e) {

    e.preventDefault();
    var form = $(this);

    $.ajax({
        type: "POST",
        url: "/NotificationManagement/Notification/SendNotification",
        data: form.serialize(),
        success: function (data) {
            if (data.Type === "success") {
                showNotification(data.Type, data.Message);
                $('#modalSendNotification').modal('hide');
                refreshGridgridNotification();
            }
            else {
                bootbox.alert({
                    title: jsResources.Warning,
                    message: data.Message,
                    callback: function (result) { }
                });
            }
        },
        error: function (data) {
            showNotification('danger', jsResources.ErrorOccurred);
        }
    });

});

function deleteNotification(EncryptedId) {
    bootbox.confirm(jsResources.AreYouSureToDeleteThisRecord, function (result) {
        if (result) {
            $.ajax({
                type: "DELETE",
                url: "/NotificationManagement/Notification/DeleteNotification",
                data: { EncryptedId: EncryptedId },
                success: function (data) {
                    if (data.ResultType !== undefined && (data.ResultType === 2 || data.ResultType === 3)) {
                        bootbox.alert({
                            title: jsResources.Warning,
                            message: data.Message,
                            callback: function (result) { }
                        });
                        e.preventDefault();
                        return false;
                    }
                    else {
                        showNotification(data.Type, data.Message);
                        refreshGridgridNotification();
                    }
                },
                error: function (data) {
                    showNotification('danger', jsResources.ErrorOccurred);
                }
            });
        }
    });
}


//events
function onOpenChangeFocus(e) {
    $(document).off('focusin.bs.modal');
}

function onNotificationDataBound(e) {
    var dropdown = this;
    var selectedValue = dropdown.value();

    if (selectedValue) {
        onNotificationChange.call(dropdown, { sender: dropdown });
    }
}

function onNotificationChange(e) {
    var dropdown = $("#EncryptedId").data("kendoDropDownList");
    var selectedValue = dropdown.value();

    if (!selectedValue) {
        $(".informationDiv").hide();
        $('button[type="submit"]').prop("disabled", true);
        return;
    }

    var url = `/NotificationManagement/Notification/GetNotificationDetailForSend?encryptedId=` + selectedValue;
-
    $.ajax({
        url: url,
        type: 'GET',
        success: function (response) {
            console.log(response.Translations);
            fillTranslations(response.Translations);
            fillDropDowns(response.LocationId, response.NationalityId);

            $(".informationDiv").show(); 
            $('button[type="submit"]').prop("disabled", false);
        },
        error: function () {
            $(".informationDiv").hide();
            $('button[type="submit"]').prop("disabled", true);
        }
    });
}
function fillTranslations(translations) {
    for (let i = 0; i < translations.length; i++) {
        $(`[name='Translations[${i}].Id']`).val(translations[i].Id);
        $(`[name='Translations[${i}].LanguageId']`).val(translations[i].LanguageId);
        $(`[name='Translations[${i}].IsActive']`).prop("checked", translations[i].IsActive);
        $(`[name='Translations[${i}].Name']`).val(translations[i].Name);
        $(`[name='Translations[${i}].Subject']`).val(translations[i].Subject);
    }
}
function fillDropDowns(locationId, nationalityId) {
    var locationDropdown = $("#LocationId").data("kendoDropDownList");
    var nationalityDropdown = $("#NationalityId").data("kendoDropDownList");

    if (locationDropdown) locationDropdown.value(locationId);
    if (nationalityDropdown) nationalityDropdown.value(nationalityId);
}
