﻿using System;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using ClosedXML.Excel;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Portal.Gateway.ApiModel.Requests.Report;
using Portal.Gateway.ApiModel.Responses.Report;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Dto.Report.Common.Report_3;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.Report;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;

namespace Portal.Gateway.UI.Areas.Appointment.Controllers
{
    [Area("Appointment")]

    public class ScanCyleReportController : BaseController<ScanCyleReportController>
    {

        public ScanCyleReportController(
            ICacheHelper cacheHelper,
            IOptions<AppSettings> appSettings)
            : base(appSettings, cacheHelper)
        {

        }

        Regex rgx = new Regex("[^a-zA-Z0-9 ]");

        [ActionAttribute(IsMenuItem = true)]
        public IActionResult ScanCycleReport()
        {
            if (UserSession.BranchId == null)
            {
                return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
            }

            var filterViewModel = new ScanCyleReportReportViewModel();


            return View(filterViewModel);
        }

        [HttpPost]
        public async Task<IActionResult> CreateReport(ScanCyleReportReportViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
            {
                ClearModelStateErrors();
                ModelState.AddModelError("InvalidModel", ResultMessage.MissingOrInvalidData.ToDescription());

                return View("ScanCycleReport");

            }

            var apiRequest = new GenerateReportApiRequest
            {
                UserId = UserSession.UserId,
                ReportTypeId = 3
            };
            apiRequest.Request = new Report_ScanCycleRequest
            {
                BranchIds = viewModel.FilterBranchIds,
                StartDate = viewModel.FilterStartDate.GetValueOrDefault(),
                EndDate = viewModel.FilterEndDate.GetValueOrDefault(),
                ApplicationStatusIds = viewModel.FilterApplicationStatusIds
            };
            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<GenerateReportApiResponse>>
                (apiRequest, ApiMethodName.Report.Generate, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return await ExportToExcel(viewModel, apiResponse.Data);

        }
        private async Task<FileContentResult> ExportToExcel(ScanCyleReportReportViewModel viewModel, GenerateReportApiResponse apiResponse)
        {
            if (apiResponse == null || apiResponse.Result == null)
                return null;
            return await ExportToExcelScanCycle(viewModel, JsonConvert.DeserializeObject<Report_ScanCycleResponse>(apiResponse.Result.ToString()));

        }
        private async Task<FileContentResult> ExportToExcelScanCycle(ScanCyleReportReportViewModel viewModel, Report_ScanCycleResponse response)
        {
            var reportName = EnumResources.ReportTypeScanCycle.ToTitleCase();

            var reportCreatedBy = UserSession.FullName;
            var reportDate = DateTime.UtcNow.Date;

            var visaCategories = await GetCachedVisaCategories();

            using (var workbook = new XLWorkbook())
            {
                foreach (var branchData in response.Branches)
                {
                    var branch = $"{rgx.Replace(branchData.BranchName, string.Empty)}";
                    var sheetName = branch.Length > 30 ?
                            branch.Substring(0, 30) : branch;

                    var worksheet = workbook.Worksheets.Add(sheetName);

                    var currentRow = 1;
                    var headerRow = -1;

                    worksheet.Cell(currentRow, 1).Value = reportName;
                    var rangeTitle = worksheet.Range(worksheet.Cell(currentRow, 1), worksheet.Cell(currentRow, 5));
                    rangeTitle.Merge().Style.Font.SetBold().Font.FontSize = 14;
                    currentRow++;
                    currentRow++;

                    worksheet.Cell(currentRow, 1).Value = SiteResources.BranchName.ToTitleCase();
                    worksheet.Cell(currentRow, 1).Style.Font.SetBold();
                    currentRow++;
                    worksheet.Cell(currentRow, 1).Value = branchData.BranchName;
                    currentRow++;
                    currentRow++;

                    worksheet.Cell(currentRow, 1).Value = SiteResources.CreatedBy.ToTitleCase();
                    worksheet.Cell(currentRow, 1).Style.Font.SetBold();
                    currentRow++;
                    worksheet.Cell(currentRow, 1).Value = reportCreatedBy;
                    currentRow++;
                    currentRow++;

                    worksheet.Cell(currentRow, 1).Value = SiteResources.ReportDate.ToTitleCase();
                    worksheet.Cell(currentRow, 1).Style.Font.SetBold();
                    worksheet.Cell(currentRow, 3).Value = SiteResources.StartDate.ToTitleCase();
                    worksheet.Cell(currentRow, 3).Style.Font.SetBold();
                    worksheet.Cell(currentRow, 5).Value = SiteResources.EndDate.ToTitleCase();
                    worksheet.Cell(currentRow, 5).Style.Font.SetBold();
                    currentRow++;

                    worksheet.Cell(currentRow, 1).Value = reportDate;
                    worksheet.Cell(currentRow, 1).Style.DateFormat.Format = SiteResources.DatePickerFormatView;
                    worksheet.Cell(currentRow, 3).Value = viewModel.FilterStartDate.GetValueOrDefault();
                    worksheet.Cell(currentRow, 3).Style.DateFormat.Format = SiteResources.DatePickerFormatView;
                    worksheet.Cell(currentRow, 5).Value = viewModel.FilterEndDate.GetValueOrDefault();
                    worksheet.Cell(currentRow, 5).Style.DateFormat.Format = SiteResources.DatePickerFormatView;

                    currentRow++;
                    currentRow++;

                    foreach (var scanCycleStatus in branchData.Data)
                    {
                        Boolean extraFeeCheck = false;
                        int currentRowMax = 13;
                        if ((scanCycleStatus.Name == SiteResources.OutscanToEmbassy || scanCycleStatus.Name == SiteResources.ReceivedAtEmbassy || scanCycleStatus.Name == SiteResources.SentToVACFromEmbassy) && viewModel.FilterBranchIds.Contains(37))
                        {
                            extraFeeCheck = true;
                            currentRowMax = 14;
                        }
                        worksheet.Cell(currentRow, 1).Value = scanCycleStatus.Name;
                        var rangeGroupHeader = worksheet.Range(worksheet.Cell(currentRow, 1), worksheet.Cell(currentRow, 12));
                        rangeGroupHeader.Merge().Style.Font.SetBold().Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                        headerRow = currentRow;
                        currentRow++;

                        worksheet.Cell(currentRow, 1).Value = SiteResources.OrderNumber.ToTitleCase();
                        worksheet.Cell(currentRow, 2).Value = SiteResources.ApplicationNumber.ToTitleCase();
                        worksheet.Cell(currentRow, 3).Value = SiteResources.PassportNumber.ToTitleCase();
                        worksheet.Cell(currentRow, 4).Value = SiteResources.ApplicationDate.ToTitleCase();
                        worksheet.Cell(currentRow, 5).Value = $"{SiteResources.Name.ToTitleCase()} {SiteResources.Surname.ToTitleCase()}";
                        worksheet.Cell(currentRow, 6).Value = SiteResources.Gender.ToTitleCase();
                        worksheet.Cell(currentRow, 7).Value = SiteResources.Nationality.ToTitleCase();
                        worksheet.Cell(currentRow, 8).Value = SiteResources.BirthDate.ToTitleCase();
                        worksheet.Cell(currentRow, 9).Value = SiteResources.ApplicantType.ToTitleCase();
                        worksheet.Cell(currentRow, 10).Value = SiteResources.ApplicationType.ToTitleCase();
                        worksheet.Cell(currentRow, 11).Value = SiteResources.VisaCategory.ToTitleCase();
                        if (extraFeeCheck)
                        {
                            worksheet.Cell(currentRow, 12).Value = SiteResources.VisaFeeInformation.ToTitleCase();
                            worksheet.Cell(currentRow, 13).Value = SiteResources.CreatedBy.ToTitleCase();
                            worksheet.Cell(currentRow, 14).Value = SiteResources.DateAndTime.ToTitleCase();

                        }
                        else
                        {
                            worksheet.Cell(currentRow, 12).Value = SiteResources.CreatedBy.ToTitleCase();
                            worksheet.Cell(currentRow, 13).Value = SiteResources.DateAndTime.ToTitleCase();
                        }
                        var rangeGroupTableHeader = worksheet.Range(worksheet.Cell(currentRow, 1), worksheet.Cell(currentRow, 100));
                        rangeGroupTableHeader.Style.Font.SetBold();
                        currentRow++;

                        for (int i = 0; i < scanCycleStatus.Applications.Count(); i++)
                        {
                            var data = scanCycleStatus.Applications.ElementAt(i);
                            worksheet.Cell(currentRow, 1).Value = data.OrderNo;
                            worksheet.Cell(currentRow, 2).SetValue(data.ApplicationNumber);
                            worksheet.Cell(currentRow, 3).SetValue(data.PassportNumber);
                            worksheet.Cell(currentRow, 4).Value = data.ApplicationTime;
                            worksheet.Cell(currentRow, 4).Style.DateFormat.Format = SiteResources.DatePickerFormatView;
                            worksheet.Cell(currentRow, 5).Value = $"{data.Name} {data.Surname}";
                            worksheet.Cell(currentRow, 6).Value = ((Gender)data.GenderId).ToDescription();
                            worksheet.Cell(currentRow, 7).Value = data.NationalityCode;
                            worksheet.Cell(currentRow, 8).Value = data.BirthDate;
                            worksheet.Cell(currentRow, 8).Style.DateFormat.Format = SiteResources.DatePickerFormatView;
                            worksheet.Cell(currentRow, 9).Value = ((ApplicantType)data.ApplicantTypeId).ToDescription();
                            worksheet.Cell(currentRow, 10).Value = ((ApplicationType)data.ApplicationTypeId).ToDescription();
                            worksheet.Cell(currentRow, 11).Value = GetVisaCategoryNameFromId(data.VisaCategoryId, visaCategories);
                            if ((scanCycleStatus.Name == SiteResources.OutscanToEmbassy || scanCycleStatus.Name == SiteResources.ReceivedAtEmbassy || scanCycleStatus.Name == SiteResources.SentToVACFromEmbassy) && viewModel.FilterBranchIds.Contains(37))
                            {
                                if (data.ExtraFeeTranslation != null)
                                    worksheet.Cell(currentRow, 12).Value = data.ExtraFeeTranslation + "-" + data.Price + " USD";
                                else
                                    worksheet.Cell(currentRow, 12).Value = "-"; worksheet.Cell(currentRow, 13).Value = $"{data.StaffName} {data.StaffSurname}";
                                worksheet.Cell(currentRow, 14).Value = data.StatusHistoryCreatedDate;
                                worksheet.Cell(currentRow, 14).Style.DateFormat.Format = SiteResources.DateTimePickerFormatView;

                            }
                            else
                            {
                                worksheet.Cell(currentRow, 12).Value = $"{data.StaffName} {data.StaffSurname}";
                                worksheet.Cell(currentRow, 13).Value = data.StatusHistoryCreatedDate;
                                worksheet.Cell(currentRow, 13).Style.DateFormat.Format = SiteResources.DateTimePickerFormatView;
                            }
                            currentRow++;
                        }

                        var rangeGroupTable = worksheet.Range(worksheet.Cell(headerRow, 1), worksheet.Cell(currentRow - 1, currentRowMax));
                        rangeGroupTable.Style.Border.SetTopBorder(XLBorderStyleValues.Thin);
                        rangeGroupTable.Style.Border.SetBottomBorder(XLBorderStyleValues.Thin);
                        rangeGroupTable.Style.Border.SetLeftBorder(XLBorderStyleValues.Thin);
                        rangeGroupTable.Style.Border.SetRightBorder(XLBorderStyleValues.Thin);
                        rangeGroupTable.Style.Border.SetInsideBorder(XLBorderStyleValues.Thin);
                        rangeGroupTable.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                        var rangeAlignment = worksheet.Range(worksheet.Cell(1, 1), worksheet.Cell(currentRow - 1, currentRowMax));
                        rangeAlignment.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);

                        for (int i = 1; i <= currentRowMax; i++)
                        {
                            worksheet.Column(i).AdjustToContents();
                        }

                        currentRow++;
                        currentRow++;
                    }
                }

                using (var stream = new MemoryStream())
                {
                    workbook.SaveAs(stream);
                    var content = stream.ToArray();
                    return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{reportName} - {reportDate.ToString("ddMMyyyy")}.xlsx");
                }
            }
        }
    }
}
