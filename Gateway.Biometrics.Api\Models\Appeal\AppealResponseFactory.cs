﻿using Gateway.Biometrics.Application.Appeal.DTO;
using Gateway.Biometrics.Application.Chunk.DTO;
using Gateway.Biometrics.Application.DemographicInformation.DTO;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Mvc;

namespace Gateway.Biometrics.Api.Models.Appeal
{
    public static class AppealResponseFactory
    {
        public static ObjectResult CreateAppealResponse(CreateAppealResult result)
        {
            switch (result.Status)
            {
                case CreateAppealStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_CREATED),
                        Message = result.Message,
                        Data = new
                        {
                            Id = result.Id
                        }
                    })
                    { StatusCode = HttpStatusCodes.Created };

                case CreateAppealStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case CreateAppealStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };

                case CreateAppealStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };

                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult InsertAppealWithMetaDataResponse(InsertAppealWithMetaDataResult result)
        {
            switch (result.Status)
            {
                case InsertAppealWithMetaDataStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_CREATED),
                        Message = result.Message,
                        Data = new
                        {
                            Id = result.Id
                        }
                    })
                    { StatusCode = HttpStatusCodes.Created };

                case InsertAppealWithMetaDataStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case InsertAppealWithMetaDataStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };

                case InsertAppealWithMetaDataStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };

                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult InsertAppealWithMetaDataFromOfflineResponse(InsertAppealWithMetaDataFromOfflineResult result)
        {
            switch (result.Status)
            {
                case InsertAppealWithMetaDataFromOfflineStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_CREATED),
                        Message = result.Message,
                        Data = new
                        {
                            OfflineId = result.OfflineId
                        }
                    })
                    { StatusCode = HttpStatusCodes.Created };

                case InsertAppealWithMetaDataFromOfflineStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case InsertAppealWithMetaDataFromOfflineStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };

                case InsertAppealWithMetaDataFromOfflineStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };

                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }


        public static ObjectResult InsertAppealWithMetaDataFastResponse(InsertAppealWithMetaDataFastResult result)
        {
            switch (result.Status)
            {
                case InsertAppealWithMetaDataFastStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_CREATED),
                        Message = result.Message,
                        Data = new
                        {
                            Id = result.Id,
                            AppealId = result.AppealId
                        }
                    })
                    { StatusCode = HttpStatusCodes.Created };

                case InsertAppealWithMetaDataFastStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case InsertAppealWithMetaDataFastStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };

                case InsertAppealWithMetaDataFastStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };

                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult SaveAppealMetaDataFullResponse(SaveAppealMetaDataFullResult result)
        {
            switch (result.Status)
            {
                case SaveAppealMetaDataFullStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_CREATED),
                        Message = result.Message,
                        Data = new
                        {
                            Id = result.Id,
                        }
                    })
                    { StatusCode = HttpStatusCodes.Created };

                case SaveAppealMetaDataFullStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case SaveAppealMetaDataFullStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };

                case SaveAppealMetaDataFullStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };

                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }


        public static ObjectResult InsertAppealMetaDataResponse(InsertAppealMetaDataResult result)
        {
            switch (result.Status)
            {
                case InsertAppealMetaDataStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_CREATED),
                        Message = result.Message,
                        Data = new
                        {
                            Id = result.Id
                        }
                    })
                    { StatusCode = HttpStatusCodes.Created };

                case InsertAppealMetaDataStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case InsertAppealMetaDataStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };

                case InsertAppealMetaDataStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };

                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }


        public static ObjectResult GetAppealsByPassportAndCountryResponse(GetAppealsResult result)
        {
            switch (result.Status)
            {
                case GetAppealsStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.Appeals
                    })
                    { StatusCode = HttpStatusCodes.Created };

                case GetAppealsStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case GetAppealsStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };

                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult GetAppealsByXmlResponse(GetAppealsResult result)
        {
            switch (result.Status)
            {
                case GetAppealsStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.Appeals
                    })
                    { StatusCode = HttpStatusCodes.Created };

                case GetAppealsStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case GetAppealsStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };

                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult GetFullAppealMetaDataResponse(GetFullAppealMetaDataResult result)
        {
            switch (result.Status)
            {
                case GetFullAppealMetaDataStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.AppealMetaData
                    })
                    { StatusCode = HttpStatusCodes.Created };

                case GetFullAppealMetaDataStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case GetFullAppealMetaDataStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };

                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult SaveAppealMetaDataByChunksResponse(SaveDataByChunksResult result)
        {
            switch (result.Status)
            {
                case SaveDataByChunksStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_CREATED),
                        Message = result.Message,
                        Data = new
                        {
                            RecordId = result.RecordId,
                            Part = result.Part,
                            TotalPart = result.TotalPart,
                            UniqueId = result.UniqueId,
                            FromOffline = result.FromOffline
                        }
                    })
                    { StatusCode = HttpStatusCodes.Created };

                case SaveDataByChunksStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case SaveDataByChunksStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };

                case SaveDataByChunksStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };

                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }



    }
}
