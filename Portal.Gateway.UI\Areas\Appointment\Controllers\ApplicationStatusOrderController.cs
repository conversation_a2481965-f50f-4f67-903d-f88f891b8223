﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.Appointment.ApplicationStatusOrder;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.ApplicationStatus;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Appointment.Controllers
{
    [Area("Appointment")]
    public class ApplicationStatusOrderController : BaseController<ApplicationStatusOrderController>
    {
        public ApplicationStatusOrderController(
            IOptions<AppSettings> appSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper)
        {

        }

        [HttpGet]
        public async Task<IActionResult> List(string encryptedAppStatusId)
        {
            if (string.IsNullOrEmpty(encryptedAppStatusId))
            {
                TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
                return RedirectToAction("/");
            }

            var apiResponse = await PortalHttpClientHelper
                                    .GetAsync<ApiResponse<ApplicationStatusOrderApiResponse>>
                                    (ApiMethodName.Parameter.GetApplicationStatusOrder + encryptedAppStatusId.ToDecryptInt(), AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                                    .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                TempData.Put("Notification", result);
                return RedirectToAction("/");
            }

            var viewModel = new ApplicationStatusOrderViewModel()
            {
                EncryptedApplicationStatusId = apiResponse.Data.ApplicationStatusId.ToEncrypt(),
                Name = apiResponse.Data.NameTranslation.Any(q => q.LanguageId == LanguageId) ?
                            apiResponse.Data.NameTranslation.First(q => q.LanguageId == LanguageId).Name :
                            apiResponse.Data.NameTranslation.FirstOrDefault().Name,
                ApplcationStatusOrders = apiResponse.Data.ApplcationStatusOrders
                                        .Where(p => p.IdName.Id != apiResponse.Data.ApplicationStatusId)
                                        .Select(p => new ApplicationStatusOrdersViewModel() 
                {
                    IsActive = p.IsActive,
                    EncryptedApplicationStatusOrderId = p.IdName.Id.ToEncrypt(),
                    Name = p.IdName.NameTranslation.Any(q => q.LanguageId == LanguageId) ?
                            p.IdName.NameTranslation.First(q => q.LanguageId == LanguageId).Name :
                            p.IdName.NameTranslation.FirstOrDefault().Name,
                }).ToList()
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateApplicationStatusOrder(ApplicationStatusOrderViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new UpdateApplicationStatusOrderApiRequest
            {
                ApplicationStatusId = viewModel.EncryptedApplicationStatusId.ToDecryptInt(),
                ApplicationStatusOrderIds = viewModel.ApplcationStatusOrders.Where(p => p.IsActive)
                                            .Select(p => p.EncryptedApplicationStatusOrderId.ToDecryptInt())
                                            .ToList()
            };

            var apiResponse = await PortalHttpClientHelper
                .PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
                (apiRequest, ApiMethodName.Appointment.UpdateApplicationStatusOrder, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }
    }
}