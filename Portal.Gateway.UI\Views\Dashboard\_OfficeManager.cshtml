﻿@using Microsoft.AspNetCore.Builder
@using Microsoft.Extensions.Options
@using Microsoft.AspNetCore.Http
@using Portal.Gateway.UI.Constants
@inject IHttpContextAccessor HttpContextAccessor
@inject CurrentUserDataHelper CurrentUserDataHelper
@inject IOptions<RequestLocalizationOptions> RequestLocalizationOptions

@{
    ViewBag.Title = SiteResources.Dashboard;
    var currentUser = Portal.Gateway.UI.Extensions.SessionExtensions.Get<UserModel>(HttpContextAccessor.HttpContext.Session, SessionKeys.UserSession);
    var userBranches = await CurrentUserDataHelper.BuildUserBranches(currentUser);
    var currentBranchName = "";

    if (userBranches.Any() && currentUser.BranchId.HasValue)
    {
        var branchList = new Dictionary<int, string>();

        foreach (var item in userBranches)
        {
            if (@item.BranchNames.Any())
            {
                var branchName = "";

                if (item.BranchNames.Any(p => p.Key == Html.CurrentLanguageId().ToString().ToInt()))
                    branchName = item.BranchNames.First(p => p.Key == Html.CurrentLanguageId().ToString().ToInt()).Value;
                else
                    branchName = item.BranchNames.First().Value;

                branchList.Add(item.BranchId, branchName);
            }
        }

        branchList.TryGetValue(currentUser.BranchId.Value, out currentBranchName);
    }
}

<div class="row">
    <div class="col-xl-12">
        <div class="card card-custom gutter-b">
            <div class="card-header h-auto border-0">
                <div class="card-title py-5">
                    <h3 class="card-label">
                        <span class="d-block text-dark font-weight-bolder">@SiteResources.Dashboard</span>
                        <span class="d-block text-muted mt-2 font-size-sm">@currentBranchName</span>
                    </h3>
                </div>
            </div>
        </div>
    </div>

    <partial name="_OfficeManager/_GeneralStats" />
    <partial name="_OfficeManager/_WeeklyStats" />
    <partial name="_WorldMap" />
    <partial name="_OfficeManager/_QuarterStats" />
    <partial name="_OfficeManager/_ExtraFeeStats" />
    <partial name="_OfficeManager/_DeliveryStats" />
    <partial name="_OfficeManager/_InsuranceStats" />
    <partial name="_OfficeManager/_DailyApplicationTypeStats" />

</div>

<script src="~/js/Dashboard/officeManager.js"></script>
