namespace Portal.Gateway.Api.IntegrationTests.Helpers;

public static class HttpClientExtensions
{
    private static readonly JsonSerializerOptions JsonSerializerOptions = new()
    {
        PropertyNameCaseInsensitive = true,
        DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
        WriteIndented = true
    };

    public static async Task<ApiResponse<T>> PostAndGetResultAsync<T>(
        this HttpClient httpClient,
        string requestUri,
        object request)
    {
        var response = await httpClient.PostAsJsonAsync(requestUri, request);
        return await DeserializeApiResponse<T>(response);
    }
    
    public static async Task<T> PostAndGetResultWithoutApiResponseAsync<T>(
        this HttpClient httpClient,
        string requestUri,
        object request)
    {
        var response = await httpClient.PostAsJsonAsync(requestUri, request);
        return await DeserializeWithoutApiResponse<T>(response);
    }

    public static async Task<ApiResponse<T>> GetAndDeserializeAsync<T>(
        this HttpClient httpClient,
        string requestUri)
    {
        var response = await httpClient.GetAsync(requestUri);
        return await DeserializeApiResponse<T>(response);
    }

    public static async Task<ApiResponse<T>> PutAndGetResultAsync<T>(
        this HttpClient httpClient,
        string requestUri,
        object request)
    {
        var response = await httpClient.PutAsJsonAsync(requestUri, request);
        return await DeserializeApiResponse<T>(response);
    }

    public static async Task<T> PutAndGetResultWithoutApiResponseAsync<T>(
        this HttpClient httpClient,
        string requestUri,
        object request)
    {
        var response = await httpClient.PutAsJsonAsync(requestUri, request);
        return await DeserializeWithoutApiResponse<T>(response);
    }

    public static async Task<ApiResponse<T>> DeleteAndGetResultAsync<T>(
        this HttpClient httpClient,
        string requestUri)
    {
        var response = await httpClient.DeleteAsync(requestUri);
        return await DeserializeApiResponse<T>(response);
    }

    private static async Task<ApiResponse<T>> DeserializeApiResponse<T>(HttpResponseMessage response)
    {
        var responseBody = await response.Content.ReadAsStringAsync();
        return System.Text.Json.JsonSerializer.Deserialize<ApiResponse<T>>(responseBody, JsonSerializerOptions)!;
    } 
    private static async Task<T> DeserializeWithoutApiResponse<T>(HttpResponseMessage response)
    {
        var responseBody = await response.Content.ReadAsStringAsync();
        return System.Text.Json.JsonSerializer.Deserialize<T>(responseBody, JsonSerializerOptions)!;
    }
}
