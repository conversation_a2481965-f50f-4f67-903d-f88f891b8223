﻿namespace Portal.Gateway.Entity.Entities.Portal
{
    public class PushNotificationHistory : AuditableEntity
    {
        public PushNotificationHistory()
        {
            IsActive = true;
            IsDeleted = false;
        }
        public int Id { get; set; }
        public int CustomerUserId { get; set; }
        public int PushNotificationId { get; set; }
        public int LocationId { get; set; }
        public int LanguageId { get; set; }
        public int NationalityId { get; set; }
        public string Text { get; set; }
        public string Subject { get; set; }
        public PushNotification PushNotification { get; set; }
    }
}
