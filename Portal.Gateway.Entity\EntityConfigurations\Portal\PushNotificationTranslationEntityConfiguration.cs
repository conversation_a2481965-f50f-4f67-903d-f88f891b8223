﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Portal.Gateway.Entity.Entities.Portal;

namespace Portal.Gateway.Entity.EntityConfigurations.Portal
{
    public class PushNotificationTranslationEntityConfiguration : IEntityTypeConfiguration<PushNotificationTranslation>
    {
        public void Configure(EntityTypeBuilder<PushNotificationTranslation> builder)
        {
            builder.ToTable("PushNotificationTranslation");

            #region StaticAuditableEntityConfig

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).IsRequired().ValueGeneratedOnAdd();
            builder.Property(e => e.IsActive).IsRequired();
            builder.Property(e => e.IsDeleted).IsRequired();
            builder.Property(e => e.CreatedBy);
            builder.Property(e => e.CreatedAt).HasColumnType("timestamp with time zone");
            builder.Property(e => e.UpdatedBy);
            builder.Property(e => e.UpdatedAt).HasColumnType("timestamp with time zone");
            builder.Property(e => e.DeletedBy);
            builder.Property(e => e.DeletedAt).HasColumnType("timestamp with time zone");

            #endregion

            builder.Property(e => e.Content)
                .IsRequired()
                .HasColumnType("citext");

            builder.Property(e => e.Subject).HasColumnType("citext").HasDefaultValueSql("''::citext");

            builder.Property(e => e.LanguageId)
                .IsRequired();

            builder.HasOne(d => d.PushNotification)
                .WithMany(p => p.PushNotificationTranslations)
                .HasForeignKey(d => d.PushNotificationId)
                .OnDelete(DeleteBehavior.Restrict);
        }
    }
}
