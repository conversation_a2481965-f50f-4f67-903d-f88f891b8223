﻿using Gateway.Extensions;
using Gateway.Http;
using Gateway.Redis;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests.Management.Counter;
using Portal.Gateway.ApiModel.Requests.QmsFolder;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Management.ViewModels.Counter;
using Portal.Gateway.UI.Areas.Management.ViewModels.Counter.RequestModels;
using Portal.Gateway.UI.Areas.Management.ViewModels.Counter.Results;
using Portal.Gateway.UI.Areas.Management.ViewModels.Counter.ViewModels;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text.Json;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Management.Controllers
{
    [Area("Management")]
    public class CounterController : BaseController<CounterController>
    {
        public CounterController(
            IOptions<AppSettings> appSettings,
            ICacheHelper cacheHelper)
            : base(appSettings, cacheHelper) { }

        #region Add

        public IActionResult PartialAddCounter()
        {
            var viewModel = new AddCounterViewModel();

            return PartialView("_AddCounter", viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddCounter(AddCounterViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var request = new AddCounterRequestModel
            {
                LineId = viewModel.LineId,
                DepartmentId = viewModel.DepartmentId,
                BranchId = viewModel.BranchId,
                Name = viewModel.Name,
                BlockNumber = viewModel.BlockNumber,
                FloorNumber = viewModel.FloorNumber,
                OrderList = viewModel.OrderData,
                UserId = UserSession.UserId,
            };

            var apiResponse = await RestHttpClient.Create().Post<AddCounterResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.AddCounter, headers, request);

            if (apiResponse.Data == null)
                return Json(new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update

        public async Task<IActionResult> PartialUpdateCounter(string encryptedCounterId)
        {
            if (encryptedCounterId.IsNullOrWhitespace())
            {
                return Content("Missing counter id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedCounterId);

            var apiResponse = await RestHttpClient.Create().Get<GetCounterResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.GetCounter + resourceId, headers);

            var viewModel = new UpdateCounterViewModel
            {
                Id = apiResponse.Data.Id,
                BlockNumber = apiResponse.Data.BlockNumber,
                Order = apiResponse.Data.Order,
                BranchId = apiResponse.Data.Branch.Id,
                FloorNumber = apiResponse.Data.FloorNumber,
                LineId = apiResponse.Data.Line.Id,
                DepartmentId = apiResponse.Data.Department.Id,
                Name = apiResponse.Data.Name
            };

            return PartialView("_UpdateCounter", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateCounter(UpdateCounterViewModel viewModel)
        {
            if (viewModel == null || !ModelState.IsValid) //buraya fluent val yazılıcak viewmodellere
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var apiRequest = new UpdateCounterRequestModel
            {
                Name = viewModel.Name,
                LineId = viewModel.LineId,
                DepartmentId = viewModel.DepartmentId,
                BranchId = viewModel.BranchId,
                FloorNumber = viewModel.FloorNumber,
                BlockNumber = viewModel.BlockNumber,
                UserId = UserSession.UserId
            };

            var apiResponse = await RestHttpClient.Create().Put<UpdateCounterResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.UpdateCounter + viewModel.Id, headers, apiRequest);


            if (apiResponse.Data == null)
                return Json(new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Danger });

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

        #region Update Order
        public async Task<IActionResult> PartialUpdateCounterOrder(int lineDepartmentId, int branchId)
        {
            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var apiResponse = await RestHttpClient.Create().Get<GetCountersByLineDepartmentResult>(AppSettings.Qms.BaseApiUrl + $"/api/linedepartments/{lineDepartmentId}/branches/{branchId}/counters", headers);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var viewModel = apiResponse.Data.Select(p => new UpdateCounterOrderViewModel
            {
                Order = p.Order,
                BlockNumber = p.BlockNumber,
                FloorNumber = p.FloorNumber,
                Id = p.Id,
                Name = p.Name
            }).ToList();

            return PartialView("_UpdateCounterOrder", viewModel);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateCounterOrder([FromBody] List<UpdateCounterOrdersViewModel> counters)
        {
            if(counters == null || !counters.Any())
                return Content(EnumResources.MissingOrInvalidData);

            var index = 1;

            var apiRequest = new UpdateCounterOrderRequestModel
            {
                Counters = counters.Select(r => new CounterOrderDto()
                {
                    Id = r.Id,
                    Order = index++
                }).ToList()
            };

            var apiResponse = await RestHttpClient.Create().Put<UpdateCounterOrderResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.UpdateCounterOrder, QMSApiDefaultRequestHeaders, apiRequest);

            return Json(apiResponse.Data == null ? new ResultModel { Message = apiResponse.Message, ResultType = ResultType.Danger } : new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }
        #endregion

        #region Get

        public async Task<IActionResult> PartialCounter(string encryptedCounterId)
        {
            if (encryptedCounterId.IsNullOrWhitespace())
            {
                return Content("Missing counter id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedCounterId);

            var apiResponse = await RestHttpClient.Create().Get<GetCounterResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.GetCounter + resourceId, headers);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var viewModel = new CounterViewModel
            {
                EncryptedId = apiResponse.Data.Id.ToEncrypt(),
                Order = apiResponse.Data.Order,
                Line = apiResponse.Data.Line,
                Department = apiResponse.Data.Department,
                Branch = apiResponse.Data.Branch,
                BlockNumber = apiResponse.Data.BlockNumber,
                FloorNumber = apiResponse.Data.FloorNumber,
                Name = apiResponse.Data.Name,
            };

            return PartialView("_Counter", viewModel);
        }


        [Action(IsMenuItem = true)]
        public IActionResult List()
        {
            return View();
        }

        public async Task<IActionResult> GetPaginatedCounters([DataSourceRequest] DataSourceRequest request, FilterCounterViewModel filterViewModel)
        {
            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedCounterApiRequest
            {
                FilterBranchId = filterViewModel.FilterBranchId,
                FilterLineId = filterViewModel.FilterLineId,
                FilterDepartmentId = filterViewModel.FilterDepartmentId,
                Pagination = new QmsPaginationApiRequest
                {
                    PageNumber = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = "Name",
                    Ascending = true
                }
            };

            var apiResponse = await RestHttpClient.Create().Post<GetPaginatedCountersResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.GetPaginatedCounters, headers, apiRequest);

            if (apiResponse.Data == null)
                return Content(apiResponse.Message);

            var paginatedData = apiResponse.Data.Select(p =>
                new CounterViewModel
                {
                    Name = p.Name,
                    Order = p.Order,
                    LineDepartmentId = p.LineDepartmentId,
                    Line = new LineDto
                    {
                        Id = p.Line.Id,
                        Name = p.Line.Name,
                    },
                    Department = new DepartmentDto
                    {
                        Id = p.Department.Id,
                        Name = p.Department.Name,
                    },
                    BlockNumber = p.BlockNumber,
                    FloorNumber = p.FloorNumber,
                    Branch = new BranchDto
                    {
                        Id = p.Branch.Id,
                        Name = p.Branch.Name,
                        CountryName = p.Branch.CountryName
                    },
                    EncryptedId = p.Id.ToString()
                }).ToList();

            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.TotalNumberOfRecords });
        }

        #endregion

        #region Delete

        [HttpDelete]
        public async Task<IActionResult> DeleteCounter(string encryptedCounterId)
        {
            if (encryptedCounterId.IsNullOrWhitespace())
            {
                return Content("Missing counter id");
            }

            var headers = new WebHeaderCollection();

            foreach (var item in PortalGatewayApiDefaultRequestHeaders)
            {
                headers.Add(item.Key, item.Value);
            }

            headers.Add("Accept-Language", LanguageId == 1 ? "tr-TR" : "en");

            var resourceId = Convert.ToInt32(encryptedCounterId);

            var apiResponse = await RestHttpClient.Create().Delete<DeleteCounterResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.DeleteCounter + resourceId, headers);

            if (apiResponse.Data == null)
                return Json(apiResponse.Message);

            return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        #endregion

    }
}
